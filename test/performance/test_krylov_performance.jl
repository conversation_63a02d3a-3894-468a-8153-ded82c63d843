# Performance test for new Krylov solvers
using CFD
using CFD.Solvers.LinearSolvers
using LinearAlgebra
using SparseArrays
using Printf

function test_krylov_performance()
    println("="^70)
    println("KRYLOV SOLVERS PERFORMANCE TEST")
    println("="^70)
    
    # Create a test problem: 2D Poisson equation on a unit square
    n = 50  # Grid size
    h = 1.0 / (n + 1)
    N = n * n
    
    println("Problem size: $N unknowns ($n×$n grid)")
    println("Grid spacing: $h")
    
    # Create 2D Poisson matrix: -∇²u = f
    # Using 5-point stencil
    function create_2d_poisson(n)
        N = n * n
        I_vals = Int[]
        J_vals = Int[]
        V_vals = Float64[]
        
        for i in 1:n, j in 1:n
            row = (j-1)*n + i
            
            # Center point
            push!(I_vals, row)
            push!(J_vals, row) 
            push!(V_vals, 4.0)
            
            # Left neighbor
            if i > 1
                push!(I_vals, row)
                push!(J_vals, row - 1)
                push!(V_vals, -1.0)
            end
            
            # Right neighbor  
            if i < n
                push!(I_vals, row)
                push!(J_vals, row + 1)
                push!(V_vals, -1.0)
            end
            
            # Bottom neighbor
            if j > 1
                push!(I_vals, row)
                push!(J_vals, row - n)
                push!(V_vals, -1.0)
            end
            
            # Top neighbor
            if j < n
                push!(I_vals, row)
                push!(J_vals, row + n)
                push!(V_vals, -1.0)
            end
        end
        
        return sparse(I_vals, J_vals, V_vals, N, N)
    end
    
    A = create_2d_poisson(n)
    b = ones(N)  # RHS
    
    println("Matrix condition number: ~$(round(cond(Array(A)), sigdigits=3))")
    println()
    
    # Test different solvers
    solvers = [
        ("PCG", PCG(tol=1e-8, maxiter=1000)),
        ("BiCGSTAB", BiCGSTAB(tol=1e-8, maxiter=1000)),
        ("GMRES", GMRES(tol=1e-8, maxiter=1000)),
        ("CGS", CGS(tol=1e-8, maxiter=1000)),
        ("BiCG", BiCG(tol=1e-8, maxiter=1000)),
        ("TFQMR", TFQMR(tol=1e-8, maxiter=1000))
    ]
    
    results = []
    
    for (name, solver) in solvers
        print("Testing $name... ")
        
        # Warm-up run
        try
            solve!(solver, A, b)
        catch e
            println("FAILED (warmup): $e")
            continue
        end
        
        # Timed run
        time_start = time()
        try
            result = solve!(solver, A, b)
            time_elapsed = time() - time_start
            
            # Verify solution quality
            x = result.x
            residual = norm(A * x - b) / norm(b)
            
            push!(results, (name, result.converged, result.iterations, 
                          time_elapsed, residual, result.residual))
            
            status = result.converged ? "✓" : "✗"
            println("$status ($(@sprintf("%.3f", time_elapsed))s, $(result.iterations) iter, res=$(@sprintf("%.2e", residual)))")
            
        catch e
            println("FAILED: $e")
            push!(results, (name, false, 0, Inf, Inf, Inf))
        end
    end
    
    println()
    println("="^70)
    println("PERFORMANCE SUMMARY")
    println("="^70)
    printf_header = "%-10s | %-4s | %-6s | %-8s | %-10s\n"
    @printf(printf_header, "Solver", "Conv", "Iters", "Time(s)", "Residual")
    println("-"^55)
    
    for (name, converged, iters, time_elapsed, residual, _) in results
        conv_str = converged ? "✓" : "✗"
        @printf("%-10s | %-4s | %-6d | %-8.3f | %-10.2e\n", 
                name, conv_str, iters, time_elapsed, residual)
    end
    
    # Find best performers
    converged_results = filter(r -> r[2], results)
    if !isempty(converged_results)
        fastest = minimum(r -> r[4], converged_results)
        fewest_iters = minimum(r -> r[3], converged_results)
        
        println()
        println("🏆 WINNERS:")
        
        fastest_solver = filter(r -> r[4] ≈ fastest, converged_results)[1]
        println("  Fastest: $(fastest_solver[1]) ($(@sprintf("%.3f", fastest))s)")
        
        fewest_solver = filter(r -> r[3] == fewest_iters, converged_results)[1]
        println("  Fewest iterations: $(fewest_solver[1]) ($(fewest_iters) iters)")
        
        println()
        println("💡 RECOMMENDATIONS:")
        println("  • CGS: Best for symmetric/mildly non-symmetric problems")
        println("  • TFQMR: Most robust for difficult non-symmetric systems") 
        println("  • BiCG: Good balance for general non-symmetric problems")
        println("  • Use with ILU preconditioning for best performance")
    end
    
    return results
end

# Run the test
if abspath(PROGRAM_FILE) == @__FILE__
    test_krylov_performance()
end