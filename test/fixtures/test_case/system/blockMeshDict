vertices
(
    (0 0 0) (1 0 0) (1 1 0) (0 1 0)
    (0 0 0.1) (1 0 0.1) (1 1 0.1) (0 1 0.1)
);

blocks
(
    hex (0 1 2 3 4 5 6 7) (10 10 1) simpleGrading (1 1 1)
);

boundary
(
    inlet   { type patch; faces ((0 4 7 3)); }
    outlet  { type patch; faces ((2 6 5 1)); }
    wall    { type wall; faces ((1 5 4 0) (3 7 6 2)); }
    front   { type empty; faces ((0 3 2 1)); }
    back    { type empty; faces ((4 5 6 7)); }
);
