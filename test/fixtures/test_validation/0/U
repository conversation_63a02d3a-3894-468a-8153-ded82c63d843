/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
|  \\    /   O peration     | Version:  v2112
|   \\  /    A nd           | Website:  www.openfoam.com
|    \\/     M anipulation  |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       vol$(field_data[:type] == :vector ? "Vector" : "Scalar")Field;
    location    "0";
    object      $(field_data[:name]);
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      $(field_data[:dimensions]);

internalField   $(field_data[:internal_field]);

boundaryField
{
    inlet
    {
        type            $(get(field_data[:boundary_conditions], :inlet, "fixedValue"));
    }
    
    outlet
    {
        type            $(get(field_data[:boundary_conditions], :outlet, "zeroGradient"));
    }
    
    walls
    {
        type            $(get(field_data[:boundary_conditions], :walls, "fixedValue"));
        value           uniform (0 0 0);
    }
    
    ".*"
    {
        type            $(get(field_data[:boundary_conditions], :default, "zeroGradient"));
    }
}

// ************************************************************************* //
