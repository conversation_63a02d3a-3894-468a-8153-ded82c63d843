#!/usr/bin/env julia
"""
    Test the new solver framework
"""

using CFD
using CFD.UnicodeDSL: @solver, @physics, @algorithm, @bc
using Test

@testset "Solver Framework Tests" begin
    
    @testset "Solver Registry" begin
        # Test listing solvers
        println("\n=== Testing Solver Registry ===")
        CFD.Solvers.list_solvers()
        
        # Test solver suggestions
        println("\n=== Testing Solver Suggestions ===")
        CFD.Solvers.suggest_solver("turbulent flow with heat transfer")
    end
    
    @testset "Solver DSL" begin
        println("\n=== Testing Solver DSL ===")
        
        # Create a simple test solver
        @solver TestHeatSolver begin
            @fields begin
                _builder.fields[:T] = CFD.Solvers.SolverDSL.ScalarField("T", required=true)
                _builder.fields[:k] = CFD.Solvers.SolverDSL.ScalarField("k", properties=true)
            end
            
            @equations begin
                @equation energy begin
                    ∂T/∂t = α∇²T + Q
                end
            end
            
            @algorithm begin
                _builder.algorithm[:type] = :explicit
                _builder.algorithm[:maxIter] = 100
            end
        end
        
        @test haskey(CFD.Solvers.SolverRegistry.REGISTERED_SOLVERS, :TestHeatSolver)
    end
    
    @testset "Mathematical Physics" begin
        println("\n=== Testing Mathematical Physics ===")
        
        # Test physics definition
        physics = @physics TestPhysics begin
            @equation momentum begin
                ∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮
            end
            
            @constants begin
                _physics.constants[:ν] = 0.01
            end
        end
        
        @test physics.name == :TestPhysics
        @test haskey(physics.equations, :momentum)
        @test physics.constants[:ν] == 0.01
    end
    
    @testset "FVM Workflow" begin
        println("\n=== Testing FVM Workflow ===")
        
        # Create a simple workflow
        workflow = @fvm_workflow TestWorkflow begin
            @stage Initialization begin
                println("Initializing workflow...")
                _workflow.context[:initialized] = true
            end
            
            @stage Discretization begin
                println("Discretizing equations...")
                CFD.FVMWorkflow.analyze_discretization(
                    "∂φ/∂t + ∇⋅(𝐮φ) = ∇²φ",
                    "linearUpwind"
                )
            end
        end
        
        @test workflow.name == :TestWorkflow
        @test length(workflow.stages) == 2
        
        # Execute workflow
        CFD.FVMWorkflow.execute_workflow(workflow, interactive=false)
    end
    
    @testset "Simple Usage Mode" begin
        println("\n=== Testing Simple Usage Mode ===")
        
        # Test that the simple interface is available
        @test isdefined(CFD.Solvers, :solve)
        @test isdefined(CFD.Solvers, :list_solvers)
        
        # Would test actual solving if we had a test case:
        # CFD.solve("test_case", solver=:heatTransferFoam)
    end
end

println("\n✅ All framework tests passed!")