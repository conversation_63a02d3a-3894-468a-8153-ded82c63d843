# Test for HPC-optimized SIMPLE solver with mathematical fixes

push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))
using CFD
using Test
using LinearAlgebra
using StaticArrays
using SparseArrays

println("🔬 Testing HPC-Optimized SIMPLE Solver")
println("=" ^ 70)

# Helper function to create a simple cavity mesh
function create_test_case(n::Int)
    # Create a simple n×n mesh
    dx = 1.0 / n
    
    # Create structured mesh (simplified implementation)
    nodes = Vector{CFDCore.Node}()
    for j in 1:(n+1)
        for i in 1:(n+1)
            x = (i-1) * dx
            y = (j-1) * dx
            node_id = (j-1)*(n+1) + i
            push!(nodes, CFDCore.Node(node_id, SVector(x, y, 0.0), false))
        end
    end
    
    # Create cells and faces
    cells = Vector{CFDCore.Cell}()
    faces = Vector{CFDCore.Face{Float64, 3}}()
    face_id = 1
    cell_id = 1
    
    for j in 1:n
        for i in 1:n
            # Cell node indices
            sw = (j-1)*(n+1) + i
            se = (j-1)*(n+1) + i + 1
            ne = j*(n+1) + i + 1
            nw = j*(n+1) + i
            
            # Cell center
            cell_center = SVector((i-0.5)*dx, (j-0.5)*dx, 0.0)
            
            # Cell face indices (will create later)
            cell_faces = [face_id, face_id+1, face_id+2, face_id+3]
            
            # Create cell
            push!(cells, CFDCore.Cell(cell_id, cell_center, [sw, se, ne, nw], cell_faces, dx^2, false))
            
            # Create faces
            # South face
            south_face_center = SVector((i-0.5)*dx, (j-1)*dx, 0.0)
            south_face_normal = SVector(0.0, -1.0, 0.0)
            south_face_owner = cell_id
            south_face_neighbor = j == 1 ? -1 : cell_id - n
            push!(faces, CFDCore.Face(face_id, [sw, se], south_face_normal, dx, 
                                    south_face_center, south_face_owner, south_face_neighbor, j == 1))
            
            # East face
            east_face_center = SVector(i*dx, (j-0.5)*dx, 0.0)
            east_face_normal = SVector(1.0, 0.0, 0.0)
            east_face_owner = cell_id
            east_face_neighbor = i == n ? -1 : cell_id + 1
            push!(faces, CFDCore.Face(face_id+1, [se, ne], east_face_normal, dx, 
                                    east_face_center, east_face_owner, east_face_neighbor, i == n))
            
            # North face
            north_face_center = SVector((i-0.5)*dx, j*dx, 0.0)
            north_face_normal = SVector(0.0, 1.0, 0.0)
            north_face_owner = cell_id
            north_face_neighbor = j == n ? -1 : cell_id + n
            push!(faces, CFDCore.Face(face_id+2, [ne, nw], north_face_normal, dx, 
                                    north_face_center, north_face_owner, north_face_neighbor, j == n))
            
            # West face
            west_face_center = SVector((i-1)*dx, (j-0.5)*dx, 0.0)
            west_face_normal = SVector(-1.0, 0.0, 0.0)
            west_face_owner = cell_id
            west_face_neighbor = i == 1 ? -1 : cell_id - 1
            push!(faces, CFDCore.Face(face_id+3, [nw, sw], west_face_normal, dx, 
                                    west_face_center, west_face_owner, west_face_neighbor, i == 1))
            
            face_id += 4
            cell_id += 1
        end
    end
    
    # Create mesh
    mesh = CFDCore.Mesh(nodes, faces, cells)
    
    # Create velocity field (lid-driven cavity)
    U_field = CFDCore.VectorField("U", mesh)
    fill!(U_field.data, SVector(0.0, 0.0, 0.0))
    
    # Set lid-driven cavity boundary conditions
    U_field.boundaries = Dict(
        "bottom" => CFDCore.DirichletBC((x,y,t) -> SVector(0.0, 0.0, 0.0)),
        "right" => CFDCore.DirichletBC((x,y,t) -> SVector(0.0, 0.0, 0.0)),
        "top" => CFDCore.DirichletBC((x,y,t) -> SVector(1.0, 0.0, 0.0)),
        "left" => CFDCore.DirichletBC((x,y,t) -> SVector(0.0, 0.0, 0.0))
    )
    
    # Create pressure field
    p_field = CFDCore.ScalarField("p", mesh)
    fill!(p_field.data, 0.0)
    
    # Create face flux field
    phi_face_flux = CFDCore.ScalarField("phi", mesh, location=:face)
    fill!(phi_face_flux.data, 0.0)
    
    return mesh, U_field, p_field, phi_face_flux
end

# Test case 1: Basic functionality
@testset "HPC SIMPLE Basic Functionality" begin
    println("\n🧪 Test 1: HPC SIMPLE Basic Functionality")
    println("-" ^ 50)
    
    # Create small test case
    mesh, U_field, p_field, phi_face_flux = create_test_case(8)
    
    # Check if the HPC solver module exists
    if isdefined(CFD, :hpcOptimizedSolvers)
        println("✓ HPC solver module found")
        
        # Check for the SIMPLE solver function
        hpc_module = CFD.hpcOptimizedSolvers
        has_simple = false
        
        try
            # This is a basic check to see if the function exists
            # We're not running it yet as it might be expensive
            if isdefined(hpc_module, :solve_hpc_simple!)
                has_simple = true
                println("✓ HPC SIMPLE solver function found")
            end
        catch
            println("✗ Could not find HPC SIMPLE solver function")
        end
        
        @test has_simple
        
        # Basic test case parameters
        rho = 1.0
        nu = 0.01  # Reynolds number ~100
        max_iter = 5  # Just a few iterations for testing
        
        if has_simple
            # Try to run a minimal version of the solver just to check
            # if it executes without errors
            try
                # Only run a couple of iterations for testing
                hpc_module.solve_hpc_simple!(
                    U_field, 
                    p_field,
                    phi_face_flux,
                    rho, 
                    nu,
                    mesh,
                    max_iter=max_iter,
                    tolerance=1e-2,
                    p_under_relaxation=0.3,
                    U_under_relaxation=0.7
                )
                
                println("✓ HPC SIMPLE solver executed without errors")
                
                # Check for NaNs or Infs in the solution
                @test all(isfinite, p_field.data)
                @test all(x -> all(isfinite, x), U_field.data)
                println("✓ Solution contains finite values")
                
            catch e
                println("✗ Error running HPC SIMPLE solver: $e")
                @test false
            end
        end
    else
        println("✗ HPC solver module not found")
        @test false
    end
end

# Test case 2: Matrix assembly with non-orthogonal correction
@testset "Matrix Assembly with Non-Orthogonal Correction" begin
    println("\n📊 Test 2: Matrix Assembly with Non-Orthogonal Correction")
    println("-" ^ 50)
    
    # Create mesh with skew to test non-orthogonal correction
    n = 8
    dx = 1.0 / n
    
    # Create skewed mesh (just for testing)
    mesh, U_field, p_field, phi_face_flux = create_test_case(n)
    
    # Check if the momentum matrix assembly functions exist
    if isdefined(CFD, :hpcOptimizedSolvers)
        hpc_module = CFD.hpcOptimizedSolvers
        
        # Check for momentum matrix assembly function
        has_assembly = false
        
        try
            if isdefined(hpc_module, :assemble_momentum_matrix!)
                has_assembly = true
                println("✓ Momentum matrix assembly function found")
            end
        catch
            println("✗ Could not find momentum matrix assembly function")
        end
        
        @test has_assembly
        
        # Try assembling the momentum matrix
        if has_assembly
            try
                # Create a test sparse matrix
                n_cells = length(mesh.cells)
                A = spzeros(n_cells, n_cells)
                source = zeros(n_cells)
                
                # Call the assembly function with our test matrix
                # This is a very basic test to ensure it doesn't error
                rho = 1.0
                nu = 0.01
                
                if isdefined(hpc_module, :assemble_momentum_matrix!)
                    hpc_module.assemble_momentum_matrix!(
                        A, source, U_field, phi_face_flux, rho, nu, mesh, 
                        use_non_orthogonal_correction=true
                    )
                    
                    println("✓ Momentum matrix assembly executed without errors")
                    
                    # Check matrix properties
                    if !isempty(A.nzval)
                        # Check diagonal dominance
                        diag_entries = diag(A)
                        @test all(diag_entries .> 0)
                        println("✓ Matrix has positive diagonal entries")
                        
                        # Check conservation
                        A_dense = Array(A)
                        row_sums = sum(A_dense, dims=2)
                        @test all(abs.(row_sums .- source) .< 1e-10)
                        println("✓ Matrix satisfies conservation property")
                    else
                        println("! Matrix is empty or not assembled correctly")
                    end
                else
                    println("! Function signature may have changed")
                end
                
            catch e
                println("✗ Error in momentum matrix assembly: $e")
                @test false
            end
        end
    else
        println("✗ HPC solver module not found")
        @test false
    end
end

# Run all tests
println("\n✅ All tests completed")
