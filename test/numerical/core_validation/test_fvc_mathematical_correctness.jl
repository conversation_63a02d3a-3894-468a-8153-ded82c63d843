# test/numerical/core_validation/test_fvc_mathematical_correctness.jl
"""
Mathematical correctness tests for FVC operators.
These tests verify that fvc operators achieve correct mathematical behavior.
"""

using Pkg
Pkg.activate(".")
using CFD
using LinearAlgebra
using StaticArrays
using Test
using Statistics

# Import the fvc module explicitly
const fvc = CFD.Numerics.fvc

println("=== FVC Mathematical Correctness Tests ===")

@testset "FVC Mathematical Correctness" begin
    
    @testset "Linear Function Exactness Tests" begin
        println("Testing FVC operators on linear functions...")
        
        # Create a simple 2×2×2 mesh with consistent face orientations
        mesh = CFD.Utilities.ConsistentMeshFixed.create_consistent_unit_cube_mesh(2, 2, 2)
        println("Created mesh with $(length(mesh.cells)) cells")
        
        # Test 1: Gradient of linear function φ = x should be ∇φ = (1, 0, 0)
        println("  Test 1: ∇(x) = (1, 0, 0)")
        
        # Create field φ = x (linear in x-direction)
        phi_data = [c.center[1] for c in mesh.cells]  # x-coordinate at cell centers
        
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "walls" => CFD.CFDCore.DirichletBC((x, y, z, t) -> x),
            "inlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> x), 
            "outlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> x)
        )
        
        phi_field = CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(:phi_x, mesh, phi_data, bcs, nothing, nothing)
        
        # Compute gradient
        grad_phi = fvc.grad(phi_field)
        
        # Check results - should be (1, 0, 0) everywhere
        println("    Sample gradient values:")
        for i in 1:min(4, length(grad_phi.data))
            println("      Cell $i: $(grad_phi.data[i])")
        end
        
        # Check x-component should be close to 1.0
        x_components = [grad_phi.data[i][1] for i in 1:length(grad_phi.data)]
        y_components = [grad_phi.data[i][2] for i in 1:length(grad_phi.data)]
        z_components = [grad_phi.data[i][3] for i in 1:length(grad_phi.data)]
        
        println("    x-component range: [$(minimum(x_components)), $(maximum(x_components))]")
        println("    y-component range: [$(minimum(y_components)), $(maximum(y_components))]")
        println("    z-component range: [$(minimum(z_components)), $(maximum(z_components))]")
        
        # For linear functions, gradient should be exact (within numerical precision)
        @test isapprox(mean(x_components), 1.0, atol=1e-12)
        @test isapprox(maximum(abs.(y_components)), 0.0, atol=1e-12)
        @test isapprox(maximum(abs.(z_components)), 0.0, atol=1e-12)
        
        # Test 2: Laplacian of quadratic function φ = x² should be ∇²φ = 2
        println("  Test 2: ∇²(x²) = 2")
        
        phi_data_quad = [c.center[1]^2 for c in mesh.cells]  # x² at cell centers
        phi_field_quad = CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(:phi_x2, mesh, phi_data_quad, bcs, nothing, nothing)
        
        # Compute Laplacian
        lap_phi = fvc.laplacian(1.0, phi_field_quad)
        
        println("    Sample Laplacian values:")
        for i in 1:min(4, length(lap_phi.data))
            println("      Cell $i: $(lap_phi.data[i])")
        end
        
        println("    Laplacian range: [$(minimum(lap_phi.data)), $(maximum(lap_phi.data))]")
        
        # For quadratic functions, Laplacian should be constant = 2
        @test isapprox(mean(lap_phi.data), 2.0, atol=0.1)
        @test std(lap_phi.data) < 0.5
    end
    
    @testset "Conservation Properties" begin
        println("Testing conservation properties...")
        
        mesh = CFD.Utilities.ConsistentMeshFixed.create_consistent_unit_cube_mesh(3, 3, 3)
        
        # Test: Divergence of a constant vector field should be zero
        println("  Test: ∇·(constant vector) = 0")
        
        # Create constant vector field U = (1, 0, 0)
        const_vector = SVector(1.0, 0.0, 0.0)
        U_data = [const_vector for _ in 1:length(mesh.cells)]
        
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "walls" => CFD.CFDCore.DirichletBC((x, y, z, t) -> [1.0, 0.0, 0.0]),
            "inlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> [1.0, 0.0, 0.0]), 
            "outlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> [1.0, 0.0, 0.0])
        )
        
        U_field = CFD.CFDCore.Field{SVector{3,Float64}, 3, typeof(mesh)}(:U_const, mesh, U_data, bcs, nothing, nothing)
        
        # Compute divergence - should be zero for constant field
        div_U = fvc.div(U_field)
        
        println("    Divergence range: [$(minimum(div_U.data)), $(maximum(div_U.data))]")
        println("    Max absolute divergence: $(maximum(abs.(div_U.data)))")
        
        # Should be zero within numerical precision
        @test maximum(abs.(div_U.data)) < 1e-12
    end
    
    @testset "Boundary Condition Handling" begin
        println("Testing boundary condition handling...")
        
        mesh = CFD.Utilities.ConsistentMeshFixed.create_consistent_unit_cube_mesh(2, 2, 2)
        
        # Test mixed boundary conditions
        phi_data = [sum(c.center) for c in mesh.cells]  # φ = x + y + z
        
        # Mix of Dirichlet and Neumann BCs
        bcs_mixed = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "walls" => CFD.CFDCore.DirichletBC((x, y, z, t) -> x + y + z),
            "inlet" => CFD.CFDCore.NeumannBC((x, y, z, t) -> 1.0),  # ∂φ/∂n = 1
            "outlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> x + y + z)
        )
        
        phi_field_mixed = CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(:phi_mixed, mesh, phi_data, bcs_mixed, nothing, nothing)
        
        # Test that operators don't crash with mixed BCs
        try
            grad_phi_mixed = fvc.grad(phi_field_mixed)
            lap_phi_mixed = fvc.laplacian(1.0, phi_field_mixed)
            
            @test length(grad_phi_mixed.data) == length(mesh.cells)
            @test length(lap_phi_mixed.data) == length(mesh.cells)
            @test all(isfinite.(lap_phi_mixed.data))
            
            println("    ✓ Mixed boundary conditions handled correctly")
        catch e
            @test false
        end
    end
    
    @testset "Physical Consistency" begin
        println("Testing physical consistency...")
        
        mesh = CFD.Utilities.ConsistentMeshFixed.create_consistent_unit_cube_mesh(3, 3, 3)
        
        # Test: Heat equation steady state ∇²T = 0
        # Create a field that varies linearly in one direction
        T_data = [c.center[1] for c in mesh.cells]  # Linear temperature profile
        
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "walls" => CFD.CFDCore.NeumannBC((x, y, z, t) -> 0.0),  # Insulated walls
            "inlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0),   # Cold inlet
            "outlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> 1.0)   # Hot outlet
        )
        
        T_field = CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(:T, mesh, T_data, bcs, nothing, nothing)
        
        # For linear profile, Laplacian should be close to zero
        lap_T = fvc.laplacian(1.0, T_field)
        
        println("    Laplacian of linear profile range: [$(minimum(lap_T.data)), $(maximum(lap_T.data))]")
        println("    Max absolute Laplacian: $(maximum(abs.(lap_T.data)))")
        
        # Should be small for linear profile
        @test maximum(abs.(lap_T.data)) < 1.0
    end
end

println("\\n=== FVC Mathematical Correctness Tests Complete ===")