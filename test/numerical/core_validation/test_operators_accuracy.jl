# Test accuracy of basic FVM operators (gradient, divergence, Laplacian)
# Mathematical verification against analytical solutions

push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))
using CFD
using LinearAlgebra
using SparseArrays
using StaticArrays
using Test
using CFD.CFDCore: ScalarField, Face, Cell, Node, UnstructuredMesh

# Use the CFD module's FVM implementation
using CFD.Numerics: fvm

# Create 1D mesh for testing
function create_1d_mesh(n_cells::Int, L::Float64=1.0)
    dx = L / n_cells
    
    nodes = Node{Float64,3}[]
    cells = Cell{Float64,3}[]
    faces = Face{Float64,3}[]
    
    # Create nodes
    for i in 0:n_cells
        x = i * dx
        push!(nodes, Node{Float64,3}(i+1, SVector(x, 0.0, 0.0), i == 0 || i == n_cells))
    end
    
    # Create cells
    for i in 1:n_cells
        center = SVector((i - 0.5) * dx, 0.0, 0.0)
        volume = dx
        node_ids = [i, i+1]
        face_ids = [i, i+1]
        push!(cells, Cell{Float64,3}(i, node_ids, face_ids, center, volume))
    end
    
    # Create faces
    for i in 1:n_cells+1
        x = (i - 1) * dx
        center = SVector(x, 0.0, 0.0)
        area = 1.0
        normal = SVector(1.0, 0.0, 0.0)
        
        if i == 1
            # Left boundary face
            owner = 1
            neighbor = -1
            boundary = true
        elseif i == n_cells + 1
            # Right boundary face
            owner = n_cells
            neighbor = -1
            boundary = true
        else
            # Internal face
            owner = i - 1
            neighbor = i
            boundary = false
        end
        
        push!(faces, Face{Float64,3}(i, [i], center, area, normal, owner, neighbor, boundary))
    end
    
    # Define boundaries
    boundaries = Dict{String, Vector{Int}}(
        "left" => [1],
        "right" => [n_cells + 1]
    )
    
    # Create connectivity
    cell_to_cell = [Int[] for _ in 1:n_cells]
    for i in 1:n_cells
        if i > 1
            push!(cell_to_cell[i], i-1)
        end
        if i < n_cells
            push!(cell_to_cell[i], i+1)
        end
    end
    
    face_to_cell = Tuple{Int,Int}[]
    for face in faces
        push!(face_to_cell, (face.owner, face.neighbor))
    end
    
    bbox = (SVector(0.0, 0.0, 0.0), SVector(L, 0.0, 0.0))
    
    return UnstructuredMesh{Float64,3}(nodes, faces, cells, boundaries, cell_to_cell, face_to_cell, bbox)
end

@testset "FVM Operator Accuracy Tests" begin
    
    # Test 1: Laplacian operator accuracy
    @testset "Laplacian operator accuracy" begin
        # Test convergence for different mesh resolutions
        mesh_sizes = [10, 20, 40, 80]
        errors = Float64[]
        
        for n_cells in mesh_sizes
            mesh = create_1d_mesh(n_cells)
            
            # Test function: φ = sin(πx)
            # Laplacian: ∇²φ = -π² sin(πx)
            phi_data = [sin(π * cell.center[1]) for cell in mesh.cells]
            bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
                "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),   # sin(π*0) = 0
                "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)   # sin(π*1) = 0
            )
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
            
            # Get Laplacian matrix
            gamma = 1.0
            laplacian_matrix = fvm.laplacian(gamma, phi_field)
            
            # Apply Laplacian to the analytical function
            # Solve: A*φ = b to get the discrete Laplacian
            # But since we want to test the operator, we compute A*φ_analytical
            laplacian_result = laplacian_matrix.A * phi_data
            
            # Expected result: -π² sin(πx) at each cell center
            expected = [-π^2 * sin(π * cell.center[1]) for cell in mesh.cells]
            
            # Note: boundary contributions in laplacian_matrix.b need to be added
            # For homogeneous Dirichlet BCs, b should be zero
            total_result = laplacian_result - laplacian_matrix.b
            
            # Compute error
            error = norm(total_result - expected) / norm(expected)
            push!(errors, error)
            
            println("  n_cells=$n_cells, Laplacian error: $error")
        end
        
        # Check convergence rate (should be O(h²) for second-order scheme)
        if length(errors) >= 2
            # Calculate convergence rate between last two resolutions
            h_ratio = mesh_sizes[end-1] / mesh_sizes[end]  # h_coarse / h_fine
            error_ratio = errors[end-1] / errors[end]      # error_coarse / error_fine
            convergence_rate = log(error_ratio) / log(h_ratio)
            
            println("  Convergence rate: $convergence_rate (expected ≈ 2.0)")
            @test convergence_rate > 1.5  # Should be close to 2nd order
        end
        
        # Final error should be small
        @test errors[end] < 0.01
    end
    
    # Test 2: Matrix properties verification
    @testset "Matrix properties verification" begin
        n_cells = 20
        mesh = create_1d_mesh(n_cells)
        
        # Create a test field
        phi_data = [cell.center[1]^2 for cell in mesh.cells]  # φ = x²
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 1.0)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Get Laplacian matrix
        laplacian_matrix = fvm.laplacian(1.0, phi_field)
        A = laplacian_matrix.A
        
        # Test 1: Matrix should be symmetric for internal part
        # (This is only true for orthogonal meshes with constant diffusion coefficient)
        
        # Test 2: Matrix should be diagonally dominant for stability
        diagonal_dominance = true
        for i in 1:n_cells
            row_sum_off_diag = sum(abs(A[i,j]) for j in 1:n_cells if j != i)
            if abs(A[i,i]) <= row_sum_off_diag
                diagonal_dominance = false
                break
            end
        end
        @test diagonal_dominance
        
        # Test 3: Matrix should be positive definite for pure diffusion
        eigenvals = eigvals(Array(A))
        all_positive = all(λ > -1e-12 for λ in eigenvals)  # Allow small numerical errors
        @test all_positive
        
        println("  Matrix is diagonally dominant: $diagonal_dominance")
        println("  Matrix is positive semi-definite: $all_positive")
        println("  Smallest eigenvalue: $(minimum(eigenvals))")
    end
    
    # Test 3: Boundary condition implementation accuracy
    @testset "Boundary condition accuracy" begin
        n_cells = 10
        mesh = create_1d_mesh(n_cells)
        
        # Test different BC types
        bc_types = [
            ("Dirichlet-Dirichlet", 
             CFDCore.DirichletBC((x,y,z,t) -> 0.0), 
             CFDCore.DirichletBC((x,y,z,t) -> 1.0)),
            ("Dirichlet-Neumann", 
             CFDCore.DirichletBC((x,y,z,t) -> 0.0), 
             CFDCore.NeumannBC((x,y,z,t) -> 1.0)),
            ("Neumann-Neumann", 
             CFDCore.NeumannBC((x,y,z,t) -> 0.0), 
             CFDCore.NeumannBC((x,y,z,t) -> 0.0))
        ]
        
        for (name, left_bc, right_bc) in bc_types[1:2]  # Skip Neumann-Neumann for now
            phi_data = zeros(n_cells)
            bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
                "left" => left_bc,
                "right" => right_bc
            )
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
            
            # Get Laplacian matrix
            laplacian_matrix = fvm.laplacian(1.0, phi_field)
            
            # For Dirichlet-Dirichlet with no source, solution should be linear
            if name == "Dirichlet-Dirichlet"
                solution = laplacian_matrix.A \ laplacian_matrix.b
                x_centers = [cell.center[1] for cell in mesh.cells]
                analytical = x_centers  # Linear: φ = x
                
                error = norm(solution - analytical) / norm(analytical)
                println("  $name BC error: $error")
                @test error < 1e-14
            end
            
            # Check that matrix is well-conditioned
            cond_num = cond(Array(laplacian_matrix.A))
            println("  $name condition number: $cond_num")
            @test cond_num < 1e12  # Should be well-conditioned
        end
    end
    
    # Test 4: Consistency check (discrete conservation)
    @testset "Discrete conservation verification" begin
        n_cells = 15
        mesh = create_1d_mesh(n_cells)
        
        # Create field with homogeneous Neumann BCs (no flux at boundaries)
        phi_data = ones(n_cells)  # Constant field
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.NeumannBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.NeumannBC((x,y,z,t) -> 0.0)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Get Laplacian matrix
        laplacian_matrix = fvm.laplacian(1.0, phi_field)
        
        # For a constant field with homogeneous Neumann BCs,
        # the discrete Laplacian should give zero (conservation)
        result = laplacian_matrix.A * phi_data + laplacian_matrix.b
        total_source = sum(result)
        
        println("  Total discrete source for constant field: $total_source")
        @test abs(total_source) < 1e-12
        
        # Test conservation for any field with Neumann BCs
        phi_data_var = [sin(2π * cell.center[1]) for cell in mesh.cells]
        result_var = laplacian_matrix.A * phi_data_var + laplacian_matrix.b
        total_source_var = sum(result_var)
        
        println("  Total discrete source for variable field: $total_source_var")
        @test abs(total_source_var) < 1e-12
    end
    
    # Test 5: Order of accuracy verification
    @testset "Order of accuracy" begin
        # Use manufactured solution: φ = x³ - x
        # Laplacian: ∇²φ = 6x
        
        mesh_sizes = [8, 16, 32]
        errors = Float64[]
        
        for n_cells in mesh_sizes
            mesh = create_1d_mesh(n_cells)
            dx = 1.0 / n_cells
            
            # Manufactured solution at cell centers
            phi_analytical = [cell.center[1]^3 - cell.center[1] for cell in mesh.cells]
            
            # Boundary values from analytical solution
            bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
                "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),      # 0³ - 0 = 0
                "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)      # 1³ - 1 = 0
            )
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_analytical, bc_dict)
            
            # Expected Laplacian source: -∇²φ = -6x
            expected_source = [6.0 * cell.center[1] * dx for cell in mesh.cells]  # Multiply by volume
            
            # Get Laplacian matrix and solve with known source
            laplacian_matrix = fvm.laplacian(1.0, phi_field)
            rhs = laplacian_matrix.b + expected_source
            phi_computed = laplacian_matrix.A \ rhs
            
            # Compute error
            error = norm(phi_computed - phi_analytical) / norm(phi_analytical)
            push!(errors, error)
            
            println("  n_cells=$n_cells, manufactured solution error: $error")
        end
        
        # Check convergence rate
        if length(errors) >= 2
            h_ratio = mesh_sizes[end-1] / mesh_sizes[end]
            error_ratio = errors[end-1] / errors[end]
            convergence_rate = log(error_ratio) / log(h_ratio)
            
            println("  Convergence rate: $convergence_rate")
            @test convergence_rate > 1.8  # Should be close to 2nd order
        end
    end
end

# Run the tests
println("\n" * "="^70)
println("Running FVM Operator Accuracy Tests")
println("="^70 * "\n")