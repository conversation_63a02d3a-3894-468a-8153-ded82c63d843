# Test 1D transient diffusion with analytical solution
# Tests the finite volume method time-dependent implementation

push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))
using CFD
using LinearAlgebra
using SparseArrays
using StaticArrays
using Test
using CFD.CFDCore: ScalarField, Face, Cell, Node, UnstructuredMesh

# Include the FVM module
include("../../src/Numerics/fvm.jl")
using .fvm

# Create 1D mesh for testing
function create_1d_mesh(n_cells::Int, L::Float64=1.0)
    dx = L / n_cells
    
    nodes = Node{Float64,3}[]
    cells = Cell{Float64,3}[]
    faces = Face{Float64,3}[]
    
    # Create nodes
    for i in 0:n_cells
        x = i * dx
        push!(nodes, Node{Float64,3}(i+1, SVector(x, 0.0, 0.0), i == 0 || i == n_cells))
    end
    
    # Create cells
    for i in 1:n_cells
        center = SVector((i - 0.5) * dx, 0.0, 0.0)
        volume = dx
        node_ids = [i, i+1]
        face_ids = [i, i+1]
        push!(cells, Cell{Float64,3}(i, node_ids, face_ids, center, volume))
    end
    
    # Create faces
    for i in 1:n_cells+1
        x = (i - 1) * dx
        center = SVector(x, 0.0, 0.0)
        area = 1.0
        normal = SVector(1.0, 0.0, 0.0)
        
        if i == 1
            # Left boundary face
            owner = 1
            neighbor = -1
            boundary = true
        elseif i == n_cells + 1
            # Right boundary face
            owner = n_cells
            neighbor = -1
            boundary = true
        else
            # Internal face
            owner = i - 1
            neighbor = i
            boundary = false
        end
        
        push!(faces, Face{Float64,3}(i, [i], center, area, normal, owner, neighbor, boundary))
    end
    
    # Define boundaries
    boundaries = Dict{String, Vector{Int}}(
        "left" => [1],
        "right" => [n_cells + 1]
    )
    
    # Create connectivity
    cell_to_cell = [Int[] for _ in 1:n_cells]
    for i in 1:n_cells
        if i > 1
            push!(cell_to_cell[i], i-1)
        end
        if i < n_cells
            push!(cell_to_cell[i], i+1)
        end
    end
    
    face_to_cell = Tuple{Int,Int}[]
    for face in faces
        push!(face_to_cell, (face.owner, face.neighbor))
    end
    
    bbox = (SVector(0.0, 0.0, 0.0), SVector(L, 0.0, 0.0))
    
    return UnstructuredMesh{Float64,3}(nodes, faces, cells, boundaries, cell_to_cell, face_to_cell, bbox)
end

@testset "1D Transient Diffusion Tests" begin
    
    # Test 1: Decay of sinusoidal initial condition
    # ∂φ/∂t = α ∂²φ/∂x² with φ(x,0) = sin(πx), φ(0,t) = φ(1,t) = 0
    # Analytical solution: φ(x,t) = sin(πx) * exp(-απ²t)
    @testset "Sinusoidal decay" begin
        n_cells = 50
        mesh = create_1d_mesh(n_cells)
        
        # Diffusion coefficient
        alpha = 0.1
        
        # Initial condition: sin(πx)
        phi_data = [sin(π * cell.center[1]) for cell in mesh.cells]
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)
        )
        
        # Time stepping parameters
        dt = 0.001
        t_final = 0.1
        n_steps = Int(t_final / dt)
        
        # Euler implicit time stepping
        phi_old = copy(phi_data)
        phi_new = copy(phi_data)
        
        for step in 1:n_steps
            # Create field for current time step
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_new, bc_dict)
            phi_old_field = CFDCore.ScalarField(:phi_old, mesh, phi_old, bc_dict)
            
            # Time derivative matrix
            time_matrix = fvm.ddt(phi_field, phi_old_field, dt)
            
            # Laplacian matrix
            laplacian_matrix = fvm.laplacian(alpha, phi_field)
            
            # Combined system: (ddt + laplacian) * phi = source
            # For implicit Euler: (I/dt - α∇²) * φⁿ⁺¹ = φⁿ/dt
            A_total = time_matrix.A - laplacian_matrix.A
            b_total = time_matrix.b - laplacian_matrix.b
            
            # Solve
            phi_new = A_total \ b_total
            
            # Update old solution
            phi_old = copy(phi_new)
        end
        
        # Analytical solution at t_final
        phi_analytical = [sin(π * cell.center[1]) * exp(-alpha * π^2 * t_final) for cell in mesh.cells]
        
        # Check error
        error = norm(phi_new - phi_analytical) / norm(phi_analytical)
        println("Test 1 - Sinusoidal decay error at t=$t_final: $error")
        @test error < 0.01  # 1% error tolerance
    end
    
    # Test 2: Step response (sudden temperature change)
    # ∂φ/∂t = α ∂²φ/∂x² with φ(x,0) = 0, φ(0,t) = 1, φ(1,t) = 0
    @testset "Step response" begin
        n_cells = 20
        mesh = create_1d_mesh(n_cells)
        
        # Diffusion coefficient
        alpha = 1.0
        
        # Initial condition: φ = 0 everywhere
        phi_data = zeros(n_cells)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 1.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)
        )
        
        # Time stepping
        dt = 0.0001
        t_test = 0.01  # Test at this time
        n_steps = Int(t_test / dt)
        
        phi_old = copy(phi_data)
        phi_new = copy(phi_data)
        
        for step in 1:n_steps
            # Create fields
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_new, bc_dict)
            phi_old_field = CFDCore.ScalarField(:phi_old, mesh, phi_old, bc_dict)
            
            # Time derivative matrix
            time_matrix = fvm.ddt(phi_field, phi_old_field, dt)
            
            # Laplacian matrix
            laplacian_matrix = fvm.laplacian(alpha, phi_field)
            
            # Combined system
            A_total = time_matrix.A - laplacian_matrix.A
            b_total = time_matrix.b - laplacian_matrix.b
            
            # Solve
            phi_new = A_total \ b_total
            
            # Update
            phi_old = copy(phi_new)
        end
        
        # For the step response, check that:
        # 1. Solution is monotonic (decreasing from left to right)
        # 2. Values are between 0 and 1
        # 3. Left side is closer to 1, right side closer to 0
        
        println("Test 2 - Step response at t=$t_test:")
        println("  φ[1] = $(phi_new[1]) (should be close to 1)")
        println("  φ[end] = $(phi_new[end]) (should be close to 0)")
        
        # Check monotonicity
        is_monotonic = all(phi_new[i] >= phi_new[i+1] for i in 1:n_cells-1)
        @test is_monotonic
        
        # Check bounds
        @test all(0 <= φ <= 1 for φ in phi_new)
        
        # Check that left side has diffused inward
        @test phi_new[1] > 0.5
        @test phi_new[end] < 0.5
    end
    
    # Test 3: Energy conservation test
    # For homogeneous Neumann BCs, total energy should be conserved
    @testset "Energy conservation" begin
        n_cells = 30
        mesh = create_1d_mesh(n_cells)
        
        alpha = 1.0
        
        # Initial condition: Gaussian pulse
        phi_data = [exp(-50 * (cell.center[1] - 0.5)^2) for cell in mesh.cells]
        initial_energy = sum(phi_data) / n_cells  # Average value
        
        # Neumann BCs (no flux at boundaries)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.NeumannBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.NeumannBC((x,y,z,t) -> 0.0)
        )
        
        # Time stepping
        dt = 0.0001
        t_final = 0.01
        n_steps = Int(t_final / dt)
        
        phi_old = copy(phi_data)
        phi_new = copy(phi_data)
        
        for step in 1:n_steps
            phi_field = CFDCore.ScalarField(:phi, mesh, phi_new, bc_dict)
            phi_old_field = CFDCore.ScalarField(:phi_old, mesh, phi_old, bc_dict)
            
            time_matrix = fvm.ddt(phi_field, phi_old_field, dt)
            laplacian_matrix = fvm.laplacian(alpha, phi_field)
            
            A_total = time_matrix.A - laplacian_matrix.A
            b_total = time_matrix.b - laplacian_matrix.b
            
            phi_new = A_total \ b_total
            phi_old = copy(phi_new)
        end
        
        # Check energy conservation
        final_energy = sum(phi_new) / n_cells
        energy_error = abs(final_energy - initial_energy) / initial_energy
        
        println("Test 3 - Energy conservation:")
        println("  Initial energy: $initial_energy")
        println("  Final energy: $final_energy")
        println("  Relative error: $energy_error")
        
        @test energy_error < 1e-10  # Should be conserved to machine precision
    end
end

# Run the tests
println("\n" * "="^70)
println("Running 1D Transient Diffusion Tests")
println("="^70 * "\n")