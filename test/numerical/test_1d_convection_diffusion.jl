# Test 1D convection-diffusion with analytical solution
# Tests the finite volume method implementation for mixed operators

push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))
using CFD
using LinearAlgebra
using SparseArrays
using StaticArrays
using Test
using CFD.CFDCore: ScalarField, Face, Cell, Node, UnstructuredMesh

# Include the FVM module
include("../../src/Numerics/fvm.jl")
using .fvm

# Create 1D mesh for testing
function create_1d_mesh(n_cells::Int, L::Float64=1.0)
    dx = L / n_cells
    
    nodes = Node{Float64,3}[]
    cells = Cell{Float64,3}[]
    faces = Face{Float64,3}[]
    
    # Create nodes
    for i in 0:n_cells
        x = i * dx
        push!(nodes, Node{Float64,3}(i+1, SVector(x, 0.0, 0.0), i == 0 || i == n_cells))
    end
    
    # Create cells
    for i in 1:n_cells
        center = SVector((i - 0.5) * dx, 0.0, 0.0)
        volume = dx
        node_ids = [i, i+1]
        face_ids = [i, i+1]
        push!(cells, Cell{Float64,3}(i, node_ids, face_ids, center, volume))
    end
    
    # Create faces
    for i in 1:n_cells+1
        x = (i - 1) * dx
        center = SVector(x, 0.0, 0.0)
        area = 1.0
        normal = SVector(1.0, 0.0, 0.0)
        
        if i == 1
            # Left boundary face
            owner = 1
            neighbor = -1
            boundary = true
        elseif i == n_cells + 1
            # Right boundary face
            owner = n_cells
            neighbor = -1
            boundary = true
        else
            # Internal face
            owner = i - 1
            neighbor = i
            boundary = false
        end
        
        push!(faces, Face{Float64,3}(i, [i], center, area, normal, owner, neighbor, boundary))
    end
    
    # Define boundaries
    boundaries = Dict{String, Vector{Int}}(
        "left" => [1],
        "right" => [n_cells + 1]
    )
    
    # Create connectivity
    cell_to_cell = [Int[] for _ in 1:n_cells]
    for i in 1:n_cells
        if i > 1
            push!(cell_to_cell[i], i-1)
        end
        if i < n_cells
            push!(cell_to_cell[i], i+1)
        end
    end
    
    face_to_cell = Tuple{Int,Int}[]
    for face in faces
        push!(face_to_cell, (face.owner, face.neighbor))
    end
    
    bbox = (SVector(0.0, 0.0, 0.0), SVector(L, 0.0, 0.0))
    
    return UnstructuredMesh{Float64,3}(nodes, faces, cells, boundaries, cell_to_cell, face_to_cell, bbox)
end

@testset "1D Convection-Diffusion Tests" begin
    
    # Test 1: Pure convection test
    # ∇·(ρUφ) = 0 with constant velocity U
    @testset "Pure convection (constant velocity)" begin
        n_cells = 20
        mesh = create_1d_mesh(n_cells)
        
        # Create velocity field (face fluxes)
        U = 1.0  # Constant velocity
        flux_data = Float64[]
        
        # Calculate face fluxes: F = ρ * U * A
        # For 1D: F = ρ * U * A where ρ=1, A=1
        for face in mesh.faces
            if face.boundary
                if face.owner == 1  # Left boundary
                    # Inflow face
                    flux = U * face.area
                else  # Right boundary
                    # Outflow face
                    flux = U * face.area
                end
            else
                # Internal face
                flux = U * face.area
            end
            push!(flux_data, flux)
        end
        
        # Create flux field
        flux_field = CFDCore.ScalarField(:flux, mesh, flux_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
        
        # Create transported scalar field with BCs
        phi_data = zeros(n_cells)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 1.0),  # Inflow value
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)  # Outflow (shouldn't matter for pure convection)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Get convection matrix using upwind scheme
        convection_matrix = fvm.div(flux_field, phi_field, scheme=:upwind)
        
        # Solve: div(φU) = 0
        # This should give φ = 1 everywhere for steady flow with φ_inlet = 1
        solution = convection_matrix.A \ convection_matrix.b
        
        # Expected: φ should be 1 everywhere (constant profile)
        expected = ones(n_cells)
        error = norm(solution - expected)
        
        println("Test 1 - Pure convection error: $error")
        println("  Solution: ", solution[1:5], "...")
        @test error < 1e-10
    end
    
    # Test 2: Diffusion-dominated case (high Peclet number)
    @testset "Diffusion-dominated case" begin
        n_cells = 20
        mesh = create_1d_mesh(n_cells)
        
        # Small velocity, large diffusion coefficient
        U = 0.01
        gamma = 1.0
        
        # Create flux field
        flux_data = fill(U, length(mesh.faces))  # All faces have same flux
        flux_field = CFDCore.ScalarField(:flux, mesh, flux_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
        
        # Create scalar field
        phi_data = zeros(n_cells)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 1.0)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Get matrices
        convection_matrix = fvm.div(flux_field, phi_field, scheme=:upwind)
        diffusion_matrix = fvm.laplacian(gamma, phi_field)
        
        # Combined equation: div(φU) - div(γ∇φ) = 0
        # Or: convection - diffusion = 0
        A_total = convection_matrix.A - diffusion_matrix.A
        b_total = convection_matrix.b - diffusion_matrix.b
        
        solution = A_total \ b_total
        
        # For diffusion-dominated case, should be nearly linear
        # Analytical solution for pure diffusion: φ = x
        x_centers = [cell.center[1] for cell in mesh.cells]
        analytical_pure_diffusion = x_centers
        
        # The solution should be close to linear since diffusion dominates
        error = norm(solution - analytical_pure_diffusion) / norm(analytical_pure_diffusion)
        
        println("Test 2 - Diffusion-dominated error: $error")
        @test error < 0.1  # Should be close to linear solution
    end
    
    # Test 3: Convection-dominated case (central vs upwind schemes)
    @testset "Convection-dominated: upwind vs central" begin
        n_cells = 50  # Need finer mesh for convection-dominated
        mesh = create_1d_mesh(n_cells)
        
        # Large velocity, small diffusion
        U = 10.0
        gamma = 0.01
        
        # Create flux field
        flux_data = fill(U, length(mesh.faces))
        flux_field = CFDCore.ScalarField(:flux, mesh, flux_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
        
        # Create scalar field
        phi_data = zeros(n_cells)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 1.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 0.0)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Test upwind scheme
        convection_upwind = fvm.div(flux_field, phi_field, scheme=:upwind)
        diffusion_matrix = fvm.laplacian(gamma, phi_field)
        
        A_upwind = convection_upwind.A - diffusion_matrix.A
        b_upwind = convection_upwind.b - diffusion_matrix.b
        solution_upwind = A_upwind \ b_upwind
        
        # Test central scheme
        convection_central = fvm.div(flux_field, phi_field, scheme=:central)
        A_central = convection_central.A - diffusion_matrix.A
        b_central = convection_central.b - diffusion_matrix.b
        solution_central = A_central \ b_central
        
        # For high Peclet number, upwind should be more stable
        # Check that upwind solution is bounded (0 ≤ φ ≤ 1)
        upwind_bounded = all(0 <= phi <= 1 for phi in solution_upwind)
        
        # Central scheme might have oscillations (unbounded values)
        central_oscillations = any(phi < -0.1 || phi > 1.1 for phi in solution_central)
        
        println("Test 3 - Convection-dominated:")
        println("  Upwind bounded: $upwind_bounded")
        println("  Central has oscillations: $central_oscillations")
        println("  Upwind range: [$(minimum(solution_upwind)), $(maximum(solution_upwind))]")
        println("  Central range: [$(minimum(solution_central)), $(maximum(solution_central))]")
        
        @test upwind_bounded
        # Central scheme may or may not oscillate depending on mesh resolution
    end
    
    # Test 4: Exact solution for constant coefficients
    # For 1D steady convection-diffusion: U(dφ/dx) - γ(d²φ/dx²) = 0
    # With φ(0) = 0, φ(1) = 1, analytical solution is:
    # φ(x) = (exp(Ux/γ) - 1) / (exp(U/γ) - 1)
    @testset "Exact solution test" begin
        n_cells = 30
        mesh = create_1d_mesh(n_cells)
        
        U = 2.0
        gamma = 0.5
        Pe = U / gamma  # Peclet number
        
        # Create flux field
        flux_data = fill(U, length(mesh.faces))
        flux_field = CFDCore.ScalarField(:flux, mesh, flux_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
        
        # Create scalar field
        phi_data = zeros(n_cells)
        bc_dict = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "left" => CFDCore.DirichletBC((x,y,z,t) -> 0.0),
            "right" => CFDCore.DirichletBC((x,y,z,t) -> 1.0)
        )
        phi_field = CFDCore.ScalarField(:phi, mesh, phi_data, bc_dict)
        
        # Solve with upwind scheme
        convection_matrix = fvm.div(flux_field, phi_field, scheme=:upwind)
        diffusion_matrix = fvm.laplacian(gamma, phi_field)
        
        A_total = convection_matrix.A - diffusion_matrix.A
        b_total = convection_matrix.b - diffusion_matrix.b
        solution = A_total \ b_total
        
        # Analytical solution: φ(x) = (exp(Pe*x) - 1) / (exp(Pe) - 1)
        x_centers = [cell.center[1] for cell in mesh.cells]
        analytical = [(exp(Pe * x) - 1) / (exp(Pe) - 1) for x in x_centers]
        
        # Check error
        error = norm(solution - analytical) / norm(analytical)
        
        println("Test 4 - Exact solution test:")
        println("  Peclet number: $Pe")
        println("  Error: $error")
        
        @test error < 0.05  # 5% error tolerance
    end
end

# Run the tests
println("\n" * "="^70)
println("Running 1D Convection-Diffusion Tests")
println("="^70 * "\n")