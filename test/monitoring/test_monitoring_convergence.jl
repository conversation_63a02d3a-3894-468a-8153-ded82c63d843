#!/usr/bin/env julia

# Test convergence scenario with SolverMonitoring
include("src/Monitoring/SolverMonitoring.jl")
using .SolverMonitoring

println("🎯 Testing convergence detection...")

# Create monitoring system with stricter tolerances
monitor = create_monitoring_system(50, 
    abs_tol=1e-7, 
    rel_tol=1e-8, 
    min_iter=5,
    display=true, 
    output_freq=3
)

register_residual!(monitor, "momentum", 1e-7)
register_residual!(monitor, "continuity", 1e-9)

println("\n🔄 Running until convergence...")

# Simulate fast convergence
for iter in 1:50
    # Exponential decay - should converge quickly
    mom_res = 1.0 * exp(-0.5 * iter) + 1e-10
    cont_res = 0.1 * exp(-0.8 * iter) + 1e-12
    
    update_residuals!(monitor, "momentum", mom_res)
    update_residuals!(monitor, "continuity", cont_res)
    
    if check_convergence!(monitor)
        println("\n🎯 Successfully converged!")
        break
    end
    
    show_progress!(monitor)
    sleep(0.05)  # Faster for demonstration
end

finalize_monitoring!(monitor)
println("\n✅ Convergence test completed!")