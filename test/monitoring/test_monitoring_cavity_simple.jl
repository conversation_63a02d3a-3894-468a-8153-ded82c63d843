#!/usr/bin/env julia

"""
Simple Integration Test: SolverMonitoring + Cavity Flow Validation
Tests the monitoring system with our existing cavity flow validation
"""

# Load modules directly (avoid CFD module import issues)
include("src/Monitoring/SolverMonitoring.jl")
include("validation/phase6_navier_stokes.jl")

using .SolverMonitoring
using StaticArrays
using LinearAlgebra
using Printf

function test_monitoring_with_simple_cavity()
    println("🌊 Testing SolverMonitoring with Cavity Flow Validation")
    println("=" * "="^50)
    
    # ==========================================================================
    # Setup Monitoring System  
    # ==========================================================================
    
    println("🖥️  Setting up monitoring system...")
    
    max_iterations = 200
    monitor = create_monitoring_system(max_iterations,
        abs_tol=1e-7,
        rel_tol=1e-9,
        min_iter=10,
        display=true,
        plotting=true,
        output_freq=10
    )
    
    # Access functions directly from module 
    register_func = getfield(SolverMonitoring, :register_residual!)
    update_func = getfield(SolverMonitoring, :update_residuals!)
    check_func = getfield(SolverMonitoring, :check_convergence!)
    progress_func = getfield(SolverMonitoring, :show_progress!)
    finalize_func = getfield(SolverMonitoring, :finalize_monitoring!)
    
    # Register residual fields
    register_func(monitor, "velocity", 1e-7)
    register_func(monitor, "pressure", 1e-8)
    register_func(monitor, "continuity", 1e-9)
    
    # ==========================================================================
    # Run Enhanced Cavity Flow with Monitoring
    # ==========================================================================
    
    println("\n🔄 Running cavity flow with comprehensive monitoring...")
    
    # Use our existing cavity flow test as base
    n = 20
    mesh = create_unit_cube_mesh(n, n, 1)
    
    # Flow parameters
    Re = 200.0
    ρ = 1.0
    U_lid = 1.0
    ν = U_lid / Re
    
    println("Flow setup: Re=$Re, mesh=$(n)x$(n), cells=$(length(mesh.cells))")
    
    # Initialize fields (same as validation test)
    U_data = Vector{SVector{3,Float64}}()
    p_data = Float64[]
    
    for cell in mesh.cells
        push!(U_data, SVector(0.0, 0.0, 0.0))
        push!(p_data, 0.0)
    end
    
    U_bcs = Dict{String, AbstractBoundaryCondition}()
    p_bcs = Dict{String, AbstractBoundaryCondition}()
    
    U = VectorField(:U, mesh, U_data, U_bcs, copy(U_data))
    p = ScalarField(:p, mesh, p_data, p_bcs, copy(p_data))
    
    # Apply boundary conditions
    apply_lid_driven_bc!(U, mesh, U_lid)
    
    # ==========================================================================
    # Enhanced Solver Loop with Monitoring
    # ==========================================================================
    
    dt = 0.002
    under_relaxation = 0.8
    
    println("\nSolver parameters:")
    println("  • Time step: $dt")
    println("  • Under-relaxation: $under_relaxation")
    println("  • Max iterations: $max_iterations")
    
    println("\n🚀 Starting monitored simulation...")
    
    for iter in 1:max_iterations
        # Store old values
        U_old = copy(U.data)
        p_old = copy(p.data)
        
        # Simplified momentum equations (enhanced from validation test)
        for i in 1:length(U.data)
            if !is_boundary_cell(mesh, i)
                # Enhanced diffusion term
                laplacian_u = compute_simple_laplacian(U, mesh, i)
                
                # Simple convection term
                vel = U.data[i]
                convection = SVector(vel[1] * vel[1] * 0.1, vel[2] * vel[2] * 0.1, 0.0)
                
                # Pressure gradient (simplified)
                pressure_grad = SVector(0.0, 0.0, 0.0)  # Simplified for this test
                
                # Update with under-relaxation
                dU_dt = ν * laplacian_u - convection - pressure_grad
                U_new = U.data[i] + dt * dU_dt
                U.data[i] = under_relaxation * U_new + (1 - under_relaxation) * U.data[i]
            end
        end
        
        # Re-apply boundary conditions
        apply_lid_driven_bc!(U, mesh, U_lid)
        
        # Simple pressure correction
        for i in 1:length(p.data)
            if !is_boundary_cell(mesh, i)
                # Simple pressure update based on velocity divergence
                vel = U.data[i]
                divergence = abs(vel[1]) + abs(vel[2])
                pressure_correction = -0.1 * divergence
                p.data[i] += pressure_correction
            end
        end
        
        # =================================================================
        # Compute Enhanced Residuals
        # =================================================================
        
        # Velocity residual (L2 norm)
        vel_residual = 0.0
        for i in 1:length(U.data)
            diff = norm(U.data[i] - U_old[i])
            vel_residual += diff^2
        end
        vel_residual = sqrt(vel_residual / length(U.data))
        
        # Pressure residual (L2 norm)
        press_residual = 0.0
        for i in 1:length(p.data)
            diff = abs(p.data[i] - p_old[i])
            press_residual += diff^2
        end
        press_residual = sqrt(press_residual / length(p.data))
        
        # Continuity residual (divergence)
        cont_residual = 0.0
        for i in 1:length(U.data)
            vel = U.data[i]
            div = abs(vel[1]) + abs(vel[2])  # Simplified divergence
            cont_residual += div^2
        end
        cont_residual = sqrt(cont_residual / length(U.data))
        
        # =================================================================
        # Update Monitoring System
        # =================================================================
        
        current_time = iter * dt
        update_func(monitor, "velocity", vel_residual, current_time)
        update_func(monitor, "pressure", press_residual, current_time)
        update_func(monitor, "continuity", cont_residual, current_time)
        
        # =================================================================
        # Check Convergence and Display Progress
        # =================================================================
        
        if check_func(monitor)
            println("\n🎯 CONVERGENCE ACHIEVED!")
            println("Converged at iteration $iter")
            break
        end
        
        progress_func(monitor)
        
        # Physics validation every 25 iterations
        if iter % 25 == 0
            max_u = maximum(norm(u) for u in U.data)
            max_p = maximum(p.data)
            min_p = minimum(p.data)
            
            println("\n📊 Physics check at iteration $iter:")
            println("   • Max velocity: $(round(max_u, digits=4))")
            println("   • Pressure range: [$(round(min_p, digits=4)), $(round(max_p, digits=4))]")
            
            # Check for issues  
            if any(u -> any(isnan, u), U.data) || any(isnan, p.data)
                println("❌ NaN detected - stopping simulation")
                break
            end
            
            if max_u > 5.0 * U_lid
                println("⚠️  Warning: Excessive velocity magnitude")
            end
        end
    end
    
    # ==========================================================================
    # Finalization and Analysis
    # ==========================================================================
    
    finalize_func(monitor)
    
    # Final physics analysis
    println("\n🔬 FINAL ANALYSIS:")
    println("=" * "="^40)
    
    max_velocity = maximum(norm(u) for u in U.data)
    avg_velocity = sum(norm(u) for u in U.data) / length(U.data)
    max_pressure = maximum(p.data)
    min_pressure = minimum(p.data)
    
    println("Physics Results:")
    println("  • Maximum velocity: $(round(max_velocity, digits=6))")
    println("  • Average velocity: $(round(avg_velocity, digits=6))")
    println("  • Pressure range: [$(round(min_pressure, digits=6)), $(round(max_pressure, digits=6))]")
    
    # Convergence analysis
    if monitor.convergence.converged
        println("✅ Convergence Status: SUCCESS")
        println("  • Reason: $(monitor.convergence.convergence_reason)")
        println("  • Total iterations: $(monitor.iteration_count)")
        
        # Analyze residual reduction
        for (name, history) in monitor.residuals
            if length(history.values) > 1
                initial_res = history.values[1]
                final_res = history.values[end]
                reduction_factor = initial_res / final_res
                println("  • $name reduction: $(round(reduction_factor, digits=1))×")
            end
        end
    else
        println("❌ Convergence Status: NOT CONVERGED")
        println("  • Iterations completed: $(monitor.iteration_count)")
        println("  • Suggestion: Increase max_iterations or adjust tolerances")
    end
    
    # Monitoring system performance
    total_time = time() - monitor.progress.start_time
    println("\nMonitoring Performance:")
    println("  • Total runtime: $(round(total_time, digits=2))s")
    println("  • Time per iteration: $(round(total_time/monitor.iteration_count*1000, digits=1))ms")
    println("  • Residual updates: $(sum(length(h.values) for h in values(monitor.residuals)))")
    
    return monitor, U, p
end

# ==============================================================================
# Main Execution
# ==============================================================================

if abspath(PROGRAM_FILE) == @__FILE__
    println("🧪 COMPREHENSIVE SOLVERMONITORING TEST")
    println("Integration with Cavity Flow Validation")
    println()
    
    try
        monitor, U, p = test_monitoring_with_simple_cavity()
        
        println("\n🎉 TEST COMPLETED SUCCESSFULLY!")
        println("\nFeatures Tested:")
        println("✅ SolverMonitoring module integration")
        println("✅ Multi-field residual tracking")
        println("✅ Real-time progress visualization") 
        println("✅ Convergence detection")
        println("✅ Physics validation")
        println("✅ Performance monitoring")
        println("✅ Error detection")
        println("✅ Professional interface")
        
        if monitor.convergence.converged
            println("\n🎯 Bonus: Numerical convergence achieved!")
        end
        
        if SolverMonitoring.HAS_PLOTS
            println("📈 Residual plots generated")
        end
        
    catch e
        println("\n❌ TEST FAILED: $e")
        rethrow(e)
    end
end