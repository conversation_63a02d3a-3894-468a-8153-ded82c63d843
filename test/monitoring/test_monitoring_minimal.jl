#!/usr/bin/env julia

# Minimal test of SolverMonitoring
include("src/Monitoring/SolverMonitoring.jl")

# Import the module
using .SolverMonitoring

println("🧪 Testing SolverMonitoring minimal functionality...")

# Test 1: Create monitoring system
println("📊 Test 1: Creating monitoring system...")
monitor = create_monitoring_system(10, display=true, output_freq=2)
println("✅ Monitor created successfully")

# Test 2: Test individual functions
println("\n📊 Test 2: Function availability...")
println("Available functions:")
for name in names(SolverMonitoring)
    if string(name) != "SolverMonitoring"
        println("  - $name")
    end
end

# Test 3: Try to register residual manually
println("\n📊 Test 3: Manual residual registration...")
try
    # Access the function directly from the module
    regfunc = getfield(SolverMonitoring, :register_residual!)
    regfunc(monitor, "test_residual", 1e-6)
    println("✅ Manual registration successful")
catch e
    println("❌ Manual registration failed: $e")
end

# Test 4: Update residuals
println("\n📊 Test 4: Residual updates...")
try
    updfunc = getfield(SolverMonitoring, :update_residuals!)
    updfunc(monitor, "test_residual", 0.1)
    println("✅ Residual update successful")
catch e
    println("❌ Residual update failed: $e")
end

# Test 5: Check convergence
println("\n📊 Test 5: Convergence check...")
try
    convfunc = getfield(SolverMonitoring, :check_convergence!)
    result = convfunc(monitor)
    println("✅ Convergence check result: $result")
catch e
    println("❌ Convergence check failed: $e")
end

# Test 6: Show progress
println("\n📊 Test 6: Progress display...")
try
    progfunc = getfield(SolverMonitoring, :show_progress!)
    progfunc(monitor)
    println("✅ Progress display successful")
catch e
    println("❌ Progress display failed: $e")
end

# Test 7: Finalize
println("\n📊 Test 7: Finalization...")
try
    finalfunc = getfield(SolverMonitoring, :finalize_monitoring!)
    finalfunc(monitor)
    println("✅ Finalization successful")
catch e
    println("❌ Finalization failed: $e")
end

println("\n🎯 Minimal test completed!")