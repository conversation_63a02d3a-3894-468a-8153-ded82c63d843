#!/usr/bin/env julia

# Simple test of SolverMonitoring module
include("src/Monitoring/SolverMonitoring.jl")
using .SolverMonitoring

println("🧪 Testing SolverMonitoring module...")

# Create monitoring system
monitor = create_monitoring_system(100, display=true, output_freq=5)

# Register residuals
register_residual!(monitor, "velocity", 1e-6)
register_residual!(monitor, "pressure", 1e-8)

println("\n🔄 Simulating solver iterations...")

# Simulate solver iterations
for iter in 1:20
    # Simulate decreasing residuals
    vel_res = 1.0 * exp(-0.1 * iter) + 1e-8
    press_res = 0.5 * exp(-0.15 * iter) + 1e-10
    
    # Update residuals
    update_residuals!(monitor, "velocity", vel_res)
    update_residuals!(monitor, "pressure", press_res)
    
    # Check convergence
    if check_convergence!(monitor)
        println("\n🎯 Converged!")
        break
    end
    
    # Show progress
    show_progress!(monitor)
    
    # Small delay for demonstration
    sleep(0.1)
end

# Finalize
finalize_monitoring!(monitor)

println("\n✅ SolverMonitoring test completed successfully!")