#!/usr/bin/env julia

# Terminal test runner for CFD.jl
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

println("""
🧪 CFD.jl Enhanced Terminal Test Suite
=======================================

Running all terminal-related tests to verify enhanced functionality.
""")

test_files = [
    "test_enhanced_terminal.jl",
    "test_mpi_detection.jl", 
    "final_comprehensive_test.jl"
]

global passed_tests = 0
global failed_tests = 0

for test_file in test_files
    println("\n" * "="^60)
    println("🔬 Running: $test_file")
    println("="^60)
    
    try
        include(test_file)
        global passed_tests += 1
        println("✅ $test_file: PASSED")
    catch e
        global failed_tests += 1
        println("❌ $test_file: FAILED - $e")
    end
end

println("\n" * "="^60)
println("🎯 TERMINAL TEST SUMMARY")
println("="^60)
println("Total tests: $(passed_tests + failed_tests)")
println("Passed: $passed_tests ✅")
println("Failed: $failed_tests $(failed_tests > 0 ? "❌" : "✅")")
println("Success rate: $(round(passed_tests/(passed_tests + failed_tests)*100, digits=1))%")

if failed_tests == 0
    println("\n🎉 All terminal tests passed! Enhanced terminal is ready for use!")
else
    println("\n⚠️  Some terminal tests failed. Check the errors above.")
end