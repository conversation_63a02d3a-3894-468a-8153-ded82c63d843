#!/usr/bin/env julia

# Test MPI detection fix
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

using CFD
using CFD.CFDTerminal

function test_command(command::String, args::Vector{String}=String[])
    substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
    CFD.CFDTerminal.handle_command(command, substrings)
end

# Overload for Any[] type
function test_command(command::String, args::Vector{Any})
    string_args = String[string(arg) for arg in args]
    test_command(command, string_args)
end

println("🔧 Testing Enhanced MPI Detection")
println("="^50)

# Test the status command which shows MPI detection
test_command("status", [])

println("\n🧪 Direct MPI Detection Test:")
println("="^30)

# Test MPI detection directly
mpi_status = try
    run(`mpirun --version`)
    if haskey(ENV, "OMPI_COMM_WORLD_SIZE")
        "✓ MPI active ($(ENV["OMPI_COMM_WORLD_SIZE"]) processes)"
    else
        "✓ MPI available (Open MPI installed)"
    end
catch e
    "❌ No MPI: $e"
end

println("MPI Status: $mpi_status")

# Test what version we can get
try
    version_output = readchomp(`mpirun --version`)
    version_line = split(version_output, '\n')[1]
    println("MPI Version: $version_line")
catch e
    println("Could not get MPI version: $e")
end

println("\n✅ MPI detection test complete!")