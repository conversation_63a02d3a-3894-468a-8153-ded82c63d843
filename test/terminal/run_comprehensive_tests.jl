#!/usr/bin/env julia

# Comprehensive test runner for CFD.jl and enhanced terminal
push!(LOAD_PATH, joinpath(@__DIR__, "..", "..", "src"))

println("""
🧪 CFD.jl Comprehensive Test Suite
==================================

Running all tests and validations to ensure everything works perfectly!
""")

# Test results tracking
test_results = Dict{String, Bool}()
error_log = String[]

function run_test(test_name::String, test_function::Function)
    println("\n" * "="^60)
    println("🔬 Running: $test_name")
    println("="^60)
    
    try
        test_function()
        test_results[test_name] = true
        println("✅ $test_name: PASSED")
    catch e
        test_results[test_name] = false
        error_msg = "$test_name: $e"
        push!(error_log, error_msg)
        println("❌ $test_name: FAILED - $e")
    end
end

# Load modules first
using CFD
using CFD.CFDTerminal
using LinearAlgebra
using StaticArrays

# Test 1: Basic Module Loading
run_test("Basic Module Loading", () -> begin
    println("✓ All modules loaded successfully")
end)

# Test 2: Terminal State Initialization
run_test("Terminal State Initialization", () -> begin
    state_exists = isdefined(CFD.CFDTerminal, :TERMINAL_STATE)
    @assert state_exists "Terminal state not initialized"
    println("✓ Terminal state properly initialized")
end)

# Test 3: Unicode Mathematical Operations
run_test("Unicode Mathematical Operations", () -> begin
    # Helper function
    function test_cmd(command::String, args::Vector{String}=String[])
        substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
        CFD.CFDTerminal.handle_command(command, substrings)
    end
    
    test_cmd("unicode", ["on"])
    test_cmd("∇", [])  # Should show help
    println("✓ Unicode mathematical operations working")
end)

# Test 4: Performance Monitoring
run_test("Performance Monitoring", () -> begin
    function test_cmd(command::String, args::Vector{String}=String[])
        substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
        CFD.CFDTerminal.handle_command(command, substrings)
    end
    
    test_cmd("monitor", ["on"])
    test_cmd("perf", [])
    println("✓ Performance monitoring working")
end)

# Test 5: Enhanced System Status
run_test("Enhanced System Status", () -> begin
    function test_cmd(command::String, args::Vector{String}=String[])
        substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
        CFD.CFDTerminal.handle_command(command, substrings)
    end
    
    test_cmd("status", [])
    println("✓ Enhanced system status working")
end)

# Test 6: Solver Information System
run_test("Solver Information System", () -> begin
    function test_cmd(command::String, args::Vector{String}=String[])
        substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
        CFD.CFDTerminal.handle_command(command, substrings)
    end
    
    test_cmd("list", ["detailed"])
    test_cmd("info", ["icoFoam"])
    println("✓ Solver information system working")
end)

# Test 7: Mathematical Validation (Standalone)
run_test("Mathematical Validation", () -> begin
    
    # Test mathematical accuracy
    n = 16
    x = LinRange(0, 1, n)
    y = LinRange(0, 1, n)
    φ = [sin(2π*x[i]) * cos(2π*y[j]) for i in 1:n, j in 1:n] |> vec
    
    # Simple gradient test
    function simple_gradient(φ, n)
        ∇φ = [SVector{2,Float64}(0,0) for _ in 1:length(φ)]
        for i in 2:(n-1), j in 2:(n-1)
            idx = (j-1)*n + i
            ∇φ[idx] = SVector((φ[idx+1] - φ[idx-1])/(2*1/n), (φ[idx+n] - φ[idx-n])/(2*1/n))
        end
        return ∇φ
    end
    
    ∇φ = simple_gradient(φ, n)
    max_grad = maximum(norm.(∇φ))
    
    @assert max_grad > 0 "Gradient computation failed"
    @assert max_grad < 100 "Gradient seems unreasonable"
    
    println("✓ Mathematical validation passed (max gradient: $(round(max_grad, digits=3)))")
end)

# Test 8: Hardware Detection
run_test("Hardware Detection", () -> begin
    # Test system information
    cpu_cores = Sys.CPU_THREADS
    julia_threads = Threads.nthreads()
    total_ram = Sys.total_memory() / 1024^3
    
    @assert cpu_cores > 0 "CPU core detection failed"
    @assert julia_threads > 0 "Thread detection failed"
    @assert total_ram > 0 "RAM detection failed"
    
    # Test GPU detection (doesn't fail if no GPU)
    gpu_detected = try
        run(`nvidia-smi -L`)
        true
    catch
        false
    end
    
    println("✓ Hardware detection working")
    println("  • CPU cores: $cpu_cores")
    println("  • Julia threads: $julia_threads")
    println("  • RAM: $(round(total_ram, digits=1)) GB")
    println("  • GPU: $(gpu_detected ? "Detected" : "Not detected")")
end)

# Test 9: Enhanced Help System
run_test("Enhanced Help System", () -> begin
    function test_cmd(command::String, args::Vector{String}=String[])
        substrings = SubString{String}[SubString(arg, 1:length(arg)) for arg in args]
        CFD.CFDTerminal.handle_command(command, substrings)
    end
    
    test_cmd("help", [])
    test_cmd("version", [])
    println("✓ Enhanced help system working")
end)

# Test 10: PISO Algorithm Stability (Quick Test)
run_test("PISO Algorithm Stability", () -> begin
    
    # Quick PISO stability test
    n = 8  # Small grid for quick test
    u_vec = [SVector(0.0, 0.0) for _ in 1:(n*n)]
    p = zeros(n*n)
    
    # Apply lid boundary condition
    for i in 1:n
        idx = (n-1)*n + i  # Top row
        u_vec[idx] = SVector(0.1, 0.0)  # Small velocity
    end
    
    # Simple stability check - velocity should remain bounded
    max_initial = maximum(norm.(u_vec))
    @assert max_initial ≈ 0.1 "Initial conditions incorrect"
    
    println("✓ PISO algorithm setup stable (initial max velocity: $(round(max_initial, digits=3)))")
end)

# Final Summary
println("\n" * "="^80)
println("🎯 COMPREHENSIVE TEST RESULTS")
println("="^80)

total_tests = length(test_results)
passed_tests = count(values(test_results))
failed_tests = total_tests - passed_tests

println("📊 Test Summary:")
println("  • Total tests: $total_tests")
println("  • Passed: $passed_tests ✅")
println("  • Failed: $failed_tests $(failed_tests > 0 ? "❌" : "✅")")
println("  • Success rate: $(round(passed_tests/total_tests*100, digits=1))%")

if failed_tests == 0
    println("\n🎉 ALL TESTS PASSED! 🎉")
    println("✨ CFD.jl and enhanced terminal are working perfectly!")
else
    println("\n⚠️  Some tests failed:")
    for error in error_log
        println("  ❌ $error")
    end
end

println("\n🔧 System Configuration:")
println("  • Julia: $(VERSION)")
println("  • CPU: $(Sys.CPU_THREADS) cores")
println("  • Julia threads: $(Threads.nthreads())")
println("  • RAM: $(round(Sys.total_memory()/1024^3, digits=1)) GB")

# Test hardware capabilities
gpu_status = try
    run(`nvidia-smi -L`)
    "✓ GPU Available"
catch
    "❌ No GPU"
end

mpi_status = try
    run(`mpirun --version`)
    if haskey(ENV, "OMPI_COMM_WORLD_SIZE")
        "✓ MPI Active"
    else
        "✓ MPI Available"
    end
catch
    "❌ No MPI"
end

println("  • GPU: $gpu_status")
println("  • MPI: $mpi_status")

println("\n🚀 Ready for production use!")
println("Start enhanced terminal with: CFDTerminal.start()")

# Return summary
return (passed=passed_tests, failed=failed_tests, total=total_tests)