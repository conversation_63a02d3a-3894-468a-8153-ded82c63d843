# test/debug/debug_simple_solver.jl
"""
Debug version of SIMPLE solver with comprehensive diagnostics.

This script runs the SIMPLE solver with extensive debugging and analysis
to identify the source of numerical instability.
"""

using Pkg
Pkg.activate(".")

include("../../src/CFD.jl")
using .CFD

# Load debugging tools
include("../../src/Utilities/SIMPLEDebugger.jl")
include("../../src/Utilities/PressureMatrixDebugger.jl")
using .SIMPLEDebugger
using .PressureMatrixDebugger

using LinearAlgebra
using SparseArrays
using Printf
using Plots

println("🔍 SIMPLE SOLVER DEBUG SESSION")
println("="^50)

# Create a small test case for debugging
println("Setting up small test case...")

# Create small mesh for detailed analysis
nx, ny, nz = 4, 4, 1  # Very small for detailed inspection
mesh = CFD.Utilities.create_unit_cube_mesh(nx, ny, nz)
num_cells = length(mesh.cells)
num_faces = length(mesh.faces)

println("Mesh: $(num_cells) cells, $(num_faces) faces")

# Physical properties - use moderate values
rho = 1000.0
nu = 1e-3    # Moderate viscosity
U_lid = 0.01 # Very low velocity to avoid instability

# Reynolds number
Re = U_lid * 1.0 / nu
println("Reynolds number: Re = $(Re)")

# Create boundary conditions
lid_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [U_lid, 0.0, 0.0])
wall_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.0, 0.0, 0.0])

velocity_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
    "inlet" => lid_bc,    # Top wall (moving lid)
    "outlet" => wall_bc,  # Bottom wall  
    "walls" => wall_bc    # Side walls
)

# Initialize fields
U_data = [SVector{3,Float64}(0.0, 0.0, 0.0) for _ in 1:num_cells]
U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, velocity_bcs)

p_data = zeros(num_cells)
p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())

# Conservative SIMPLE settings
simple_settings = CFD.Solvers.IncompressibleSolvers.SIMPLESettings(
    max_iterations = 3,     # Only a few iterations for debugging
    tolerance = 1e-3,
    alpha_u = 0.5,         # Conservative under-relaxation
    alpha_p = 0.2,         # Very conservative pressure relaxation
    residual_output_frequency = 1
)

println("\nInitial field analysis...")
initial_debug = debug_simple_solver(U_field, p_field, mesh, simple_settings; 
                                   full_analysis=true, save_plots=false)

println("\n" * "="^50)
println("RUNNING MODIFIED SIMPLE SOLVER WITH DEBUG HOOKS")
println("="^50)

# Run one iteration with detailed debugging
function debug_simple_iteration!(U_field, p_field, mesh, rho, nu, simple_settings)
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)
    mu = rho * nu
    
    println("\n🔧 MOMENTUM PREDICTOR DEBUGGING")
    println("-"^30)
    
    # Storage for momentum equation diagonal coefficients
    A_U_diag = Vector{Float64}(undef, num_cells)
    face_mass_fluxes = zeros(num_faces)
    
    # Solve momentum equations component-wise
    for d in 1:3
        println("\nComponent $d momentum equation:")
        
        # Extract velocity component
        U_component_data = [U_field.data[i][d] for i in 1:num_cells]
        
        # Create boundary conditions for this component
        U_component_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        for (patch_name, vector_bc) in U_field.boundary_conditions
            if isa(vector_bc, CFD.CFDCore.DirichletBC)
                U_component_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[d])
            else
                U_component_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
            end
        end
        
        U_component = CFD.CFDCore.ScalarField(Symbol("U$d"), mesh, U_component_data, U_component_bcs)
        
        println("  Field range: [$(minimum(U_component.data)), $(maximum(U_component.data))]")
        
        # Test matrix assembly
        try
            # Create simple mass flux field (zero initially)
            mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes, 
                                                     Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
            
            println("  Testing convection term assembly...")
            A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_component)
            println("    Convection matrix: $(size(A_conv.A)), nnz=$(nnz(A_conv.A))")
            
            # Check convection matrix properties
            conv_props = check_matrix_properties(A_conv.A, "Convection Matrix")
            
            println("  Testing diffusion term assembly...")
            A_diff = CFD.Numerics.fvm.laplacian(mu, U_component)
            println("    Diffusion matrix: $(size(A_diff.A)), nnz=$(nnz(A_diff.A))")
            
            # Check diffusion matrix properties  
            diff_props = check_matrix_properties(A_diff.A, "Diffusion Matrix")
            
            # Combine matrices
            A_momentum = A_conv.A + A_diff.A
            b_momentum = A_conv.b + A_diff.b
            
            println("  Combined momentum matrix:")
            momentum_props = check_matrix_properties(A_momentum, "Momentum Matrix")
            
            # Store diagonal coefficients
            if d == 1
                for i in 1:num_cells
                    A_U_diag[i] = A_momentum[i, i]
                end
                println("  Diagonal coefficients range: [$(minimum(A_U_diag)), $(maximum(A_U_diag))]")
            end
            
            # Check for problematic values
            if any(A_U_diag .≤ 0)
                @error "Non-positive diagonal coefficients detected!"
                zero_diag_cells = findall(A_U_diag .≤ 0)
                println("  Problematic cells: $(zero_diag_cells)")
            end
            
        catch e
            @error "Matrix assembly failed for component $d: $e"
            return false
        end
    end
    
    println("\n🔧 PRESSURE CORRECTION DEBUGGING")
    println("-"^30)
    
    # Create pressure correction field
    p_prime_data = zeros(Float64, num_cells)
    p_prime_bc = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
    for patch_name in keys(mesh.boundaries)
        p_prime_bc[patch_name] = CFD.CFDCore.NeumannBC((x, y, z, t) -> 0.0)
    end
    p_prime_field = CFD.CFDCore.ScalarField(:p_prime, mesh, p_prime_data, p_prime_bc)
    
    # Test pressure matrix assembly
    try
        println("Testing pressure correction matrix assembly...")
        
        # Simple approach: constant diffusivity
        gamma_p_prime_const = 1.0 / rho  # Simplified
        
        A_pressure = CFD.Numerics.fvm.laplacian(gamma_p_prime_const, p_prime_field)
        println("  Pressure matrix: $(size(A_pressure.A)), nnz=$(nnz(A_pressure.A))")
        
        # Debug the pressure matrix in detail
        println("\n📊 DETAILED PRESSURE MATRIX ANALYSIS")
        pressure_debug = debug_pressure_matrix(A_pressure.A, A_pressure.b, mesh;
                                             check_eigenvalues=false,  # Skip eigenvalues for small matrix
                                             fix_reference=true,
                                             test_solve=true)
        
        # Create a small reference matrix for comparison
        if num_cells <= 25
            println("\n📋 CREATING REFERENCE MATRIX")
            ref_A, (ref_nx, ref_ny) = assemble_reference_poisson(Int(sqrt(num_cells)), Int(sqrt(num_cells)))
            ref_props = check_matrix_properties(ref_A, "Reference Poisson")
            
            # Compare matrices
            println("\nMatrix comparison:")
            println("  CFD matrix diagonal range: [$(minimum(diag(A_pressure.A))), $(maximum(diag(A_pressure.A)))]")
            println("  Ref matrix diagonal range: [$(minimum(diag(ref_A))), $(maximum(diag(ref_A)))]")
        end
        
    catch e
        @error "Pressure matrix assembly failed: $e"
        return false
    end
    
    println("\n✅ Single iteration debug complete")
    return true
end

# Run the debug iteration
success = debug_simple_iteration!(U_field, p_field, mesh, rho, nu, simple_settings)

if success
    println("\n🎉 Debug iteration completed successfully!")
    println("\nNext steps:")
    println("1. Check matrix assembly sign conventions")
    println("2. Verify boundary condition implementation")
    println("3. Test with even smaller Reynolds number")
    println("4. Add proper Rhie-Chow interpolation")
else
    println("\n❌ Debug iteration failed - check error messages above")
end

println("\n" * "="^50)
println("DEBUG SESSION COMPLETE")
println("="^50)