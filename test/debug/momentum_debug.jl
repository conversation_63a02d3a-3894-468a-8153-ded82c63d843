# test/debug/momentum_debug.jl
"""
Debug momentum equation assembly in SIMPLE solver.
"""

using Pkg
Pkg.activate(".")

include("../../src/CFD.jl")
using .CFD

using LinearAlgebra
using SparseArrays
using Printf
using StaticArrays

println("🔍 MOMENTUM EQUATION DEBUG")
println("="^50)

# Create small test case
nx, ny, nz = 3, 3, 1
mesh = CFD.Utilities.create_unit_cube_mesh(nx, ny, nz)
num_cells = length(mesh.cells)
num_faces = length(mesh.faces)

println("Mesh: $(num_cells) cells, $(num_faces) faces")

# Create test velocity field with small values
U_data = [SVector{3,Float64}(0.01, 0.0, 0.0) for _ in 1:num_cells]

# Create boundary conditions 
lid_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.01, 0.0, 0.0])
wall_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.0, 0.0, 0.0])

velocity_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
    "inlet" => lid_bc,   
    "outlet" => wall_bc,  
    "walls" => wall_bc    
)

U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, velocity_bcs)

println("Max velocity: $(maximum(norm.(U_field.data)))")

# Test momentum equation assembly for x-component
println("\n🔧 Testing x-momentum equation assembly...")

# Extract x-component
U_x_data = [U_field.data[i][1] for i in 1:num_cells]

# Create scalar boundary conditions for x-component
U_x_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
for (patch_name, vector_bc) in U_field.boundary_conditions
    if isa(vector_bc, CFD.CFDCore.DirichletBC)
        U_x_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[1])
    else
        U_x_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
    end
end

U_x_field = CFD.CFDCore.ScalarField(:Ux, mesh, U_x_data, U_x_bcs)

println("U_x field range: [$(minimum(U_x_field.data)), $(maximum(U_x_field.data))]")

# Test diffusion term
rho = 1000.0
nu = 1e-3
mu = rho * nu

println("\n📊 Testing diffusion term: μ∇²U_x")
try
    A_diff = CFD.Numerics.fvm.laplacian(mu, U_x_field)
    
    println("Diffusion matrix:")
    println("  Size: $(size(A_diff.A))")
    println("  Non-zeros: $(nnz(A_diff.A))")
    
    if num_cells <= 16
        println("\nDiffusion matrix structure:")
        A_dense = Matrix(A_diff.A)
        for i in 1:size(A_dense, 1)
            row_str = @sprintf("Row %2d: ", i)
            for j in 1:size(A_dense, 2)
                row_str *= @sprintf("%8.1f ", A_dense[i, j])
            end
            println(row_str)
        end
    end
    
    # Check diagonal values
    diag_vals = diag(A_diff.A)
    println("Diffusion diagonal range: [$(minimum(diag_vals)), $(maximum(diag_vals))]")
    
    # Check if any diagonal is negative (this would be wrong!)
    negative_diags = count(diag_vals .< 0)
    if negative_diags > 0
        @error "$(negative_diags) negative diagonal entries in diffusion matrix!"
    else
        println("✅ All diffusion diagonal entries positive")
    end
    
catch e
    @error "Diffusion term assembly failed: $e"
end

# Test convection term
println("\n📊 Testing convection term: ∇⋅(ρUU_x)")
try
    # Create face mass flux field (initially zero)
    face_mass_fluxes = zeros(num_faces)
    mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes, 
                                             Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
    
    A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_x_field)
    
    println("Convection matrix:")
    println("  Size: $(size(A_conv.A))")  
    println("  Non-zeros: $(nnz(A_conv.A))")
    
    # Since mass flux is zero, convection matrix should be zero
    max_conv_entry = maximum(abs.(A_conv.A))
    println("  Max |entry|: $(max_conv_entry)")
    
    if max_conv_entry < 1e-14
        println("✅ Zero mass flux gives zero convection matrix (correct)")
    else
        @warn "Non-zero convection matrix with zero mass flux!"
    end
    
catch e
    @error "Convection term assembly failed: $e"
end

# Test combined momentum matrix
println("\n📊 Testing combined momentum matrix")
try
    # Simple case: diffusion only (zero convection)
    face_mass_fluxes = zeros(num_faces)
    mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes, 
                                             Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
    
    A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_x_field)
    A_diff = CFD.Numerics.fvm.laplacian(mu, U_x_field)
    
    A_momentum = A_conv.A + A_diff.A
    b_momentum = A_conv.b + A_diff.b
    
    println("Combined momentum matrix:")
    println("  Size: $(size(A_momentum))")
    
    # Check diagonal values
    diag_vals = diag(A_momentum)
    println("  Diagonal range: [$(minimum(diag_vals)), $(maximum(diag_vals))]")
    
    # Critical check: are diagonal values reasonable?
    if any(diag_vals .<= 0)
        @error "Non-positive diagonal entries in momentum matrix!"
        negative_cells = findall(diag_vals .<= 0)
        println("  Problem cells: $(negative_cells)")
    else
        println("  ✅ All diagonal entries positive")
    end
    
    # Check if diagonal values are huge (could cause instability)
    if any(diag_vals .> 1e10)
        @warn "Very large diagonal entries detected: max = $(maximum(diag_vals))"
    end
    
    # Test momentum equation solve
    println("\n🔧 Testing momentum equation solve...")
    try
        x = A_momentum \ b_momentum
        residual = norm(A_momentum * x - b_momentum)
        
        println("  Solution norm: $(norm(x))")
        println("  Residual: $(residual)")
        
        if residual < 1e-10
            println("  ✅ Momentum solve successful")
        else
            @warn "High residual in momentum solve: $(residual)"
        end
        
        # Check for extreme solution values
        if any(abs.(x) .> 1e6)
            @error "Extreme solution values detected: max = $(maximum(abs.(x)))"
        end
        
    catch e
        @error "Momentum solve failed: $e"
    end
    
catch e
    @error "Combined momentum matrix failed: $e"
end

# Test under-relaxation effects
println("\n📊 Testing under-relaxation effects")
try
    # Create momentum matrix
    face_mass_fluxes = zeros(num_faces)
    mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes, 
                                             Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
    
    A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_x_field)
    A_diff = CFD.Numerics.fvm.laplacian(mu, U_x_field)
    A_momentum = A_conv.A + A_diff.A
    
    # Apply under-relaxation
    alpha_u = 0.7
    A_relaxed = copy(A_momentum)
    
    for i in 1:num_cells
        A_relaxed[i, i] = A_momentum[i, i] / alpha_u
    end
    
    println("Under-relaxation factor: $(alpha_u)")
    println("  Original diagonal range: [$(minimum(diag(A_momentum))), $(maximum(diag(A_momentum)))]")
    println("  Relaxed diagonal range: [$(minimum(diag(A_relaxed))), $(maximum(diag(A_relaxed)))]")
    
    ratio = minimum(diag(A_relaxed)) / minimum(diag(A_momentum))
    println("  Diagonal scaling factor: $(ratio) (should be ≈ $(1/alpha_u))")
    
    if abs(ratio - 1/alpha_u) < 1e-12
        println("  ✅ Under-relaxation applied correctly")
    else
        @warn "Under-relaxation scaling incorrect!"
    end
    
catch e
    @error "Under-relaxation test failed: $e"
end

println("\n" * "="^50)
println("MOMENTUM DEBUG COMPLETE")
println("\n🎯 Key findings:")
println("1. Check diagonal entry values in momentum matrix")
println("2. Verify under-relaxation implementation")
println("3. Look for extreme values causing overflow")
println("4. Check boundary condition application")