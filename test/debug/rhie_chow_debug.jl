# test/debug/rhie_chow_debug.jl
"""
Debug Rhie-Chow interpolation and face flux calculation in SIMPLE solver.
This is where the extreme velocity values are likely being generated.
"""

using Pkg
Pkg.activate(".")

include("../../src/CFD.jl")
using .CFD

using LinearAlgebra
using SparseArrays
using Printf
using StaticArrays

println("🔍 RHIE-CHOW INTERPOLATION DEBUG")
println("="^50)

# Create small test case
nx, ny, nz = 3, 3, 1
mesh = CFD.Utilities.create_unit_cube_mesh(nx, ny, nz)
num_cells = length(mesh.cells)
num_faces = length(mesh.faces)

println("Mesh: $(num_cells) cells, $(num_faces) faces")

# Create reasonable test velocity field
U_data = [SVector{3,Float64}(0.001, 0.0, 0.0) for _ in 1:num_cells]  # Very small velocities

# Create boundary conditions
lid_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.001, 0.0, 0.0])
wall_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.0, 0.0, 0.0])

velocity_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
    "inlet" => lid_bc,   
    "outlet" => wall_bc,  
    "walls" => wall_bc    
)

U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, velocity_bcs)

# Create pressure field with small variations
p_data = [0.001 * sin(i) for i in 1:num_cells]  # Small pressure variations
p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())

println("Initial field statistics:")
println("  Max velocity: $(maximum(norm.(U_field.data)))")
println("  Pressure range: [$(minimum(p_field.data)), $(maximum(p_field.data))]")

# Test momentum diagonal coefficient calculation
println("\n🔧 Testing momentum diagonal coefficient calculation...")

try
    # Physical properties
    rho = 1000.0
    nu = 1e-3
    mu = rho * nu
    
    # Calculate momentum diagonal coefficients (simplified)
    A_U_diag = Vector{Float64}(undef, num_cells)
    
    # Extract x-component for testing
    U_x_data = [U_field.data[i][1] for i in 1:num_cells]
    U_x_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
    for (patch_name, vector_bc) in U_field.boundary_conditions
        if isa(vector_bc, CFD.CFDCore.DirichletBC)
            U_x_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[1])
        else
            U_x_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
        end
    end
    U_x_field = CFD.CFDCore.ScalarField(:Ux, mesh, U_x_data, U_x_bcs)
    
    # Assemble momentum matrix
    face_mass_fluxes = zeros(num_faces)
    mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes, 
                                             Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
    
    A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_x_field)
    A_diff = CFD.Numerics.fvm.laplacian(mu, U_x_field)
    A_momentum = A_conv.A + A_diff.A
    
    # Extract diagonal coefficients
    for i in 1:num_cells
        A_U_diag[i] = A_momentum[i, i]
    end
    
    println("Momentum diagonal coefficients:")
    println("  Range: [$(minimum(A_U_diag)), $(maximum(A_U_diag))]")
    println("  Mean: $(sum(A_U_diag)/length(A_U_diag))")
    
    # Check for problematic values
    if any(A_U_diag .<= 0)
        @error "Non-positive diagonal coefficients!"
        problem_cells = findall(A_U_diag .<= 0)
        println("  Problem cells: $(problem_cells)")
    else
        println("  ✅ All diagonal coefficients positive")
    end
    
    # Check for extreme values
    if any(A_U_diag .> 1e10)
        @warn "Very large diagonal coefficients detected!"
        large_cells = findall(A_U_diag .> 1e10)
        println("  Large coefficient cells: $(large_cells)")
    end
    
    # Test Rhie-Chow interpolation
    println("\n🔧 Testing Rhie-Chow interpolation...")
    
    # Storage for face velocities
    face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
    
    try
        # Call Rhie-Chow interpolation
        CFD.Solvers.rhie_chow_velocity_interpolation!(
            face_velocities, U_field, p_field, A_U_diag, mesh
        )
        
        println("Rhie-Chow interpolation results:")
        face_velocity_magnitudes = [norm(v) for v in face_velocities]
        println("  Face velocity range: [$(minimum(face_velocity_magnitudes)), $(maximum(face_velocity_magnitudes))]")
        println("  Mean face velocity: $(sum(face_velocity_magnitudes)/length(face_velocity_magnitudes))")
        
        # Check for extreme values
        extreme_faces = findall(face_velocity_magnitudes .> 1e3)
        if !isempty(extreme_faces)
            @error "Extreme face velocities detected!"
            println("  Extreme velocity faces: $(extreme_faces[1:min(5, end)])")
            println("  Max velocity: $(maximum(face_velocity_magnitudes))")
            
            # Analyze the first extreme face
            if !isempty(extreme_faces)
                face_idx = extreme_faces[1]
                face = mesh.faces[face_idx]
                println("\n📊 Analyzing extreme face $(face_idx):")
                println("  Owner cell: $(face.owner)")
                println("  Neighbor cell: $(face.neighbor)")
                println("  Is boundary: $(face.boundary)")
                
                if !face.boundary && face.neighbor > 0
                    owner_diag = A_U_diag[face.owner]
                    neighbor_diag = A_U_diag[face.neighbor]
                    println("  Owner diagonal: $(owner_diag)")
                    println("  Neighbor diagonal: $(neighbor_diag)")
                    
                    owner_p = p_field.data[face.owner]
                    neighbor_p = p_field.data[face.neighbor]
                    println("  Owner pressure: $(owner_p)")
                    println("  Neighbor pressure: $(neighbor_p)")
                    
                    pressure_diff = neighbor_p - owner_p
                    face_distance = norm(mesh.cells[face.neighbor].center - mesh.cells[face.owner].center)
                    pressure_gradient = pressure_diff / face_distance
                    println("  Pressure gradient: $(pressure_gradient)")
                    
                    # Check if diagonal coefficients are causing issues
                    if min(owner_diag, neighbor_diag) < 1e-10
                        @error "Very small diagonal coefficient causing extreme Rhie-Chow correction!"
                    end
                    
                    # Estimate Rhie-Chow correction magnitude
                    gamma_f = 0.5 * (1.0/owner_diag + 1.0/neighbor_diag)
                    rc_correction_magnitude = gamma_f * abs(pressure_gradient) * face.area
                    println("  Estimated RC correction magnitude: $(rc_correction_magnitude)")
                end
            end
            
        else
            println("  ✅ No extreme face velocities detected")
        end
        
        # Test face mass flux calculation
        println("\n🔧 Testing face mass flux calculation...")
        
        face_mass_fluxes_rc = zeros(num_faces)
        
        for face_idx in 1:num_faces
            face = mesh.faces[face_idx]
            face_area_vector = face.normal * face.area
            face_mass_fluxes_rc[face_idx] = rho * dot(face_velocities[face_idx], face_area_vector)
        end
        
        println("Face mass flux results:")
        flux_magnitudes = abs.(face_mass_fluxes_rc)
        println("  Mass flux range: [$(minimum(flux_magnitudes)), $(maximum(flux_magnitudes))]")
        println("  Mean |mass flux|: $(sum(flux_magnitudes)/length(flux_magnitudes))")
        
        # Check for extreme mass fluxes
        extreme_flux_faces = findall(flux_magnitudes .> 1e6)
        if !isempty(extreme_flux_faces)
            @error "Extreme mass fluxes detected!"
            println("  Extreme flux faces: $(extreme_flux_faces[1:min(5, end)])")
            println("  Max mass flux: $(maximum(flux_magnitudes))")
        else
            println("  ✅ No extreme mass fluxes detected")
        end
        
        # Test mass conservation
        println("\n🔧 Testing mass conservation...")
        
        total_mass_imbalance = 0.0
        cell_imbalances = Vector{Float64}(undef, num_cells)
        
        for cell_idx in 1:num_cells
            cell = mesh.cells[cell_idx]
            mass_flux_sum = 0.0
            
            for face_idx in cell.faces
                face = mesh.faces[face_idx]
                
                if face.owner == cell_idx
                    # Outgoing flux (positive)
                    mass_flux_sum += face_mass_fluxes_rc[face_idx]
                elseif !face.boundary && face.neighbor == cell_idx
                    # Incoming flux (negative)
                    mass_flux_sum -= face_mass_fluxes_rc[face_idx]
                end
            end
            
            cell_imbalances[cell_idx] = abs(mass_flux_sum)
            total_mass_imbalance += abs(mass_flux_sum)
        end
        
        avg_mass_imbalance = total_mass_imbalance / num_cells
        max_mass_imbalance = maximum(cell_imbalances)
        
        println("Mass conservation check:")
        println("  Average |imbalance| per cell: $(avg_mass_imbalance)")
        println("  Maximum |imbalance|: $(max_mass_imbalance)")
        
        if max_mass_imbalance > 1e-3
            @warn "Poor mass conservation detected!"
            worst_cells = findall(cell_imbalances .> 0.1 * max_mass_imbalance)
            println("  Problematic cells: $(worst_cells[1:min(5, end)])")
        else
            println("  ✅ Reasonable mass conservation")
        end
        
    catch e
        @error "Rhie-Chow interpolation failed: $e"
        println("Stacktrace:")
        for (i, frame) in enumerate(stacktrace(catch_backtrace()))
            println("  $i: $frame")
            if i > 5 break end
        end
    end
    
catch e
    @error "Momentum diagonal calculation failed: $e"
end

# Test with different diagonal coefficient values
println("\n🔧 Testing with artificial diagonal coefficients...")

try
    # Test with reasonable artificial diagonal values
    A_U_diag_test = fill(5.0, num_cells)  # Uniform, reasonable values
    face_velocities_test = Vector{SVector{3,Float64}}(undef, num_faces)
    
    CFD.Solvers.rhie_chow_velocity_interpolation!(
        face_velocities_test, U_field, p_field, A_U_diag_test, mesh
    )
    
    test_velocity_magnitudes = [norm(v) for v in face_velocities_test]
    println("Test with uniform diagonal coefficients:")
    println("  Face velocity range: [$(minimum(test_velocity_magnitudes)), $(maximum(test_velocity_magnitudes))]")
    
    if maximum(test_velocity_magnitudes) < 1e3
        println("  ✅ Uniform diagonal coefficients give reasonable velocities")
        println("  → Problem may be in diagonal coefficient calculation, not Rhie-Chow implementation")
    else
        println("  ⚠️  Still extreme velocities with uniform coefficients")
        println("  → Problem may be in Rhie-Chow implementation itself")
    end
    
catch e
    @error "Test with artificial coefficients failed: $e"
end

println("\n" * "="^50)
println("RHIE-CHOW DEBUG COMPLETE")
println("\n🎯 Key findings:")
println("1. Check momentum diagonal coefficient calculation")
println("2. Verify Rhie-Chow pressure gradient computation") 
println("3. Check for division by small numbers in RC interpolation")
println("4. Verify face area and distance calculations")
println("5. Check boundary condition handling in RC interpolation")