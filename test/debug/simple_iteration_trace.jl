"""
Trace through one complete SIMPLE iteration to identify where extreme values develop.
This will help us find the exact step where the solver explodes.
"""

using Pkg
Pkg.activate(".")

include("../../src/CFD.jl")
using .CFD

using LinearAlgebra
using SparseArrays
using Printf
using StaticArrays

println("🔍 SIMPLE ITERATION TRACE DEBUG")
println("="^60)

# Create small test case
nx, ny, nz = 3, 3, 1
mesh = CFD.Utilities.create_unit_cube_mesh(nx, ny, nz)
num_cells = length(mesh.cells)
num_faces = length(mesh.faces)

println("Mesh: $(num_cells) cells, $(num_faces) faces")

# Physical properties
rho = 1000.0
nu = 1e-3
mu = rho * nu

# Create initial fields with small, reasonable values
U_data = [SVector{3,Float64}(0.001, 0.0, 0.0) for _ in 1:num_cells]
p_data = zeros(num_cells)  # Start with zero pressure

# Boundary conditions
lid_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.001, 0.0, 0.0])
wall_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [0.0, 0.0, 0.0])

velocity_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
    "inlet" => lid_bc,
    "outlet" => wall_bc,
    "walls" => wall_bc
)

U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, velocity_bcs)
p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())

# SIMPLE parameters
alpha_U = 0.7  # Under-relaxation for velocity
alpha_p = 0.3  # Under-relaxation for pressure

println("Initial conditions:")
println("  Max velocity: $(maximum(norm.(U_field.data)))")
println("  Pressure range: [$(minimum(p_field.data)), $(maximum(p_field.data))]")

function check_field_sanity(field_name, field_data, max_reasonable=1e3)
    """Check if field values are reasonable"""
    if isa(field_data, Vector{<:SVector})
        magnitudes = [norm(v) for v in field_data]
        max_val = maximum(magnitudes)
        mean_val = sum(magnitudes) / length(magnitudes)
    else
        max_val = maximum(abs.(field_data))
        mean_val = sum(abs.(field_data)) / length(field_data)
    end
    
    println("  $(field_name): max=$(max_val), mean=$(mean_val)")
    
    if max_val > max_reasonable
        @error "$(field_name) has extreme values!"
        return false
    end
    return true
end

# Trace one SIMPLE iteration step by step
println("\n🔧 STEP 1: Calculate face mass fluxes")
println("-"^40)

try
    # Calculate face mass fluxes using current velocity field
    face_mass_fluxes = zeros(num_faces)
    
    for face_idx in 1:num_faces
        face = mesh.faces[face_idx]
        
        if face.boundary
            # Boundary face - use boundary condition
            patch_name = mesh.boundary_patches[face.patch_id].name
            if haskey(U_field.boundary_conditions, patch_name)
                bc = U_field.boundary_conditions[patch_name]
                if isa(bc, CFD.CFDCore.DirichletBC)
                    face_center = face.center
                    bc_velocity = bc.value(face_center[1], face_center[2], face_center[3], 0.0)
                    face_area_vector = face.normal * face.area
                    face_mass_fluxes[face_idx] = rho * dot(bc_velocity, face_area_vector)
                end
            end
        else
            # Internal face - interpolate from cell centers
            owner_vel = U_field.data[face.owner]
            neighbor_vel = U_field.data[face.neighbor]
            face_velocity = 0.5 * (owner_vel + neighbor_vel)
            face_area_vector = face.normal * face.area
            face_mass_fluxes[face_idx] = rho * dot(face_velocity, face_area_vector)
        end
    end
    
    check_field_sanity("Face mass fluxes", face_mass_fluxes, 1e6)
    
catch e
    @error "Step 1 failed: $e"
    rethrow(e)
end

println("\n🔧 STEP 2: Solve momentum equations")
println("-"^40)

try
    # Create mass flux field for FVM operators
    mass_flux_field = CFD.CFDCore.ScalarField(:mass_flux, mesh, face_mass_fluxes,
                                             Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
    
    # Solve for each velocity component
    U_new_data = Vector{SVector{3,Float64}}(undef, num_cells)
    A_U_diag = Vector{Float64}(undef, num_cells)
    
    for comp in 1:3
        println("  Component $(comp):")
        
        # Extract component field
        U_comp_data = [U_field.data[i][comp] for i in 1:num_cells]
        U_comp_bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        
        for (patch_name, vector_bc) in U_field.boundary_conditions
            if isa(vector_bc, CFD.CFDCore.DirichletBC)
                U_comp_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[comp])
            else
                U_comp_bcs[patch_name] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
            end
        end
        
        U_comp_field = CFD.CFDCore.ScalarField(Symbol("U$(comp)"), mesh, U_comp_data, U_comp_bcs)
        
        # Assemble momentum equation: ∇⋅(ρUU) - ∇⋅(μ∇U) = -∇p
        A_conv = CFD.Numerics.fvm.div(mass_flux_field, U_comp_field)
        A_diff = CFD.Numerics.fvm.laplacian(mu, U_comp_field)
        
        A_momentum = A_conv.A + A_diff.A
        b_momentum = A_conv.b + A_diff.b
        
        # Store diagonal coefficients for Rhie-Chow (only need x-component)
        if comp == 1
            for i in 1:num_cells
                A_U_diag[i] = A_momentum[i, i]
            end
        end
        
        # Check matrix condition
        diag_vals = [A_momentum[i, i] for i in 1:num_cells]
        println("    Matrix diagonal range: [$(minimum(diag_vals)), $(maximum(diag_vals))]")
        
        if any(diag_vals .<= 0)
            @error "Non-positive diagonal in momentum matrix!"
            problem_cells = findall(diag_vals .<= 0)
            println("    Problem cells: $(problem_cells)")
        end
        
        # Solve momentum equation
        U_comp_new = A_momentum \ b_momentum
        
        check_field_sanity("U$(comp) solution", U_comp_new)
        
        # Apply under-relaxation
        U_comp_relaxed = alpha_U * U_comp_new + (1 - alpha_U) * U_comp_data
        
        check_field_sanity("U$(comp) after relaxation", U_comp_relaxed)
        
        # Store component
        if comp == 1
            U_new_data = [SVector{3,Float64}(U_comp_relaxed[i], 0.0, 0.0) for i in 1:num_cells]
        elseif comp == 2
            for i in 1:num_cells
                U_new_data[i] = SVector{3,Float64}(U_new_data[i][1], U_comp_relaxed[i], U_new_data[i][3])
            end
        else  # comp == 3
            for i in 1:num_cells
                U_new_data[i] = SVector{3,Float64}(U_new_data[i][1], U_new_data[i][2], U_comp_relaxed[i])
            end
        end
    end
    
    # Update velocity field
    U_field.data .= U_new_data
    check_field_sanity("Velocity after momentum solve", U_field.data)
    
catch e
    @error "Step 2 failed: $e"
    rethrow(e)
end
