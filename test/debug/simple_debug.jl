# test/debug/simple_debug.jl
"""
Simple debug test to analyze SIMPLE solver matrix assembly issues.
This uses only basic Julia packages.
"""

using Pkg
Pkg.activate(".")

include("../../src/CFD.jl")
using .CFD

using LinearAlgebra
using SparseArrays
using Printf

println("🔍 SIMPLE SOLVER MATRIX DEBUG")
println("="^50)

# Create very small test case for detailed analysis
nx, ny, nz = 3, 3, 1  # Tiny mesh for manual inspection
mesh = CFD.Utilities.create_unit_cube_mesh(nx, ny, nz)
num_cells = length(mesh.cells)

println("Mesh: $(num_cells) cells")

# Create test pressure field
p_data = zeros(num_cells)
p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())

# Test pressure correction matrix assembly
try
    println("\n🔧 Testing pressure correction matrix assembly...")
    
    # Create pressure correction field with zero Neumann BCs
    p_prime_data = zeros(Float64, num_cells)
    p_prime_bc = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
    for patch_name in keys(mesh.boundaries)
        p_prime_bc[patch_name] = CFD.CFDCore.NeumannBC((x, y, z, t) -> 0.0)
    end
    p_prime_field = CFD.CFDCore.ScalarField(:p_prime, mesh, p_prime_data, p_prime_bc)
    
    # Test with constant diffusivity
    gamma = 1.0
    A_pressure = CFD.Numerics.fvm.laplacian(gamma, p_prime_field)
    
    println("Pressure matrix assembled:")
    println("  Size: $(size(A_pressure.A))")
    println("  Non-zeros: $(nnz(A_pressure.A))")
    
    # Print matrix structure for small case
    if num_cells <= 16
        println("\nPressure matrix structure:")
        A_dense = Matrix(A_pressure.A)
        for i in 1:size(A_dense, 1)
            row_str = @sprintf("Row %2d: ", i)
            for j in 1:size(A_dense, 2)
                row_str *= @sprintf("%8.2f ", A_dense[i, j])
            end
            println(row_str)
        end
    end
    
    # Check matrix properties
    println("\nMatrix properties:")
    diag_vals = diag(A_pressure.A)
    println("  Diagonal range: [$(minimum(diag_vals)), $(maximum(diag_vals))]")
    
    # Row sums (should be ~0 for pure Neumann)
    row_sums = [sum(A_pressure.A[i, :]) for i in 1:size(A_pressure.A, 1)]
    max_row_sum = maximum(abs.(row_sums))
    println("  Max |row sum|: $(max_row_sum)")
    
    if max_row_sum < 1e-12
        println("  ✅ Matrix has zero row sums (pure Neumann BC consistent)")
    else
        println("  ⚠️  Non-zero row sums detected")
    end
    
    # Check for zero diagonals
    zero_diags = count(abs.(diag_vals) .< 1e-14)
    if zero_diags > 0
        @error "$(zero_diags) zero diagonal entries!"
    else
        println("  ✅ All diagonal entries non-zero")
    end
    
    # Test solve with reference fixing
    try
        println("\n🔧 Testing solve with pressure reference...")
        
        # Create simple RHS (constant divergence)
        b_test = ones(num_cells)
        
        # Fix pressure reference at first cell
        A_fixed = copy(A_pressure.A)
        b_fixed = copy(b_test)
        
        # Set first row to identity (p[1] = 0)
        A_fixed[1, :] .= 0.0
        A_fixed[1, 1] = 1.0
        b_fixed[1] = 0.0
        
        println("  Fixed matrix diagonal range: [$(minimum(diag(A_fixed))), $(maximum(diag(A_fixed)))]")
        
        # Test solve
        x = A_fixed \ b_fixed
        residual = norm(A_fixed * x - b_fixed)
        
        println("  Solution norm: $(norm(x))")
        println("  Residual: $(residual)")
        
        if residual < 1e-10
            println("  ✅ Direct solve successful")
        else
            @warn "High residual in direct solve: $(residual)"
        end
        
    catch e
        @error "Direct solve failed: $e"
    end
    
catch e
    @error "Matrix assembly failed: $e"
    println("Stacktrace:")
    for (i, frame) in enumerate(stacktrace(catch_backtrace()))
        println("  $i: $frame")
        if i > 10 break end
    end
end

println("\n" * "="^50)
println("DEBUG COMPLETE")

# Test what specific FVM operators we have available
println("\n🔧 Available FVM operators:")
try
    println("  CFD.Numerics.fvm methods:")
    for name in names(CFD.Numerics.fvm)
        if name != :fvm
            println("    - $name")
        end
    end
catch e
    println("  Error accessing fvm methods: $e")
end