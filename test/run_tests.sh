#!/bin/bash

# CFD.jl Test Runner Script

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}CFD.jl Test Runner${NC}"
echo "===================="

# Parse command line arguments
VERBOSE=false
PARALLEL=false
GPU=false
BENCHMARKS=false
CATEGORY=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        -g|--gpu)
            GPU=true
            shift
            ;;
        -b|--benchmarks)
            BENCHMARKS=true
            shift
            ;;
        unit|integration|all)
            CATEGORY=$1
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Usage: $0 [options] [category]"
            echo "Options:"
            echo "  -v, --verbose     Enable verbose output"
            echo "  -p, --parallel    Run parallel tests"
            echo "  -g, --gpu         Run GPU tests"
            echo "  -b, --benchmarks  Run benchmarks"
            echo "Categories:"
            echo "  unit             Run unit tests only"
            echo "  integration      Run integration tests only"
            echo "  all              Run all tests (default)"
            exit 1
            ;;
    esac
done

# Set environment variables
export CFD_TEST_VERBOSE=$VERBOSE
export CFD_TEST_PARALLEL=$PARALLEL
export CFD_TEST_GPU=$GPU
export CFD_TEST_BENCHMARKS=$BENCHMARKS

# Run tests based on category
case $CATEGORY in
    unit)
        echo -e "${YELLOW}Running unit tests...${NC}"
        julia --project=. -e 'using Test; include("test/runtests.jl")' -- unit
        ;;
    integration)
        echo -e "${YELLOW}Running integration tests...${NC}"
        julia --project=. -e 'using Test; include("test/runtests.jl")' -- integration
        ;;
    *)
        echo -e "${YELLOW}Running all tests...${NC}"
        julia --project=. test/runtests.jl
        ;;
esac

echo -e "${GREEN}Test run complete!${NC}"