# Test configuration for CFD.jl

# Test execution settings
const TEST_VERBOSE = get(ENV, "CFD_TEST_VERBOSE", "false") == "true"
const TEST_PARALLEL = get(ENV, "CFD_TEST_PARALLEL", "false") == "true"
const TEST_GPU = get(ENV, "CFD_TEST_GPU", "false") == "true"
const TEST_BENCHMARKS = get(ENV, "CFD_TEST_BENCHMARKS", "false") == "true"

# Test data paths
const TEST_FIXTURES_DIR = joinpath(dirname(@__DIR__), "fixtures")
const TEST_MESH_2D_SIMPLE = joinpath(TEST_FIXTURES_DIR, "test_2d_empty")
const TEST_MESH_2D_COMPARISON = joinpath(TEST_FIXTURES_DIR, "test_2d_comparison")
const TEST_MESH_3D_COMPARISON = joinpath(TEST_FIXTURES_DIR, "test_3d_comparison")

# Test tolerances
const TEST_TOLERANCE_STRICT = 1e-12
const TEST_TOLERANCE_NORMAL = 1e-8
const TEST_TOLERANCE_LOOSE = 1e-4

# Helper functions
function get_test_mesh(name::Symbol)
    meshes = Dict(
        :simple_2d => TEST_MESH_2D_SIMPLE,
        :comparison_2d => TEST_MESH_2D_COMPARISON,
        :comparison_3d => TEST_MESH_3D_COMPARISON,
        :bc_test => joinpath(TEST_FIXTURES_DIR, "test_bc"),
        :operators => joinpath(TEST_FIXTURES_DIR, "test_operators"),
        :piso => joinpath(TEST_FIXTURES_DIR, "test_piso"),
        :validation => joinpath(TEST_FIXTURES_DIR, "test_validation")
    )
    
    return get(meshes, name, nothing)
end

# Skip slow tests in CI
const SKIP_SLOW_TESTS = get(ENV, "CI", "false") == "true"

# Export test utilities
export TEST_VERBOSE, TEST_PARALLEL, TEST_GPU, TEST_BENCHMARKS
export TEST_FIXTURES_DIR, get_test_mesh
export TEST_TOLERANCE_STRICT, TEST_TOLERANCE_NORMAL, TEST_TOLERANCE_LOOSE
export SKIP_SLOW_TESTS