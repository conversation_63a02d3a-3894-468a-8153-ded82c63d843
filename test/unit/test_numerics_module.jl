# Test suite for Numerics module
using Test
using CFD
using CFD.CFDCore
using CFD.Numerics
using StaticArrays
using SparseArrays
using LinearAlgebra

# Import the fvm and fvc modules explicitly
const fvm = CFD.Numerics.fvm
const fvc = CFD.Numerics.fvc

@testset "Numerics Tests" begin
    
    # Create a simple test mesh
    function create_test_mesh()
        nodes = [
            Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
            Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
            Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
            Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false)
        ]
        
        faces = [
            Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, -1, true),
            Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, -1, true),
            Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, -1, true),
            Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, -1, true)
        ]
        
        cells = [Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0)]
        
        boundaries = Dict{String, Vector{Int}}("walls" => [1, 2, 3, 4])
        
        return UnstructuredMesh{Float64,3}(
            nodes, faces, cells, boundaries,
            [Int[]], [(0, -1), (0, -1), (0, -1), (0, -1)],
            (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 0.0))
        )
    end
    
    @testset "FVM Module Tests" begin
        mesh = create_test_mesh()
        
        # Test scalar field creation
        T_data = [300.0]  # Temperature
        T_bcs = Dict{String, AbstractBoundaryCondition}(
            "walls" => DirichletBC((x, y, z, t) -> 350.0)
        )
        T = Field{Float64,3,typeof(mesh)}(:temperature, mesh, T_data, T_bcs, nothing, nothing)
        
        @testset "Laplacian Operator" begin
            # Test laplacian with constant coefficient
            gamma = 0.1
            fv_matrix = fvm.laplacian(gamma, T)
            
            @test typeof(fv_matrix) == fvm.FvMatrix{Float64}
            @test size(fv_matrix.A) == (1, 1)
            @test length(fv_matrix.b) == 1
            
            # Check that matrix is symmetric for pure laplacian
            @test issymmetric(fv_matrix.A)
        end
        
        @testset "Time Derivative" begin
            T_old = Field{Float64,3,typeof(mesh)}(:temperature_old, mesh, [295.0], T_bcs, nothing, nothing)
            dt = 0.1
            
            fv_matrix = fvm.ddt(T, T_old, dt)
            
            @test typeof(fv_matrix) == fvm.FvMatrix{Float64}
            @test fv_matrix.A[1, 1] ≈ 1.0 / dt
            @test fv_matrix.b[1] ≈ 295.0 / dt
        end
        
        @testset "Convection Operator" begin
            # Create velocity field
            U_data = [SVector(1.0, 0.0, 0.0)]
            U = Field{SVector{3,Float64},3,typeof(mesh)}(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}(), nothing, nothing)
            
            conv_matrix = fvm.convection(U, T)
            @test typeof(conv_matrix) == fvm.FvMatrix{Float64}
        end
        
        @testset "Gradient Operator" begin
            grad_matrix = fvm.grad(T)
            @test typeof(grad_matrix) == fvm.FvMatrix{Float64}
        end
    end
    
    @testset "FVC Module Tests" begin
        mesh = create_test_mesh()
        
        # Test scalar field
        phi_data = [100.0]
        phi = Field{Float64,3,typeof(mesh)}(:phi, mesh, phi_data, Dict{String, AbstractBoundaryCondition}(), nothing, nothing)
        
        @testset "Gradient Operation" begin
            grad_phi = fvc.grad(phi, GaussGradient())
            @test grad_phi isa AbstractField
            @test eltype(grad_phi.data) <: SVector{3,Float64}
            @test length(grad_phi.data) == 1
        end
        
        @testset "Divergence Operation" begin
            U_data = [SVector(1.0, 1.0, 0.0)]
            U = Field{SVector{3,Float64},3,typeof(mesh)}(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}(), nothing, nothing)
            
            div_U = fvc.div(U, CentralDifferencing())
            @test typeof(div_U) == ScalarField{Float64,3,typeof(mesh)}
            @test length(div_U.data) == 1
        end
        
        @testset "Interpolation Operation" begin
            face_values = fvc.interpolate(phi, LinearInterpolation())
            @test typeof(face_values) <: AbstractVector
            @test length(face_values) == length(mesh.faces)
        end
    end
    
    @testset "Scheme Tests" begin
        @test typeof(GaussGradient()) == GaussGradient
        @test typeof(CentralDifferencing()) == CentralDifferencing
        @test typeof(UpwindInterpolation()) == UpwindInterpolation
        @test typeof(LinearInterpolation()) == LinearInterpolation
    end
    
    @testset "Matrix Assembly" begin
        mesh = create_test_mesh()
        T_data = [300.0]
        T_bcs = Dict{String, AbstractBoundaryCondition}(
            "walls" => DirichletBC((x, y, z, t) -> 350.0)
        )
        T = Field{Float64,3,typeof(mesh)}(:temperature, mesh, T_data, T_bcs, nothing, nothing)
        
        # Test complete equation assembly: ∂T/∂t + ∇·(UT) = ∇·(α∇T)
        U_data = [SVector(1.0, 0.0, 0.0)]
        U = Field{SVector{3,Float64},3,typeof(mesh)}(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}(), nothing, nothing)
        alpha = 0.1
        dt = 0.01
        
        T_old = Field{Float64,3,typeof(mesh)}(:T_old, mesh, [295.0], T_bcs, nothing, nothing)
        alpha_field = Field{Float64,3,typeof(mesh)}(:alpha, mesh, [alpha], Dict{String, AbstractBoundaryCondition}(), nothing, nothing)
        
        # Assemble terms
        ddt_term = fvm.ddt(T, T_old, dt)
        conv_term = fvm.convection(U, T)
        diff_term = fvm.laplacian(alpha_field, T)
        
        # Combined matrix: ∂T/∂t + ∇·(UT) - ∇·(α∇T) = 0
        A_total = ddt_term.A + conv_term.A - diff_term.A
        b_total = ddt_term.b + conv_term.b - diff_term.b
        
        @test size(A_total) == (1, 1)
        @test length(b_total) == 1
        @test A_total[1, 1] > 0  # Should be positive definite for stable schemes
    end
end