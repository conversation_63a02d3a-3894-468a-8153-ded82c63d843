#!/usr/bin/env julia
# CFD.jl Installation Test Script
# Run this to verify CFD.jl is working correctly

println("=== CFD.jl Installation Test ===\n")

# Test 1: Basic loading
println("1. Testing CFD.jl loading...")
try
    using CFD
    using CFD.UnicodeDSL: @solver, @physics, @algorithm, @bc, solve
    println("✅ CFD.jl loaded successfully")
catch e
    println("❌ Failed to load CFD.jl: $e")
    exit(1)
end

# Test 2: Solver definition
println("\n2. Testing @solver macro...")
try
    @solver TestSolver begin
        @physics IncompressibleFlow
        @algorithm PISO(correctors=2)
    end
    println("✅ @solver macro works")
catch e
    println("❌ @solver macro failed: $e")
end

# Test 3: Boundary conditions
println("\n3. Testing @bc macro...")
try
    @bc wall = 𝐮 → (0, 0, 0)
    @bc inlet = 𝐮 → (1, 0, 0)
    println("✅ @bc macro works")
catch e
    println("❌ @bc macro failed: $e")
end

# Test 4: Solver definition
println("\n4. Testing @solver macro...")
try
    @solver TestSolver begin
        @physics TestPhysics
        @algorithm PISO(correctors=2)
    end
    println("✅ @solver macro works")
catch e
    println("❌ @solver macro failed: $e")
end

# Test 5: Solve function
println("\n5. Testing solve() function...")
try
    result = solve("test.foam", TestSolver, time=1.0)
    println("✅ solve() function works")
    println("   Result: $result")
catch e
    println("❌ solve() function failed: $e")
end

# Test 6: All examples from README
println("\n6. Testing README examples...")
try
    # Lid-driven cavity
    @solver LidDrivenCavity begin
        @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮)
        @equation continuity (∇⋅𝐮 = 0)
        @algorithm PISO(correctors=2)
    end

    @bc cavity_wall = 𝐮 → (0, 0, 0)
    @bc cavity_lid = 𝐮 → (1, 0, 0)

    solve("cavity.foam", LidDrivenCavity, time=10.0, dt=0.001)
    
    # Heat transfer
    @physics HeatTransfer begin
        @equation energy (∂T/∂t + ∇⋅(𝐮T) = α∇²T + Q̇)
        @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮)
        @equation continuity (∇⋅𝐮 = 0)
    end

    @solver ThermalFlow begin
        @physics HeatTransfer
        @algorithm PIMPLE(outer=3)
    end

    solve("heat_exchanger.foam", ThermalFlow, time=60.0)
    
    println("✅ All README examples work")
catch e
    println("❌ README examples failed: $e")
end

println("\n=== Installation Test Complete ===")
println("🎉 CFD.jl is ready to use!")
println("\nYou can now:")
println("- Define physics with @physics macro")
println("- Set boundary conditions with @bc macro") 
println("- Create solvers with @solver macro")
println("- Run simulations with solve() function")
println("\nFor more examples, see examples/working_examples.jl")