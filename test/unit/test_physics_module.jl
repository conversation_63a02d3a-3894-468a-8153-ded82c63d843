# Test suite for Physics module  
using Test
using CFD
using CFD.CFDCore
using CFD.Physics
using StaticArrays
using LinearAlgebra

# Import the fvm module for FvMatrix type
const fvm = CFD.Numerics.fvm

@testset "Physics Tests" begin
    
    # Create test mesh
    function create_test_mesh()
        nodes = [
            Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
            Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
            Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
            Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false)
        ]
        
        faces = [
            Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, -1, true),
            Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, -1, true),
            Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, <PERSON>ector(0.0, 1.0, 0.0), 1, -1, true),
            Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, -1, true)
        ]
        
        cells = [Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0)]
        boundaries = Dict{String, Vector{Int}}("walls" => [1, 2, 3, 4])
        
        return UnstructuredMesh{Float64,3}(
            nodes, faces, cells, boundaries,
            [Int[]], [(1, 0), (1, 0), (1, 0), (1, 0)],
            (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 0.0))
        )
    end
    
    @testset "Flow Models" begin
        @testset "Incompressible Model" begin
            model = Incompressible(1.0, 0.01)  # ρ=1, ν=0.01
            @test model.ρ == 1.0
            @test model.ν == 0.01
        end
        
        @testset "Compressible Model" begin
            model = Compressible(1.4, 287.0, 0.72)  # Air properties: γ, R, Pr
            @test model.γ == 1.4
            @test model.R == 287.0
            @test model.Pr == 0.72
        end
    end
    
    @testset "Turbulence Models" begin
        @testset "k-ε Model" begin
            ke_model = kEpsilon()
            @test ke_model.Cμ ≈ 0.09
            @test ke_model.C1ε ≈ 1.44
            @test ke_model.C2ε ≈ 1.92
            @test ke_model.σk ≈ 1.0
            @test ke_model.σε ≈ 1.3
        end
        
        @testset "k-ω SST Model" begin
            komega_model = kOmegaSST()
            @test typeof(komega_model) == kOmegaSST
        end
        
        @testset "Turbulent Viscosity Calculation" begin
            mesh = create_test_mesh()
            ke_model = kEpsilon()
            
            # Create k and ε fields
            k_data = [1.0]  # m²/s²
            ε_data = [0.1]  # m²/s³
            k = ScalarField(:k, mesh, k_data, Dict{String, AbstractBoundaryCondition}())
            ε = ScalarField(:epsilon, mesh, ε_data, Dict{String, AbstractBoundaryCondition}())
            
            νt = turbulent_viscosity(ke_model, k, ε)
            expected_νt = ke_model.Cμ * k_data[1]^2 / ε_data[1]
            @test νt[1] ≈ expected_νt
        end
    end
    
    @testset "Strain Rate and Production" begin
        mesh = create_test_mesh()
        
        # Create velocity field with simple shear
        U_data = [SVector(1.0, 0.5, 0.0)]  # Linear shear profile
        U = VectorField(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}())
        
        strain_mag = strain_rate_magnitude(U)
        @test length(strain_mag) == 1
        @test strain_mag[1] >= 0.0  # Strain rate magnitude should be non-negative
        
        # Test production term
        ke_model = kEpsilon()
        k_data = [1.0]
        ε_data = [0.1]
        k = ScalarField(:k, mesh, k_data, Dict{String, AbstractBoundaryCondition}())
        ε = ScalarField(:epsilon, mesh, ε_data, Dict{String, AbstractBoundaryCondition}())
        νt = [0.9]  # Turbulent viscosity
        
        Pk = production_term(ke_model, U, k, ε, νt)
        @test length(Pk) == 1
        @test Pk[1] >= 0.0  # Production should be non-negative
    end
    
    @testset "Turbulence Source Terms" begin
        mesh = create_test_mesh()
        ke_model = kEpsilon()
        
        U_data = [SVector(1.0, 0.0, 0.0)]
        k_data = [1.0]
        ε_data = [0.1]
        νt = [0.9]
        
        U = VectorField(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}())
        k = ScalarField(:k, mesh, k_data, Dict{String, AbstractBoundaryCondition}())
        ε = ScalarField(:epsilon, mesh, ε_data, Dict{String, AbstractBoundaryCondition}())
        
        Sk, Sε = source_terms(ke_model, U, k, ε, νt)
        
        @test length(Sk) == 1
        @test length(Sε) == 1
        # Source terms can be positive or negative depending on production vs dissipation
    end
    
    @testset "Heat Transfer" begin
        @testset "Thermal Model" begin
            thermal = ThermalModel()  # Default values
            @test thermal.Pr ≈ 0.71
            @test thermal.Prt ≈ 0.9
            @test thermal.β == 0.0
            @test thermal.T_ref ≈ 293.15
        end
        
        @testset "Buoyancy Source" begin
            mesh = create_test_mesh()
            thermal = ThermalModel(β=3e-3)  # Thermal expansion coefficient for air
            
            T_data = [300.0]  # Temperature above reference
            T = ScalarField(:temperature, mesh, T_data, Dict{String, AbstractBoundaryCondition}())
            g = SVector(0.0, -9.81, 0.0)  # Gravity vector
            
            buoyancy = buoyancy_source(thermal, T, g)
            @test isa(buoyancy, VectorField)
            @test length(buoyancy.data) == 1
            
            # Check buoyancy direction (should be upward for hot fluid)
            dT = T_data[1] - thermal.T_ref
            expected_buoyancy_y = -thermal.β * dT * g[2]
            @test buoyancy.data[1][2] ≈ expected_buoyancy_y
        end
        
        @testset "Turbulent Thermal Diffusivity" begin
            thermal = ThermalModel()
            νt = [0.1]  # Turbulent viscosity
            
            αt = turbulent_thermal_diffusivity(thermal, νt)
            expected_αt = νt[1] / thermal.Prt
            @test αt[1] ≈ expected_αt
        end
    end
    
    @testset "Momentum Equation Assembly" begin
        mesh = create_test_mesh()
        model = Incompressible(1.0, 0.01)
        
        # Create fields with proper boundary conditions
        U_data = [SVector(1.0, 0.0, 0.0)]
        p_data = [0.0]
        
        # Add boundary conditions to prevent errors
        U_bcs = Dict{String, AbstractBoundaryCondition}("walls" => DirichletBC((x, y, z, t) -> SVector(0.0, 0.0, 0.0)))
        p_bcs = Dict{String, AbstractBoundaryCondition}("walls" => NeumannBC((x, y, z, t) -> 0.0))
        
        U = VectorField(:velocity, mesh, U_data, U_bcs)
        p = ScalarField(:pressure, mesh, p_data, p_bcs)
        
        dt = 0.01
        
        # Test momentum equation without turbulence
        U_matrices = momentum_equation(model, U, p, dt)
        @test length(U_matrices) == 3  # One matrix per velocity component
        
        for comp in 1:3
            @test typeof(U_matrices[comp]) == fvm.FvMatrix{Float64}
            @test size(U_matrices[comp].A) == (1, 1)
        end
        
        # Test momentum equation with turbulence
        νt = [0.05]  # Turbulent viscosity
        U_matrices_turb = momentum_equation(model, U, p, dt; νt=νt)
        
        # Effective viscosity should be higher with turbulence
        for comp in 1:3
            @test typeof(U_matrices_turb[comp]) == fvm.FvMatrix{Float64}
        end
    end
    
    @testset "Wall Distance Calculation" begin
        mesh = create_test_mesh()
        y_wall = wall_distance(mesh)
        
        @test length(y_wall) == length(mesh.cells)
        @test all(y_wall .> 0.0)  # All distances should be positive
        @test all(y_wall .< 1.0)  # Should be reasonable for unit square
    end
    
    @testset "Turbulence Equations" begin
        mesh = create_test_mesh()
        ke_model = kEpsilon()
        dt = 0.01
        
        # Create fields with proper boundary conditions
        U_data = [SVector(1.0, 0.0, 0.0)]
        k_data = [1.0]
        ε_data = [0.1]
        νt = [0.9]
        
        # Add boundary conditions for all fields
        U_bcs = Dict{String, AbstractBoundaryCondition}("walls" => DirichletBC((x, y, z, t) -> SVector(0.0, 0.0, 0.0)))
        k_bcs = Dict{String, AbstractBoundaryCondition}("walls" => DirichletBC((x, y, z, t) -> 1.0))
        ε_bcs = Dict{String, AbstractBoundaryCondition}("walls" => DirichletBC((x, y, z, t) -> 0.1))
        
        U = VectorField(:velocity, mesh, U_data, U_bcs)
        k = ScalarField(:k, mesh, k_data, k_bcs)
        ε = ScalarField(:epsilon, mesh, ε_data, ε_bcs)
        
        # Test k-equation
        k_matrix = k_equation(ke_model, U, k, ε, νt, dt)
        @test typeof(k_matrix) == CFD.Numerics.fvm.FvMatrix{Float64}
        @test size(k_matrix.A) == (1, 1)
        
        # Test ε-equation
        ε_matrix = epsilon_equation(ke_model, U, k, ε, νt, dt)
        @test typeof(ε_matrix) == CFD.Numerics.fvm.FvMatrix{Float64}
        @test size(ε_matrix.A) == (1, 1)
    end
end