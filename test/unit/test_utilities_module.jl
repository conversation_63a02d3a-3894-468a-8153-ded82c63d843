# Test suite for Utilities module
using Test
using CFD
using CFD.CFDCore
using CFD.Utilities
using StaticArrays

@testset "Utilities Tests" begin
    
    @testset "MeshReader Construction" begin
        openfoam_reader = MeshReader(:openfoam)
        @test openfoam_reader.format == :openfoam
        
        gmsh_reader = MeshReader(:gmsh)
        @test gmsh_reader.format == :gmsh
    end
    
    @testset "VTKWriter Construction" begin
        writer = VTKWriter("test_output")
        @test writer.basename == "test_output"
        @test writer.append == false
        
        writer_append = VTKWriter("test_output_append", true)
        @test writer_append.append == true
    end
    
    @testset "Domain Decomposition" begin
        decomp = DomainDecomposition(:metis, 4)
        @test decomp.method == :metis
        @test decomp.ndomains == 4
        
        decomp_simple = DomainDecomposition(:simple, 2)
        @test decomp_simple.method == :simple
        @test decomp_simple.ndomains == 2
    end
    
    @testset "Convergence Monitor" begin
        fields = [:U, :p, :k, :epsilon]
        tolerances = Dict(:U => 1e-5, :p => 1e-6, :k => 1e-7, :epsilon => 1e-7)
        
        monitor = ConvergenceMonitor(fields, tolerances)
        @test monitor.fields_to_monitor == fields
        @test monitor.tolerances == tolerances
        @test monitor.current_iteration == 0
        @test length(monitor.residuals_history) == length(fields)
        
        # Test convergence check
        residuals_converged = Dict(:U => 1e-6, :p => 1e-7, :k => 1e-8, :epsilon => 1e-8)
        @test check_convergence(monitor, residuals_converged) == true
        @test monitor.current_iteration == 1
        
        # Test non-convergence
        residuals_not_converged = Dict(:U => 1e-4, :p => 1e-5, :k => 1e-6, :epsilon => 1e-6)
        @test check_convergence(monitor, residuals_not_converged) == false
        @test monitor.current_iteration == 2
    end
    
    @testset "Performance Profiling" begin
        # Test timed_section macro
        result = @timed_section "test_computation" begin
            sum(i^2 for i in 1:1000)
        end
        
        expected_result = sum(i^2 for i in 1:1000)
        @test result == expected_result
    end
    
    @testset "Force Calculation Setup" begin
        # Create simple test mesh
        nodes = [
            Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
            Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
            Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
            Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false)
        ]
        
        faces = [
            Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, 0, true),
            Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, 0, true),
            Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, 0, true),
            Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, 0, true)
        ]
        
        cells = [Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0)]
        
        boundaries = Dict{String, Vector{Int}}(
            "wall" => [1, 2, 3, 4]
        )
        
        mesh = UnstructuredMesh{Float64,3}(
            nodes, faces, cells, boundaries,
            [Int[]], [(1, 0), (1, 0), (1, 0), (1, 0)],
            (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 0.0))
        )
        
        # Create pressure and velocity fields
        p_data = [1000.0]  # Pascal
        U_data = [SVector(10.0, 0.0, 0.0)]  # m/s
        
        p = ScalarField(:pressure, mesh, p_data, Dict{String, AbstractBoundaryCondition}())
        U = VectorField(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}())
        
        # Test force calculation
        ρ = 1.225  # kg/m³ (air density)
        ν = 1.5e-5  # m²/s (kinematic viscosity)
        reference_point = SVector(0.0, 0.0, 0.0)
        
        force, moment = calculate_forces(p, U, "wall", ρ, ν, reference_point)
        
        @test typeof(force) == SVector{3,Float64}
        @test typeof(moment) == SVector{3,Float64}
        @test length(force) == 3
        @test length(moment) == 3
    end
    
    @testset "Error Handling" begin
        # Test invalid mesh format
        invalid_reader = MeshReader(:invalid_format)
        @test_throws ErrorException read_mesh(invalid_reader, "dummy_path")
        
        # Test GMSH mesh reading (should throw error for unimplemented)
        gmsh_reader = MeshReader(:gmsh)
        @test_throws ErrorException read_mesh(gmsh_reader, "dummy.msh")
    end
    
    @testset "File I/O Helpers" begin
        # Test that functions exist and have correct signatures
        @test hasmethod(read_mesh, (MeshReader, String))
        @test hasmethod(write_solution, (VTKWriter, AbstractMesh, Dict{Symbol,<:Field}, Int, Float64))
        @test hasmethod(decompose_mesh, (AbstractMesh, DomainDecomposition))
    end
    
    @testset "Mesh Format Recognition" begin
        # Test different mesh format readers
        formats = [:openfoam, :gmsh, :cgns, :fluent]
        
        for format in formats
            reader = MeshReader(format)
            @test reader.format == format
            
            if format == :openfoam
                # Only OpenFOAM reading is implemented
                @test hasmethod(read_mesh, (typeof(reader), String))
            else
                # Others should throw errors
                @test_throws ErrorException read_mesh(reader, "dummy")
            end
        end
    end
end