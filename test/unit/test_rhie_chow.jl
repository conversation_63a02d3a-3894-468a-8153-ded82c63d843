# test/unit/test_rhie_chow.jl
"""
Comprehensive unit tests for Rhie-Chow interpolation implementation.

These tests verify:
1. Diagonal coefficient computation
2. Velocity interpolation accuracy  
3. Mass conservation properties
4. Boundary condition handling
5. Type stability and performance
"""

using Test
using LinearAlgebra
using StaticArrays
using SparseArrays

# Setup test environment
using Pkg
Pkg.activate(".")
include("../../src/CFD.jl")
using .CFD

# Import Rhie-Chow module
include("../../src/Solvers/rhie_chow.jl")
using .CFD.Solvers.RhieChowInterpolation

@testset "Rhie-Chow Interpolation Tests" begin
    
    @testset "Momentum Diagonal Coefficient Computation" begin
        println("Testing momentum diagonal coefficient computation...")
        
        # Create simple 2x2x2 test mesh
        mesh = CFD.Utilities.create_unit_cube_mesh(2, 2, 2)
        num_cells = length(mesh.cells)
        
        # Create mock momentum matrices (diagonal dominant)
        A_momentum = spdiagm(0 => fill(10.0, num_cells)) + 
                     sprand(num_cells, num_cells, 0.1) * 0.1
        
        # Ensure diagonal dominance
        for i in 1:num_cells
            A_momentum[i, i] = abs(A_momentum[i, i]) + sum(abs.(A_momentum[i, :]))
        end
        
        A_momentum_matrices = [A_momentum, A_momentum, A_momentum]  # x, y, z components
        
        # Test steady-state case (dt = 0)
        A_U_diag = Vector{Float64}(undef, num_cells)
        rho = 1000.0
        dt = 0.0
        
        compute_momentum_diagonal_coefficients!(A_U_diag, mesh, rho, dt, A_momentum_matrices)
        
        # Verify diagonal coefficients are positive
        @test all(A_U_diag .> 0)
        
        # Verify diagonal coefficients match matrix diagonal
        expected_diag = [A_momentum[i, i] for i in 1:num_cells]
        @test A_U_diag ≈ expected_diag atol=1e-12
        
        # Test transient case (dt > 0)
        dt = 0.001
        compute_momentum_diagonal_coefficients!(A_U_diag, mesh, rho, dt, A_momentum_matrices)
        
        # Verify temporal contribution is added
        expected_temporal = rho * [mesh.cells[i].volume for i in 1:num_cells] / dt
        expected_total = expected_diag + expected_temporal
        @test A_U_diag ≈ expected_total atol=1e-12
        
        println("  ✅ Diagonal coefficient computation tests passed")
    end
    
    @testset "Velocity Interpolation Accuracy" begin
        println("Testing Rhie-Chow velocity interpolation accuracy...")
        
        # Create test mesh and fields
        mesh = CFD.Utilities.create_unit_cube_mesh(3, 3, 3)
        num_cells = length(mesh.cells)
        num_faces = length(mesh.faces)
        
        # Create uniform velocity field U = (1, 0, 0)
        U_data = [SVector{3,Float64}(1.0, 0.0, 0.0) for _ in 1:num_cells]
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, bcs)
        
        # Create zero pressure field (no pressure gradients)
        p_data = zeros(num_cells)
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, bcs)
        
        # Create uniform diagonal coefficients
        A_U_diag = fill(1000.0, num_cells)
        
        # Perform Rhie-Chow interpolation
        face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
        rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # For uniform velocity and zero pressure gradient, face velocities should match cell velocities
        for face_idx in 1:num_faces
            face = mesh.faces[face_idx]
            if !face.boundary
                expected_velocity = SVector{3,Float64}(1.0, 0.0, 0.0)
                @test norm(face_velocities[face_idx] - expected_velocity) < 1e-12
            end
        end
        
        println("  ✅ Uniform field interpolation test passed")
        
        # Test with linear pressure field p = x
        p_data = [mesh.cells[i].center[1] for i in 1:num_cells]
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, bcs)
        
        # Rhie-Chow interpolation should correct for pressure gradients
        rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Velocities should be corrected by pressure gradient
        # For this case, we expect some correction but exact values depend on mesh geometry
        @test all(isfinite.(norm.(face_velocities)))
        @test all(norm.(face_velocities) .< 10.0)
        
        println("  ✅ Linear pressure field interpolation test passed")
    end
    
    @testset "Mass Conservation Validation" begin
        println("Testing mass conservation properties...")
        
        # Create test case with known divergence-free field
        mesh = CFD.Utilities.create_unit_cube_mesh(4, 4, 4)
        num_cells = length(mesh.cells)
        num_faces = length(mesh.faces)
        
        # Create divergence-free velocity field (solid body rotation around z-axis)
        omega = 1.0  # Angular velocity
        U_data = Vector{SVector{3,Float64}}(undef, num_cells)
        for i in 1:num_cells
            x, y, z = mesh.cells[i].center
            # Solid body rotation: u = -ω*y, v = ω*x, w = 0
            u = -omega * (y - 0.5)  # Center rotation around (0.5, 0.5)
            v = omega * (x - 0.5)
            w = 0.0
            U_data[i] = SVector{3,Float64}(u, v, w)
        end
        
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, bcs)
        
        # Zero pressure field
        p_data = zeros(num_cells)
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, bcs)
        
        # Uniform diagonal coefficients
        A_U_diag = fill(500.0, num_cells)
        
        # Perform Rhie-Chow interpolation
        face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
        rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Validate mass conservation
        validation_results = validate_rhie_chow_interpolation(face_velocities, U_field, mesh)
        
        @test validation_results[:mass_conservation_ok]
        @test validation_results[:no_invalid_values]
        @test validation_results[:velocity_bounds_ok]
        @test validation_results[:overall_valid]
        
        println("  ✅ Mass conservation: max imbalance = $(validation_results[:max_mass_imbalance])")
        println("  ✅ Mass conservation tests passed")
    end
    
    @testset "Boundary Condition Handling" begin
        println("Testing boundary condition handling...")
        
        mesh = CFD.Utilities.create_unit_cube_mesh(2, 2, 2)
        num_cells = length(mesh.cells)
        num_faces = length(mesh.faces)
        
        # Create velocity field with boundary conditions
        U_data = [SVector{3,Float64}(0.0, 0.0, 0.0) for _ in 1:num_cells]
        
        # Add inlet boundary condition
        inlet_bc = CFD.CFDCore.DirichletBC((x, y, z, t) -> [1.0, 0.0, 0.0])
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
            "inlet" => inlet_bc
        )
        U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, bcs)
        
        # Zero pressure field
        p_data = zeros(num_cells)
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, Dict{String, CFD.CFDCore.AbstractBoundaryCondition}())
        
        # Uniform diagonal coefficients
        A_U_diag = fill(100.0, num_cells)
        
        # Perform Rhie-Chow interpolation
        face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
        rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Check that inlet faces have correct boundary velocities
        inlet_faces = mesh.boundaries["inlet"]
        for face_idx in inlet_faces
            face_vel = face_velocities[face_idx]
            expected_vel = SVector{3,Float64}(1.0, 0.0, 0.0)
            @test norm(face_vel - expected_vel) < 1e-10
        end
        
        println("  ✅ Boundary condition tests passed")
    end
    
    @testset "Type Stability and Performance" begin
        println("Testing type stability and performance...")
        
        mesh = CFD.Utilities.create_unit_cube_mesh(3, 3, 3)
        num_cells = length(mesh.cells)
        num_faces = length(mesh.faces)
        
        # Setup fields
        U_data = [SVector{3,Float64}(1.0, 0.0, 0.0) for _ in 1:num_cells]
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, bcs)
        
        p_data = zeros(num_cells)
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, bcs)
        
        A_U_diag = fill(100.0, num_cells)
        face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
        
        # Test type stability
        @inferred rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Performance benchmark
        t_start = time()
        for _ in 1:100
            rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        end
        t_elapsed = time() - t_start
        
        println("  ✅ Type stability verified")
        println("  ✅ Performance: $(t_elapsed/100*1000) ms per interpolation ($(num_faces) faces)")
        
        # Verify no allocations in main loop (after compilation)
        allocs = @allocated rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        @test allocs < 1000
        
        println("  ✅ Memory allocations: $allocs bytes")
    end
    
    @testset "Error Handling and Edge Cases" begin
        println("Testing error handling and edge cases...")
        
        mesh = CFD.Utilities.create_unit_cube_mesh(2, 2, 2)
        num_cells = length(mesh.cells)
        num_faces = length(mesh.faces)
        
        # Setup valid fields
        U_data = [SVector{3,Float64}(1.0, 0.0, 0.0) for _ in 1:num_cells]
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        U_field = CFD.CFDCore.VectorField(:U, mesh, U_data, bcs)
        
        p_data = zeros(num_cells)
        p_field = CFD.CFDCore.ScalarField(:p, mesh, p_data, bcs)
        
        A_U_diag = fill(100.0, num_cells)
        face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
        
        # Test with wrong size arrays
        wrong_size_velocities = Vector{SVector{3,Float64}}(undef, num_faces - 1)
        @test_throws AssertionError rhie_chow_velocity_interpolation!(
            wrong_size_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Test with zero diagonal coefficients
        A_U_diag_zero = zeros(num_cells)
        A_momentum_zero = spzeros(num_cells, num_cells)
        A_momentum_matrices_zero = [A_momentum_zero, A_momentum_zero, A_momentum_zero]
        
        # Should handle gracefully with warnings
        @test_logs (:warn,) compute_momentum_diagonal_coefficients!(
            A_U_diag_zero, mesh, 1000.0, 0.001, A_momentum_matrices_zero)
        
        # Should produce positive coefficients even with zero input
        @test all(A_U_diag_zero .> 0)
        
        println("  ✅ Error handling tests passed")
    end
    
    println("\\n🎉 All Rhie-Chow interpolation tests passed!")
    println("📊 Test Summary:")
    println("  ✅ Diagonal coefficient computation")
    println("  ✅ Velocity interpolation accuracy") 
    println("  ✅ Mass conservation validation")
    println("  ✅ Boundary condition handling")
    println("  ✅ Type stability and performance")
    println("  ✅ Error handling and edge cases")
end