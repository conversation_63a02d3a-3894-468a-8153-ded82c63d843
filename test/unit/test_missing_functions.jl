#!/usr/bin/env julia
using CFD
using Test

# Test specific missing functions
try
    println("Testing turbulent_viscosity...")
    mesh = CFD.CFDCore.UnstructuredMesh([], [], [], Dict(), [], [], (CFD.CFDCore.SVector(0,0,0), CFD.CFDCore.SVector(1,1,1)))
    k = CFD.CFDCore.ScalarField(:k, mesh, [0.1], Dict())
    ε = CFD.CFDCore.ScalarField(:eps, mesh, [0.01], Dict())
    model = CFD.Physics.kEpsilon()
    νt = CFD.Physics.turbulent_viscosity(model, k, ε)
    println("✓ turbulent_viscosity works: ", νt)
catch e
    println("✗ turbulent_viscosity failed: ", e)
end

try
    println("Testing extract_scalar_component...")
    mesh = CFD.CFDCore.UnstructuredMesh([], [], [], Dict(), [], [], (CFD.CFDCore.SVector(0,0,0), CFD.CFDCore.SVector(1,1,1)))
    U_data = [CFD.CFDCore.SVector(1.0, 2.0, 3.0)]
    U = CFD.CFDCore.VectorField(:U, mesh, U_data, Dict())
    U_x = CFD.CFDCore.extract_scalar_component(U, 1)
    println("✓ extract_scalar_component works: ", U_x.data[1])
catch e
    println("✗ extract_scalar_component failed: ", e)
end

try
    println("Testing energy_equation...")
    mesh = CFD.CFDCore.UnstructuredMesh([], [], [], Dict(), [], [], (CFD.CFDCore.SVector(0,0,0), CFD.CFDCore.SVector(1,1,1)))
    thermal = CFD.Physics.ThermalModel()
    T = CFD.CFDCore.ScalarField(:T, mesh, [300.0], Dict())
    U = CFD.CFDCore.VectorField(:U, mesh, [CFD.CFDCore.SVector(1.0, 0.0, 0.0)], Dict()) 
    α = CFD.CFDCore.ScalarField(:alpha, mesh, [1e-5], Dict())
    αt = CFD.CFDCore.ScalarField(:alpha_t, mesh, [1e-6], Dict())
    A, b = CFD.Physics.energy_equation(thermal, T, U, α, αt, 0.01)
    println("✓ energy_equation works")
catch e
    println("✗ energy_equation failed: ", e)
end