# Test suite for CFDCore module
using Test
using CFD
using CFD.CFDCore
using StaticArrays
using LinearAlgebra

@testset "CFDCore Tests" begin
    
    @testset "Node Construction" begin
        node = Node{Float64,3}(1, SVector(1.0, 2.0, 3.0), false)
        @test node.id == 1
        @test node.coords == SVector(1.0, 2.0, 3.0)
        @test node.boundary == false
    end
    
    @testset "Face Construction" begin
        face = Face{Float64,3}(
            1,                              # id
            [1, 2, 3],                     # nodes
            SVector(0.5, 0.5, 0.0),        # center
            1.0,                           # area
            SVector(0.0, 0.0, 1.0),        # normal
            1,                             # owner
            2,                             # neighbor
            false                          # boundary
        )
        
        @test face.id == 1
        @test face.nodes == [1, 2, 3]
        @test face.center == SVector(0.5, 0.5, 0.0)
        @test face.area == 1.0
        @test face.normal == SVector(0.0, 0.0, 1.0)
        @test face.owner == 1
        @test face.neighbor == 2
        @test face.boundary == false
    end
    
    @testset "Cell Construction" begin
        cell = Cell{Float64,3}(
            1,                              # id
            [1, 2, 3, 4],                  # nodes
            [1, 2, 3, 4],                  # faces
            SVector(0.5, 0.5, 0.5),        # center
            1.0                            # volume
        )
        
        @test cell.id == 1
        @test cell.nodes == [1, 2, 3, 4]
        @test cell.faces == [1, 2, 3, 4]
        @test cell.center == SVector(0.5, 0.5, 0.5)
        @test cell.volume == 1.0
    end
    
    @testset "Boundary Conditions" begin
        # Dirichlet BC
        dirichlet_bc = DirichletBC((x, y, z, t) -> 1.0)
        @test dirichlet_bc.value(0.0, 0.0, 0.0, 0.0) == 1.0
        
        # Neumann BC
        neumann_bc = NeumannBC((x, y, z, t) -> 0.5)
        @test neumann_bc.gradient(0.0, 0.0, 0.0, 0.0) == 0.5
        
        # Robin BC
        robin_bc = RobinBC((x, y, z, t) -> 2.0, (x, y, z, t) -> 0.1, (x, y, z, t) -> 3.0)
        @test robin_bc.α(0.0, 0.0, 0.0, 0.0) == 2.0
        @test robin_bc.β(0.0, 0.0, 0.0, 0.0) == 0.1
        @test robin_bc.γ(0.0, 0.0, 0.0, 0.0) == 3.0
    end
    
    @testset "Simple Mesh Construction" begin
        # Create a simple 2-cell mesh
        nodes = [
            Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
            Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
            Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
            Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false),
            Node{Float64,3}(5, SVector(2.0, 0.0, 0.0), false),
            Node{Float64,3}(6, SVector(2.0, 1.0, 0.0), false)
        ]
        
        faces = [
            Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, 0, true),
            Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, 2, false),
            Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, 0, true),
            Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, 0, true),
            Face{Float64,3}(5, [5, 6], SVector(2.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 2, 0, true)
        ]
        
        cells = [
            Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0),
            Cell{Float64,3}(2, [2, 3, 5, 6], [2, 5], SVector(1.5, 0.5, 0.0), 1.0)
        ]
        
        boundaries = Dict{String, Vector{Int}}(
            "inlet" => [1],
            "outlet" => [5],
            "walls" => [3, 4]
        )
        
        mesh = UnstructuredMesh{Float64,3}(
            nodes, faces, cells, boundaries,
            [Int[], Int[]], [(1, 0), (1, 2), (1, 0), (1, 0), (2, 0)],
            (SVector(0.0, 0.0, 0.0), SVector(2.0, 1.0, 0.0))
        )
        
        @test length(mesh.nodes) == 6
        @test length(mesh.faces) == 5
        @test length(mesh.cells) == 2
        @test haskey(mesh.boundaries, "inlet")
        @test mesh.boundaries["inlet"] == [1]
    end
    
    @testset "Basic Type Checks" begin
        # Test that types are properly exported and accessible
        @test isa(AbstractMesh, Type)
        @test isa(AbstractField, Type)
        @test isa(AbstractBoundaryCondition, Type)
        @test isa(DirichletBC, Type)
        @test isa(NeumannBC, Type)
        @test isa(RobinBC, Type)
    end
end