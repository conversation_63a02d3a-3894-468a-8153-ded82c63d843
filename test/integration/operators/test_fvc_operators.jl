#!/usr/bin/env julia

# Simple test script for FVC operators
using Pkg
Pkg.activate(".")
using CFD
using LinearAlgebra  # For norm function
using StaticArrays   # For SVector

# Import the fvc module explicitly
const fvc = CFD.Numerics.fvc

println("=== Testing FVC Operators ===")

# Create mesh
println("Creating 3×3×3 mesh...")
mesh = CFD.Utilities.create_unit_cube_mesh(3, 3, 3)
println("✓ Mesh created: $(length(mesh.cells)) cells, $(length(mesh.faces)) faces")

# Helper function to create fields like ValidationWorkflow does
function create_scalar_field(name::Symbol, mesh, data::Vector{Float64})
    bcs = Dict{String, Any}()
    return CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(name, mesh, data, bcs, nothing, nothing)
end

function create_vector_field(name::Symbol, mesh, data::Vector{SVector{3,Float64}})
    bcs = Dict{String, Any}()
    return CFD.CFDCore.Field{SVector{3,Float64}, 3, typeof(mesh)}(name, mesh, data, bcs, nothing, nothing)
end

# Test scalar field creation using ValidationWorkflow approach
println("\nTesting scalar field creation...")
data = [1.0 + i*0.1 for i in 1:length(mesh.cells)]

# Use direct constructor that ValidationWorkflow uses with proper boundary conditions
bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}(
    "walls" => CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0),
    "inlet" => CFD.CFDCore.DirichletBC((x, y, z, t) -> 1.0), 
    "outlet" => CFD.CFDCore.NeumannBC((x, y, z, t) -> 0.0)
)
phi = CFD.CFDCore.Field{Float64, 3, typeof(mesh)}(:test, mesh, data, bcs, nothing, nothing)
println("✓ Scalar field created with $(length(phi.data)) values")

# Test gradient operator
println("\nTesting FVC gradient operator...")
try
    grad_phi = CFD.Numerics.fvc.grad(phi)
    max_grad = maximum(norm.(grad_phi.data))
    println("✓ FVC gradient works! Max gradient magnitude: $max_grad")
catch e
    println("✗ FVC gradient failed: $e")
end

# Test vector field for divergence
println("\nTesting vector field creation...")
vector_data = [SVector(c.center[2], -c.center[1], 0.0) for c in mesh.cells]
U = CFD.CFDCore.Field{SVector{3,Float64}, 3, typeof(mesh)}(:velocity, mesh, vector_data, bcs, nothing, nothing)
println("✓ Vector field created with $(length(U.data)) vectors")

# Test divergence operator
println("\nTesting FVC divergence operator...")
try
    div_U = CFD.Numerics.fvc.div(U)
    max_div = maximum(abs.(div_U.data))
    println("✓ FVC divergence works! Max divergence: $max_div")
    
    # This should be ~0 for rotational field (y, -x, 0)
    if max_div < 1e-10
        println("✓ Divergence-free field correctly computed")
    else
        println("⚠ Divergence higher than expected for rotational field")
    end
catch e
    println("✗ FVC divergence failed: $e")
end

# Test laplacian operator
println("\nTesting FVC laplacian operator...")
try
    lap_phi = CFD.Numerics.fvc.laplacian(1.0, phi)
    max_lap = maximum(abs.(lap_phi.data))
    println("✓ FVC laplacian works! Max laplacian: $max_lap")
catch e
    println("✗ FVC laplacian failed: $e")
end

println("\n=== FVC Operators Test Complete ===")