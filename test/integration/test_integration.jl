# Integration tests for CFD.jl - testing complete workflows
using Test
using CFD
using CFD.CFDCore
using CFD.Physics
using CFD.Solvers
using CFD.Utilities
using StaticArrays
using LinearAlgebra

# Import the fvm module for FvMatrix type
const fvm = CFD.Numerics.fvm

@testset "Integration Tests" begin
    
    @testset "Complete RANS Workflow" begin
        # Create a simple channel mesh for testing
        function create_channel_mesh()
            # Simple 2D channel with 2 cells
            nodes = [
                Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
                Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
                Node{Float64,3}(3, SVector(2.0, 0.0, 0.0), false),
                Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false),
                Node{Float64,3}(5, SVector(1.0, 1.0, 0.0), false),
                Node{Float64,3}(6, SVector(2.0, 1.0, 0.0), false)
            ]
            
            faces = [
                # Bottom boundary
                Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, -1, true),
                Face{Float64,3}(2, [2, 3], SVector(1.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 2, -1, true),
                # Internal face
                Face{Float64,3}(3, [2, 5], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, 2, false),
                # Top boundary
                Face{Float64,3}(4, [4, 5], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, -1, true),
                Face{Float64,3}(5, [5, 6], SVector(1.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 2, -1, true),
                # Left inlet
                Face{Float64,3}(6, [1, 4], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, -1, true),
                # Right outlet
                Face{Float64,3}(7, [3, 6], SVector(2.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 2, -1, true)
            ]
            
            cells = [
                Cell{Float64,3}(1, [1, 2, 4, 5], [1, 3, 4, 6], SVector(0.5, 0.5, 0.0), 1.0),
                Cell{Float64,3}(2, [2, 3, 5, 6], [2, 3, 5, 7], SVector(1.5, 0.5, 0.0), 1.0)
            ]
            
            boundaries = Dict{String, Vector{Int}}(
                "inlet" => [6],
                "outlet" => [7],
                "walls" => [1, 2, 4, 5]
            )
            
            return UnstructuredMesh{Float64,3}(
                nodes, faces, cells, boundaries,
                [[2], [1]], [(1, 0), (2, 0), (1, 2), (1, 0), (2, 0), (1, 0), (2, 0)],
                (SVector(0.0, 0.0, 0.0), SVector(2.0, 1.0, 0.0))
            )
        end
        
        # Setup test case
        mesh = create_channel_mesh()
        num_cells = length(mesh.cells)
        
        # Create flow model
        model = Incompressible(1.0, 0.01)  # ρ=1, ν=0.01
        
        # Create turbulence model
        ke_model = kEpsilon()
        
        # Initialize fields
        U_data = [SVector(1.0, 0.0, 0.0) for _ in 1:num_cells]
        p_data = zeros(Float64, num_cells)
        k_data = fill(0.1, num_cells)  # Initial turbulent kinetic energy
        ε_data = fill(0.01, num_cells)  # Initial dissipation rate
        
        # Boundary conditions
        inlet_bc = DirichletBC((x, y, z, t) -> SVector(2.0, 0.0, 0.0))
        wall_bc = DirichletBC((x, y, z, t) -> SVector(0.0, 0.0, 0.0))
        
        U_bcs = Dict{String, AbstractBoundaryCondition}(
            "inlet" => inlet_bc,
            "outlet" => NeumannBC((x, y, z, t) -> SVector(0.0, 0.0, 0.0)),
            "walls" => wall_bc
        )
        
        p_bcs = Dict{String, AbstractBoundaryCondition}(
            "outlet" => DirichletBC((x, y, z, t) -> 0.0),
            "walls" => NeumannBC((x, y, z, t) -> 0.0)
        )
        
        k_bcs = Dict{String, AbstractBoundaryCondition}(
            "inlet" => DirichletBC((x, y, z, t) -> 0.15),
            "outlet" => NeumannBC((x, y, z, t) -> 0.0),
            "walls" => DirichletBC((x, y, z, t) -> 1e-12)
        )
        
        ε_bcs = Dict{String, AbstractBoundaryCondition}(
            "inlet" => DirichletBC((x, y, z, t) -> 0.02),
            "outlet" => NeumannBC((x, y, z, t) -> 0.0),
            "walls" => DirichletBC((x, y, z, t) -> 1e-12)
        )
        
        # Create fields
        U = VectorField(:velocity, mesh, U_data, U_bcs)
        p = ScalarField(:pressure, mesh, p_data, p_bcs)
        k = ScalarField(:k, mesh, k_data, k_bcs)
        ε = ScalarField(:epsilon, mesh, ε_data, ε_bcs)
        
        # Test turbulent viscosity calculation
        νt = turbulent_viscosity(ke_model, k, ε)
        @test length(νt) == num_cells
        @test all(νt .>= 0.0)  # Turbulent viscosity should be non-negative
        
        # Test momentum equation assembly with turbulence
        dt = 0.01
        U_matrices = momentum_equation(model, U, p, dt; νt=νt)
        @test length(U_matrices) == 3
        
        # Test turbulence equations
        k_matrix = k_equation(ke_model, U, k, ε, νt, dt)
        ε_matrix = epsilon_equation(ke_model, U, k, ε, νt, dt)
        
        @test typeof(k_matrix) == fvm.FvMatrix{Float64}
        @test typeof(ε_matrix) == fvm.FvMatrix{Float64}
        @test size(k_matrix.A) == (num_cells, num_cells)
        @test size(ε_matrix.A) == (num_cells, num_cells)
        
        # Test that matrices are well-conditioned
        @test cond(Array(k_matrix.A)) < 1e12  # Not too ill-conditioned
        @test cond(Array(ε_matrix.A)) < 1e12
        
        println("RANS workflow test completed successfully")
    end
    
    @testset "Heat Transfer Integration" begin
        # Simple heat transfer test case
        mesh = let
            nodes = [Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false)]
            faces = [Face{Float64,3}(1, [1], SVector(0.0, 0.0, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, -1, true)]
            cells = [Cell{Float64,3}(1, [1], [1], SVector(0.0, 0.0, 0.0), 1.0)]
            boundaries = Dict{String, Vector{Int}}("wall" => [1])
            
            UnstructuredMesh{Float64,3}(
                nodes, faces, cells, boundaries,
                [Int[]], [(1, 0)],
                (SVector(0.0, 0.0, 0.0), SVector(0.0, 0.0, 0.0))
            )
        end
        
        # Create thermal model and fields
        thermal = ThermalModel(Pr=0.71, β=3e-3, T_ref=300.0)
        
        T_data = [350.0]  # Hot temperature
        U_data = [SVector(1.0, 0.0, 0.0)]
        α_data = [2.2e-5]  # Thermal diffusivity of air
        
        T_bcs = Dict{String, AbstractBoundaryCondition}("wall" => DirichletBC((x, y, z, t) -> 350.0))
        T = ScalarField(:temperature, mesh, T_data, T_bcs)
        U = VectorField(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}())
        α = ScalarField(:alpha, mesh, α_data, Dict{String, AbstractBoundaryCondition}())
        
        # Test turbulent thermal diffusivity
        νt = [0.001]  # Turbulent viscosity
        αt_data = turbulent_thermal_diffusivity(thermal, νt)
        αt = ScalarField(:alpha_t, mesh, αt_data, Dict{String, AbstractBoundaryCondition}())
        
        @test αt.data[1] ≈ νt[1] / thermal.Prt
        
        # Test buoyancy calculation
        g = SVector(0.0, -9.81, 0.0)
        buoyancy = buoyancy_source(thermal, T, g)
        
        dT = T_data[1] - thermal.T_ref
        expected_buoyancy_y = -thermal.β * dT * g[2]
        @test buoyancy.data[1][2] ≈ expected_buoyancy_y
        
        # Test energy equation assembly
        dt = 0.01
        A_energy, b_energy = energy_equation(thermal, T, U, α, αt, dt)
        
        @test size(A_energy) == (1, 1)
        @test length(b_energy) == 1
        
        println("Heat transfer integration test completed successfully")
    end
    
    @testset "Convergence Monitoring" begin
        # Test convergence monitoring in iterative solver
        fields = [:U, :p]
        tolerances = Dict(:U => 1e-4, :p => 1e-5)
        monitor = ConvergenceMonitor(fields, tolerances)
        
        # Simulate convergence over iterations
        iteration_residuals = [
            Dict(:U => 1e-2, :p => 1e-3),  # Initial high residuals
            Dict(:U => 1e-3, :p => 1e-4),  # Improving
            Dict(:U => 1e-4, :p => 1e-5),  # Just converged
            Dict(:U => 1e-5, :p => 1e-6)   # Well converged
        ]
        
        converged_flags = []
        for residuals in iteration_residuals
            converged = check_convergence(monitor, residuals)
            push!(converged_flags, converged)
        end
        
        @test converged_flags == [false, false, true, true]
        @test monitor.current_iteration == 4
        @test length(monitor.residuals_history[:U]) == 4
        @test length(monitor.residuals_history[:p]) == 4
        
        println("Convergence monitoring test completed successfully")
    end
    
    @testset "Field Operations Integration" begin
        # Test integration of various field operations
        mesh = let
            nodes = [
                Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
                Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
                Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
                Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false)
            ]
            
            faces = [
                Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, -1, true),
                Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, -1, true),
                Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, -1, true),
                Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, -1, true)
            ]
            
            cells = [Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0)]
            boundaries = Dict{String, Vector{Int}}("walls" => [1, 2, 3, 4])
            
            UnstructuredMesh{Float64,3}(
                nodes, faces, cells, boundaries,
                [Int[]], [(1, 0), (1, 0), (1, 0), (1, 0)],
                (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 0.0))
            )
        end
        
        # Create fields
        T_data = [300.0]
        U_data = [SVector(1.0, 1.0, 0.0)]
        
        T_bcs = Dict{String, AbstractBoundaryCondition}("walls" => DirichletBC((x, y, z, t) -> 300.0))
        T = ScalarField(:temperature, mesh, T_data, T_bcs)
        U = VectorField(:velocity, mesh, U_data, Dict{String, AbstractBoundaryCondition}())
        
        # Test field extraction
        U_x = extract_scalar_component(U, 1; component_name_suffix="_x")
        @test U_x.data[1] == 1.0
        @test U_x.name == :velocity_x
        
        # Test combined operations: convection-diffusion equation
        α = 0.1
        dt = 0.01
        
        T_old = ScalarField(:T_old, mesh, [295.0], T.boundary_conditions)
        α_field = ScalarField(:alpha, mesh, [α], Dict{String, AbstractBoundaryCondition}())
        
        # Assemble full convection-diffusion equation
        ddt_T = CFD.Numerics.fvm.ddt(T, T_old, dt)
        conv_T = CFD.Numerics.fvm.convection(U, T)
        diff_T = CFD.Numerics.fvm.laplacian(α_field, T)
        
        # Combined matrix: ∂T/∂t + ∇·(UT) - ∇·(α∇T) = 0
        A_total = ddt_T.A + conv_T.A - diff_T.A
        b_total = ddt_T.b + conv_T.b - diff_T.b
        
        @test size(A_total) == (1, 1)
        @test length(b_total) == 1
        @test A_total[1, 1] > 0  # Should be stable
        
        # Test that solution is reasonable
        T_solution = A_total \ b_total
        @test T_solution[1] > 0  # Temperature should be positive
        @test T_solution[1] < 1000  # Should be reasonable
        
        println("Field operations integration test completed successfully")
    end
    
    @testset "Error Handling and Robustness" begin
        # Test error handling for invalid inputs
        mesh = let
            nodes = [Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false)]
            faces = [Face{Float64,3}(1, [1], SVector(0.0, 0.0, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, 0, true)]
            cells = [Cell{Float64,3}(1, [1], [1], SVector(0.0, 0.0, 0.0), 1.0)]
            
            UnstructuredMesh{Float64,3}(
                nodes, faces, cells, Dict{String, Vector{Int}}(),
                [Int[]], [(1, 0)],
                (SVector(0.0, 0.0, 0.0), SVector(0.0, 0.0, 0.0))
            )
        end
        
        # Test with invalid time step
        T = ScalarField(:temperature, mesh, [300.0], Dict{String, AbstractBoundaryCondition}())
        T_old = ScalarField(:T_old, mesh, [295.0], Dict{String, AbstractBoundaryCondition}())
        
        @test_throws ErrorException CFD.Numerics.fvm.ddt(T, T_old, -0.01)  # Negative dt
        @test_throws ErrorException CFD.Numerics.fvm.ddt(T, T_old, 0.0)   # Zero dt
        
        # Test with very small positive values (should work but warn about stability)
        result = CFD.Numerics.fvm.ddt(T, T_old, 1e-12)
        @test typeof(result) == CFD.Numerics.fvm.FvMatrix{Float64}
        
        println("Error handling and robustness test completed successfully")
    end
end