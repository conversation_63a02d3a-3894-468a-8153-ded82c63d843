#!/usr/bin/env julia

# Quick test summary script
using Test

println("="^60)
println("CFD.jl Test Summary")
println("="^60)

test_files = [
    ("Core Module", "test_core_module.jl"),
    ("Physics Module", "test_physics_module.jl"),
    ("Numerics Module", "test_numerics_module.jl"),
    ("Utilities Module", "test_utilities_module.jl"),
    ("Integration Tests", "test_integration.jl"),
    ("OpenFOAM Compatibility", "test_openfoam_compatibility.jl"),
    ("Linear Solvers", "test_linear_solvers.jl"),
]

total_pass = 0
total_fail = 0
total_error = 0
total_broken = 0

for (name, file) in test_files
    print("Testing $name... ")
    flush(stdout)
    
    try
        # Capture test output
        output = read(`julia --project=. test/$file`, String)
        
        # Parse test summary line
        if match(r"Test Summary:.*\|\s*(\d+)\s+(\d+)?\s*(\d+)?\s*(\d+)?\s*Total", output) !== nothing
            m = match(r"Pass\s+(?:Broken\s+)?(?:Error\s+)?Total\s+Time.*\n.*\|\s*(\d+)\s*(?:(\d+)\s*)?(?:(\d+)\s*)?(?:(\d+)\s*)?(\d+)", output)
            if m !== nothing
                pass = parse(Int, m[1])
                broken = m[2] !== nothing ? parse(Int, m[2]) : 0
                error = m[3] !== nothing ? parse(Int, m[3]) : 0
                total = parse(Int, m[end])
                
                total_pass += pass
                total_broken += broken
                total_error += error
                
                status = error > 0 ? "❌" : (broken > 0 ? "⚠️" : "✅")
                println("$status Pass: $pass, Broken: $broken, Error: $error")
            else
                println("⚠️  Could not parse results")
            end
        else
            println("❌ No test summary found")
        end
    catch e
        println("❌ Error: $e")
        total_error += 1
    end
end

println("="^60)
println("Total Results:")
println("  Pass:   $total_pass")
println("  Broken: $total_broken")
println("  Error:  $total_error")
println("  Total:  $(total_pass + total_broken + total_error)")
println("="^60)