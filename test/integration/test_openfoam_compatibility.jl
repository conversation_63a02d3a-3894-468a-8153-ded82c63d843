# Test suite for OpenFOAM compatibility
using Test
using CFD
using CFD.CFDCore
using CFD.Utilities
using StaticArrays

@testset "OpenFOAM Compatibility Tests" begin
    
    @testset "Test Case Availability" begin
        # Check that OpenFOAM test cases are available
        test_cases_dir = joinpath(@__DIR__, "..", "of", "tut")
        @test isdir(test_cases_dir)
        
        # Check specific test cases
        airfoil_case = joinpath(test_cases_dir, "airFoil2D")
        potential_case = joinpath(test_cases_dir, "potentialFoam")
        
        @test isdir(airfoil_case)
        @test isdir(potential_case)
        
        # Check for essential directories in test cases
        for case_dir in [airfoil_case, potential_case]
            @test isdir(joinpath(case_dir, "constant"))
            @test isdir(joinpath(case_dir, "system"))
            
            # Check for polyMesh directory
            polymesh_dir = joinpath(case_dir, "constant", "polyMesh")
            if isdir(polymesh_dir)
                @test isfile(joinpath(polymesh_dir, "points"))
                @test isfile(joinpath(polymesh_dir, "faces"))
                @test isfile(joinpath(polymesh_dir, "owner"))
                @test isfile(joinpath(polymesh_dir, "boundary"))
                # neighbour file may not always exist
            end
        end
    end
    
    @testset "OpenFOAM File Format Parsing" begin
        # Test parsing of OpenFOAM format (simplified test)
        
        # Create a mock points file content
        mock_points_content = """
        /*--------------------------------*- C++ -*----------------------------------*\\
        | =========                 |                                                 |
        | \\\\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
        |  \\\\    /   O peration     | Version:  8                                     |
        |   \\\\  /    A nd           | Web:      www.OpenFOAM.org                      |
        |    \\\\/     M anipulation  |                                                 |
        \\*---------------------------------------------------------------------------*/
        FoamFile
        {
            version     2.0;
            format      ascii;
            class       vectorField;
            location    "constant/polyMesh";
            object      points;
        }
        // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
        
        4
        (
        (0 0 0)
        (1 0 0)
        (1 1 0)
        (0 1 0)
        )
        
        // ************************************************************************* //
        """
        
        # Write to temporary file and test parsing
        temp_points_file = tempname()
        open(temp_points_file, "w") do f
            write(f, mock_points_content)
        end
        
        try
            points = read_openfoam_points(temp_points_file)
            @test length(points) == 4
            @test points[1] == SVector(0.0, 0.0, 0.0)
            @test points[2] == SVector(1.0, 0.0, 0.0)
            @test points[3] == SVector(1.0, 1.0, 0.0)
            @test points[4] == SVector(0.0, 1.0, 0.0)
        finally
            rm(temp_points_file, force=true)
        end
    end
    
    @testset "OpenFOAM Faces File Parsing" begin
        # Create mock faces file content
        mock_faces_content = """
        /*--------------------------------*- C++ -*----------------------------------*\\
        FoamFile
        {
            version     2.0;
            format      ascii;
            class       faceList;
            location    "constant/polyMesh";
            object      faces;
        }
        // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
        
        4
        (
        3(0 1 2)
        3(0 2 3)
        3(1 2 3)
        3(0 1 3)
        )
        
        // ************************************************************************* //
        """
        
        temp_faces_file = tempname()
        open(temp_faces_file, "w") do f
            write(f, mock_faces_content)
        end
        
        try
            # Note: Current parser may not handle this exact format perfectly
            # This tests the robustness of the parser
            @test_nowarn read_openfoam_faces(temp_faces_file)
        finally
            rm(temp_faces_file, force=true)
        end
    end
    
    @testset "OpenFOAM Owner File Parsing" begin
        # Create mock owner file content
        mock_owner_content = """
        /*--------------------------------*- C++ -*----------------------------------*\\
        FoamFile
        {
            version     2.0;
            format      ascii;
            class       labelList;
            location    "constant/polyMesh";
            object      owner;
        }
        // * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
        
        4
        (
        0
        0
        1
        1
        )
        
        // ************************************************************************* //
        """
        
        temp_owner_file = tempname()
        open(temp_owner_file, "w") do f
            write(f, mock_owner_content)
        end
        
        try
            owners = read_openfoam_owner(temp_owner_file)
            @test length(owners) == 4
            # Check 1-based indexing conversion
            @test owners[1] == 1  # 0 + 1
            @test owners[2] == 1  # 0 + 1
            @test owners[3] == 2  # 1 + 1
            @test owners[4] == 2  # 1 + 1
        finally
            rm(temp_owner_file, force=true)
        end
    end
    
    @testset "Mesh Reader Integration" begin
        # Test MeshReader with OpenFOAM format
        reader = MeshReader(:openfoam)
        @test reader.format == :openfoam
        
        # Test error handling for non-existent case
        @test_throws ErrorException read_mesh(reader, "/nonexistent/case")
        
        # Test error handling for case without polyMesh
        temp_case_dir = mktempdir()
        try
            mkdir(joinpath(temp_case_dir, "constant"))
            @test_throws ErrorException read_mesh(reader, temp_case_dir)
        finally
            rm(temp_case_dir, recursive=true)
        end
    end
    
    @testset "Boundary Handling" begin
        # Test boundary condition mapping
        dirichlet_bc = DirichletBC((x, y, z, t) -> 1.0)
        neumann_bc = NeumannBC((x, y, z, t) -> 0.0)
        
        # Test that boundary conditions work with OpenFOAM-style naming
        boundary_patches = ["inlet", "outlet", "wall", "symmetry"]
        
        for patch_name in boundary_patches
            bcs = Dict{String, AbstractBoundaryCondition}(
                patch_name => dirichlet_bc
            )
            @test haskey(bcs, patch_name)
            @test bcs[patch_name] isa DirichletBC
        end
    end
    
    @testset "Index Conversion" begin
        # Test 0-based to 1-based index conversion
        openfoam_indices = [0, 1, 2, 3, 10, 100]
        julia_indices = openfoam_indices .+ 1
        
        @test julia_indices == [1, 2, 3, 4, 11, 101]
        
        # Test that conversion is consistent
        for i in openfoam_indices
            @test (i + 1) - 1 == i  # Round-trip conversion
        end
    end
    
    @testset "File Format Robustness" begin
        # Test parser robustness with various file formats
        
        # Test with different comment styles
        test_cases = [
            "// Comment",
            "/* Block comment */", 
            "/*--------------------------------*- C++ -*----------------------------------*\\",
            "# Hash comment",
            ""
        ]
        
        for comment in test_cases
            # Test that comments don't break parsing
            temp_file = tempname()
            open(temp_file, "w") do f
                write(f, """
                $comment
                FoamFile
                {
                    version     2.0;
                    format      ascii;
                }
                $comment
                1
                (
                (0 0 0)
                )
                """)
            end
            
            try
                # Should not throw errors for comments
                @test_nowarn read_openfoam_points(temp_file)
            finally
                rm(temp_file, force=true)
            end
        end
    end
    
    @testset "Case Directory Structure" begin
        # Test recognition of OpenFOAM case structure
        function is_openfoam_case(case_dir)
            return isdir(joinpath(case_dir, "constant")) && 
                   isdir(joinpath(case_dir, "system"))
        end
        
        # Test with mock case directory
        temp_case = mktempdir()
        try
            @test !is_openfoam_case(temp_case)  # Initially not a case
            
            mkdir(joinpath(temp_case, "constant"))
            @test !is_openfoam_case(temp_case)  # Still missing system
            
            mkdir(joinpath(temp_case, "system"))
            @test is_openfoam_case(temp_case)   # Now it's a valid case
            
        finally
            rm(temp_case, recursive=true)
        end
    end
    
    @testset "Mesh Quality Checks" begin
        # Test basic mesh quality checks after reading
        
        # Create a simple valid mesh structure
        nodes = [
            Node{Float64,3}(1, SVector(0.0, 0.0, 0.0), false),
            Node{Float64,3}(2, SVector(1.0, 0.0, 0.0), false),
            Node{Float64,3}(3, SVector(1.0, 1.0, 0.0), false),
            Node{Float64,3}(4, SVector(0.0, 1.0, 0.0), false)
        ]
        
        faces = [
            Face{Float64,3}(1, [1, 2], SVector(0.5, 0.0, 0.0), 1.0, SVector(0.0, -1.0, 0.0), 1, 0, true),
            Face{Float64,3}(2, [2, 3], SVector(1.0, 0.5, 0.0), 1.0, SVector(1.0, 0.0, 0.0), 1, 0, true),
            Face{Float64,3}(3, [3, 4], SVector(0.5, 1.0, 0.0), 1.0, SVector(0.0, 1.0, 0.0), 1, 0, true),
            Face{Float64,3}(4, [4, 1], SVector(0.0, 0.5, 0.0), 1.0, SVector(-1.0, 0.0, 0.0), 1, 0, true)
        ]
        
        cells = [Cell{Float64,3}(1, [1, 2, 3, 4], [1, 2, 3, 4], SVector(0.5, 0.5, 0.0), 1.0)]
        
        boundaries = Dict{String, Vector{Int}}("walls" => [1, 2, 3, 4])
        
        mesh = UnstructuredMesh{Float64,3}(
            nodes, faces, cells, boundaries,
            [Int[]], [(1, 0), (1, 0), (1, 0), (1, 0)],
            (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 0.0))
        )
        
        # Basic mesh quality checks
        @test length(mesh.nodes) > 0
        @test length(mesh.faces) > 0
        @test length(mesh.cells) > 0
        
        # Check that all face owners are valid
        for face in mesh.faces
            @test 1 <= face.owner <= length(mesh.cells)
            if face.neighbor > 0
                @test 1 <= face.neighbor <= length(mesh.cells)
            end
        end
        
        # Check that face areas are positive
        for face in mesh.faces
            @test face.area > 0
        end
        
        # Check that cell volumes are positive
        for cell in mesh.cells
            @test cell.volume > 0
        end
        
        # Check bounding box
        bbox_min, bbox_max = mesh.bbox
        @test all(bbox_min .<= bbox_max)
    end
end