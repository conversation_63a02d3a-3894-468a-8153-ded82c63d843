#!/usr/bin/env julia

# Real test script for CFD_LLM with actual OpenRouter API calls
using Pkg
Pkg.activate(".")

using CFD_LLM
using CFD_LLM.LLMInterface

println("🚀 Testing CFD_LLM with Real OpenRouter API")
println("=" ^ 50)

# Get API key and model from environment
api_key = get(ENV, "OPENROUTER_API_KEY", nothing)
model = get(ENV, "OPENROUTER_MODEL", "deepseek/deepseek-chat-v3-0324:free")

if api_key === nothing
    println("❌ OPENROUTER_API_KEY environment variable not set")
    println("Please run:")
    println("export OPENROUTER_API_KEY=\"your-key-here\"")
    exit(1)
end

println("✓ Using API key: $(api_key[1:10])...")
println("✓ Using model: $model")

# Test 1: LLM Configuration
println("\n📋 Test 1: Creating LLM Configuration")
config = create_llm_config(
    provider="openrouter",
    api_key=api_key,
    model=model,
    temperature=0.7,
    max_tokens=1000
)
println("✓ LLM configuration created")

# Test 2: Connection Test
println("\n🔌 Test 2: Testing OpenRouter Connection")
connection_result = test_llm_connection(config)

if connection_result["connected"]
    println("✅ Connection successful!")
    println("   Response time: $(round(connection_result["response_time"], digits=2))s")
    println("   Test response: $(connection_result["test_response"])")
else
    println("❌ Connection failed!")
    println("   Error: $(connection_result["error"])")
    exit(1)
end

# Test 3: Initialize CFD_LLM
println("\n⚙️  Test 3: Initializing CFD_LLM")
success = CFD_LLM.initialize!(config)

if success
    println("✅ CFD_LLM initialized successfully")
else
    println("❌ CFD_LLM initialization failed")
    exit(1)
end

# Test 4: Simple Query
println("\n💬 Test 4: Simple Physics Question")
try
    response = @ask "What is the Reynolds number and what does it represent in fluid mechanics?"
    
    println("✅ Query successful!")
    println("   Response type: $(response.type)")
    println("   Confidence: $(response.confidence)")
    println("   Content length: $(length(response.content)) characters")
    println("\n📝 Response preview:")
    println("   $(response.content[1:min(200, length(response.content))])...")
    
catch e
    println("❌ Query failed: $e")
end

# Test 5: Boundary Condition Generation
println("\n🔧 Test 5: Boundary Condition Generation")
try
    bc_response = @extend """
    Create a simple no-slip wall boundary condition for CFD.jl.
    The wall should have zero velocity and zero pressure gradient.
    Make it a short, practical Julia function.
    """
    
    println("✅ BC generation successful!")
    println("   Response type: $(bc_response.type)")
    println("   Has generated code: $(bc_response.generated_code !== nothing)")
    
    if bc_response.generated_code !== nothing
        println("\n🔍 Generated code preview:")
        code_preview = bc_response.generated_code[1:min(300, length(bc_response.generated_code))]
        println("   $code_preview...")
    else
        content_preview = bc_response.content[1:min(300, length(bc_response.content))]
        println("\n📝 Content preview:")
        println("   $content_preview...")
    end
    
catch e
    println("❌ BC generation failed: $e")
end

# Test 6: Physics Analysis
println("\n🔬 Test 6: Physics Analysis")
try
    analysis = @ask """
    For turbulent flow over a flat plate at Re=1e6, which turbulence model 
    would you recommend: k-ε or k-ω SST? Give a brief technical reason.
    """
    
    println("✅ Physics analysis successful!")
    println("   Analysis length: $(length(analysis.content)) characters")
    println("\n🧠 Analysis preview:")
    println("   $(analysis.content[1:min(250, length(analysis.content))])...")
    
catch e
    println("❌ Physics analysis failed: $e")
end

# Test 7: Integration Status
println("\n🔗 Test 7: CFD Integration Status")
status = CFD_LLM.CFDInterface.get_cfd_integration_status()
println("   CFD.jl available: $(status["cfd_available"])")
println("   Integration level: $(status["integration_level"])")

if haskey(status, "available_field_types")
    println("   Available field types: $(length(status["available_field_types"]))")
end

# Summary
println("\n🎉 Test Summary")
println("=" ^ 30)
println("✅ All core functionality working")
println("✅ Real OpenRouter API integration successful")
println("✅ CFD_LLM agents responding correctly")
println("✅ Physics knowledge accessible")
println("\n🚀 CFD_LLM is ready for use!")

# Optional: Interactive test
println("\n💡 You can now test interactively:")
println("   julia --project=. -e 'using CFD_LLM; initialize_with_openrouter(ENV[\"OPENROUTER_API_KEY\"], model=ENV[\"OPENROUTER_MODEL\"])'")
println("   Then use @ask \"your question\" or @extend \"your request\"")