using CFD_LLM
using CFD_LLM.RetrievalEngine

# Test basic module import
println("✓ Modules loaded successfully")

# Create a retriever to test the methods
retriever = create_retriever()
println("✓ Retriever created")

# Test 4-argument call (should use convenience method)
try
    doc_id = add_document!(
        retriever,
        "Test content",
        :test,
        Dict(:test => true)
    )
    println("✓ 4-argument call successful: ", doc_id)
catch e
    println("❌ 4-argument call failed: ", e)
end

# Test 5-argument call (should use main method)
try
    doc_id = add_document!(
        retriever,
        "Test content 2",
        :test,
        Dict(:test => true),
        [:velocity, :pressure]
    )
    println("✓ 5-argument call successful: ", doc_id)
catch e
    println("❌ 5-argument call failed: ", e)
end

println("✓ All tests completed")