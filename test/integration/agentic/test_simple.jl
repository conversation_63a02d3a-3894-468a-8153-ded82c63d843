#!/usr/bin/env julia

using Pkg
Pkg.activate(".")

# Test simple loading
println("Testing simple CFD_LLM loading...")

try
    using CFD_LLM.PhysicsOntology
    println("✓ PhysicsOntology loaded")
    
    # Test basic functionality
    knowledge = PhysicsOntology.load_physics_knowledge()
    println("✓ Physics knowledge loaded")
    println("   Concepts: $(length(knowledge.concepts))")
    
catch e
    println("❌ Error loading PhysicsOntology: $e")
    rethrow(e)
end

try
    using CFD_LLM.LLMInterface
    println("✓ LLMInterface loaded")
    
    # Test configuration
    config = LLMInterface.create_llm_config(
        provider="openrouter",
        api_key="test",
        model="test"
    )
    println("✓ LLM config created")
    
catch e
    println("❌ Error with LLMInterface: $e")
    rethrow(e)
end

try
    using CFD_LLM.BaseAgent
    println("✓ BaseAgent loaded")
    
catch e
    println("❌ Error with BaseAgent: $e")
    rethrow(e)
end

println("🎉 Basic modules loaded successfully")