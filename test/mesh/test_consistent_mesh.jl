# test/mesh/test_consistent_mesh.jl
using Test
using CFD
using LinearAlgebra
using StaticArrays

@testset "ConsistentMesh Tests" begin
    
    @testset "Mesh Generation and Validation" begin
        # Test different mesh sizes
        for (nx, ny, nz) in [(2, 2, 2), (3, 3, 3), (4, 4, 4)]
            @testset "$(nx)×$(ny)×$(nz) mesh" begin
                mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(nx, ny, nz)
                
                # Basic mesh properties
                @test length(mesh.cells) == nx * ny * nz
                @test length(mesh.nodes) == (nx + 1) * (ny + 1) * (nz + 1)
                
                # Expected face count
                n_internal = (nx-1)*ny*nz + nx*(ny-1)*nz + nx*ny*(nz-1)
                n_boundary = 2*(nx*ny + nx*nz + ny*nz)
                @test length(mesh.faces) == n_internal + n_boundary
                
                # Check face properties
                internal_faces = [f for f in mesh.faces if !f.boundary]
                boundary_faces = [f for f in mesh.faces if f.boundary]
                
                @test length(internal_faces) == n_internal
                @test length(boundary_faces) == n_boundary
                
                # Verify owner < neighbor for all internal faces
                for face in internal_faces
                    @test face.owner < face.neighbor
                    @test face.neighbor > 0
                end
                
                # Verify boundary faces have no neighbor
                for face in boundary_faces
                    @test face.neighbor == -1
                end
            end
        end
    end
    
    @testset "Conservation Properties" begin
        for (nx, ny, nz) in [(2, 2, 2), (3, 3, 3), (4, 4, 4)]
            @testset "$(nx)×$(ny)×$(nz) conservation" begin
                mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(nx, ny, nz)
                
                # Check conservation: ∑(Sf) = 0 for internal faces
                internal_area_sum = zero(SVector{3,Float64})
                for face in mesh.faces
                    if !face.boundary
                        internal_area_sum += face.normal * face.area
                    end
                end
                
                conservation_error = norm(internal_area_sum)
                @test conservation_error < 1e-12
            end
        end
    end
    
    @testset "Face Normal Orientation" begin
        mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(3, 3, 3)
        
        # Check that face normals point from owner to neighbor
        for face in mesh.faces
            if !face.boundary && face.neighbor > 0
                # Vector from owner to neighbor
                d = mesh.cells[face.neighbor].center - mesh.cells[face.owner].center
                # Normal should point in same general direction
                @test dot(face.normal, d) > 0
            end
        end
    end
    
    @testset "Boundary Patch Assignment" begin
        mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(3, 3, 3)
        
        # Check boundary patches exist
        @test haskey(mesh.boundaries, "inlet")
        @test haskey(mesh.boundaries, "outlet")
        @test haskey(mesh.boundaries, "walls")
        
        # Check boundary face locations
        for face_idx in mesh.boundaries["inlet"]
            face = mesh.faces[face_idx]
            @test face.center[1] ≈ 0.0 atol=1e-10
        end
        
        for face_idx in mesh.boundaries["outlet"]
            face = mesh.faces[face_idx]
            @test face.center[1] ≈ 1.0 atol=1e-10
        end
    end
    
    @testset "Cell Volume Calculation" begin
        for (nx, ny, nz) in [(2, 2, 2), (3, 3, 3)]
            mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(nx, ny, nz)
            
            # Expected cell volume
            expected_volume = 1.0 / (nx * ny * nz)
            
            for cell in mesh.cells
                @test cell.volume ≈ expected_volume rtol=1e-12
            end
            
            # Total volume should be 1.0
            total_volume = sum(c.volume for c in mesh.cells)
            @test total_volume ≈ 1.0 rtol=1e-12
        end
    end
    
    @testset "FVC Operators with ConsistentMesh" begin
        mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(3, 3, 3)
        
        @testset "Gradient operator" begin
            # Test φ = x (gradient should be [1, 0, 0])
            phi_data = [cell.center[1] for cell in mesh.cells]
            
            # Set up proper boundary conditions
            bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
            bcs["inlet"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
            bcs["outlet"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 1.0)
            bcs["walls"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> x)
            
            phi_field = CFD.CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi, mesh, phi_data, bcs, nothing, nothing)
            
            # Compute gradient
            grad_phi = CFD.Numerics.fvc.grad(phi_field)
            
            # Check gradient accuracy
            for grad in grad_phi.data
                @test grad[1] ≈ 1.0 atol=1e-12
                @test grad[2] ≈ 0.0 atol=1e-12
                @test grad[3] ≈ 0.0 atol=1e-12
            end
        end
        
        @testset "Divergence operator" begin
            # Test constant vector field (divergence should be 0)
            U_data = [SVector(1.0, 0.0, 0.0) for _ in mesh.cells]
            bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
            
            U_field = CFD.CFDCore.VectorField{SVector{3,Float64}, 3, typeof(mesh)}(:U, mesh, U_data, bcs, nothing, nothing)
            
            # Compute divergence
            div_U = CFD.Numerics.fvc.div(U_field)
            
            # Check divergence is zero
            for div_val in div_U.data
                @test abs(div_val) < 1e-12
            end
        end
        
        @testset "Laplacian operator" begin
            # Test φ = x² (Laplacian should be 2.0)
            phi_data = [cell.center[1]^2 for cell in mesh.cells]
            
            # Set up proper boundary conditions
            bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
            bcs["inlet"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 0.0)
            bcs["outlet"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> 1.0)
            bcs["walls"] = CFD.CFDCore.DirichletBC((x, y, z, t) -> x^2)
            
            phi_field = CFD.CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi, mesh, phi_data, bcs, nothing, nothing)
            
            # Compute Laplacian
            laplacian_phi = CFD.Numerics.fvc.laplacian(phi_field)
            
            # Check Laplacian accuracy (should be 2.0 everywhere)
            for lap_val in laplacian_phi.data
                @test lap_val ≈ 2.0 rtol=0.1  # Allow 10% tolerance for now
            end
        end
    end
    
    @testset "FVM Matrix Properties with ConsistentMesh" begin
        mesh = CFD.Utilities.ConsistentMesh.create_consistent_unit_cube_mesh(3, 3, 3)
        
        # Create a simple Laplacian matrix
        n_cells = length(mesh.cells)
        phi_data = zeros(n_cells)
        bcs = Dict{String, CFD.CFDCore.AbstractBoundaryCondition}()
        
        phi_field = CFD.CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi, mesh, phi_data, bcs, nothing, nothing)
        
        # Generate FVM Laplacian matrix
        fvm_matrix = CFD.Numerics.fvm.laplacian(1.0, phi_field)
        A = fvm_matrix.A
        
        @testset "Matrix symmetry" begin
            symmetry_error = norm(A - A')
            @test symmetry_error < 1e-12
        end
        
        @testset "Diagonal dominance" begin
            for i in 1:n_cells
                diagonal = abs(A[i,i])
                off_diagonal_sum = sum(abs(A[i,j]) for j in 1:n_cells if j != i)
                @test diagonal >= off_diagonal_sum
            end
        end
        
        @testset "Matrix sign pattern" begin
            # Diagonal elements should be positive
            for i in 1:n_cells
                @test A[i,i] > 0
            end
            
            # Off-diagonal elements should be non-positive
            for i in 1:n_cells, j in 1:n_cells
                if i != j && abs(A[i,j]) > 1e-12
                    @test A[i,j] <= 0
                end
            end
        end
    end
end