using Test
using CFD

# Load test configuration
include("unit/test_config.jl")

# Main test runner for CFD.jl with organized structure
@testset "CFD.jl Test Suite" begin
    
    # Unit Tests
    @testset "Unit Tests" begin
        @testset "Core Module" begin
            include("unit/test_core_module.jl")
        end
        
        @testset "Physics Module" begin
            include("unit/test_physics_module.jl")
        end
        
        @testset "Numerics Module" begin
            include("unit/test_numerics_module.jl")
        end
        
        @testset "Utilities Module" begin
            include("unit/test_utilities_module.jl")
        end
        
        @testset "Installation Tests" begin
            include("unit/test_installation.jl")
        end
        
        @testset "Missing Functions Tests" begin
            include("unit/test_missing_functions.jl")
        end
    end
    
    # Framework Tests
    @testset "Framework Tests" begin
        @testset "CFD Components" begin
            include("framework/test_cfd_components.jl")
        end
        
        @testset "Complete Framework" begin
            include("framework/test_complete_framework.jl")
        end
        
        @testset "Solver Framework" begin
            include("framework/test_solver_framework.jl")
        end
    end
    
    # Terminal Tests
    @testset "Enhanced Terminal Tests" begin
        @testset "Basic Terminal Functionality" begin
            include("terminal/test_enhanced_terminal.jl")
        end
        
        @testset "MPI Detection" begin
            include("terminal/test_mpi_detection.jl")
        end
        
        @testset "Comprehensive Terminal Validation" begin
            include("terminal/final_comprehensive_test.jl")
        end
    end
    
    # Integration Tests
    @testset "Integration Tests" begin
        @testset "Basic Integration" begin
            include("integration/test_integration.jl")
        end
        
        @testset "OpenFOAM Compatibility" begin
            include("integration/test_openfoam_compatibility.jl")
        end
        
        @testset "Operators" begin
            include("integration/operators/numerics_fvc_tests.jl")
            include("integration/operators/test_fvc_operators.jl")
        end
        
        @testset "Solvers" begin
            include("integration/solvers/test_linear_solvers.jl")
            
            if TEST_PARALLEL
                include("integration/solvers/test_parallel_solvers.jl")
                include("integration/solvers/test_parallel_solvers_simple.jl")
            else
                @info "Skipping parallel solver tests (set CFD_TEST_PARALLEL=true to enable)"
            end
        end
        
        @testset "Validation" begin
            include("integration/validation/test_validation_phase2.jl")
        end
        
        @testset "Agentic Tool" begin
            include("integration/agentic/test_methods.jl")
            include("integration/agentic/test_real.jl")
            include("integration/agentic/test_simple.jl")
        end
        
        @testset "HPC Framework Validation" begin
            @info "Running HPC framework validation tests..."
            
            @testset "HPC Quick Tests" begin
                include("hpc/test_hpc_quick.jl")
            end
            
            @testset "HPC SIMPLE Solver" begin
                include("hpc/test_hpc_simple_solver.jl")
            end
            
            @testset "Numerical Accuracy Validation" begin
                include("numerical/test_numerical_accuracy_validation.jl")
            end
            
            @testset "Parallel Scaling Tests" begin
                include("performance/test_parallel_scaling_larger_problems.jl")
            end
        end
    end
    
    # Validation Tests (comprehensive physics validation)
    @testset "Validation Suite" begin
        @info "Running comprehensive validation tests..."
        
        # Include validation framework
        include("../validation/ValidationWorkflow.jl")
        using .ValidationWorkflow
        
        # Run basic validation phases (skip slow/advanced ones in regular testing)
        @testset "Basic Validation" begin
            @test_nowarn run_phase(1)  # Basic mesh and field operations
            @test_nowarn run_phase(2)  # Gradient and divergence operators
        end
        
        if get(ENV, "CFD_FULL_VALIDATION", "false") == "true"
            @testset "Full Validation" begin
                @test_nowarn run_all_tests()
            end
        end
    end
end

println("\n" * "="^60)
println("All CFD.jl tests completed!")
println("="^60)