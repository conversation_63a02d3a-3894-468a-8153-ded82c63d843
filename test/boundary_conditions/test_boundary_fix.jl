#!/usr/bin/env julia

"""
Test Boundary Patch Fix and Scientific Notation Function
"""

using Pkg
Pkg.activate(".")

using CFD
using CFD.Solvers: scientific_notation
using CFD.MinimalCFD
using Printf

println("🧪 Testing Boundary Patch Fix and Scientific Notation")
println("="^60)

function test_boundary_patches_and_scientific_notation()
    println("\n1. Testing scientific_notation function:")
    
    test_values = [0.0, 1.0, 123.456, 0.001234, 1234567.89, -0.000001, 1.23e-15, 2.5e12]
    
    for val in test_values
        sci_notation = scientific_notation(val)
        println("   $(val) → $(sci_notation)")
    end
    
    println("\n2. Testing boundary patch assignment in MinimalCFD:")
    
    # Create a simple test case
    case_path = auto_mesh("test_patch_fix", (4, 4, 4))
    mesh = read_mesh(case_path)
    
    println("✅ Mesh loaded: $(mesh.ncells) cells")
    
    # Check available patches
    println("\n   Available patches in mesh:")
    for (patch_name, face_ids) in mesh.boundaries
        println("     • $(patch_name): $(length(face_ids)) faces")
    end
    
    # Create fields
    U = 𝐮(:U, mesh)
    p = φ(:p, mesh)
    
    println("\n3. Testing boundary condition assignment:")
    
    # Test that we can set boundary conditions on all expected patches
    try
        set_bc!(U, :inlet, (1.0, 0.0, 0.0))
        println("   ✅ inlet BC set successfully")
    catch e
        println("   ❌ inlet BC failed: $e")
        return false
    end
    
    try
        set_bc!(U, :outlet, (0.5, 0.0, 0.0))
        println("   ✅ outlet BC set successfully")
    catch e
        println("   ❌ outlet BC failed: $e")
        return false
    end
    
    try
        set_bc!(U, :walls, (0.0, 0.0, 0.0))
        println("   ✅ walls BC set successfully")
    catch e
        println("   ❌ walls BC failed: $e")
        return false
    end
    
    try
        set_bc!(p, :outlet, 0.0)
        set_bc!(p, :inlet, "zeroGradient")
        set_bc!(p, :walls, "zeroGradient")
        println("   ✅ pressure BCs set successfully")
    catch e
        println("   ❌ pressure BC failed: $e")
        return false
    end
    
    println("\n4. Testing boundary condition application:")
    
    try
        apply_boundary_conditions!(U)
        apply_boundary_conditions!(p)
        println("   ✅ Boundary conditions applied successfully")
    catch e
        println("   ❌ BC application failed: $e")
        return false
    end
    
    return true
end

# Run the test
success = test_boundary_patches_and_scientific_notation()

if success
    println("\n🎉 All tests PASSED!")
    println("   • scientific_notation function works correctly")
    println("   • Boundary patches are properly created and named")
    println("   • Boundary conditions can be set and applied")
    println("   • The fix resolves the boundary face association issue")
else
    println("\n❌ Some tests FAILED!")
end

println("\n" * "="^60)