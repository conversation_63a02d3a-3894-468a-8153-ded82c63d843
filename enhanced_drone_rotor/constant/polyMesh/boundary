/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2112                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    inlet
    {
        type            patch;
        nFaces          1600;
        startFace       86400;
        physicalType    inlet;
    }
    outlet  
    {
        type            patch;
        nFaces          1600;
        startFace       88000;
        physicalType    outlet;
    }
    walls
    {
        type            wall;
        nFaces          6400;
        startFace       89600;
        physicalType    wall;
    }
    symmetry_top
    {
        type            symmetryPlane;
        nFaces          1600;
        startFace       96000;
        physicalType    symmetry;
    }
    symmetry_bottom
    {
        type            symmetryPlane;
        nFaces          1600;
        startFace       97600;
        physicalType    symmetry;
    }
    rotor_region
    {
        type            patch;
        nFaces          20000;
        startFace       99200;
        physicalType    rotorDisk;
    }
)

// ************************************************************************* //