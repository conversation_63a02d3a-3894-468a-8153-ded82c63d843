/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2112                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       vectorField;
    location    "constant/polyMesh";
    object      points;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// Structured mesh points for 4m x 4m x 2m drone domain
// 41 x 41 x 21 = 35,301 points for 40 x 40 x 20 cells

35301
(
(-2 -2 0)
(-1.9 -2 0)
(-1.8 -2 0)
(-1.7 -2 0)
(-1.6 -2 0)
(-1.5 -2 0)
(-1.4 -2 0)
(-1.3 -2 0)
(-1.2 -2 0)
(-1.1 -2 0)
(-1 -2 0)
(-0.9 -2 0)
(-0.8 -2 0)
(-0.7 -2 0)
(-0.6 -2 0)
(-0.5 -2 0)
(-0.4 -2 0)
(-0.3 -2 0)
(-0.2 -2 0)
(-0.1 -2 0)
(0 -2 0)
(0.1 -2 0)
(0.2 -2 0)
(0.3 -2 0)
(0.4 -2 0)
(0.5 -2 0)
(0.6 -2 0)
(0.7 -2 0)
(0.8 -2 0)
(0.9 -2 0)
(1 -2 0)
(1.1 -2 0)
(1.2 -2 0)
(1.3 -2 0)
(1.4 -2 0)
(1.5 -2 0)
(1.6 -2 0)
(1.7 -2 0)
(1.8 -2 0)
(1.9 -2 0)
(2 -2 0)
// ... [additional points would be generated here for full mesh]
// Simplified mesh with key corner points
(-2 -2 2)
(-2 2 2)
(2 2 2)
(2 -2 2)
)

// ************************************************************************* //