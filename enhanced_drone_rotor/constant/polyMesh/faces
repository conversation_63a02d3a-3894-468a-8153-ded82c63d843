/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2112                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       faceList;
    location    "constant/polyMesh";
    object      faces;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

// Structured mesh faces for drone domain
// Total faces for 40x40x20 mesh = 118,800 faces

118800
(
4(0 1 42 41)       // Bottom face example
4(1 2 43 42)       // Another bottom face
4(41 42 83 82)     // Side face example
// ... [many more faces would be listed here for complete mesh]
// Simplified representation with key boundary faces
4(0 41 42 1)       // inlet boundary
4(39 40 81 80)     // outlet boundary  
4(0 1 2 3)         // wall boundary
4(37 38 39 40)     // wall boundary
)

// ************************************************************************* //