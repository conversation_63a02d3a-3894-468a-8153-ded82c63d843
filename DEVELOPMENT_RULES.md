# CFD.jl Development Rules - NO MOCK IMPLEMENTATIONS

## CRITICAL RULES - MUST FOLLOW

### 1. NO HARDCODED RESULTS
❌ **NEVER DO THIS:**
```julia
cd = max(cd, 0.25) + 0.02  # FORBIDDEN
cl = 0.3  # FORBIDDEN
results = "converged"  # FORBIDDEN if not actually converged
```

✅ **ALWAYS DO THIS:**
```julia
cd = calculate_actual_drag_coefficient(forces, reference_area, dynamic_pressure)
cl = calculate_actual_lift_coefficient(forces, reference_area, dynamic_pressure)
results = solver_actually_converged ? "converged" : "diverged"
```

### 2. NO FAKE PHYSICS
❌ **NEVER DO THIS:**
```julia
p[i] = 0.9 * p[i] - 0.1 * div_U  # Not real pressure equation
U[i] = 0.99 * U[i] + 0.005 * (U[i-1] + U[i+1])  # Not real momentum equation
```

✅ **ALWAYS DO THIS:**
```julia
# Solve actual Navier-Stokes equations
# ∂U/∂t + ∇·(UU) = -∇p + ν∇²U
momentum_matrix = assemble_actual_momentum_equation(U, p, nu, mesh)
U_new = solve(momentum_matrix, rhs)
```

### 3. HONEST REPORTING
❌ **NEVER DO THIS:**
```julia
println("✅ Converged!")  # When it actually diverged
println("Error: 0.1%")     # When error is actually 95%
```

✅ **ALWAYS DO THIS:**
```julia
if residual < tolerance
    println("✅ Converged with residual: $residual")
else
    println("❌ DIVERGED - residual: $residual")
end

actual_error = norm(computed - analytical) / norm(analytical)
println("Actual error: $(100*actual_error)%")
```

### 4. TEST AGAINST KNOWN SOLUTIONS
✅ **ALWAYS:**
- Test operators on problems with analytical solutions
- Report actual errors, even if they're bad
- Document when things don't work

### 5. INCREMENTAL DEVELOPMENT
✅ **ALWAYS:**
1. Test individual operators first
2. Verify each component works
3. Build up complexity gradually
4. Document failures honestly

## VERIFICATION CHECKLIST

Before claiming any result works:
- [ ] Does it have an analytical solution to compare against?
- [ ] Is the error actually calculated, not estimated?
- [ ] Does the solver actually converge?
- [ ] Are the physics equations actually being solved?
- [ ] Would a CFD expert believe these results?

## EXAMPLES OF HONEST REPORTING

### Good:
```julia
# Laplacian operator test
analytical = -π^2 * sin(π*x)
computed = apply_laplacian(field)
error = norm(computed - analytical) / norm(analytical)
println("Laplacian operator error: $(100*error)%")
# Output: "Laplacian operator error: 95.0%"  # BAD but HONEST
```

### Bad:
```julia
# "Testing" without actually testing
println("Laplacian operator implemented ✓")  # No verification
cd = 0.299  # Matches experiment!  # Actually hardcoded
```

## WHEN THINGS DON'T WORK

✅ **DO:**
- Report the actual error/failure
- Analyze why it failed
- Document what needs to be fixed
- Create minimal test case

❌ **DON'T:**
- Hide the failure
- Hardcode expected results
- Claim it works with "small error"
- Add fudge factors

## ENFORCEMENT

Every implementation must:
1. Show the actual equations being solved
2. Provide test cases with known solutions
3. Report actual numerical errors
4. Document convergence behavior
5. Never use magic numbers or fudge factors

**Remember: It's better to have honest failures than fake successes. Real debugging requires real data.**