diff --git a/src/Numerics/fvc.jl b/src/Numerics/fvc.jl
index e2d8530..d442c21 100644
--- a/src/Numerics/fvc.jl
+++ b/src/Numerics/fvc.jl
@@ -21,7 +21,8 @@ function grad(field::ScalarField{TF_base, NF_idx, M_type}, scheme::AbstractGradi
     grad_data = [zero(SVector{N_dims, TF_base}) for _ in 1:num_cells]
 
     if scheme isa GaussGradient
-        face_interpolation_scheme = LinearInterpolation() # Or CentralDifferencing()
+        # Use Green-Gauss theorem for gradient calculation
+        face_interpolation_scheme = LinearInterpolation() # Linear interpolation for faces
         phi_f_values = fvc.interpolate(field, face_interpolation_scheme, current_time=current_time)
 
         for f_idx in 1:num_faces
@@ -38,13 +39,79 @@ function grad(field::ScalarField{TF_base, NF_idx, M_type}, scheme::AbstractGradi
             end
         end
 
+        # Handle boundary faces with appropriate BCs
+        for (patch_name, faces_indices) in mesh.boundaries
+            if haskey(field.boundary_conditions, patch_name)
+                bc = field.boundary_conditions[patch_name]
+                
+                for f_idx in faces_indices
+                    face = mesh.faces[f_idx]
+                    owner_idx = face.owner
+                    
+                    # Get boundary value based on boundary condition type
+                    phi_b = if bc isa CFDCore.DirichletBC
+                        bc.value(face.center..., current_time)
+                    elseif bc isa CFDCore.NeumannBC
+                        # For zero gradient or fixed gradient, compute appropriate value
+                        field.data[owner_idx] + bc.gradient(face.center..., current_time) * 
+                            norm(face.center - mesh.cells[owner_idx].center)
+                    else
+                        # Default to field value for unknown BC types
+                        field.data[owner_idx]
+                    end
+                    
+                    S_f_vec = face.normal * face.area
+                    grad_data[owner_idx] = grad_data[owner_idx] + phi_b * S_f_vec
+                end
+            end
+        end
+
+        # Divide by cell volume to complete gradient calculation
         for i_cell in 1:num_cells
             if mesh.cells[i_cell].volume <= 0
                 error("Cell $(i_cell) has zero or negative volume: $(mesh.cells[i_cell].volume) for gradient calculation.")
             end
             grad_data[i_cell] = grad_data[i_cell] / mesh.cells[i_cell].volume
         end
-        
+    elseif scheme isa LeastSquaresGradient
+        # Implement least squares gradient reconstruction for better accuracy on non-orthogonal meshes
+        for cell_idx in 1:num_cells
+            cell = mesh.cells[cell_idx]
+            
+            # Build least squares system for this cell
+            A = zeros(TF_base, N_dims, N_dims)
+            b = zeros(TF_base, N_dims)
+            
+            # Add contributions from each neighbor
+            n_neighbors = 0
+            for f_idx in cell.faces
+                face = mesh.faces[f_idx]
+                
+                if face.boundary
+                    continue  # Skip boundary faces for now in LS reconstruction
+                end
+                
+                # Get neighbor cell
+                neighbor_idx = face.owner == cell_idx ? face.neighbor : face.owner
+                if neighbor_idx < 1
+                    continue  # Skip invalid neighbors
+                end
+                
+                # Vector from cell center to neighbor center
+                d_vec = mesh.cells[neighbor_idx].center - cell.center
+                w = 1.0 / norm(d_vec)  # Weight by inverse distance
+                
+                # Build normal equations components
+                A += w^2 * (d_vec * d_vec')
+                b += w^2 * d_vec * (field.data[neighbor_idx] - field.data[cell_idx])
+                n_neighbors += 1
+            end
+            
+            # Solve system if we have enough neighbors
+            if n_neighbors >= N_dims
+                grad_data[cell_idx] = SVector{N_dims, TF_base}(A \ b)
+            end
+        end
     else
         error("Unsupported gradient scheme: $(typeof(scheme)) in fvc.grad")
     end
@@ -84,31 +151,86 @@ function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, sch
     return CFDCore.ScalarField(Symbol(string(field.name)*"_div"), mesh, divergence_data, field.boundary_conditions, nothing, nothing)
 end
 
-# Specific implementation for CentralDifferencing scheme
-function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, scheme::CentralDifferencing) where {SVecTF<:SVector, NF, M<:AbstractMesh}
+# Specific implementation for CentralDifferencing scheme with proper face interpolation
+function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, scheme::CentralDifferencing; 
+            rhie_chow_data=nothing) where {SVecTF<:SVector, NF, M<:AbstractMesh}
     mesh = field.mesh
     n_cells = length(mesh.cells)
     TF = eltype(SVecTF)
     divergence_data = zeros(TF, n_cells)
 
-    for face in mesh.faces
+    for face_idx in 1:length(mesh.faces)
+        face = mesh.faces[face_idx]
         owner_idx = face.owner  # Already 1-based indexing
         
-        U_f = if face.neighbor >= 0 && !face.boundary
-            neighbor_idx = face.neighbor
-            0.5 * (field.data[owner_idx] + field.data[neighbor_idx])
-        else 
-            field.data[owner_idx]
-        end
-        
-        flux = dot(U_f, face.normal) * face.area
-        divergence_data[owner_idx] += flux
-        if face.neighbor >= 0 && !face.boundary
+        if !face.boundary && face.neighbor >= 0
             neighbor_idx = face.neighbor
+            
+            # Standard interpolation of velocity to face
+            g_f = face_distance_weight(face, mesh.cells[owner_idx], mesh.cells[neighbor_idx])
+            U_f = g_f * field.data[owner_idx] + (1 - g_f) * field.data[neighbor_idx]
+            
+            # Apply Rhie-Chow correction if data is provided (for pressure-velocity coupling)
+            if !isnothing(rhie_chow_data) && haskey(rhie_chow_data, :p) && haskey(rhie_chow_data, :aP)
+                p = rhie_chow_data[:p]
+                aP = rhie_chow_data[:aP]
+                
+                # Face normal vector
+                n = normalize(face.normal)
+                
+                # Distance vector and magnitude
+                d_vec = mesh.cells[neighbor_idx].center - mesh.cells[owner_idx].center
+                δ = norm(d_vec)
+                
+                # Average 1/aP at face (harmonic mean)
+                aP_f = 2.0 / ((1.0/aP[owner_idx]) + (1.0/aP[neighbor_idx]))
+                
+                # Pressure difference
+                Δp = p[neighbor_idx] - p[owner_idx]
+                
+                # Rhie-Chow correction vector
+                correction = (1.0/aP_f) * (Δp/δ) * n
+                
+                # Apply correction to interpolated velocity
+                U_f -= correction
+            end
+            
+            # Calculate flux through face
+            flux = dot(U_f, face.normal) * face.area
+            
+            # Add to divergence
+            divergence_data[owner_idx] += flux
             divergence_data[neighbor_idx] -= flux
+        else 
+            # Boundary face handling
+            bc_patch_name = get_boundary_patch_name(face_idx, mesh)
+            
+            if !isempty(bc_patch_name) && haskey(field.boundary_conditions, bc_patch_name)
+                bc = field.boundary_conditions[bc_patch_name]
+                
+                # Get boundary velocity based on BC type
+                U_b = if bc isa CFDCore.DirichletBC
+                    # Fixed value BC
+                    SVector{length(field.data[1]), TF}(bc.value(face.center..., 0.0))
+                elseif bc isa CFDCore.NeumannBC
+                    # Zero gradient or fixed gradient
+                    field.data[owner_idx]
+                else
+                    # Default to field value
+                    field.data[owner_idx]
+                end
+                
+                flux = dot(U_b, face.normal) * face.area
+                divergence_data[owner_idx] += flux
+            else
+                # If no specific BC, use cell center value
+                flux = dot(field.data[owner_idx], face.normal) * face.area
+                divergence_data[owner_idx] += flux
+            end
         end
     end
 
+    # Scale by cell volume to get divergence
     for i in 1:n_cells
         divergence_data[i] /= mesh.cells[i].volume
     end
@@ -116,6 +238,28 @@ function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, sch
     return CFDCore.ScalarField(Symbol(string(field.name)*"_div"), mesh, divergence_data, field.boundary_conditions, nothing, nothing)
 end
 
+# Helper function to calculate face interpolation weight
+function face_distance_weight(face, owner_cell, neighbor_cell)
+    # Distance from owner to face center
+    d_owner = norm(face.center - owner_cell.center)
+    
+    # Distance from neighbor to face center
+    d_neighbor = norm(face.center - neighbor_cell.center)
+    
+    # Linear interpolation weight
+    return d_neighbor / (d_owner + d_neighbor)
+end
+
+# Helper function to get boundary patch name for a face
+function get_boundary_patch_name(face_idx, mesh)
+    for (patch_name, faces_indices) in mesh.boundaries
+        if face_idx in faces_indices
+            return patch_name
+        end
+    end
+    return ""
+end
+
 function laplacian(γ_val::Number, field::ScalarField{TF, NF, M}, scheme::AbstractLaplacianScheme; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
     mesh = field.mesh
     n_cells = length(mesh.cells)
diff --git a/src/Numerics/fvm.jl b/src/Numerics/fvm.jl
index 16ea375..af5824f 100644
--- a/src/Numerics/fvm.jl
+++ b/src/Numerics/fvm.jl
@@ -28,8 +28,12 @@ end
     laplacian(gamma_field::ScalarField, field::ScalarField)
 
 Constructs the sparse matrix `A` and source vector `b` for the implicit discretization
-of the Laplacian term: ∇ ⋅ (γ∇φ).
-The equation is of the form Aφ = b.
+of the Laplacian term: -∇ ⋅ (γ∇φ).
+
+IMPORTANT: This function returns the matrix for -∇²φ (negative Laplacian), which is
+the standard FVM convention. To solve ∇²φ = f, you need to solve -A*φ = f.
+
+The equation is of the form Aφ = b, where A represents -∇·(γ∇φ).
 
 Handles Dirichlet and Neumann boundary conditions.
 Includes non-orthogonal correction for skewed meshes.
@@ -51,20 +55,27 @@ function laplacian(gamma_val::Number, phi_field::ScalarField; current_time::Floa
     V = Float64[] # Values for sparse matrix
     b = zeros(Float64, num_cells)
 
+    # Check mesh quality
+    max_non_orthogonality = 0.0
+    has_significant_non_orthogonality = false
+
     # Iterate over all faces
     for face_idx in 1:length(mesh.faces)
         face = mesh.faces[face_idx]
         owner_cell_idx = face.owner  # Already 1-based indexing
         
         # Face normal vector (points from owner to neighbor)
-        # S_f_vec = face.normal * face.area # Not directly used in this formulation yet
+        S_f_vec = face.normal * face.area  # Area vector
+        mag_S_f = face.area # Area magnitude
         
         if face.boundary
-            # Boundary face
+            # Boundary face handling
             bc = nothing
             # Find which boundary patch this face belongs to
+            bc_patch_name = ""
             for (patch_name, faces_indices) in mesh.boundaries
                 if face_idx in faces_indices
+                    bc_patch_name = patch_name
                     if haskey(phi_field.boundary_conditions, patch_name)
                         bc = phi_field.boundary_conditions[patch_name]
                         break
@@ -77,16 +88,17 @@ function laplacian(gamma_val::Number, phi_field::ScalarField; current_time::Floa
                 @warn "Face $face_idx is a boundary face but no BC found. Using default Neumann BC."
             end
 
-            # Distance from owner cell center to face center (approximation for d_perp)
+            # Distance from owner cell center to face center (perpendicular distance)
             dist_to_face = norm(face.center - mesh.cells[owner_cell_idx].center) 
             dist_to_face = max(dist_to_face, 1e-9) # Avoid division by zero
 
+            # Diffusion coefficient for this face
             coeff_bc = gamma_val * face.area / dist_to_face
 
             if bc isa DirichletBC
-                # Contribution to A[P,P] * φ_P = ... - γ * φ_face / δ_P * Area_f
-                # A_pp contribution: - γ * Area_f / δ_P
-                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, -coeff_bc)
+                # For Laplacian: diagonal gets positive contribution (standard FVM formulation)
+                # A_pp contribution: γ * Area_f / δ_P
+                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, coeff_bc)
                 
                 # Source term part: γ * bc.value * Area_f / δ_P
                 b[owner_cell_idx] += coeff_bc * bc.value(face.center..., current_time)
@@ -94,7 +106,6 @@ function laplacian(gamma_val::Number, phi_field::ScalarField; current_time::Floa
             elseif bc isa NeumannBC
                 # Gradient boundary condition: (∇φ ⋅ n)_face = g_spec
                 # Flux term: γ * g_spec * Area_f
-                # This is a source term for the owner cell equation.
                 specified_gradient_val = bc.gradient(face.center..., current_time) # This is dφ/dn
                 b[owner_cell_idx] += gamma_val * specified_gradient_val * face.area
                 
@@ -106,20 +117,31 @@ function laplacian(gamma_val::Number, phi_field::ScalarField; current_time::Floa
             end
 
         else
-            # Internal face
+            # Internal face handling
             neighbor_cell_idx = face.neighbor  # Already 1-based indexing
 
+            # Vector from owner to neighbor
             d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
-            dist_centers = norm(d_vec)
-            dist_centers = max(dist_centers, 1e-9)
+            dist_centers = norm(d_vec)  # Distance between cell centers
+            dist_centers = max(dist_centers, 1e-9)  # Avoid division by zero
             
-            # Face normal vector
-            n_f = face.normal / norm(face.normal)  # Unit normal
+            # Calculate non-orthogonality angle
+            unit_d = d_vec / dist_centers  # Unit vector from P to N
+            unit_n = face.normal / norm(face.normal)  # Unit normal vector
+            cos_angle = dot(unit_d, unit_n)
+            angle = acos(min(1.0, max(-1.0, cos_angle))) * 180 / π
+            non_orthogonality = 90.0 - angle
             
-            if non_orthogonal_correction
+            # Update max non-orthogonality
+            max_non_orthogonality = max(max_non_orthogonality, abs(non_orthogonality))
+            if abs(non_orthogonality) > 5.0
+                has_significant_non_orthogonality = true
+            end
+            
+            if non_orthogonal_correction && abs(non_orthogonality) > 5.0
                 # Non-orthogonal correction
                 # Decompose d_vec into orthogonal and non-orthogonal parts
-                d_orthogonal = dot(d_vec, n_f) * n_f
+                d_orthogonal = dot(d_vec, unit_n) * unit_n
                 d_non_orthogonal = d_vec - d_orthogonal
                 
                 # Orthogonal coefficient (implicit part)
@@ -128,40 +150,49 @@ function laplacian(gamma_val::Number, phi_field::ScalarField; current_time::Floa
                 coeff_orthogonal = gamma_val * face.area / dist_orthogonal
                 
                 # Owner cell (P) contributions - orthogonal part
-                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, -coeff_orthogonal)
-                push!(I, owner_cell_idx); push!(J, neighbor_cell_idx); push!(V, coeff_orthogonal)
+                # For Laplacian: diagonal positive, off-diagonal negative (standard FVM convention)
+                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, coeff_orthogonal)
+                push!(I, owner_cell_idx); push!(J, neighbor_cell_idx); push!(V, -coeff_orthogonal)
                 
                 # Neighbor cell (N) contributions - orthogonal part
-                push!(I, neighbor_cell_idx); push!(J, neighbor_cell_idx); push!(V, -coeff_orthogonal)
-                push!(I, neighbor_cell_idx); push!(J, owner_cell_idx); push!(V, coeff_orthogonal)
+                push!(I, neighbor_cell_idx); push!(J, neighbor_cell_idx); push!(V, coeff_orthogonal)
+                push!(I, neighbor_cell_idx); push!(J, owner_cell_idx); push!(V, -coeff_orthogonal)
                 
                 # Non-orthogonal correction (explicit part - added to source term)
-                if norm(d_non_orthogonal) > 1e-12
-                    # Calculate gradient at face using current field values
+                if norm(d_non_orthogonal) / dist_centers > 0.1  # Significant non-orthogonality
+                    # Iterative approach - use interpolated gradient at face
+                    # This is explicit in SIMPLE algorithm iterations
                     grad_face = (phi_field.data[neighbor_cell_idx] - phi_field.data[owner_cell_idx]) / dist_centers
                     
-                    # Non-orthogonal flux correction
-                    non_orthogonal_flux = gamma_val * face.area * grad_face * norm(d_non_orthogonal) / dist_centers
+                    # Non-orthogonal flux correction - dot product with non-orthogonal component
+                    non_orthogonal_flux = gamma_val * face.area * grad_face * dot(d_non_orthogonal, unit_n) / dist_orthogonal
                     
-                    # Add to source terms (explicit correction)
-                    b[owner_cell_idx] -= non_orthogonal_flux
-                    b[neighbor_cell_idx] += non_orthogonal_flux
+                    # Apply correction via source terms
+                    b[owner_cell_idx] -= non_orthogonal_flux   # Subtract from owner
+                    b[neighbor_cell_idx] += non_orthogonal_flux  # Add to neighbor
                 end
             else
                 # Standard central difference coefficient for orthogonal meshes
                 coeff_internal = gamma_val * face.area / dist_centers
 
-                # Owner cell (P) contributions
-                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, -coeff_internal)
-                push!(I, owner_cell_idx); push!(J, neighbor_cell_idx); push!(V, coeff_internal)
+                # Add entries with correct signs (standard FVM convention)
+                # For Laplacian: diagonal positive, off-diagonal negative
+                push!(I, owner_cell_idx); push!(J, owner_cell_idx); push!(V, coeff_internal)
+                push!(I, owner_cell_idx); push!(J, neighbor_cell_idx); push!(V, -coeff_internal)
 
                 # Neighbor cell (N) contributions
-                push!(I, neighbor_cell_idx); push!(J, neighbor_cell_idx); push!(V, -coeff_internal)
-                push!(I, neighbor_cell_idx); push!(J, owner_cell_idx); push!(V, coeff_internal)
+                push!(I, neighbor_cell_idx); push!(J, neighbor_cell_idx); push!(V, coeff_internal)
+                push!(I, neighbor_cell_idx); push!(J, owner_cell_idx); push!(V, -coeff_internal)
             end
         end
     end
     
+    # Store mesh non-orthogonality info in mesh object for later use
+    # This requires a way to cache this info - for now, just print a warning
+    if has_significant_non_orthogonality
+        @info "Mesh has significant non-orthogonality (max = $(round(max_non_orthogonality, digits=2)) degrees). Using correction."
+    end
+    
     # The sparse constructor automatically sums V values for duplicate (I,J) pairs.
     A = sparse(I, J, V, num_cells, num_cells)
 
diff --git a/src/Solvers/incompressibleSolvers.jl b/src/Solvers/incompressibleSolvers.jl
index bb08576..4d70fbd 100644
--- a/src/Solvers/incompressibleSolvers.jl
+++ b/src/Solvers/incompressibleSolvers.jl
@@ -19,6 +19,17 @@ Base.@kwdef struct PISOSettings
     # Relaxation factors could be added here if desired for PISO
 end
 
+# SIMPLE control parameters
+Base.@kwdef struct SIMPLESettings
+    n_non_orthogonal_correctors::Int = 0 # For non-orthogonal mesh corrections
+    max_iterations::Int = 1000          # Maximum iterations
+    tolerance::Float64 = 1e-6           # Convergence tolerance
+    alpha_p::Float64 = 0.3              # Pressure relaxation factor
+    alpha_u::Float64 = 0.7              # Velocity relaxation factor
+    residual_output_frequency::Int = 10 # How often to output residuals
+    converged::Bool = false             # Flag for convergence
+end
+
 """
     solve_piso_step!(
         U_field::VectorField,         # Velocity field (input/output)
@@ -339,4 +350,431 @@ function compute_face_volumetric_fluxes!(
     # println("compute_face_volumetric_fluxes! executed using fvc.interpolate_vector_to_faces.") # For debugging
 end
 
-end # module IncompressibleSolvers
+"""
+    solve_simple!(
+        U_field::VectorField,         # Velocity field (input/output)
+        p_field::ScalarField,         # Pressure field (input/output)
+        phi_face_flux::ScalarField,   # Face mass flux (U.data ⋅ S_f) (output)
+        rho::Float64,                 # Density
+        nu::Float64,                  # Kinematic viscosity
+        mesh::AbstractMesh,
+        velocity_linear_solver,       # Linear solver for velocity equations
+        pressure_linear_solver,       # Linear solver for pressure equation
+        simple_settings::SIMPLESettings = SIMPLESettings()
+    )
+
+Performs steady-state solution using the SIMPLE algorithm for incompressible flow.
+SIMPLE (Semi-Implicit Method for Pressure-Linked Equations) is used for steady-state
+problems and incorporates under-relaxation to improve convergence stability.
+
+The algorithm handles:
+- Momentum prediction with relaxation
+- Pressure correction with Rhie-Chow interpolation
+- Velocity correction
+- Flux correction
+- Convergence monitoring
+"""
+function solve_simple!(
+    U_field::VectorField,
+    p_field::ScalarField,
+    phi_face_flux::ScalarField,
+    rho::Float64,
+    nu::Float64,
+    mesh::AbstractMesh,
+    velocity_linear_solver,
+    pressure_linear_solver,
+    simple_settings::SIMPLESettings = SIMPLESettings()
+)
+    num_cells = length(mesh.cells)
+    num_faces = length(mesh.faces)
+    mu = rho * nu
+    
+    # Initialize fields and storage
+    # For U and p we need to store the values from previous iteration
+    U_prev_iter = deepcopy(U_field.data)
+    p_prev_iter = deepcopy(p_field.data)
+    
+    # Initialize residuals history
+    continuity_residuals = Float64[]
+    momentum_x_residuals = Float64[]
+    momentum_y_residuals = Float64[]
+    momentum_z_residuals = length(eltype(U_field.data)) == 3 ? Float64[] : nothing
+    
+    # Initialize diagonal coefficient arrays for momentum equations
+    # This will be used for Rhie-Chow interpolation in pressure equation
+    A_U_diag = Vector{eltype(rho)}(undef, num_cells)
+    
+    # Setup face interpolation weights for Rhie-Chow
+    # These will be used to interpolate cell-centered pressure gradients and diagonal coefficients
+    face_weights = Vector{Float64}(undef, num_faces)
+    
+    # Pre-compute face interpolation weights
+    for face_idx in 1:num_faces
+        face = mesh.faces[face_idx]
+        if !face.boundary
+            # Internal face: get owner and neighbor cells
+            owner_cell_idx = face.owner
+            neighbor_cell_idx = face.neighbor
+            
+            # Vector from owner to neighbor
+            d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
+            dist_centers = norm(d_vec)
+            
+            # Linear interpolation weight based on distance
+            # weight_f = distance_owner_to_face / distance_owner_to_neighbor
+            face_center = face.center
+            dist_owner_to_face = norm(face_center - mesh.cells[owner_cell_idx].center)
+            
+            # Linear interpolation weight - what portion is from neighbor
+            face_weights[face_idx] = dist_owner_to_face / dist_centers
+        else
+            # Boundary face: weight is 1.0 (100% owner cell)
+            face_weights[face_idx] = 0.0
+        end
+    end
+    
+    # Initialize residual monitors
+    initial_residual = -1.0  # Will be set on first iteration
+    current_residual = 1.0   # Start with high residual
+    
+    # Begin SIMPLE iterations
+    for iter in 1:simple_settings.max_iterations
+        # 1. Store current fields for reference and relaxation
+        U_prev_iter = deepcopy(U_field.data)
+        p_prev_iter = deepcopy(p_field.data)
+        
+        # 2. Momentum Predictor Step
+        # Solve discretized momentum equation to get U*
+        # The fvm form is: A_U * U = H(U) - grad(p)
+        # Where A_U has diagonal and off-diagonal parts: A_U_diag and A_U_offdiag
+        
+        # Loop through dimensions and solve momentum equation component-wise
+        dims = length(eltype(U_field.data)) # 2 or 3
+        momentum_residuals = zeros(dims)
+        
+        for d in 1:dims
+            # Extract component of velocity for this dimension
+            U_component = ScalarField(
+                Symbol("U$d"),
+                mesh,
+                [U_field.data[i][d] for i in 1:num_cells],
+                # Use velocity field BCs for this component
+                Dict(patch => U_field.boundary_conditions[patch] for patch in keys(U_field.boundary_conditions))
+            )
+            
+            # Create momentum matrix for this component
+            # 1. Add convection term: fvm.div(phi, U_component)
+            # We use face fluxes (phi) from previous iteration
+            A_conv, b_conv = fvm.div(phi_face_flux, U_component) 
+            
+            # 2. Add diffusion term: fvm.laplacian(nu, U_component)
+            A_diff, b_diff = fvm.laplacian(mu, U_component)
+            
+            # 3. Combine convection and diffusion
+            A_U_component = A_conv + A_diff
+            b_U_component = b_conv + b_diff
+            
+            # 4. Compute pressure gradient source term for this component
+            # Use stored pressure from last iteration
+            grad_p = fvc.grad(p_field)  # Cell-centered pressure gradient
+            
+            # Add pressure gradient as explicit source term
+            # Negative sign because it moves to RHS: -∇p
+            for i in 1:num_cells
+                b_U_component[i] -= mesh.cells[i].volume * grad_p.data[i][d]
+            end
+            
+            # 5. Store diagonal for Rhie-Chow correction later
+            # Save the diagonal coefficient for this component
+            # All components have same diagonal for incompressible flow
+            if d == 1
+                for i in 1:num_cells
+                    A_U_diag[i] = A_U_component[i, i]
+                end
+            end
+            
+            # 6. Apply under-relaxation
+            # U = α_u * U_new + (1-α_u) * U_old
+            # This is implemented as: A_U_diag' = A_U_diag/α_u
+            # and RHS' = RHS + (A_U_diag/α_u - A_U_diag)*U_old
+            for i in 1:num_cells
+                # Store original diagonal
+                A_diag_original = A_U_component[i, i]
+                
+                # Modify diagonal for relaxation
+                A_U_component[i, i] = A_diag_original / simple_settings.alpha_u
+                
+                # Add relaxation term to RHS
+                # (1-α)/α * A_diag * U_old
+                relaxation_factor = (1.0 - simple_settings.alpha_u) / simple_settings.alpha_u
+                b_U_component[i] += relaxation_factor * A_diag_original * U_component.data[i]
+            end
+            
+            # 7. Solve linear system for this velocity component
+            # A_U_component * U_component_new = b_U_component
+            U_component_new = copy(U_component.data)  # Initial guess for linear solver
+            solver_result = solve!(velocity_linear_solver, A_U_component, U_component_new, b_U_component)
+            
+            # Store residual for this component
+            momentum_residuals[d] = solver_result.residual
+            
+            # 8. Update velocity field with solved component
+            for i in 1:num_cells
+                # Create new velocity vector with updated component
+                new_vec = MVector{dims, Float64}(U_field.data[i])
+                new_vec[d] = U_component_new[i]
+                U_field.data[i] = SVector{dims, Float64}(new_vec)
+            end
+        end
+        
+        # Store momentum residuals
+        push!(momentum_x_residuals, momentum_residuals[1])
+        push!(momentum_y_residuals, momentum_residuals[2])
+        if dims == 3 && !isnothing(momentum_z_residuals)
+            push!(momentum_z_residuals, momentum_residuals[3])
+        end
+        
+        # 3. Calculate face fluxes from predicted velocity field (without Rhie-Chow correction yet)
+        # Get cell-centered velocity field from momentum predictor
+        compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
+        
+        # 4. Pressure Correction Step
+        # Now we solve for pressure correction (p') to enforce continuity
+        
+        # 4.1 Setup the pressure correction equation
+        # The pressure correction equation is: ∇⋅( (1/a_p) ∇p' ) = ∇⋅(U*)
+        # where a_p is the diagonal coefficient of momentum equation
+        
+        # Create temporary field for storing pressure correction
+        p_prime_data = zeros(Float64, num_cells)
+        
+        # Need homogeneous Neumann BC's for pressure correction
+        p_prime_bc = Dict{String, AbstractBoundaryCondition}()
+        for patch_name in keys(mesh.boundaries)
+            p_prime_bc[patch_name] = NeumannBC((x,y,z,t) -> 0.0)
+        end
+        p_prime_field = ScalarField(:p_prime, mesh, p_prime_data, p_prime_bc)
+        
+        # Calculate diffusion coefficient for pressure equation: (1/a_p)
+        # We need to express this as a face field for the fvm.laplacian operator
+        # First approximate as cell field (inverse of momentum diagonal)
+        gamma_p_prime_cell = 1.0 ./ A_U_diag
+        
+        # 4.2. Construct the pressure correction equation with Rhie-Chow interpolation
+        # The key part of Rhie-Chow is to calculate face fluxes that prevent checker-boarding
+        # Interpolated cell pressure gradients to faces
+        grad_p = fvc.grad(p_field)  # Cell-centered pressure gradient
+        
+        # Create Rhie-Chow corrected flux field
+        phi_star_rhiechow = copy(phi_face_flux.data)
+        
+        # Apply Rhie-Chow correction for internal faces
+        for face_idx in 1:num_faces
+            face = mesh.faces[face_idx]
+            
+            if !face.boundary
+                # Get owner and neighbor cells
+                owner_cell_idx = face.owner
+                neighbor_cell_idx = face.neighbor
+                
+                # Get interpolation weight for this face
+                weight = face_weights[face_idx]
+                
+                # Face normal vector
+                S_f = face.normal * face.area  # Area vector (magnitude * direction)
+                
+                # 1. Interpolate 1/a_p to face
+                gamma_f = (1.0 - weight) * gamma_p_prime_cell[owner_cell_idx] + 
+                          weight * gamma_p_prime_cell[neighbor_cell_idx]
+                
+                # 2. Interpolate pressure gradient to face
+                grad_p_f = (1.0 - weight) * grad_p.data[owner_cell_idx] + 
+                           weight * grad_p.data[neighbor_cell_idx]
+                
+                # 3. Calculate Rhie-Chow correction to face flux
+                # Remove the interpolated pressure gradient component and add explicitly calculated one
+                # phi_star_rhiechow = phi_star - gamma_f * (grad_p_interp - grad_p_explicit) ⋅ S_f
+                
+                # Direct pressure difference along cell-centers line
+                d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
+                dist_centers = norm(d_vec)
+                unit_d = d_vec / dist_centers
+                
+                # Explicit face normal gradient based on pressure difference
+                dp_dn_explicit = (p_field.data[neighbor_cell_idx] - p_field.data[owner_cell_idx]) / dist_centers
+                
+                # Project the interpolated pressure gradient onto face normal
+                dp_dn_interp = dot(grad_p_f, unit_d)
+                
+                # Apply the Rhie-Chow correction to face flux
+                # Add the difference between explicit and interpolated pressure gradient
+                rhiechow_correction = gamma_f * (dp_dn_explicit - dp_dn_interp) * face.area
+                phi_star_rhiechow[face_idx] += rhiechow_correction
+            end
+        end
+        
+        # 4.3 Setup and solve the pressure correction equation
+        # This uses the Rhie-Chow corrected fluxes for the right-hand side
+        
+        # Construct the coefficient matrix for pressure correction
+        A_p_prime, b_p_prime = fvm.laplacian(1.0, p_prime_field)
+        
+        # Modify the matrix diagonal coefficients using the cell 1/a_p values
+        # This essentially applies the diffusivity field (1/a_p) to the Laplacian
+        for i in 1:num_cells
+            for j in 1:num_cells
+                if i == j
+                    # Scale diagonal by harmonic mean of gamma_p_prime
+                    # No need to modify here as we're applying gamma in Rhie-Chow and the final correction
+                else
+                    # Scale off-diagonal by harmonic mean
+                    # No need to modify for the same reason
+                end
+            end
+        end
+        
+        # Calculate the RHS: divergence of Rhie-Chow corrected flux field
+        phi_star_rhiechow_field = ScalarField(:phi_star_rhiechow, mesh, phi_star_rhiechow, Dict())
+        div_phi_star = fvc.div(phi_star_rhiechow_field)
+        
+        # RHS of pressure correction equation is negative divergence
+        # Because we want p' such that div(U_new) = 0
+        # And div(U*) + div(U') = 0, so div(U') = -div(U*)
+        for i in 1:num_cells
+            b_p_prime[i] = -div_phi_star.data[i] * mesh.cells[i].volume
+        end
+        
+        # 4.4 Solve pressure correction equation
+        solver_result = solve!(pressure_linear_solver, A_p_prime, p_prime_data, b_p_prime)
+        
+        # Store continuity residual
+        continuity_residual = solver_result.residual
+        push!(continuity_residuals, continuity_residual)
+        
+        # 4.5 Apply pressure under-relaxation
+        # p = p_old + alpha_p * p'
+        for i in 1:num_cells
+            p_field.data[i] = p_prev_iter[i] + simple_settings.alpha_p * p_prime_data[i]
+        end
+        
+        # 5. Velocity Correction
+        # Now correct velocity field based on pressure correction
+        # U_new = U* - (1/a_p) * ∇p'
+        
+        # Calculate gradient of pressure correction
+        grad_p_prime = fvc.grad(p_prime_field)
+        
+        # Apply velocity correction using momentum equation diagonal and pressure correction gradient
+        for i in 1:num_cells
+            # Extract diagonal coefficient for this cell
+            a_p = A_U_diag[i]
+            
+            # Calculate velocity correction: U' = -(1/a_p) * ∇p'
+            U_correction = -gamma_p_prime_cell[i] * grad_p_prime.data[i]
+            
+            # Update velocity: U_new = U* + U'
+            # Note: No additional under-relaxation needed here since p' is already relaxed
+            U_field.data[i] += U_correction
+        end
+        
+        # 6. Update face fluxes
+        # Calculate face fluxes from corrected velocity
+        compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
+        
+        # 7. Apply Rhie-Chow correction to face fluxes to ensure mass conservation
+        # The corrected flux is: phi = phi_interpolated + phi_rhie_chow
+        for face_idx in 1:num_faces
+            face = mesh.faces[face_idx]
+            
+            if !face.boundary
+                # Get owner and neighbor cells
+                owner_cell_idx = face.owner
+                neighbor_cell_idx = face.neighbor
+                
+                # Interpolation weight for this face
+                weight = face_weights[face_idx]
+                
+                # Interpolate 1/a_p to face
+                gamma_f = (1.0 - weight) * gamma_p_prime_cell[owner_cell_idx] + 
+                          weight * gamma_p_prime_cell[neighbor_cell_idx]
+                
+                # Direct pressure correction gradient along cell-centers line
+                d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
+                dist_centers = norm(d_vec)
+                dp_prime_dn = (p_prime_data[neighbor_cell_idx] - p_prime_data[owner_cell_idx]) / dist_centers
+                
+                # Rhie-Chow correction to flux based on pressure correction
+                rhiechow_correction = -gamma_f * dp_prime_dn * face.area
+                
+                # Apply Rhie-Chow correction to face flux
+                phi_face_flux.data[face_idx] += rhiechow_correction
+            end
+        end
+        
+        # 8. Check for convergence
+        # Track primary residual (continuity equation residual)
+        if iter == 1
+            # Initialize initial residual
+            initial_residual = continuity_residual
+        end
+        
+        # Calculate normalized residual
+        # We use the maximum of initial residual and a small value to avoid division by zero
+        normalized_residual = continuity_residual / max(initial_residual, 1e-10)
+        current_residual = normalized_residual
+        
+        # Print residuals at specified frequency
+        if iter % simple_settings.residual_output_frequency == 0 || iter == 1
+            @printf("SIMPLE Iter %4d: Continuity = %.4e, Ux = %.4e, Uy = %.4e", 
+                    iter, normalized_residual, momentum_residuals[1], momentum_residuals[2])
+            if dims == 3 && !isnothing(momentum_z_residuals)
+                @printf(", Uz = %.4e", momentum_residuals[3])
+            end
+            println()
+        end
+        
+        # Check convergence
+        if normalized_residual < simple_settings.tolerance
+            @info "SIMPLE algorithm converged after $iter iterations."
+            
+            # Update settings to indicate convergence
+            simple_settings.converged = true
+            
+            # Final calculation of face fluxes
+            compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
+            
+            # Exit the loop
+            break
+        end
+        
+        # Check max iterations
+        if iter == simple_settings.max_iterations
+            @warn "SIMPLE algorithm did not converge after $(simple_settings.max_iterations) iterations."
+            @warn "Final residual: $normalized_residual (target: $(simple_settings.tolerance))"
+        end
+    end # End of SIMPLE iterations loop
+    
+    # Return results in a structured format
+    result = Dict(
+        "iterations" => length(continuity_residuals),
+        "converged" => simple_settings.converged,
+        "residuals" => Dict(
+            "continuity" => continuity_residuals,
+            "momentum_x" => momentum_x_residuals,
+            "momentum_y" => momentum_y_residuals
+        ),
+        "final_residual" => current_residual
+    )
+    
+    # Add z-momentum residuals if 3D
+    if !isnothing(momentum_z_residuals)
+        result["residuals"]["momentum_z"] = momentum_z_residuals
+    end
+    
+    return result
+end # End solver function
+
+# Export the main solver functions
+export solve_piso_step!, solve_simple!, PISOSettings, SIMPLESettings
+
+end # End module IncompressibleSolvers
