/*--------------------------------*- C++ -*----------------------------------*\
FoamFile
{
    version     2.0;
    format      ascii;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

3
(
    inlet
    {
        type            patch;
        nFaces          1;
        startFace       0;
    }
    outlet
    {
        type            patch;
        nFaces          1;
        startFace       1;
    }
    walls
    {
        type            wall;
        nFaces          4;
        startFace       2;
    }
)

// ************************************************************************* //
