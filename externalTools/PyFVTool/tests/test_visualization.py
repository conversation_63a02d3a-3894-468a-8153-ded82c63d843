# -*- coding: utf-8 -*-

print()
print("TO DO: set up convenient testing for visualization.")
print()
print("For now, all plotting and visualization in the test scripts has been")
print("deactivated. For instance, there is some PyFVTool visualization in the")
print("'test_PyFVTool_basic_test.py' script, which has been commented out.")
print("It is probably very well possible to have silent plotting, just by")
print("leaving out any `plt.show()` statements.")
print()

