# minimalist pyproject.toml

[project]
name = "pyfvtool"
version = "0.3.5"
authors = [
    { name="<PERSON>", email="<EMAIL>" },
    { name="<PERSON>"},
    { name="<PERSON><PERSON> H. V. Werts"}
]
description = "Finite volume toolbox in Python"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Development Status :: Beta",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Mathematics"
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
        "numpy>=1.26.1",
        "scipy",
		"matplotlib"
        ]


[project.urls]
"Homepage" = "https://github.com/FiniteVolumeTransportPhenomena/PyFVTool"
"Bug Tracker" = "https://github.com/FiniteVolumeTransportPhenomena/PyFVTool/issues"

[build-system]
requires = ["setuptools>=61.0", "numpy>=1.26.1", "scipy", "matplotlib"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]
