import numpy as np
from scipy.sparse import csr_array
from scipy.sparse.linalg import spsolve

from .mesh import MeshStructure
from .cell import CellVariable



def solveMatrixPDE(m: MeshStructure, M:csr_array, RHS: np.ndarray,
             externalsolver = None) -> CellVariable:
    """
    Solve the PDE discretized according to the finite volume method    
        
    This solver routine uses the M matrix and RHS right-hand side vector 
    directly. These matrices should be constructed beforehand by combining 
    the different M matrices and RHS vectors generated using the
    xxxTerm routines.
    
    Returns a new CellVariable without changing the input ('old') CellVariable.
    
    Parameters
    ----------
    m: MeshStructure
        Mesh structure
    M: csr_array
        Matrix of the linear system
    RHS: np.ndarray
        Right hand side of the linear system
    externalsolver: function (optional)
        If provided, use an external sparse solver via a function call
        having the same interface as the default solver
        scipy.sparse.linalg.spsolve.
    
    Returns
    -------
    phi: CellVariable
        Solution of the PDE (newly created CellVariable instance)
    
    """

    if externalsolver is None:
        solver = spsolve
    else:
        solver = externalsolver
    phi = solver(M, RHS)
    return CellVariable(m, np.reshape(phi, m.dims+2)) # BCs handled by user



def solvePDE(phi: CellVariable, eqnterms: list, 
              externalsolver = None) -> CellVariable:
    """
    Solve a PDE using the finite volume method
    

    This solver routine constructs the matrix equation based on the terms
    provided, each term being the output of a call to the appropriate
    xxxTerm() function. It requires the matrix equation terms for the
    boundary conditions applied to the solution variable, and the terms
    of the equation are provided as a lists ("sum of the terms = 0")
    
    The provided CellVariable is updated with the solution values, and a
    reference to this one and the same variable is returned. If the 'old' 
    input CellVariable is to be conserved, it should be copied beforehand to
    a separate instance.

    Parameters
    ----------
    phi : CellVariable
        Solution variable subject to the boundary conditions represented by
        bcterm.
    bcterm : tuple(csr_array, np.ndarray)
        Matrix equation terms (M, RHS) representing the boundary conditions,
        i.e. the result from a call to boundaryConditionsTerm
    eqnterms : list
        List of matrix equation terms generated by the xxxTerm functions.
        The elements of the list can either be a tuple (M, RHS), a simple 
        csr_array matrix (M) or a 1D np.ndarray (RHS). These will be added
        up to create both the M and RHS of the matrix equation to be solved.
    externalsolver : function, optional
        If specified, use an external sparse solver via a function call
        having the same interface as the default solver
        `scipy.sparse.linalg.spsolve`. If not specified, this default
        solver will be used.

    Raises
    ------
    TypeError
        If any of the provided terms is not conform to expectations.

    Returns
    -------
    CellVariable
        The updated CellVariable.

    """
    # Presently the bcterm and the eqnterms are simply the objects
    # returned by the respective xxxTerm routines. This is done for
    # simplicity. Later, we might perhaps consider
    # a specific `Term` class, with properties M, RHS and arithmetic
    # magic methods defined suitably. Certain terms will have `None`
    # for either M or RHS etc.
    if externalsolver is None:
        solver = spsolve
    else:
        solver = externalsolver
    
    # Construct BCs Term
    # Mbc, RHSbc = boundaryConditionsTerm(phi.BCs)
    # Retrieve pre-constructed BCs Term
    Mbc, RHSbc = phi._BCsTerm
    
    # Initialize overall cumulative matrix and right-hand side for 
    # matrix equation
    M = Mbc.copy() # need to copy, so that 'bcterm' is protected
    RHS = RHSbc.copy() # need to copy, so that 'bcterm' is protected
    
    # Cumulate all equation terms
    for term in eqnterms:
        if isinstance(term, tuple):
            Mterm, RHSterm = term
            if (Mterm.ndim != 2) or (RHSterm.ndim != 1):
                raise TypeError('Unknown term')
            M += Mterm
            RHS += RHSterm
        elif term.ndim == 1:
            RHS += term
        elif term.ndim == 2:
            M += term
        else:
            raise TypeError('Unknown term')

    # Solve!
    phi_new_values = solver(M, RHS)
    
    # Update phi
    phi._value = np.reshape(phi_new_values, phi.domain.dims+2)
    
    return phi



def solveExplicitPDE(phi_old: CellVariable, 
                     dt: float, 
                     RHS: np.ndarray) -> CellVariable:
    """
    Solve the PDE using an explicit finite volume method.

    Parameters
    ----------
    phi_old: CellVariable
        Solution of the previous time step
    dt: float
        Time step
    RHS: np.ndarray
        Right hand side of the linear system

    
    Returns
    -------
    phi: CellVariable
        Solution of the PDE
    
    """
    
    x = phi_old._value + dt*RHS.reshape(phi_old._value.shape)
    phi = CellVariable(phi_old.domain, 0.0, phi_old.BCs, 
                       BCsTerm_precalc = False)
    phi._value = x
    phi.apply_BCs()
    return phi
