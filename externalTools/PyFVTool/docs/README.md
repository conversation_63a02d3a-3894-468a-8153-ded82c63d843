# PyFVTool documentation

In the present, pre-release state of PyFVTool, documentation is scarce, and no automated documentation management (Sphinx, for example) has been set up.

Docstrings are being added to the code, enabling to use the `help` function in interactive Python sessions. 

The `examples` and `examples-notebooks` folders of the PyFVTool repository contain, well, examples of how to set up finite-volume solvers using PyFVTool for certain textbook cases. These examples also serve for testing PyFVTool, as they often include also the known analytic solutions for such cases.

Further documents will be collected here. Initially as simple MarkDown text files, which can later be integrated into automated documentation management.

