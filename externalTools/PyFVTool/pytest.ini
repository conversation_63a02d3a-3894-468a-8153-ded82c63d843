# Initial pytest configuration file for PyFVTool
#
# This allows tests to be run by simpling invoking
#     `pytest`
# from the command line, when in the `pyfvtool` project root directory.
#
# Configuration is fine-tuned as we go. The intention is to keep
# this configuration file as minimalist as possible.
#
# Running tests requires to have installed
# - `pytest`
#
# The present pytest configuration scans all directories for files
# named `test_*.py` or `*_test.py. These
# will be considered "tests".
#
# pytest runs tests from `unittest`, and furthermore runs all "test"
# scripts found. 
#
# **PLEASE NOTE!** If a script creates matplotlib graph windows, 
# these windows need to be closed manually in order for test execution
# to continue.
#
[pytest]
minversion = 6.0
