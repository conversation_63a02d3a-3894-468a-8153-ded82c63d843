{"cells": [{"cell_type": "markdown", "id": "6ec88f25", "metadata": {}, "source": ["### Poisson equation example\n", "\n", "The generalized form of the equations solved in this package looks like, \n", "    $$ \\alpha \\frac{\\partial \\varphi}{\\partial t} + \\nabla\\cdot\\left(\\vec{u}\\varphi\\right) + \\nabla\\cdot\\left(-D\\nabla\\varphi\\right) + \\beta \\varphi = \\gamma $$\n", "\n", "with boundary condition,\n", "    $$ a\\nabla\\varphi\\cdot \\vec{e} + b\\varphi = c $$.\n", "\n", "\n", "The inhomogeneous Poisson equation\n", "    $$ \\nabla^2 \\varphi + s\\left(\\vec{x}\\right) = 0 $$\n", "\n", "can be generalized to a simple 1D case,\n", "\n", "\\begin{align}\n", "    \\frac{\\partial^2 \\varphi}{\\partial ^2 x}  + s\\left(x\\right) &= 0 \\\\\n", "    \\varphi\\left(x_L\\right) &= 0 \\\\\n", "    \\frac{\\partial \\varphi}{\\partial x}|_{x_R} &= 0\n", "\\end{align}    \n", "\n", "The corresponding equation in our form has \n", "\\begin{align}\n", "    D = 1.0, \\vec{u} &= \\vec{0}  \\\\\n", "    \\alpha = 0, \\beta = 0, \\gamma &= s  \\\\\n", "    a_L = 0, b_L = 1, c_L &= 0 \\\\\n", "    a_R = 1, b_R = 0, c_R &= 0 \\\\    \n", "\\end{align}\n", "\n", "see this link\n", "http://scicomp.stackexchange.com/questions/8577/peculiar-error-when-solving-the-poisson-equation-on-a-non-uniform-mesh-1d-only\n", "\n", "Strange behavior when change the number of grids from even to odd\n", "Wrong results does not always mean that the code has bugs.\n", "\n", "Wrong use of the code can also give you wrong results\n", "\n", " Written by <PERSON>\n", " Last checked: June 2021\n", " \n", " Ported to python and full rewrite by <PERSON> June, 2023\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a57b83ab", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "f43c27c6", "metadata": {}, "outputs": [], "source": ["# explicit imports as an alternative to `import pyfvtool as pf`\n", "from pyfvtool import Grid1D\n", "from pyfvtool import cellLocations, CellVariable\n", "from pyfvtool import faceLocations, FaceVariable\n", "from pyfvtool import BoundaryConditions\n", "from pyfvtool import diffusionTerm\n", "from pyfvtool import constantSourceTerm\n", "from pyfvtool import boundaryConditionsTerm\n", "from pyfvtool import solveMatrixPDE\n", "from pyfvtool import visualizeCells"]}, {"cell_type": "code", "execution_count": 3, "id": "fcdd31ce", "metadata": {}, "outputs": [], "source": ["# Define the domain and create a mesh structure\n", "L = 20      # domain length\n", "# Nx = 10000  # number of cells (original)\n", "Nx = 100000  # number of cells (test)\n", "m = Grid1D(Nx, L)\n", "\n", "# Create the boundary condition structure\n", "BC = BoundaryConditions(m)  # Neumann boundary conditions \n", "\n", "# left-boundary:\n", "BC.left.a[:] = 0.0\n", "BC.left.b[:] = 1.0\n", "BC.left.c[:] = 0.0\n", "\n", "# right-boundary (<PERSON> is the default in this package)\n", "BC.right.a[:] = 1.00 \n", "BC.right.b[:] = 0.0 \n", "BC.right.c[:] = 0.0\n", "\n", "x = m.cellcenters.x-0.5*L; # shift the domain to [-10,10]\n", "\n", "# define the transfer coeffs\n", "D_val = 1;\n", "D = FaceVariable(m, D_val);\n", "\n", "# define source term\n", "def rho(x):\n", "    # rho = @(x)(-1.0*((x>=-1.0)&(x<=0))+((x>0)&(x<=1)));\n", "    return -1.0*((x>=-1.0)*(x<=0))+(x>0)*(x<=1)\n", "\n", "s1 = constantSourceTerm(CellVariable(m, rho(x)))\n", "Mdiff = diffusionTerm(D)\n", "Mbc, RHSbc = boundaryConditionsTerm(BC)\n", "\n", "M = Mdiff + Mbc\n", "RHS = -s1+RHSbc\n", "c = solveMatrixPDE(m, M, RHS)"]}, {"cell_type": "code", "execution_count": 4, "id": "ce6da8d0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# visualization\n", "plt.figure()\n", "plt.plot(x, c.value, 'k-', label='Potential distr. (num. sol.)')\n", "plt.plot(x, rho(x), 'b-', label='Charge distr.')\n", "plt.xlabel('Length [m]')\n", "plt.ylabel(r'$\\varphi$')\n", "plt.legend(fontsize=12, loc='best');"]}, {"cell_type": "code", "execution_count": 5, "id": "faa1fc4a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Utility to peek at solution \n", "plt.figure()\n", "visualizeCells(phi=c, vmin=0.0, vmax=0.0, cmap=\"viridis\", shading=\"gouraud\")"]}, {"cell_type": "code", "execution_count": null, "id": "e9dc1e12", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}