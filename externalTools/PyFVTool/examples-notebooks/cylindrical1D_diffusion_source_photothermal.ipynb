{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Heat diffusion with a (photothermal) heating source in 1D-cylindrical geometry\n", "\n", "MW 230817, 240503"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "A weakly focussed Gaussian laser beam traverses a medium (typically a liquid containing a dye or other light-absorbing substances). The laser light is partially absorbed by the medium, leading to local heating (Naperian absorption coefficient $\\alpha$). A quantitative idea of the temperature distribution in this system can be obtained by a mathematical model.\n", "\n", "Near the focal point, the width of the Gaussian laser beam may assumed to be constant, in which case we can write a heat equation in 1D cylindrical geometry, where the temperature is considered to spatially vary only as the radial position $r$ with respect to the propagation axis of the light. There is also a time dependence, since we \"switch on\" the laser at $t = 0$.\n", "\n", "With $T$ the change in temperature and $\\dot q(r)$ the radial distribution of photothermal heat generation, the equations modeling this system can be written as follows [1]\n", "\n", "$$\n", "c \\rho \\frac{\\partial T(r, t)}{\\partial t} = \\dot q(r) + k \\nabla^2 T(r, t)\n", "$$\n", "\n", "$$\n", "r \\in [0, +\\infty\\rangle\n", "$$\n", "\n", "$$\n", "T(r, t=0) = 0\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The heat source term $\\dot q(r)$ is obtained from the laser beam intensity $S(r)$ and the absorption coefficient $\\alpha$.\n", "\n", "$$ \\dot q(r) = \\alpha S(r) = \\frac{2\\alpha P}{\\pi w^2}\n", "\\exp \\left( \\frac{-2r^2}{w^2}\\right)\n", "$$\n", "\n", "**to do.** Give more attention to absorption coefficent $\\alpha = \\ln(10)\\textrm{OD}/l$, see [2]. For now, $\\alpha$ will be set to $1$ throughout."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Literature references\n", "\n", "[1] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, J. M. (1982). Laser-induced thermal lens effect: a new theoretical model. Applied optics, 21(9), 1663-1669.\n", "\n", "[2] <PERSON>, <PERSON><PERSON>, & <PERSON>, <PERSON> (1984). Comparison of models describing the thermal lens effect. Applied optics, 23(3), 476-481.\n", "\n", "[3] <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2021). Numerical simulation of the whole thermal lensing process with Z-scan-based methods using Gaussian beams. Materials, 14(19), 5533.\n", "\n", "[5] https://nines.cs.kuleuven.be/software/QUADPACK/\n", "\n", "[6] https://www.netlib.org/quadpack/changes\n"]}, {"cell_type": "markdown", "metadata": {"id": "dMYazmnbSeHO"}, "source": ["## Analytic solutions\n", "\n", "Several different yet equivalent expressions have been reported as an analytic solution of the heat equation above. One form employs exponential integrals [2], whereas another form uses a simplified integral expression [1][3].\n", "\n", "Here we demonstrate that both expressions are equivalent, except that the exponential integral formulation diverges at $r=0$, whereas the simplified integral expression does not. Quantitative investigation of both expressions from literature is also a good demonstration of how to evaluate analytic expressions numerically using the `scipy` and `numpy`.\n", "\n", "Where necessary the expressions from literature have been adapted to use SI units exclusively."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"executionInfo": {"elapsed": 17, "status": "aborted", "timestamp": 1691223918151, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "Hp5LmIs6HqxX"}, "outputs": [], "source": ["import numpy as np\n", "from numpy import pi, exp\n", "from scipy.special import expi\n", "from scipy.integrate import quad\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we encode the expression for the Gaussian laser beam intensity distribution."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 19, "status": "aborted", "timestamp": 1691223918153, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "NfOLZQIwIRwo"}, "outputs": [], "source": ["def S(r, P, w):\n", "  return (2*P)/(pi*w**2) * exp(-2*r**2/w**2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We immediatly initialize the model system parameters to experimentally realistic values. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 19, "status": "aborted", "timestamp": 1691223918154, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "8wrQgSeHIrCo"}, "outputs": [], "source": ["P = 1.0e-3 # 1 mW\n", "w = 50e-6 # 50 µm waist\n", "k = 0.598 # W/(m.K)\n", "rho = 998.23 # kg/m3\n", "cp = 4184. # J/(K.kg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From these, we have the characteristic time $t_c$."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"executionInfo": {"elapsed": 21, "status": "aborted", "timestamp": 1691223918157, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "v6g-5ELVKIVK"}, "outputs": [], "source": ["t_c = (w**2*cp*rho)/(4*k)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"executionInfo": {"elapsed": 22, "status": "aborted", "timestamp": 1691223918158, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "GPjBSyZxKRMK"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t_c = 4.365 ms\n"]}], "source": ["print(f't_c = {t_c*1000.:.3f} ms')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The laser beam profile is plotted for illustration."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"executionInfo": {"elapsed": 23, "status": "aborted", "timestamp": 1691223918160, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "UamKX_EpI7Ir"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rrx = np.linspace(0, w*5, 500)\n", "plt.plot(rrx, S(rrx, P, w))\n", "plt.ylabel('irradiance [W/m2]')\n", "plt.xlabel('radial position [m]'); # semicolon to avoid print text object descriptor to output "]}, {"cell_type": "markdown", "metadata": {}, "source": ["The exponential integral expression [2]  is easily evaluated using the `expi` special function routine from `scipy.special`. It will be referred to as 'Carter1984'."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"executionInfo": {"elapsed": 1579, "status": "aborted", "timestamp": 1691223918161, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "0GaNsHFTI82L"}, "outputs": [], "source": ["def Delta<PERSON><PERSON><PERSON>(r, t, P, w, k, t_c):\n", "    \"\"\"\n", "    Carter1984 expression\n", "    \"\"\"\n", "    alpha = 1.0 # for now. TODO\n", "    A = 1/(4*pi*k)\n", "    B = P*alpha\n", "    C = expi(-2*r**2/w**2) - expi(((-1/(1 + 2*t/t_c)) * (2*r**2/w**2)))\n", "    return A*B*C"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Accurate evaluation of the simplified integral expression [1] (which does not diverge at $r=0$) requires somewhat more elaborate code, but then uses the `scipy.integral.quad` routine which is an entry to the QUADPACK library[5][6] which numerically evaluates the integral of analytic function expressions with exquisite precision."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"executionInfo": {"elapsed": 1578, "status": "aborted", "timestamp": 1691223918162, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "z_M7gqisMdq5"}, "outputs": [], "source": ["def tlintgd(ti, tc, r2w2):\n", "    \"\"\"\n", "    integrand for photothermal heat profile analytic solution\n", "    \n", "    ti : float\n", "    t' in the formula (integration variable)\n", "    tc : float\n", "         t_c as specified in formula t_c=(w**2*c*rho)/(4*k)\n", "    r2w2 : float\n", "           r2w2 = r**2/w**2 (to avoid recalculation)\n", "\n", "    more optimisation may be possible (2/tc factor) but wait for now\n", "    \"\"\"\n", "    A = (1.0/(1.0 + (2*ti/tc)))\n", "    B = exp((-2*r2w2)/(1.0 + (2*ti/tc)))\n", "    return A*B\n", "\n", "def _DeltaT_<PERSON>(r, t, P, w, k, cp, rho, tc):\n", "    \"\"\"\n", "    Sheldon1982 expression (scalar only)\n", "    \"\"\"\n", "    alpha = 1.0 # for now. TODO\n", "    A = (2*P*alpha)/(pi*cp*rho*w**2)\n", "    r2w2 = r**2 / w**2\n", "    C,_ = quad(tlintgd, 0.0, t, args = (tc, r2w2))\n", "    return A*C\n", "\n", "def DeltaT_Sheldon(r, t, P, w, k, cp, rho, tc):\n", "    \"\"\"\n", "    Sheldon1982 expression (scalar/vector)\n", "    \"\"\"\n", "    if type(r)==np.ndarray:\n", "        result = np.array([_DeltaT_Sheldon(rrr, t, P, w, k, cp, rho, t_c)\\\n", "                           for rrr in r])\n", "    else:\n", "        result = _DeltaT_Sheldon(r, t, P, w, k, cp, rho, t_c)\n", "    return result\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As an illustration of the equivalence between both functions, we plot both expression for a given time $t$ and set of system parameters."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"executionInfo": {"elapsed": 1577, "status": "aborted", "timestamp": 1691223918162, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "qhdzmUlKL0vq"}, "outputs": [], "source": ["rr1 = np.linspace(1e-8, w*10, 500) # not starting at zero\n", "rr0 = np.linspace(0, w*10, 500)   # starting at zero (<PERSON> expression works here, <PERSON> not)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"executionInfo": {"elapsed": 1572, "status": "aborted", "timestamp": 1691223918163, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "S1TYKCojL3-5"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["t = 0.1\n", "plt.plot(rr1, <PERSON><PERSON><PERSON><PERSON>(rr1, t, P, w, k, t_c),\n", "         'r-', label='Carter1984 (expon. integ.)')\n", "plt.plot(rr0, Delta<PERSON>_Sheldon(rr0, t, P, w, k, cp, rho, t_c),\n", "         'k:', label='Sheldon1982 (simple integ.)')\n", "plt.xlabel('radial position / m')\n", "plt.ylabel('temp. change / K')\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {"id": "mbqRh2QczAX5"}, "source": ["## Finite-volume solution with PyFVTool"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 383}, "executionInfo": {"elapsed": 25, "status": "error", "timestamp": 1691223918148, "user": {"displayName": "<PERSON><PERSON>", "userId": "14715266388173890240"}, "user_tz": -120}, "id": "5E1ALsqZzHfT", "outputId": "1cbcd13e-34c5-41d4-9fe1-52646318c398"}, "outputs": [], "source": ["# explicity import all required routines from pyfvtool\n", "from pyfvtool import CylindricalGrid1D, BoundaryConditions\n", "from pyfvtool import CellVariable\n", "from pyfvtool import transientTerm, diffusionTerm\n", "from pyfvtool import harmonicMean, boundaryConditionsTerm\n", "from pyfvtool import constantSourceTerm\n", "from pyfvtool import solvePDE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the same system parameters and laser intensity function as above to ensure consistency."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"tags": []}, "outputs": [], "source": ["# set FVM-specific parameters\n", "Nr = 100 # number of FV cells\n", "Lr = 10.0*w # width of domain (for now, simply evenly spaced. TODO: improve meshing)\n", "T0 = 0. # initial rel. temperature\n", "deltat = 0.001 # FVM time step"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"tags": []}, "outputs": [], "source": ["# TODO: more attention to alpha (see previously)\n", "# for now, set to 1.0\n", "alpha = 1.0"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"tags": []}, "outputs": [], "source": ["# create grid\n", "mesh = CylindricalGrid1D(Nr, Lr)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"tags": []}, "outputs": [], "source": ["fv_T = CellVariable(mesh, T0) # CellVariable with standard no-flux boundary conditions"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"tags": []}, "outputs": [], "source": ["dotqval = alpha*S(mesh.cellcenters.r, P, w)\n", "fv_dotq = CellVariable(mesh, dotqval)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"tags": []}, "outputs": [], "source": ["fv_k_cell = CellVariable(mesh, k)\n", "fv_k_face = harmonicMean(fv_k_cell)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"tags": []}, "outputs": [], "source": ["fv_transcoeff =  CellVariable(mesh, cp*rho)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"tags": []}, "outputs": [], "source": ["# create constant matrix contributions \n", "# (diffusion term, source term)\n", "diffterm = diffusionTerm(fv_k_face)\n", "srcterm = constantSourceTerm(fv_dotq)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["t = 0.\n", "fv_T.value = T0 # (re)set initial conditions\n", "fv_T.apply_BCs() # apply BCs (necessary each time the BCs or the CellVariable.value change)\n", "\n", "rr0 = np.linspace(0, w*10, 500)\n", "sample_i = [0, 1, 2, 5, 10, 20, 50, 100]\n", "OKlegend = False\n", "for i in range(0,101):\n", "    if i in sample_i:\n", "        r, phi = fv_T.plotprofile()\n", "        ln0, = plt.plot(r, phi)\n", "        ln1, = plt.plot(rr0, DeltaT_Sheldon(rr0, i*deltat, P, w, k, cp, rho, t_c), 'k:')\n", "        if not OKlegend:\n", "            OKlegend = True\n", "            ln0.set_label('FVM')\n", "            ln1.set_label('analytic')\n", "    # create time-dependent matrix contributions (transientTerm)\n", "    transientterm = transientTerm(fv_T, deltat, fv_transcoeff)\n", "\n", "    # solve equation for one time step, updating fv_T solution variable\n", "    eqnterms = [ transientterm,\n", "                -diffterm,\n", "                 srcterm]\n", "    solvePDE(fv_T, eqnterms)\n", "    t += deltat\n", "\n", "if OKlegend:\n", "    plt.legend()\n", "plt.xlabel('radial position / m')\n", "plt.ylabel('temp. change / K');"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple quantitative testing of numerical FV solution against analytic"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# take last calculated time `t`\n", "r_num, phi_num = fv_T.plotprofile()\n", "phi_an_r_num = DeltaT_Sheldon(r_num, t, P, w, k, cp, rho, t_c)\n", "norm_err = (phi_num - phi_an_r_num)/phi_an_r_num.max()\n", "plt.plot(r_num, norm_err, 'o')\n", "plt.xlabel('radial position / m')\n", "plt.ylabel('norm. error FV vs analytic');"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["assert np.all(np.abs(norm_err) < 0.0025)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"authorship_tag": "ABX9TyNkdAIQqEp7z+dlWFRWgxp2", "provenance": [{"file_id": "1qIscS9HHMcSYwTf9TUCE3NwVDDOVx8dt", "timestamp": 1691223821308}]}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}