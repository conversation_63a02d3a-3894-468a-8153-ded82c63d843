{"cells": [{"cell_type": "markdown", "id": "6ec88f25", "metadata": {}, "source": ["# 1D wave equation\n", "\n", "Written by <PERSON>\n", "\n", "Last checked: June 2021\n", "\n", "Ported to Python by <PERSON>, June 2023\n", "\n", "### PDE and boundary conditions\n", "The homogeneous wave equation reads\n", "\n", " $$\\frac{\\partial^2 c}{\\partial t^2} = u^2 \\frac{\\partial^2 c}{\\partial x^2},$$\n", "\n", " where $c$ is the independent variable (displacement, concentration, temperature, etc)\n", " , $u$ is the characteristic velocity.\n", " \n", " The inhomogeneous wave equation with initial conditions on the value and derivative of the concentration takes the form \n", " \n", " \\begin{align}\n", "     & \\frac{\\partial^2 c\\left(x,t\\right)}{\\partial t^2} - u^2 \\frac{\\partial^2 c\\left(x,t\\right)}{\\partial x^2} = s\\left(x,t\\right) \\\\\n", "     & c\\left(x, 0\\right) = f\\left(x\\right) \\\\\n", "     & \\frac{\\partial c}{\\partial t}\\left(x, 0\\right) = g\\left(x\\right)\n", " \\end{align}\n", " \n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "0ac8adc8", "metadata": {}, "outputs": [], "source": ["import pyfvtool as pf\n", "import numpy as np\n", "\n", "# for animation and visualization\n", "import matplotlib.pyplot as plt\n", "from matplotlib.animation import FuncAnimation"]}, {"cell_type": "code", "execution_count": 2, "id": "a0b56eea", "metadata": {}, "outputs": [], "source": ["# c=@(x)(x.^2);\n", "# dc=@(x)(2*x);\n", "\n", "def c(x):\n", "    return x*x\n", "\n", "def dc(x):\n", "    return 2.0*x\n", "\n", "\n", "Lx = float(1.0)   # length of domain\n", "Nx = int(200)     # number of cells in domain\n", "dx = Lx/Nx        # step-size in uniform 1d grid\n", "m = pf.Grid1D(Nx, Lx) # 1D domain\n", "\n", "x_face=m.facecenters.x   # face positions\n", "x_cell=m.cellcenters.x   # node positions\n", "\n", "# Boundary conditions\n", "BC = pf.BoundaryConditions(m)\n", "BC.left.periodic = True\n", "BC.right.periodic = True\n", "\n", "# initial value on grid\n", "u0 = np.abs(np.sin(x_cell/Lx*10*np.pi))\n", "\n", "# Velocity on grid nodes (cell centers)\n", "u_old = pf.CellVariable(m, u0);\n", "u_val = u_old\n", "\n", "dt = 0.1  # time-step for calculations\n", "\n", "# Initialize the face concenrtation values and their derivatives at the faces \n", "c_face = pf.FaceVariable(m, 0.0)     # concentration values\n", "c_face.xvalue = c(x_face)                         \n", "dc_cell = pf.CellVariable(m, dc(x_cell))  # concentration derivative\n", "\n", "# Convection and inhomogeneity act as sources\n", "Mconv = pf.convectionUpwindTerm(c_face)\n", "Ms = pf.linearSourceTerm(dc_cell)            # \n", "\n", "Mbc, RHSbc = pf.boundaryConditionsTerm(BC)\n", "\n", "# ========= #\n", "\n", "ui = []\n", "for ii in range(2000): # 1:1000\n", "    Mt, RHSt = pf.transientTerm(u_old, dt, 1.0)\n", "    \n", "    # Coefficient matrix\n", "    M = Mt+Mconv-Ms+Mbc   # transient term + convection - linear source + value at boundary\n", "    \n", "    # Define right-hand side of the equation\n", "    RHS = RHSt+RHSbc   # right-hand side is the time-derivative + boundary condition (source terms)\n", "    \n", "    # Solve the PDE \n", "    ui.append(pf.solveMatrixPDE(m, M, RHS) )\n", "    \n", "    # Downgrade the current solution to a previous time-step\n", "    u_old = ui[ii]\n", "    # visualizeCells(ui[ii])"]}, {"cell_type": "code", "execution_count": 3, "id": "de25deb3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plotting and visualization of the 1D wave equation solution  \n", "\n", "hfig1, ax1 = plt.subplots()\n", "\n", "xx, uu = ui[0].plotprofile()\n", "ax1.plot(xx, uu, 'b-', label='initial value')\n", "\n", "xx, uu = ui[-1].plotprofile()\n", "ax1.plot(xx, uu, 'r-', label='final value')\n", "\n", "ax1.set_xlim((0, Lx))\n", "ax1.set_ylim((0.0, 1.0))\n", "\n", "ax1.set_xlabel('x')\n", "ax1.set_ylabel('c')\n", "ax1.set_title('1D wave equation solution')\n", "ax1.legend(fontsize=8);"]}, {"cell_type": "raw", "id": "930f1111-b263-444c-ad68-cf813cb2616a", "metadata": {}, "source": ["# ========= #\n", "\n", "# ANIMATION NEEDS TO BE FIXED\n", "\n", "# create a figure to handle the solution animation\n", "#   pre-generate the handles to the axes and the line\n", "hfig2 = plt.figure()\n", "ax2 = plt.axes(xlim=(0, Lx), ylim=(0.0, 1.0))\n", "line, = ax2.plot([], [], 'k-', lw=3, label='Numerical solution')\n", "\n", "ax2.set_xlabel('x')\n", "ax2.set_ylabel('c')\n", "ax2.set_title('1D wave equation solution')\n", "ax2.legend(fontsize=8)\n", "\n", "\n", "def init():\n", "    line.set_data([], [])\n", "    return line, \n", "\n", "def animate(ii):\n", "    # x, y = get_CellVariable_profile1D(ui[ii])\n", "\n", "    global ui\n", "    line.set_data(*get_CellVariable_profile1D(ui[ii]))\n", "    return line, \n", "\n", "anim = FuncAnimation(hfig2, animate, init_func=init,\n", "            frames=400, interval=20, blit=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2d5328dc-c877-4754-b2f0-729739fa61f8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}