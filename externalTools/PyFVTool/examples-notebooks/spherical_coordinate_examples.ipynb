{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Advection and Diffusion in Spherical Coordinates with PyFVTool\n", "\n", "In this notebook, we will solve the advection equation in spherical coordinates using the finite volume method. The notebook serves principally for testing the implementation of 'spherical' advection and diffusion terms in `PyFVTool`, but also for illustrating their use. \n", "\n", "The notebook contains several examples of advection and diffusion equations, and first attempts at visualizing 3D spherical results (suggestions welcome!)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import importlib\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following Python path extension is not needed if PyFVTool has been installed using `pip install git+https://github.com/FiniteVolumeTransportPhenomena/PyFVTool.git` (or `pip install --editable .` from your local git clone, if you are a developer).\n", "\n", "See [PyFVTool README.md](https://github.com/FiniteVolumeTransportPhenomena/PyFVTool/blob/main/README.md) and [PyFVTool CONTRIBUTING.md](https://github.com/FiniteVolumeTransportPhenomena/PyFVTool/blob/main/CONTRIBUTING.md)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["current_dir = os.getcwd()\n", "target_dir = os.path.abspath(os.path.join(current_dir, \"..\", \"src\"))\n", "sys.path.append(target_dir)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pyfvtool as pf\n", "\n", "# Reload the module to get the updated version (not necessary if installed using ``--editable``)\n", "# Be sure, of course, to restart the Notebook.\n", "importlib.reload(pf);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When working with random numbers (`numpy.random`), set the random seed explicitly.\n", "This is good practice.\n", "Make sure that you trust your random number generator to be sufficiently \n", "random (<PERSON><PERSON><PERSON>'s default is OK).\n", "With a fixed value for the initial seed, you are sure to always obtain the same\n", "sequence of random numbers from run to run. This makes testing more practical.\n", "If you want to have a different sequence of random numbers, manually change the\n", "seed, but make sure that you remember which seed number was used."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Set the random seed explicitly\n", "np.random.seed(seed=12345)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## First explorations of the spherical finite volume elements"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["X = np.array([0.01, 0.1, 0.3, 0.5, 0.55, 1.0])\n", "Y = np.array([0.0, 0.1, 1.0, 1.5, 2.9, 3.0, np.pi, np.pi])\n", "Z = np.array([0.0, 0.01, 0.1, 0.5, 0.7, 0.95, 1.0, 1.25, 1.39, 2.0])\n", "m_non = pf.SphericalGrid3D(X, Y, Z)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19.739208802178716 19.739208802178716\n", "3.141592653589793 3.1415926535897936\n"]}], "source": ["# test the volume calculations\n", "x = np.linspace(0.0, 1.0, 10)\n", "y = np.linspace(0.0, 2*np.pi, 10)\n", "z = np.linspace(0.0, 2*np.pi, 20)\n", "mc = pf.CylindricalGrid3D(x, y, z)\n", "vc = mc._getCellVolumes()\n", "v_cyl = np.pi*x[-1]**2*z[-1]\n", "print(v_cyl, np.sum(vc))\n", "\n", "mp = pf.PolarGrid2D(x, y)\n", "vp = mp._getCellVolumes()\n", "v_pol = np.pi*x[-1]*x[-1]\n", "print(v_pol, np.sum(vp))\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.1887902047863905 4.144349016011306\n"]}], "source": ["x = np.linspace(0.0, 1.0, 5)\n", "y = np.linspace(0.0, np.pi, 10)\n", "z = np.linspace(0.0, 2*np.pi, 10)\n", "ms = pf.SphericalGrid3D(x, y, z)\n", "vs = ms._getCellVolumes()\n", "v_sph = 4/3*np.pi*x[-1]**3\n", "print(v_sph, np.sum(vs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Diffusion equations: spherical and cylindrical coordinates, 1D and 3D\n", "\n", "Solving a diffusion equation with \"no flux\" concentration at the \"left boundary\" (the center) and a fixed concentration on the \"right boundary\" (the outer surface).\n", "\n", "The same equation is solved in spherical and cylindrical coordinates. Both 1D and 3D geometries are used to test consistency."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Calculation parameters\n", "def diffusion_spherical(mesh, t_simulation=7200.0, dt=60.0):\n", "    c_left = 1.0  # left boundary concentration\n", "    c_init = 0.0  # initial concentration\n", "    D_val = 1e-5  # diffusion coefficient (gas phase)\n", "\n", "    # Create a cell variable with initial concentration\n", "    # By default, 'no flux' boundary conditions are applied\n", "    c = pf.CellVariable(mesh, c_init)\n", "\n", "    # Switch the right boundary to Dirichlet: fixed concentration\n", "    c.BCs.right.a[:] = 0.0\n", "    c.BCs.right.b[:] = 1.0\n", "    c.BCs.right.c[:] = c_left\n", "    if type(mesh) == pf.SphericalGrid3D:\n", "        # make top and bottom boundaries periodic\n", "        c.BCs.back.periodic = True\n", "        c.BCs.front.periodic = True\n", "\n", "    c.apply_BCs()\n", "\n", "    # Assign diffusivity to cells\n", "    D_cell = pf.CellVariable(mesh, D_val)\n", "    D_face = pf.geometricMean(\n", "        D_cell\n", "    )  # average value of diffusivity at the interfaces between cells\n", "\n", "    # Time loop\n", "    t = 0\n", "    while t < t_simulation:\n", "        # Compose discretized terms for matrix equation\n", "        eqnterms = [pf.transientTerm(c, dt, 1.0), -pf.diffusionTerm(D_face)]\n", "\n", "        # Solve PDE\n", "        pf.solvePDE(c, eqnterms)\n", "        t += dt\n", "    return c"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Calculation parameters\n", "Nx = 20  # number of finite volume cells\n", "Ntheta = 6  # number of cells in the theta direction; avoid theta=0 and theta=pi\n", "Nphi = 5  # number of cells in the phi direction\n", "Lx = 1.0  # [m] length of the domain\n", "\n", "# Define mesh\n", "mesh1 = pf.SphericalGrid1D(Nx, Lx)\n", "c1 = diffusion_spherical(mesh1)\n", "mesh3 = pf.SphericalGrid3D(Nx, Ntheta, Nphi, Lx, np.pi, 2 * np.pi)\n", "c3 = diffusion_spherical(mesh3)\n", "mesh1_rad = pf.CylindricalGrid1D(Nx, Lx)\n", "c1_rad = diffusion_spherical(mesh1_rad)\n", "mesh3_cyl = pf.CylindricalGrid3D(Nx, Ntheta, Nphi, Lx, 2 * np.pi, 2 * np.pi)\n", "c3_cyl = diffusion_spherical(mesh3_cyl)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(mesh1.cellcenters.r, c1.value, \"--\", label=\"1D spherical\")\n", "plt.plot(mesh3.cellcenters.r, c3.value[:, 0, 0], label=\"3D spherical\")\n", "plt.plot(mesh1_rad.cellcenters.r, c1_rad.value, \"--\", label=\"1D Cylindrical\")\n", "plt.plot(mesh3_cyl.cellcenters.r, c3_cyl.value[:, 0, 0], label=\"3D cylindrical\")\n", "plt.xlabel(\"r [m]\")\n", "plt.ylabel(\"Concentration [-]\")\n", "plt.ylim(-0.05, 1.05)\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Spherical advection in 3D (with visualization)\n", "\n", "The spherical geometry defined here is that of a layer of a certain thickness at the surface of sphere, *e.g.* a virtual atmosphere on the surface of a virtual Earth."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100% completed\n"]}, {"data": {"text/plain": ["array([[1.12474992e-06, 5.89347319e-07, 3.07788221e-07, ...,\n", "        1.12448870e-05, 5.96680735e-06, 3.15333897e-06],\n", "       [1.46808022e-07, 6.96918445e-08, 3.42636345e-08, ...,\n", "        3.24891256e-06, 1.23095334e-06, 5.05690564e-07],\n", "       [1.83629659e-08, 8.06864630e-09, 3.76258740e-09, ...,\n", "        5.81033744e-07, 1.97060868e-07, 7.19942449e-08],\n", "       ...,\n", "       [3.98893067e-02, 5.35021522e-02, 4.33750566e-02, ...,\n", "        1.05030469e+00, 5.38012959e-01, 2.08924694e-01],\n", "       [1.10233675e+01, 1.05738399e+01, 7.10617623e+00, ...,\n", "        2.95071442e-01, 1.96481799e-01, 1.03522821e-01],\n", "       [4.79304997e+00, 6.42472561e+00, 6.85747726e+00, ...,\n", "        1.26437721e+00, 8.84223832e-01, 6.02753851e-01]])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["r_earth = 6.371e6  # Earth radius [m]\n", "v_wind_max = 10.0  # wind speed [m/s]\n", "# ignoring diffusion\n", "Nr = 20  # number of cells in the r direction\n", "Ntheta = 36  # number of cells in the theta direction\n", "Nphi = 36  # number of cells in the phi direction\n", "Lr = 20e3  # [m] length of the domain in the r direction\n", "r_face = np.linspace(r_earth, r_earth + Lr, Nr + 1)\n", "theta_face = np.linspace(0, np.pi, Ntheta + 1)\n", "phi_face = np.linspace(0, 2 * np.pi, Nphi + 1)\n", "mesh = pf.SphericalGrid3D(r_face, theta_face, phi_face)\n", "c = pf.CellVariable(mesh, 0.0)\n", "# assign a concentration of 1.0 to 20 random locations\n", "c.value[0, np.random.randint(0, <PERSON><PERSON><PERSON>, 50), np.random.randint(0, N<PERSON>, 50)] = 1000.0\n", "\n", "# BC\n", "# left boundary is a fixed concentration\n", "c.BCs.left.a[:] = 0.0\n", "c.BCs.left.b[:] = 1.0\n", "c.BCs.left.c[:] = 0.0\n", "# assign a concentration of 1.0 to a patch of left boundary\n", "# c.BCs.left.c[0:10, 0:10] = 1.0\n", "# right boundary is a fixed concentration\n", "# c.BCs.right.a[:] = 0.0\n", "# c.BCs.right.b[:] = 1.0\n", "# c.BCs.right.c[:] = 0.0\n", "# top and bottom boundaries are periodic\n", "# c.BCs.top.periodic = True\n", "# c.BCs.bottom.periodic = True\n", "c.BCs.back.periodic = True\n", "c.BCs.front.periodic = True\n", "c.apply_BCs()\n", "Mbc, RHSbc = pf.boundaryConditionsTerm(c.BCs)\n", "\n", "# create a constant velocity field\n", "v = pf.FaceVariable(mesh, [0.1, 10, 10])\n", "\n", "Mc = pf.convectionUpwindTerm(v)\n", "\n", "# Time loop\n", "t = 0\n", "dt = 10000.0\n", "n_steps = 5\n", "for i in tqdm(range(n_steps), bar_format=\"{desc}: {percentage:3.0f}% completed\"):\n", "    # Compose discretized terms for matrix equation\n", "    Mt, RHSt = pf.transientTerm(c, dt, 1.0)\n", "    M = Mt + Mc + Mbc\n", "    RHS = RHSt + RHSbc\n", "    c_new = pf.solveMatrixPDE(mesh, M, RHS)\n", "    c.update_value(c_new)\n", "    t += dt\n", "c.value[0, :, :]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization\n", "\n", "Meaningful and insightful visualization of 3D spherical and cylindrical FVM results can be quite challenging. It requires thinking carefully on how to best present the features that illustrate the results of your calculations. Of course, it also requires to have right code to make the drawing.\n", "\n", "The visualization is part of the 'post-processing' of numerical calculations. (BTW, already for relatively simple cases, it is wise to separate the number cruching code from the post-processing, by storing the calculation results in a file. This avoids having to wait over and over again for the same calculation to terminate.)\n", "\n", "Here, we show our first suggestions for the graphical output of 3D spherical (and cylindrical) FVM results."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# visualize the result: use a polar plot for plotting a 'slice' at a certain value of 'phi'\n", "# (lemon - or mellon - style slice)\n", "\n", "# select the 'phi' using its index\n", "iphi = 5\n", "\n", "r = mesh.cellcenters.r\n", "theta = mesh.cellcenters.theta\n", "phi = mesh.cellcenters.phi\n", "\n", "R, Theta = np.meshgrid(r, theta)\n", "X = R * np.sin(Theta)\n", "Y = R * np.cos(Theta)\n", "Z = c.value[:, :, iphi].transpose() \n", "fig = plt.figure()\n", "phideg = np.rad2deg(phi[iphi])\n", "fig.suptitle(f'phi = {phideg}°')\n", "ax = fig.add_subplot(111, polar=True)\n", "# set the color range between 0 and 1\n", "cax = ax.pcolormesh(Theta, R, Z, cmap=\"viridis\")\n", "fig.colorbar(cax, label=\"Concentration [-]\")\n", "# zoom in to the region of interest\n", "ax.set_ylim([r_earth - Lr, r_earth + Lr])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# First version of a 3D plotting routine\n", "# Plot a spherical view of a single layer at height/depth 'r' \n", "# (onion-layer style)\n", "\n", "# select r by index\n", "ir = 10 # halfway\n", "\n", "data = c.value[ir, :, :] \n", "\n", "# Generate theta and phi values\n", "theta = np.linspace(0, np.pi, data.shape[0])\n", "phi = np.linspace(0, 2 * np.pi, data.shape[1])\n", "theta, phi = np.meshgrid(theta, phi)\n", "\n", "# Convert spherical coordinates to Cartesian coordinates\n", "x = np.sin(theta) * np.cos(phi)\n", "y = np.sin(theta) * np.sin(phi)\n", "z = np.cos(theta)\n", "\n", "# Create a 3D plot\n", "fig = plt.figure()\n", "fig.suptitle(f'r = {mesh.cellcenters.r[ir]}')\n", "ax = fig.add_subplot(111, projection=\"3d\")\n", "\n", "# Plot the surface\n", "ax.plot_surface(\n", "    x, y, z, facecolors=plt.cm.viridis(data), rstride=1, cstride=1, antialiased=False\n", ")\n", "\n", "# rotate the plot\n", "ax.view_init(elev=20, azim=80)\n", "\n", "\n", "# Show the plot (perhaps interactively when not in Jupyter Lab?)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["(7.606713348051508e-13, 91.582096081292)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["c.value.min(), c.value.max()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advective transport 3D cylindrical example\n", "\n", "Here, we run a similar simulation of a cylindrical version of Earth, to test 3D cylindrical coordinates."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[6.69906320e-02, 1.53121216e+01, 0.00000000e+00, 1.53121216e+01,\n", "        7.97036372e+02, 0.00000000e+00, 0.00000000e+00, 6.69906320e-02,\n", "        2.57395707e-03, 7.97036764e+02, 1.98526430e-01, 2.93084453e-04,\n", "        4.55759284e+01, 9.01926587e-04, 5.16951986e+00, 0.00000000e+00],\n", "       [2.26052761e-02, 5.16691253e+00, 0.00000000e+00, 5.16691253e+00,\n", "        2.68951445e+02, 0.00000000e+00, 0.00000000e+00, 2.26052761e-02,\n", "        8.68554431e-04, 2.68951577e+02, 6.69906320e-02, 9.88982309e-05,\n", "        1.53791122e+01, 6.62560942e+02, 6.64305037e+02, 0.00000000e+00],\n", "       [7.62790995e-03, 1.74351967e+00, 0.00000000e+00, 1.74351967e+00,\n", "        9.07548039e+01, 0.00000000e+00, 0.00000000e+00, 7.62790995e-03,\n", "        2.93084453e-04, 7.53315487e+02, 2.26052761e-02, 3.33721559e-05,\n", "        5.18951781e+00, 2.23574142e+02, 2.24162668e+02, 0.00000000e+00],\n", "       [2.57395707e-03, 5.88332165e-01, 0.00000000e+00, 5.88332165e-01,\n", "        3.06242431e+01, 0.00000000e+00, 0.00000000e+00, 2.57395707e-03,\n", "        9.88982309e-05, 9.16758935e+02, 7.62790995e-03, 6.62560649e+02,\n", "        1.75114758e+00, 7.54427157e+01, 7.56413077e+01, 0.00000000e+00],\n", "       [8.68554431e-04, 1.98526430e-01, 0.00000000e+00, 1.98526430e-01,\n", "        1.03338251e+01, 0.00000000e+00, 0.00000000e+00, 8.68554431e-04,\n", "        3.33721559e-05, 3.09350550e+02, 2.57395707e-03, 2.23574043e+02,\n", "        5.90906122e-01, 6.88017980e+02, 2.55243546e+01, 0.00000000e+00],\n", "       [2.93084453e-04, 6.69906320e-02, 0.00000000e+00, 6.69906320e-02,\n", "        3.48703934e+00, 0.00000000e+00, 0.00000000e+00, 2.93084453e-04,\n", "        6.62560649e+02, 1.04387052e+02, 8.68554431e-04, 7.54426824e+01,\n", "        1.99394985e-01, 2.32164348e+02, 6.71173560e+02, 0.00000000e+00],\n", "       [9.88982309e-05, 2.26052761e-02, 0.00000000e+00, 2.26052761e-02,\n", "        1.17666433e+00, 0.00000000e+00, 0.00000000e+00, 9.88982309e-05,\n", "        2.23574043e+02, 3.52243003e+01, 2.93084453e-04, 2.54573306e+01,\n", "        6.72837165e-02, 7.83413895e+01, 2.26480378e+02, 0.00000000e+00],\n", "       [3.33721559e-05, 7.62790995e-03, 0.00000000e+00, 7.62790995e-03,\n", "        3.97052861e-01, 0.00000000e+00, 0.00000000e+00, 3.33721559e-05,\n", "        7.54426824e+01, 1.18860654e+01, 9.88982309e-05, 8.59030540e+00,\n", "        2.27041744e-02, 2.64354685e+01, 7.64233942e+01, 0.00000000e+00],\n", "       [6.62560649e+02, 2.57395707e-03, 0.00000000e+00, 2.57395707e-03,\n", "        1.33981264e-01, 0.00000000e+00, 0.00000000e+00, 6.62560649e+02,\n", "        2.54573306e+01, 4.01082634e+00, 3.33721559e-05, 2.89870717e+00,\n", "        7.66128211e-03, 8.92036763e+00, 2.57882614e+01, 0.00000000e+00],\n", "       [2.23574043e+02, 8.68554431e-04, 0.00000000e+00, 8.68554431e-04,\n", "        4.52105522e-02, 0.00000000e+00, 0.00000000e+00, 2.23574043e+02,\n", "        8.59030540e+00, 1.35341068e+00, 6.62560649e+02, 9.78137899e-01,\n", "        6.62563223e+02, 3.01008316e+00, 8.70197447e+00, 0.00000000e+00],\n", "       [7.54426824e+01, 2.93084453e-04, 0.00000000e+00, 2.93084453e-04,\n", "        1.52558199e-02, 0.00000000e+00, 0.00000000e+00, 7.54426824e+01,\n", "        2.89870717e+00, 4.56694036e-01, 2.23574043e+02, 3.30062229e-01,\n", "        2.23574911e+02, 1.01572054e+00, 2.93638871e+00, 0.00000000e+00],\n", "       [2.54573306e+01, 9.88982309e-05, 0.00000000e+00, 9.88982309e-05,\n", "        5.14791414e-03, 0.00000000e+00, 0.00000000e+00, 2.54573306e+01,\n", "        9.78137899e-01, 1.54106544e-01, 7.54426824e+01, 1.11375988e-01,\n", "        7.54429754e+01, 3.42744092e-01, 9.90853134e-01, 0.00000000e+00],\n", "       [8.59030540e+00, 3.33721559e-05, 0.00000000e+00, 3.33721559e-05,\n", "        1.73710886e-03, 0.00000000e+00, 0.00000000e+00, 8.59030540e+00,\n", "        3.30062229e-01, 5.20016140e-02, 2.54573306e+01, 3.75826423e-02,\n", "        2.54574295e+01, 1.15655348e-01, 6.62894991e+02, 0.00000000e+00],\n", "       [2.89870717e+00, 6.62560649e+02, 0.00000000e+00, 6.62560649e+02,\n", "        5.86168906e-04, 0.00000000e+00, 0.00000000e+00, 2.89870717e+00,\n", "        1.11375988e-01, 1.75473914e-02, 8.59030540e+00, 1.26818628e-02,\n", "        8.59033877e+00, 3.90266667e-02, 2.23686863e+02, 0.00000000e+00],\n", "       [9.78137899e-01, 2.23574043e+02, 0.00000000e+00, 2.23574043e+02,\n", "        1.97796462e-04, 0.00000000e+00, 0.00000000e+00, 9.78137899e-01,\n", "        3.75826423e-02, 5.92118058e-03, 2.89870717e+00, 4.27935971e-03,\n", "        6.65459356e+02, 1.31691335e-02, 7.54807523e+01, 0.00000000e+00],\n", "       [3.30062229e-01, 7.54426824e+01, 0.00000000e+00, 7.54426824e+01,\n", "        6.67443119e-05, 0.00000000e+00, 0.00000000e+00, 3.30062229e-01,\n", "        1.26818628e-02, 1.99803940e-03, 9.78137899e-01, 1.44402441e-03,\n", "        2.24552181e+02, 4.44378401e-03, 2.54701769e+01, 0.00000000e+00]])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["r_earth = 6.371e6  # Earth radius [m]\n", "v_wind_max = 10.0  # wind speed [m/s]\n", "L = r_earth\n", "# ignoring diffusion\n", "Nr = 20  # number of cells in the r direction\n", "Ntheta = 16  # number of cells in the theta direction\n", "Nz = 16  # number of cells in the phi direction\n", "Lr = 20e3  # [m] length of the domain in the r direction\n", "r_face = np.linspace(0, Lr, Nr + 1)\n", "theta_face = np.linspace(0, 2 * np.pi, Ntheta + 1)\n", "L_face = np.linspace(0, L, Nz + 1)\n", "mesh = pf.CylindricalGrid3D(r_face, theta_face, L_face)\n", "c = pf.CellVariable(mesh, 0.0)\n", "# assign a concentration of 1.0 to 20 random locations\n", "c.value[0, np.random.randint(0, <PERSON><PERSON><PERSON>, 20), np.random.randint(0, Nz, 20)] = 1000.0\n", "\n", "# BC\n", "# top and bottom boundaries are periodic\n", "c.BCs.top.periodic = True\n", "c.BCs.bottom.periodic = True\n", "c.apply_BCs()\n", "Mbc, RHSbc = pf.boundaryConditionsTerm(c.BCs)\n", "\n", "# create a velocity field of random values between 0 and v_wind_max\n", "v = pf.FaceVariable(mesh, [0, 0.001, 0.0])\n", "# v.thetavalue = np.random.rand(Nr, Ntheta+1, Nz) * v_wind_max\n", "# v.thetavalue[:, 0, :] = 0.0  # no wind at the poles\n", "# v.thetavalue[:, -1, :] = 0.0  # no wind at the poles\n", "\n", "Mc = pf.convectionUpwindTerm(v)\n", "\n", "# Time loop\n", "t = 0\n", "dt = 100000.0\n", "t_simulation = 1 * dt\n", "while t < t_simulation:\n", "    # Compose discretized terms for matrix equation\n", "    Mt, RHSt = pf.transientTerm(c, dt, 1.0)\n", "    M = Mt + Mc + Mbc\n", "    RHS = RHSt + RHSbc\n", "    c_new = pf.solveMatrixPDE(mesh, M, RHS)\n", "    c.update_value(c_new)\n", "    t += dt\n", "c.value[0, :, :]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hollow sphere (with visualization)\n", "\n", "This example still needs further explanations (and perhaps some tweaking)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100% completed\n"]}], "source": ["# hollow sphere\n", "r_in = 0.5\n", "r_out = 1.0\n", "Nr = 20  # number of cells in the r direction\n", "Ntheta = 16  # number of cells in the theta direction\n", "Nphi = 16  # number of cells in the phi direction\n", "r_face = np.linspace(r_in, r_out, Nr + 1)\n", "theta_face = np.linspace(0.0, np.pi, Ntheta + 1)\n", "phi_face = np.linspace(0.0, 2 * np.pi, Nphi + 1)\n", "mesh = pf.SphericalGrid3D(r_face, theta_face, phi_face)\n", "c = pf.CellVariable(mesh, 0.0)\n", "# assign a concentration of 1 to left boundary\n", "c.BCs.left.a[0:5, 0:5] = 0.0\n", "c.BCs.left.b[0:5, 0:5] = 1.0\n", "c.BCs.left.c[0:5, 0:5] = 1.0\n", "# right boundary is a fixed concentration\n", "c.BCs.right.a[:] = 0.0\n", "c.BCs.right.b[:] = 1.0\n", "c.BCs.right.c[:] = 0.0\n", "\n", "# top and back boundaries are periodic\n", "# c.BCs.top.periodic = True\n", "c.BCs.front.periodic = True\n", "\n", "c.apply_BCs()\n", "Mbc, RHSbc = pf.boundaryConditionsTerm(c.BCs)\n", "\n", "v_wind_max = 10  # wind speed [m/s]\n", "v = pf.FaceVariable(mesh, [0.0, 0.05, 0.05])\n", "v.phiavalue = np.random.rand(Nr, Ntheta, Nphi + 1) * v_wind_max\n", "v.thetavalue = np.random.rand(Nr, Ntheta + 1, Nphi) * v_wind_max\n", "\n", "Mc = pf.convectionUpwindTerm(v)\n", "\n", "# Time loop\n", "t = 0\n", "dt = 1.0\n", "n_step = 50\n", "\n", "for i in tqdm(range(n_step), bar_format=\"{desc}: {percentage:3.0f}% completed\"):\n", "    # Compose discretized terms for matrix equation\n", "    Mt, RHSt = pf.transientTerm(c, dt, 1.0)\n", "    M = Mt + Mbc + Mc\n", "    RHS = RHSt + RHSbc\n", "    c_new = pf.solveMatrixPDE(mesh, M, RHS)\n", "    c.update_value(c_new)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# visualize the result\n", "r = mesh.cellcenters.r\n", "theta = mesh.cellcenters.theta\n", "R, Theta = np.meshgrid(r, theta)\n", "X = R * np.sin(Theta)\n", "Y = R * np.cos(Theta)\n", "Z = c.value[:, :, 1].transpose() # TO DO: make sure that you extract the values for a certain 'phi'...\n", "fig = plt.figure()\n", "ax = fig.add_subplot(111, polar=True)\n", "# set the color range between 0 and 1\n", "cax = ax.pcolormesh(Theta, R, Z, cmap=\"viridis\")\n", "fig.colorbar(cax, label=\"Concentration [-]\");"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.0, 0.0)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["c.value.min(), c.value.max()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Assuming c.value[5, :, :] is the data with size 16x16\n", "data = c.value[1, :, :]  # Replace this with your actual data\n", "\n", "# Generate theta and phi values\n", "theta = np.linspace(0, np.pi, data.shape[0])\n", "phi = np.linspace(0, 2 * np.pi, data.shape[1])\n", "theta, phi = np.meshgrid(theta, phi)\n", "\n", "# Convert spherical coordinates to Cartesian coordinates\n", "x = np.sin(theta) * np.cos(phi)\n", "y = np.sin(theta) * np.sin(phi)\n", "z = np.cos(theta)\n", "\n", "# Create a 3D plot\n", "fig = plt.figure()\n", "ax = fig.add_subplot(111, projection=\"3d\")\n", "\n", "# Plot the surface\n", "ax.plot_surface(\n", "    x, y, z, facecolors=plt.cm.viridis(data), rstride=1, cstride=1, antialiased=False\n", ")\n", "\n", "# rotate the plot\n", "ax.view_init(elev=50, azim=80)\n", "\n", "\n", "# Show the plot\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}