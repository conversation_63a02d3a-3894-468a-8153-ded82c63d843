{"cells": [{"cell_type": "markdown", "id": "3f212fda-507e-4036-afc1-1d45dd2ecd4a", "metadata": {"tags": []}, "source": ["# Convection in 2D cylindrical geometry: Taylor dispersion\n", "\n", "*MW, 230906, 240503*"]}, {"cell_type": "markdown", "id": "35792f2d-5de8-4138-8971-92a877c855a0", "metadata": {}, "source": ["## Introduction\n", "\n", "This Notebook presents a first finite-volume modelisation of the dispersion of a solute in a fluid in a thin, long cylindrical tube undergoing Poiseuille flow. This dispersion is described, theoretically and experimentally, in the seminal paper by <PERSON> [1]. Further background can be found in that paper.\n", "\n", "In the present Notebook, only the purely convective case is studied. A finite-volume solution of the corresponding partial differential equation is obtained using PyFVTool. This result is compared to the analytic expression obtained by Taylor [1].\n", "\n", "## To do\n", "\n", "- Optimize numerical solution scheme and parameters\n", "- Try and compare different FV discretizations of the convective term\n", "\n", "## References\n", "[1] <PERSON><PERSON> <PERSON><PERSON>. 'Dispersion of Soluble Matter in Solvent Flowing Slowly through a Tube.', *Proc. Royal Soc. A* **1953**, *219*, 186–203. https://doi.org/10.1098/rspa.1953.0139"]}, {"cell_type": "markdown", "id": "0ce2e09d-4b2c-49bb-af39-274eff0940f0", "metadata": {}, "source": ["## Import modules & define utility functions"]}, {"cell_type": "code", "execution_count": 1, "id": "e299ab89-1c20-4e7f-b58a-18eccc1297c1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from typing import Any\n", "from numpy.typing import NDArray # type hints need numpy >= 1.21"]}, {"cell_type": "code", "execution_count": 2, "id": "c34f6557-210a-447b-a1d9-c4baa21ee724", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 3, "id": "426ff2c7-5a62-4e34-b2a7-13b61850e4e8", "metadata": {}, "outputs": [], "source": ["import pyfvtool as pf"]}, {"cell_type": "code", "execution_count": 4, "id": "7087c2aa-c1a4-4748-bdec-981eb76ed275", "metadata": {}, "outputs": [], "source": ["# visualization routine (imshow-based)\n", "def phi_visualize():\n", "    print(f't = {t:.1f} s')\n", "    # avoid ghost cells\n", "    plt.imshow(phi.value, origin = 'lower',\n", "               extent = [zz[0], zz[-1], rr[0]*rzoom, rr[-1]*rzoom])"]}, {"cell_type": "code", "execution_count": 5, "id": "9e38ac0c-dfce-451d-9aa1-3018cfe94466", "metadata": {}, "outputs": [], "source": ["# calculate simple finite-volume integral over r\n", "def integral_dr(phi0):\n", "    v = phi0.cellvolume\n", "    c = phi0.value\n", "    return (v*c).sum(axis=0)"]}, {"cell_type": "markdown", "id": "5d51304c-7bbc-4809-b79c-13316af102d9", "metadata": {}, "source": ["### Functions for evaluation of the analytic expression by <PERSON> ('A3')"]}, {"cell_type": "code", "execution_count": 6, "id": "102873a5-5160-4278-b57c-3b2eebe7b713", "metadata": {}, "outputs": [], "source": ["# analytic expression from <PERSON> 1953\n", "def TaylorA3(x: float, t: float, \n", "             X: float, C_0: float, u_0: float) -> float:\n", "    assert (t >= X/u_0), 't < X/u_0 not implemented'\n", "    if (x >= 0) and (x < X):\n", "        C_m = C_0 * x/(u_0*t)\n", "    elif (x >= X) and (x < u_0*t):\n", "        C_m = C_0 * X/(u_0*t)\n", "    elif (x >= u_0*t) and (x < u_0*t + X):\n", "        C_m = C_0*((X + u_0*t - x)/(u_0*t))\n", "    else:\n", "        C_m = 0.0\n", "    return C_m\n", "           \n", "def TaylorA3_vec(xvec: NDArray[(Any,)], t: float, \n", "                  X: float, C_0: float, u_0: float) -> NDArray[(Any,)]:\n", "    C_m_vec = np.zeros_like(xvec)\n", "    for ix, x in enumerate(xvec):\n", "        C_m_vec[ix] = TaylorA3(x, t, X, C_0, u_0)\n", "    return C_m_vec  "]}, {"cell_type": "markdown", "id": "7a1825d7-dce7-47f0-85de-c981f0fd96aa", "metadata": {"tags": []}, "source": ["## Finite-volume scheme with PyFVTool"]}, {"cell_type": "markdown", "id": "f405cc30-8f3c-40b1-a01c-fce4437247b2", "metadata": {}, "source": ["### Define system & model parameters"]}, {"cell_type": "code", "execution_count": 7, "id": "6b6736a5-e88d-455a-b849-e6c26c19d29b", "metadata": {}, "outputs": [], "source": ["Lr = 7.5e-05 # [m] radius of cylinder\n", "Lz = 0.3 # [m] length of cylinder\n", "umax = 2*9.4314e-3 # [m s^-1] max flow velocity = 2 time average flow velocity"]}, {"cell_type": "code", "execution_count": 8, "id": "aa7aabc8-253a-4819-8af2-6ff97632f43c", "metadata": {}, "outputs": [], "source": ["# regular grid parameters\n", "Nr = 40\n", "Nz = 500"]}, {"cell_type": "code", "execution_count": 9, "id": "dbcc3ce3-f654-47e7-856d-5850f32159c0", "metadata": {}, "outputs": [], "source": ["# initial condition parameters (cell indices)\n", "loadix0 = 20\n", "loadix1 = 40"]}, {"cell_type": "code", "execution_count": 10, "id": "13812476-b23a-4a06-995f-5aeca2248c79", "metadata": {}, "outputs": [], "source": ["# timestep parameters\n", "deltat = 0.01 # [s] per time step"]}, {"cell_type": "code", "execution_count": 11, "id": "eafbdcdb-8542-4757-b440-774b8b97c075", "metadata": {}, "outputs": [], "source": ["# visualization parameters\n", "rzoom = 1000"]}, {"cell_type": "markdown", "id": "3358eb55-d973-4a63-a1f2-6d08e8a67547", "metadata": {}, "source": ["### PyFVTool finite-volume definition"]}, {"cell_type": "markdown", "id": "44c8a584-d164-4dd5-b8b4-4cd36c4778a2", "metadata": {}, "source": ["#### 2D cylindrical mesh"]}, {"cell_type": "code", "execution_count": 12, "id": "83f2cd32-9b6c-4e39-bb17-e07a8ec7b97b", "metadata": {}, "outputs": [], "source": ["msh = pf.CylindricalGrid2D(Nr, Nz, Lr, Lz)"]}, {"cell_type": "markdown", "id": "6e661cba-4235-402e-9a00-ce1ff2944bd4", "metadata": {}, "source": ["#### Set up Poiseuille flow velocity field"]}, {"cell_type": "code", "execution_count": 13, "id": "627f530d-2f70-405e-8eef-67b32b34722d", "metadata": {}, "outputs": [], "source": ["rr = msh.cellcenters.r\n", "zz = msh.facecenters.z"]}, {"cell_type": "code", "execution_count": 14, "id": "6089c3f9-8fed-46e4-a167-e9e232b53c4a", "metadata": {}, "outputs": [], "source": ["uu =  umax*(1 - (rr**2)/(Lr**2)) # does not depend on zz"]}, {"cell_type": "code", "execution_count": 15, "id": "b1e57844-3d9c-48ec-a15d-7598daf86813", "metadata": {}, "outputs": [], "source": ["u =  pf.FaceVariable(msh, 1.0)"]}, {"cell_type": "code", "execution_count": 16, "id": "a2120245-69b7-4cd0-bb2a-b7a50a9765f7", "metadata": {}, "outputs": [], "source": ["u.rvalue[:] = 0\n", "u.zvalue[:] = uu[:, np.newaxis]"]}, {"cell_type": "code", "execution_count": 17, "id": "489017e8-bba5-4fcd-9bc6-1ba895aa1414", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for i in [1, 10, -1]:\n", "    plt.plot(rr*1e6, u.zvalue[:, i])\n", "plt.xlabel('$r$ / µm') \n", "plt.ylabel('$u_z(r)$ / m s$^{-1}$');"]}, {"cell_type": "markdown", "id": "e48717fb-f731-4aaf-83d5-25a47bc71153", "metadata": {}, "source": ["#### Solution variable\n", "\n", "*Standard 'no flux' boundary conditions. The convective flow field, however, will still transport matter out of the calculation domain.*"]}, {"cell_type": "code", "execution_count": 18, "id": "d9a7111d-4533-4391-9056-b669e404e07a", "metadata": {}, "outputs": [], "source": ["phi = pf.CellVariable(msh, 0.0)"]}, {"cell_type": "markdown", "id": "82630c65-90a8-4362-8164-87b278c07cb8", "metadata": {}, "source": ["#### Initial condition"]}, {"cell_type": "code", "execution_count": 19, "id": "757f9322-e9b4-403b-811d-b90b431c5dee", "metadata": {}, "outputs": [], "source": ["t=0."]}, {"cell_type": "code", "execution_count": 20, "id": "c5fe25ba-d7b8-4914-93e3-8f2ab73745d8", "metadata": {}, "outputs": [], "source": ["# initial condition\n", "for i in range(loadix0, loadix1):\n", "    phi.value[:, i] = 1.0\n", "\n", "# re-apply BCs?\n", "phi.apply_BCs()"]}, {"cell_type": "code", "execution_count": 21, "id": "bae91259-c1ef-4a3f-9f4d-38fc9e2bb0d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 0.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "code", "execution_count": 22, "id": "3582db40-3eed-4ebd-b27e-49e0dc7b1c69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.1205750411731096e-10\n"]}], "source": ["initInt = phi.domainIntegral()\n", "print(initInt)"]}, {"cell_type": "code", "execution_count": 23, "id": "3d8bd4c5-69d7-4e6d-a956-44e23dfb21d9", "metadata": {}, "outputs": [], "source": ["phiprofs = []\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "markdown", "id": "dfe81c9b-9c62-43d9-97cd-1c393ee86846", "metadata": {}, "source": ["### Solve the convection PDE with time-stepping"]}, {"cell_type": "code", "execution_count": 24, "id": "15c46f1e-56a1-4fdd-bce0-da0d79b611d6", "metadata": {}, "outputs": [], "source": ["def step_solver(Nstp):\n", "    global t\n", "\n", "    # convectionterm = pf.convectionTerm(u) # really ugly results?\n", "    convectionterm = pf.convectionUpwindTerm(u) # numerical diffusion\n", "\n", "    for i in range(Nstp):\n", "        # Transient term needs to be re-evaluated at each time step\n", "        transientterm = pf.transientTerm(phi, deltat, 1.0)\n", "        eqnterms = [transientterm,\n", "                    convectionterm]\n", "        pf.solvePDE(phi, eqnterms)\n", "        t += deltat"]}, {"cell_type": "code", "execution_count": 25, "id": "307d4477-6887-4fc1-97e2-da16a296c336", "metadata": {}, "outputs": [], "source": ["step_solver(200)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 26, "id": "12053312-f7d2-4f15-bfba-11c5d2cc6bf0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.0000000000000013 2.1205750411731096e-10 2.1205750411730972e-10\n"]}], "source": ["print(t, initInt, phi.domainIntegral())"]}, {"cell_type": "code", "execution_count": 27, "id": "55f0f569-ed13-4214-ad8c-cedd54b7d471", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 2.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "code", "execution_count": 28, "id": "3cd02c01-8bd8-4f7e-9e2d-3c384d52fe11", "metadata": {}, "outputs": [], "source": ["step_solver(300)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 29, "id": "7caa6a51-d402-4850-8221-eb3fbf820113", "metadata": {}, "outputs": [], "source": ["step_solver(500)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 30, "id": "92ed2b59-1951-48c1-845a-4c8b3dece5a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9.999999999999831 2.1205750411731096e-10 2.1205750411729814e-10\n"]}], "source": ["print(t, initInt, phi.domainIntegral())"]}, {"cell_type": "code", "execution_count": 31, "id": "6f7dde56-cee7-4800-bf17-2cf322f9d402", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 10.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "markdown", "id": "5af77947-0d57-424a-9085-e56c538529f2", "metadata": {}, "source": ["## Comparison between the finite-volume result and the analytic solution\n", "\n", "<PERSON> [1] considers the radially averaged concentration profile along the tube as a function of time. We compare that to the radially integrated finite-volume result. (The ratio between the radial integral and radial average is simply constant)."]}, {"cell_type": "code", "execution_count": 32, "id": "0fafda10-02cd-4c3d-9fa5-a43f73e09f69", "metadata": {}, "outputs": [], "source": ["DX = phi.domain.facecenters.z[loadix0]\n", "X = phi.domain.facecenters.z[loadix1] - DX\n", "C_0 = phiprofs[0][1][(loadix0+loadix1)//2] # slot#0 contains initial condition"]}, {"cell_type": "code", "execution_count": 33, "id": "895d6591-4560-4297-a2f2-91fa1670b91c", "metadata": {}, "outputs": [], "source": ["zzz = np.linspace(0, Lz, 500)"]}, {"cell_type": "code", "execution_count": 34, "id": "89444352-301f-4c19-860e-90c6beeedeb5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for ix, (tprof, phiprof) in enumerate(phiprofs):\n", "    if ix == 2:\n", "        lbl1 = 'FVM'\n", "        lbl2 = 'analytic'\n", "    else:\n", "        lbl1 = None\n", "        lbl2 = None\n", "    plt.plot(phi.domain.cellcenters.z, phiprof, \n", "             label=lbl1)\n", "    if tprof >= X/umax:\n", "        plt.plot(zzz, TaylorA3_vec(zzz-DX, tprof, X, C_0, umax),\n", "                 'k:', label=lbl2)\n", "plt.xlabel('z / m')\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "977efdc8-9a67-4d37-80b5-35bea6d035b5", "metadata": {}, "source": ["The agreement of the finite-volume solution with the analytic result is quite good. The FV calculations parameters have not been optimized. There is some obvious numerical diffusion in the FV result, and also some oscillatory artefact. These numerical artefacts may be reduced by using a different discretization scheme for the convective term. Any good advice in these matters is very welcome!"]}, {"cell_type": "markdown", "id": "705334f8-619f-4ead-b587-33d81b0cdec8", "metadata": {}, "source": ["### Simple quantitative benchmark"]}, {"cell_type": "code", "execution_count": 35, "id": "51e130bb-436a-4b4c-a0ed-313fa1c35ead", "metadata": {}, "outputs": [], "source": ["(tprof, phiprof) = phiprofs[-1]\n", "z_num, c_num = phi.domain.cellcenters.z, phiprof\n", "c_an_z_num = TaylorA3_vec(z_num-DX, tprof, X, C_0, umax)\n", "norm_err = (c_an_z_num - c_num)/c_an_z_num.max()"]}, {"cell_type": "code", "execution_count": 36, "id": "3f87c073-3982-41ef-8692-f809a495cb8d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(-0.01, 0.01)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(z_num, norm_err)\n", "plt.ylabel('norm. error')\n", "plt.xlabel('$z$ / m');\n", "plt.ylim(-0.01,0.01)"]}, {"cell_type": "code", "execution_count": 37, "id": "b80f5256-2145-4ee7-a43e-5591e36192b2", "metadata": {}, "outputs": [], "source": ["# very basic benchmark for testing integrity of Notebook and calculations\n", "# checks if the normalized error is below a certain threshold (0.15% of max)\n", "# over a range of z (between 1/3 and 1/2 of full scale)\n", "assert np.all(np.abs(norm_err[Nz//3:Nz//2]) < 0.0015), 'benchmark test failed in cylindrical2D_convection'"]}, {"cell_type": "code", "execution_count": null, "id": "610871c6-2517-494c-ac09-c49102c89a06", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}