{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os.path\n", "\n", "import numpy as np\n", "from numpy import sqrt, exp, pi\n", "from scipy.special import erf\n", "import matplotlib.pyplot as plt\n", "\n", "import pyfvtool as pf"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python:  3.12.9 | packaged by conda-forge | (main, Mar  4 2025, 22:37:18) [MSC v.1943 64 bit (AMD64)]\n", "PyFVTool:  0.3.5\n"]}], "source": ["print('Python: ', sys.version)\n", "print('PyFVTool: ', pf.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Diffusion of an initial sphere into an infinite medium\n", "\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, 2020, 2024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here, we study the diffusion equation in an infinite medium with the initial condition that all matter is homogeneously distributed in a sphere radius $a$, and no matter is outside of this sphere.\n", "\n", "**Reference:** <PERSON><PERSON> (1975) \"The Mathematics of Diffusion\", 2nd Ed., \n", "      Clarendon Press (Oxford), pages 29-30, Equation 3.8, Figure 3.1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A system of spherical symmetry in a spherical coordinate system, *i.e.* \"1D spherical\", space coordinate $r$. Time $t$.\n", "\n", "System parameters:\n", "\n", "$a$ : radius of initial sphere; \n", "$c_0$ : concentration in initial sphere; \n", "$D$ : diffusion coefficient\n", "\n", "Simple diffusion equation:\n", "\n", "$$\\frac{\\partial c}{\\partial t} = D \\nabla^2 c$$\n", "\n", "with initial condition:\n", "\n", "$$ \n", "c(t = 0, r \\leqslant a) = c_0 \\quad \\\\ \n", "c(t = 0, r > a) = 0\n", "$$\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["a = 1.0\n", "C_0 = 1.0\n", "D_val = 1.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analytic solution\n", "\n", "From <PERSON><PERSON>'s \"Mathematics of Diffusion\" (2nd Ed., 1975), Chapter 3, we take Eqn 3.8 and Fig. 3.1.\n", "\n", "<PERSON><PERSON>'s Eqn 3.8 is coded below as the function ``C_sphere_infmed(r, t, a, D)``"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# this evaluates <PERSON><PERSON> (1975), eqn. (3.8)\n", "\n", "def C_sphere_infmed(r, t, a, D):\n", "    term1 = erf((a-r)/(2*sqrt(D*t))) + erf((a+r)/(2*sqrt(D*t)))\n", "    term2b = exp(-(a-r)**2/(4*D*t)) - exp(-(a+r)**2/(4*D*t))\n", "    term2a = (D*t/pi) \n", "    C = 0.5 * C_0 * term1 - C_0/r * sqrt(term2a) * term2b\n", "    return C"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjkAAAHHCAYAAABdm0mZAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/TGe4hAAAACXBIWXMAAA9hAAAPYQGoP6dpAABzZklEQVR4nO3dd3hUddrG8e+ZmfRGCCGhhN5BBOkggkgRFMUG6iq4KoodeLGga8NV1g6uoq40FUVUQEVRwRZBI0pVaYLShIQQShIS0mbO+8ckA4EkTOoU7s91zXXOnPqcOe7m4VcN0zRNRERERPyMxdMBiIiIiFQHJTkiIiLil5TkiIiIiF9SkiMiIiJ+SUmOiIiI+CUlOSIiIuKXlOSIiIiIX1KSIyIiIn5JSY6IiIj4JSU5IiIi4peU5IiIiIhfUpIjAsydOxfDMNi5c2e5z/3xxx957LHHOHLkSJVet7pUNqbSntfTz7pgwQLat29PSEgIhmGwfv16j8RR3TIzM7nvvvsYPHgwsbGxGIbBY489Vq5rHD16lPHjx1O/fn2Cg4Pp1KkT7733XqXiWrp0KYZhuD42m42YmBjOO+88pk+fTm5ubqWufzre+ruIZynJEamkH3/8kccff7zEJOeiiy4iKSmJevXq1Xxg1aS05/Xksx44cIDrr7+e5s2b88UXX5CUlESrVq1qPI6acPDgQf73v/+Rm5vLiBEjKnSNyy+/nDfffJNHH32Uzz//nG7dunHNNdfw7rvvVjiutWvXArBo0SKSkpL47rvv+N///kfbtm2ZNGkSPXv2LPF/I1XFW38X8SybpwMQ8WexsbHExsZ6Oowa4cln/eOPP8jPz+e6666jX79+VXLN7OxsQkNDq+RaValx48YcPnwYwzBIS0tj5syZ5Tp/6dKlLF++nHfffZdrrrkGgPPPP59du3Zx7733MmrUKKxWa7njWrt2LeHh4YwYMQLDMFzbr7jiCnr37s0NN9zAI488wksvvVTua7vDW38X8SyV5IhX2b59O//85z9p2bIloaGhNGjQgOHDh/Pbb78VO+6xxx7DMAw2btzINddcQ1RUFHFxcdx4442kp6eX+3onW7FiBYZhMH/+/FP2vfXWWxiGwS+//MJjjz3GvffeC0DTpk1dRfXfffcdUHIVzpYtW7jmmmuIi4sjKCiIRo0aMXr06NMW5x84cIBbbrmFhIQEgoKCiI2NpU+fPnz11VeuY1auXMkFF1xAREQEoaGh9O7dm88++6zM6wLccMMNNGnS5JTtRb/zid9Le97Sqqvcicnd91la7Oeeey4Ao0aNwjAM+vfvX6H7r127liuvvJLo6GiaN29e5n1//vlnRo0aRZMmTQgJCaFu3bpcdtll/Pnnn2WeV1lFv3lFLV68mPDwcK666qpi2//5z3+yb98+Vq1aVaHrrlmzhk6dOpUY25gxY2jYsCELFy6s0LXd4a2/i3iWkhzxKvv27SMmJob//Oc/fPHFF7zyyivYbDZ69OjB1q1bTzn+iiuuoFWrVixcuJAHHniAd999lwkTJlT4ekX69u1L586deeWVV07Z9/LLL9OtWze6devGzTffzF133QUcL6ZPSkrinHPOKfG6GzZsoFu3bvz0009MmTKFzz//nKlTp5Kbm0teXl6Zv83111/PRx99xCOPPMKyZcuYOXMmAwcO5ODBgwAkJiYyYMAA0tPTmTVrFvPnzyciIoLhw4ezYMGCMq/trvI+b3ljOt37LMnDDz/sek9PPfUUSUlJzJgxo0L3v/zyy2nRogUffPABr732Wpn3Xb9+PWeffTbTp0/nyy+/5MUXX+SPP/7g8ssvL/Uc0zQpKChw61Ndfv/9d9q2bYvNVrwgv2PHjq795XXw4EF2795N586dSz2mVatWJCcn43A4Ttnnr7+LeAFTxIsVFBSYeXl5ZsuWLc0JEya4tj/66KMmYD7zzDPFjr/99tvN4OBg0+FwlOt6c+bMMQFzx44dp2xbt26da9vPP/9sAuabb77p2vbss8+ecm5p1x0wYIBZq1YtMzU1tRy/glN4eLg5fvz4Uvf37NnTrFu3rpmZmVnseTt06GA2bNjQ9ZuU9KxjxowxGzdufMo1i37nE5X2vCVd192YKvo+i3z77bcmYH7wwQcV+k2K7v/II4+UeZ/S2O12Mz8/35w5c6YJmIcPHy4zTnc+Jf33dLIDBw6YgPnoo4+6HWvLli3NIUOGnLJ93759JmA+9dRTbl+ryLJly0zAnD17dqnH9OnTxwwLCytxn7/+LuJ5apMjXqWgoIBnnnmGefPmsX37dvLz8137Nm/efMrxl1xySbHvHTt2JCcnh9TUVOLi4sp9vRNdc8013H///bzyyiu88cYbAPz3v/8lNjaWUaNGlfvZsrOzSUxM5KabbqpQ25Xu3bszd+5cYmJiGDhwIF26dCEgIACArKwsVq1axW233UZ4eLjrHKvVyvXXX8/999/P1q1badOmTbnvW1EViel077O673/FFVe4dW3TNFm4cCEzZsxgy5YtpKSkYJomABaLhZCQkBLP69KlC7/88otb96hfv75bx1VEWdU6FanyWbNmDQCdOnUq9Zjt27fTunXrEvf56+8inqckR7zKxIkTeeWVV7j//vvp168f0dHRWCwWbr75Zo4dO3bK8TExMcW+BwUFAbiOLe/1Tr7WrbfeyvPPP8+zzz5Lfn4+77//PhMnTnTdpzwOHz6M3W6nYcOG5T4XnF2k//3vfzNz5kwefvhhwsPDueyyy3jmmWcoKCjANM0SezYV/VEoqtaqKYcPHy53TKd7n9V9f3d7ht1yyy3MnTuXW2+9lbvvvpvY2FiCgoJc/12V9t9HeHh4mYnAiU6uNqkqMTExJf63cOjQIQBq165d7muuXbuWgIAA2rdvX+L+X375hf3793PLLbeUuN9ffxfxPCU54lXmzZvH6NGjeeqpp4ptT0tLo1atWjV+vdtuu43//Oc/zJ49m5ycHAoKChg3bly54wDn/0larVb+/vvvCp1fp04dpk2bxrRp09i9ezeffPIJDzzwAKmpqSxcuBCLxUJycvIp5+3bt891fmmCg4NLbPiclpZWoVgBV0JZ0ZgqqyL3d+df61u3bmXmzJk888wzrkbY4PytNm7cyMiRI0s9NzExkfPPP9+t+Hfs2FFiY/DKOuuss5g/fz4FBQXFEoaixvgdOnQo9zXXrl1L+/btCQwMPGWfaZr861//IiQkhNtvv73E8/31dxHPU5IjXsUwjFP+FfzZZ5+xd+9eWrRoUePXq1evHldddRUzZswgLy+P4cOH06hRo2LHuFvaEBISQr9+/fjggw948sknK/UHvlGjRtx55518/fXX/PDDD4SFhdGjRw8WLVrEc88956oucTgczJs3j4YNG5Y5bkyTJk1ITU1l//79rmqhvLw8vvzyy1OOdfd5KxtTZVXX/Xft2gVAu3btim2/5557KCgooGvXrqWe6w3VMpdddhlvvPEGCxcuLFbt+uabb1K/fn169OhRruulp6fz119/ccMNN5yyLz8/n7vuuotly5bx2muvER8fX+I1/PF3Ee+gJEe8ysUXX8zcuXNp06YNHTt2ZM2aNTz77LMVruKpiuvdc889rv+DmzNnzin7zzrrLACmT5/OmDFjCAgIoHXr1kRERJxy7AsvvMC5555Ljx49eOCBB2jRogX79+/nk08+4fXXXy/xHHD+ITn//PO59tpradOmDREREfzyyy988cUXrt48U6dOZdCgQZx//vlMmjSJwMBAZsyYwe+//878+fPLLKUYNWoUjzzyCFdffTX33nsvOTk5vPTSS9jtdreftySViakqVMf9zz77bEJDQ3n44YcxDINjx47xxhtvsH37doAyk5yIiIgy97vr888/Jysri8zMTAA2bdrEhx9+CMCwYcMIDQ0lMTGRCy64gEceeYRHHnnEde7QoUMZNGgQt912GxkZGbRo0YL58+fzxRdfMG/evGJjwRiGQb9+/VxDIpRk7dq1mKZJREQEP/30Ew6Hg8OHD7N69Wrmzp1LcnIyM2bM4NZbby31GjX5uwAl/jbl+V3Eh3iw0bPIKQ4fPmzedNNNZt26dc3Q0FDz3HPPNVesWGH269fP7Nevn+u4ot4wBw4cKHb+yT183L1eST2DTtSkSROzbdu2pcY9efJks379+qbFYjEB89tvvy31ups2bTKvuuoqMyYmxgwMDDQbNWpk3nDDDWZOTk6p18/JyTHHjRtnduzY0YyMjDRDQkLM1q1bm48++qiZlZXlOm7FihXmgAEDzLCwMDMkJMTs2bOnuWTJkjJ/oyJLly41O3XqZIaEhJjNmjUzX3755RJ7V5X2vKVd152Y3H2fpSmtd1Vl71+aTz/91GzTpo0ZFBRktmvXzpw+fbr573//27RYLMV6clWXxo0bn7b3UdFvUlIPo8zMTPPuu+824+PjzcDAQLNjx47m/PnzTzkGMK+++uoyY3nuueeK3T8oKMiMj483+/fvbz722GPmvn37quqxT8ud38U0S/9t3PldxLcYplnYJUBESvTrr79y9tln88orr5TapkDE3yxdupSLL76YDRs2uErvRHyNkhyRUvz555/s2rWLBx98kN27d7N9+3avHOZfpDrce++97N27V/M2iU9TkiNSihtuuIG3336btm3b8vrrr9OnTx9PhyQiIuWgJEdERET8kuauEhEREb+kJEdERET8kpIcERER8Utn9GCADoeDffv2ERERocnXREREfIRpmmRmZlK/fn0sltLLa87oJGffvn0kJCR4OgwRERGpgD179pQ5gv0ZneQUDaG/Z88eIiMjPRyNiIiIuCMjI4OEhIRSp8IpckYnOUVVVJGRkUpyREREfMzpmpqo4bGIiIj4JSU5IiIi4peU5IiIiIhfOqPb5IiIiH+y2+3k5+d7OgypoICAAKxWa6WvoyRHRET8hmmapKSkcOTIEU+HIpVUq1Yt4uPjKzWOnZIcERHxG0UJTt26dQkNDdVArz7INE2ys7NJTU0FoF69ehW+lpIcERHxC3a73ZXgxMTEeDocqYSQkBAAUlNTqVu3boWrrtTwWERE/EJRG5zQ0FAPRyJVoeg9VqZtlZIcERHxK6qi8g9V8R6V5IiIiIhf8pok5/vvv2f48OHUr18fwzD46KOPTntOYmIiXbp0ITg4mGbNmvHaa69Vf6AiIiLiE7wmycnKyuLss8/m5Zdfduv4HTt2MGzYMPr27cu6det48MEHufvuu1m4cGE1RyoiIlK1+vfvz/jx4yt1jYULF9KuXTuCgoJo164dixcvrprgfJjX9K4aOnQoQ4cOdfv41157jUaNGjFt2jQA2rZty+rVq3nuuee44oorqilK96QdzSUn3+7RGKqbP9d5140IIsDqNfm/iMhpJSUlMWrUKJ544gkuu+wyFi9ezMiRI1m5ciU9evTwdHge4zVJTnklJSUxePDgYtuGDBnCrFmzyM/PJyAg4JRzcnNzyc3NdX3PyMioltgeWPgrX21OrZZrS/WrEx7E/LE9aBkX4elQROQMcMMNN5CYmEhiYiLTp08HnLUVTZo0cfsa06ZNY9CgQUyePBmAyZMnk5iYyLRp05g/f351hO0TfDbJSUlJIS4urti2uLg4CgoKSEtLK3HwoKlTp/L4449Xe2wBVgtBNv8tCTA9HUA1yitwkHY0l2Wb9ivJEfFxpmlyzEOl6iEBVrdLvKdPn84ff/xBhw4dmDJlCgCxsbGnPc/hcGCxOP/WJCUlMWHChGL7hwwZ4qrtOFP5bJIDp1aZmKZZ4vYikydPZuLEia7vGRkZJCQkVHlcr17XpcqvKTXjxeV/MP3rbew5lO3pUESkko7l22n3yJceufemKUMIDXTvT2xUVBSBgYGEhoYSHx/v2v7NN9/w9NNPs2HDBho3bszVV1/N1VdfTVxcHJ9//jlffvklL730ElD6P/xTUlKq7qF8kM8mOfHx8ae8vNTUVGw2W6kjXQYFBREUFFQT4YmPalTbOfjUnsNKckTEs8aOHcsTTzzBWWedxS+//MLbb7/NpEmTsFgstGvX7pSOOiX9w9+f20+6w2eTnF69erFkyZJi25YtW0bXrl1LbI8j4o6G0c6hxPcePubhSESkskICrGyaMsRj966sNWvWUKtWLQDOOussbrzxRjIyMrDb7URHRxc7trR/+J9cunOm8Zok5+jRo2zfvt31fceOHaxfv57atWvTqFEjJk+ezN69e3nrrbcAGDduHC+//DITJ05k7NixJCUlMWvWrDO6gZVUXliQ838SOfkOD0ciIpVlGIbbVUaeFhgYiN1evP1QUYJzosjIyBLP79WrF8uXLy/WLmfZsmX07t27SuP0NV7z9levXs3555/v+l7UdmbMmDHMnTuX5ORkdu/e7drftGlTli5dyoQJE3jllVeoX78+L730kse7j4tvs1mdRbsFDiU5IlJzmjRpwqpVq9i5cyfh4eHUrl3b1ajYHffccw/nnXceTz/9NJdeeikff/wxX331FStXrqzGqL2f1yQ5/fv3dzUcLsncuXNP2davXz/Wrl1bjVHJmcZW+H8q+XZ/7kMmIt5m0qRJjBkzhnbt2nHs2LFydyHv3bs37733Hv/61794+OGHad68OQsWLDijx8gBL0pyRLxBQFFJjl0lOSJSc1q1akVSUlKlrnHllVdy5ZVXVlFE/sF/B3MRqQCrxZnk5DtUkiMi4uuU5IicoGg6B5XkiIj4PiU5IiewFZbkOExwqDRHRMSnKckROYHthIk589XDSkTEpynJETlBUcNjALtKckREfJqSHJET2E4Yl0LdyEVEfJu6kFeHldMgeQMEhEJACASGQUQ9iGoAkQ2gTisICvd0lFKCojY5oMbHIiK+TklOddj1I2wra+ZbA2o3g3odoWF3aNYf6raFM3wiNW9gsRhYDGfD4wJVV4mI+DQlOdWh283QfADkZ0P+McjNhMx9kL4XjuyGrFQ49Kfzs3Gx85zwOGgxEDpcDk37g1WvxlNsVgt5BQ7yVZIjIuLT9Je0OrQaXPb+owcg5VdnldbOlc6Sn6P7Yf07zk9oHehwhTNZim1VMzGLS4DFIA8oUJscERGfpobHnhAeCy0ugL4T4fpF8MAuGP0JdL0JQmMgOw1+fh1e6QZvXw7blkMZ83pJ1SrqRq5JOkWkpvTv35/x48dX6hqJiYl06dKF4OBgmjVrxmuvvXbac3bv3s3w4cMJCwujTp063H333eTl5RU7xjRNnnvuOVq1akVQUBAJCQk89dRTrv2LFi1i0KBBxMbGEhkZSa9evfjyy+JNNubOnYthGKd8cnJyKvXMp6OSHG9gC4Jm/Zyfoc/AX9/Bmjmw5TP482vnp/45MOBfzmowtd2pVkXdyNW7SkR8xY4dOxg2bBhjx45l3rx5/PDDD9x+++3ExsZyxRVXlHiO3W7noosuIjY2lpUrV3Lw4EHGjBmDaZr897//dR13zz33sGzZMp577jnOOuss0tPTSUtLc+3//vvvGTRoEE899RS1atVizpw5DB8+nFWrVtG5c2fXcZGRkWzdurVYDMHBwVX8S5zEPIOlp6ebgJmenu7pUEp2aIdpfvGgaf67nmk+Gun8zB5qmn+v8XRkfq3Hk1+Zje//1Px1zxFPhyIi5XDs2DFz06ZN5rFjxzwdSrmMGTPGBIp9duzYUa5r3HfffWabNm2Kbbv11lvNnj17lnrO0qVLTYvFYu7du9e1bf78+WZQUJDr7+KmTZtMm81mbtmypVzxtGvXznz88cdd3+fMmWNGRUWV6xplvU93/36rusqbRTeBIU/CPRug5x1gDYJdP8AbA+CzSXDsiKcj9Eu2opnIVV0l4ttME/KyPPMpRxOD6dOn06tXL8aOHUtycjLJyckkJCQQHh5e5mfo0KGuayQlJTF4cPH2oEOGDGH16tXk5+eXeN+kpCQ6dOhA/fr1i52Tm5vLmjVrAFiyZAnNmjXj008/pWnTpjRp0oSbb76ZQ4cOlfo8DoeDzMxMateuXWz70aNHady4MQ0bNuTiiy9m3bp1bv9GFaXqKl8QHgsXPgW97oCvHoXfPoBf3oBNH8NFz0G7Sz0doV8pGitHXchFfFx+NjxV//THVYcH9znHSHNDVFQUgYGBhIaGEh8f79q+fv36Ms8LCQlxraekpBAXF1dsf1xcHAUFBaSlpVGvXr1Tzi/pnOjoaAIDA0lJSQHgr7/+YteuXXzwwQe89dZb2O12JkyYwJVXXsk333xTYlzPP/88WVlZjBw50rWtTZs2zJ07l7POOouMjAymT59Onz592LBhAy1btizzOStDSY4viWoAV8yEztfDZ/8HB7fB+6Ph7Gth6NMQHOnpCP1CUcNjdSEXEU9q0aJFuY43TmqvaRaWJp28vaxzis4r2u5wOMjNzeWtt96iVStnb99Zs2bRpUsXtm7dSuvWrYudO3/+fB577DE+/vhj6tat69res2dPevbs6frep08fzjnnHP773//y0ksvles5y0NJji9q1g9u+wESn4aVL8KGd2HXSrhqLjTo4unofJ6rJEcNj0V8W0Cos0TFU/eupPDwskfG79u3L59//jkA8fHxrtKXIqmpqdhsNmJiYko8Pz4+nlWrVhXbdvjwYfLz810lPPXq1cNms7kSHIC2bdsCzp5ZJyY5CxYs4KabbuKDDz5g4MCBZcZusVjo1q0b27ZtK/O4ylKS46tsQXDBI9ByMCy6BY7sgtkXwkXPwzmjPR2dTwtQF3IR/2AYblcZeVpgYCB2u73YtvJUV/Xq1YslS5YU279s2TK6du1KQEBAief36tWLJ598kuTkZFd11rJlywgKCqJLF+c/mPv06UNBQQF//vknzZs3B+CPP/4AoHHjxq5rzZ8/nxtvvJH58+dz0UUXnfZ5TdNk/fr1nHXWWac9tjKU5Pi6Rj1h3ApYfBts/Qw+uQv2roVhz4K15P+wpWw2dSEXkRrWpEkTVq1axc6dOwkPD6d27drlqq4aN24cL7/8MhMnTmTs2LEkJSUxa9Ys5s+f7zpm8eLFTJ48mS1btgAwePBg2rVrx/XXX8+zzz7LoUOHmDRpEmPHjiUy0tn8YeDAgZxzzjnceOONTJs2DYfDwR133MGgQYNcpTvz589n9OjRTJ8+nZ49e7pKlEJCQoiKigLg8ccfp2fPnrRs2ZKMjAxeeukl1q9fzyuvvFIlv19p1LvKHwRHwah5znF0MJxj7Lw7yjmdhJRbUXWVXQ2PRaSGTJo0CavVSrt27YiNjWX37t3lOr9p06YsXbqU7777jk6dOvHEE0/w0ksvFRsjJz09vdg4NVarlc8++4zg4GD69OnDyJEjGTFiBM8995zrGIvFwpIlS6hTpw7nnXceF110EW3btuW9995zHfP6669TUFDAHXfcQb169Vyfe+65x3XMkSNHuOWWW2jbti2DBw9m7969fP/993Tv3r0iP5fbDNM8c4fSzcjIICoqivT0dFfW6vO2fgEf/tPZqyC+I/zjA4iIP/154jLy9SR+3nGIl6/tzMUdPdQzQ0TKLScnhx07dtC0adPqH2ROql1Z79Pdv98qyfE3rS+EGz51zn+V8ivMHuKcFFTcVliQo5k0RER8nJIcf9SgC9y83DmY4OGdMOci51LcYlDYdVJZjoiIT1OS469qN4MblkLt5pC+G+YMg4N/ejoqn2DR/ypERPyC/u/cn0U1gH8uhTqtIGMvvD0CMjw0ZoQPUUmOiIh/UJLj7yLiYcynzpKdI7vh7csgu/Q5R+T4JO/KcUREfJuSnDNBRBxc/xFE1IMDW+CdK50TyEmJXMOZK8kREfFpSnLOFNGNnYlOSDTsXQOLbwWN6Fui472rlOWIiPgyJTlnkrpt4Jr3wBoIm5fAt//2dEReqWi6OuU4IiK+TUnOmaZRT7jkv871Fc/D+vllH38GshRWV5koyxER8WVKcs5EZ18Nff/Pub7kbtjzi2fj8TJFDY/VJkdExLcpyTlTnf8vaDsc7HnwwQ3qcXWCoobHqq4SkZrSv39/xo8fX6lrJCYm0qVLF4KDg2nWrBmvvfZamcdv2LCBa665hoSEBEJCQmjbti3Tp08vdszOnTsxDOOUzxdffFGpWGuKZiE/U1kscOkM2L8JDv0Ji26Ba9/XSHgcb5OjcXJExFfs2LGDYcOGMXbsWObNm8cPP/zA7bffTmxsbLFJOk+0Zs0aYmNjmTdvHgkJCfz444/ccsstWK1W7rzzzmLHfvXVV7Rv3971vXbt2tX6PFVFSc6ZLDgSRr4FMy+A7cth5Qtw3iRPR+Vxx9vkiIhUvxtuuIHExEQSExNdJSk7duygSZMmbl/jtddeo1GjRkybNg2Atm3bsnr1ap577rlSk5wbb7yx2PdmzZqRlJTEokWLTklyYmJiiI/3vcmeleSc6eI7wEXPw8d3wLdPQkIPaNrX01F5lKEu5CJ+wTRNjhUc88i9Q2whrqrv05k+fTp//PEHHTp0YMqUKQDExsYSHh5e5nl9+/bl888/ByApKYnBgwcX2z9kyBBmzZpFfn4+AQEBbsWSnp5eYinNJZdcQk5ODi1btmTChAlceeWVbl3P05TkCHS+Dnb9COvfgcXj4PYfITjK01F5jEVtckT8wrGCY/R4t4dH7r3q2lWEBoS6dWxUVBSBgYGEhoYWKy1Zv359meeFhIS41lNSUoiLiyu2Py4ujoKCAtLS0qhXr95p40hKSuL999/ns88+c20LDw/nhRdeoE+fPlgsFj755BNGjRrFm2++yXXXXefW83mSkhxxGvqMM9E5vAM+vx8uK7vBml9z9a5SliMintOiRYtyHX9yyVFRabQ7JUobN27k0ksv5ZFHHmHQoEGu7XXq1GHChAmu7127duXw4cM888wzSnLEhwSFw2Wvw5wLYcN8aD0U2l3q6ag8QiU5Iv4hxBbCqmtXeezelVWe6qr4+HhSUlKK7U9NTcVmsxETE1PmdTZt2sSAAQMYO3Ys//rXv04bV8+ePZk5c+Zpj/MGSnLkuEY9oM94ZwPkJeOd7XMifK+hWWWpd5WIfzAMw+0qI08LDAzEbrcX21ae6qpevXqxZMmSYvuXLVtG165dy2yPs3HjRgYMGMCYMWN48skn3Yp13bp1blV/eQMlOVJc/8nOnlYpv8GnE+Dqd4+3xD1DWM6sxxURL9CkSRNWrVrFzp07CQ8Pp3bt2uWqrho3bhwvv/wyEydOZOzYsSQlJTFr1izmzz8+qv3ixYuZPHkyW7ZsAZwJzvnnn8/gwYOZOHGiqyTIarUSGxsLwJtvvklAQACdO3fGYrGwZMkSXnrpJZ5++ukqfPrqo0FRpDhboLPayhIAW5c657g6wxyfhVwlOSJSMyZNmoTVaqVdu3bExsaye/fucp3ftGlTli5dynfffUenTp144okneOmll4p1H09PT2fr1q2u7x988AEHDhzgnXfeoV69eq5Pt27dil373//+N127dqVbt2689957zJ49u1g7HW9mmGdwP9mMjAyioqJIT08nMjLS0+F4l2/+Dd8/C+HxcMcqCKnl6YhqzMT317No7V4mD23Drf2aezocEXFTTk4OO3bsoGnTpgQHB3s6HKmkst6nu3+/VZIjJes7CWo3h6Mp8PXjno6mRhkUleR4OBAREakUJTlSsoBgGF44h8nq2bArybPx1KCiNjmahVxExLcpyZHSNe0Lna93rn86AewFno2nhhwf8dizcYiISOUoyZGyDZoCIbXhwGZYPcvT0dSI4+PkKMsREfFlSnKkbKG1YUDh4FDfPglZaZ6NpwYc713l4UBERKRSlOTI6XW5AeLOgpx0Z68rP6fqKhER/6AkR07PYoVhzzjX18yF5F89Gk51s2juKhERv6AkR9zTuDe0vxww4YsH/LqYo6gLuf8+oYjImUFJjrhv8BNgC4FdP8DWzz0dTbVxdSH340RORORMoCRH3BfVEHre5lz/+nG/7VJuaBZyERG/oCRHyqfPPRASDQe2wIb5pz/eBxlqkyMiNax///6MHz++wucnJydz7bXX0rp1aywWi9vX2r17N8OHDycsLIw6depw9913k5eXV+E4vI2SHCmfkFrOKR8Avn0K8o95NJzqoDY5IuJrcnNziY2N5aGHHuLss8926xy73c5FF11EVlYWK1eu5L333mPhwoX83//9XzVHW3OU5Ej5dbsZohIgcx+ses3T0VQ59a4SkZp0ww03kJiYyPTp0zEMA8Mw2LlzZ7mu0aRJE6ZPn87o0aOJiopy65xly5axadMm5s2bR+fOnRk4cCDPP/88b7zxBhkZGRV4Eu/jVUnOjBkzXLONdunShRUrVpR5/DvvvMPZZ59NaGgo9erV45///CcHDx6soWjPYAHBcP5DzvUVL8Kxw56Np4oVVVepKEfEt5mmiSM72yOf8nRcmD59Or169WLs2LEkJyeTnJxMQkIC4eHhZX6GDh1aqd8nKSmJDh06UL9+fde2IUOGkJuby5o1ayp1bW9h83QARRYsWMD48eOZMWMGffr04fXXX2fo0KFs2rSJRo0anXL8ypUrGT16NC+++CLDhw9n7969jBs3jptvvpnFixd74AnOMB1Hwo//hdSNkDQDBjzk6YiqjMU14rGyHBFfZh47xtZzunjk3q3XrsEIDXXr2KioKAIDAwkNDSU+Pt61ff369WWeFxISUpkQSUlJIS4urti26OhoAgMDSUlJqdS1vYXXJDkvvPACN910EzfffDMA06ZN48svv+TVV19l6tSppxz/008/0aRJE+6++24AmjZtyq233sozzzxTo3GfsSxW6H8/vD8afnrV2esqtLano6oaGvFYRLxAixYtqv0ehqvo+jjTNEvc7ou8IsnJy8tjzZo1PPDAA8W2Dx48mB9//LHEc3r37s1DDz3E0qVLGTp0KKmpqXz44YdcdNFFpd4nNzeX3Nxc13d/qXP0mDbDIa4D7P8dfppxfI4rH2fR3FUifsEICaH1Ws9UuxiVLGUBCA8PL3N/3759+fzzio9ZFh8fz6pVq4ptO3z4MPn5+aeU8Pgqr0hy0tLSsNvtp/yocXFxpRaZ9e7dm3feeYdRo0aRk5NDQUEBl1xyCf/9739Lvc/UqVN5/PHHqzT2M5rFAv3uh/evh59eg563+0VpzvEmOcpyRHyZYRhuVxl5WmBgIHa7vdi26q6u6tWrF08++STJycnUq1cPcDZGDgoKoksXz1TzVTWvanh8cvFYWUVmmzZt4u677+aRRx5hzZo1fPHFF+zYsYNx48aVev3JkyeTnp7u+uzZs6dK4z8jtbnYOXlnXiYkveLpaKqERYMBikgNa9KkCatWrWLnzp2kpaXhcDho0aJFmZ8GDRoUu8b69etZv349R48e5cCBA6xfv55Nmza59i9evJg2bdq4vg8ePJh27dpx/fXXs27dOr7++msmTZrE2LFjiYyMrLFnr05ekeTUqVMHq9V6SqlNampqqUVmU6dOpU+fPtx777107NiRIUOGMGPGDGbPnk1ycnKJ5wQFBREZGVnsI5VksTjb5oCzO3n2Ic/GUwUMTesgIjVs0qRJWK1W2rVrR2xsLLt37y73NTp37kznzp1Zs2YN7777Lp07d2bYsGGu/enp6WzdutX13Wq18tlnnxEcHEyfPn0YOXIkI0aM4LnnnquSZ/IGXpHkBAYG0qVLF5YvX15s+/Lly+ndu3eJ52RnZ2OxFA/farUC+uNU49pcDPFnQd5RWPW6p6OpNENtckSkhrVq1YqkpCSyC7ufN2nSpNzXME3zlM+J4+3ccMMNp/x9bNSoEZ9++inZ2dkcPHiQ//73vwQFBVXyabyHVyQ5ABMnTmTmzJnMnj2bzZs3M2HCBHbv3u2qfpo8eTKjR492HT98+HAWLVrEq6++yl9//cUPP/zA3XffTffu3Yv1+ZcaYBhw7kTn+s+vQ16WZ+OpJLXJERHxD17R8Bhg1KhRHDx4kClTppCcnEyHDh1YunQpjRs3BpzzcpxYfHfDDTeQmZnJyy+/zP/93/9Rq1YtBgwYwNNPP+2pRziztbsUopvC4R2w9q3jE3n6IPWuEhHxD4Z5BtftZGRkEBUVRXp6utrnVIXVc+DT8RDZEO5eB7ZAT0dUIS99vY0Xlv/BNd0bMfXyszwdjoi4KScnhx07drhGzhffVtb7dPfvt9dUV4kfOPsaCI+DjL/h9w89HU2FWdTwWETELyjJkaoTEOwcKwdg5TRwODwaTkUZ6kIu4tP0DxT/UBXvUUmOVK2uN0JQFKRthT++8HQ0FWJoFnIRnxQQEAA4e9+K7yt6j0XvtSK8puGx+IngSOh2E6x8AVa+CG2Gnf4cL2MU9q9SiiPiW6xWK7Vq1SI1NRWA0NBQv5mD6UximibZ2dmkpqZSq1Yt1/AwFaEkR6pez9sg6WX4+2fY8wskdPN0ROViUUmOiM8qmsW7KNER31WrVq1is7JXhJIcqXrhdeGsq2D9O7DqVZ9LcozjA+WIiI8xDIN69epRt25d8vPzPR2OVFBAQEClSnCKKMmR6tFjnDPJ2fgRDHoCohqc9hRvcXycHGU5Ir7KarVWyR9J8W1qeCzVo15HaHwumHb45Q1PR1Murt5VHo5DREQqR0mOVJ+iUY/XzIU83+ntUFRbpRGPRUR8m5IcqT6th0KtxnDsMPy6wNPRuE2DAYqI+AclOVJ9LFbocatz/adXfWZ0PQ0GKCLiH5TkSPXqfB0EhjsHB/zzG09H4xZXSY5a5YiI+DQlOVK9gqOg0z+c6z/7SAPkot5VvjkrhYiIFFKSI9Wv283O5bYv4cgez8biBpXkiIj4ByU5Uv1iW0GTvmA6nD2tvFzRtA7qXSUi4tuU5EjN6HaTc7n2LSjI82wsp3G8d5Vn4xARkcpRkiM1o83FEB4HWamw5VNPR1MmQ13IRUT8gpIcqRnWADhnjHN99WzPxnIaGvFYRMQ/KMmRmtNlDBgW2LkCDmz1dDSlOj7isdIcERFfpiRHak5UQ2g11LnuxaU5Fg0GKCLiF5TkSM3qdqNzuX4+5GV5NpZSFLXJUUmOiIhvU5IjNavZAIhuArnpsOljT0dToqKSHBER8W1KcqRmWSzQ+Xrn+tq3PRtLKVSSIyLiH5TkSM3rdK2zAfLuHyFtu6ejOYUm6BQR8Q9KcqTmRdaHFoOc6+u8rzRHvatERPyDkhzxjHMKq6zWvwv2fM/GchL1rhIR8Q9KcsQzWl0IYbHOEZC3LfN0NMUYmtZBRMQvKMkRz7AGwNlXO9e9rAGyZiEXEfEPSnLEczqPdi63LYPMFM/GUoxmIRcR8QdKcsRzYltBQk8w7c62OV7Coi7kIiJ+QUmOeFZRA+R1b3tNIxh1IRcR8Q9KcsSz2o2AwHA49Bfs+tHT0QDHu5ArxxER8W1KcsSzgsKh/Qjn+q/veTSUIq5ZHVSUIyLi05TkiOd1LOxltfEjyD/m0VDghC7kng1DREQqSUmOeF7jPhCVALkZsPVzT0eDgdrkiIj4AyU54nkWC3Qc5Vzf4AVVVhonR0TELyjJEe9QNDDg9q/gaKpHQ1GTHBER/6AkR7xDnZbQoKtzzJzfPvRoKOpCLiLiH5TkiPcoKs3ZMN+jYagLuYiIf1CSI96jwxVgCYCUX2H/Jo+FcXyCTqU5IiK+TEmOeI/Q2tBqiHPdg2PmGK6yHBER8WVKcsS7FFVZ/fo+OOweCeF4SY5Hbi8iIlVESY54l5aDISQaMpNhR6JHQjjeJkdZjoiIL1OSI97FFuRsmwOeGzNHJTkiIn5BSY54n6KBATd/CnnZNX5714jHNX5nERGpSkpyxPs07Aa1GkF+FvzxRY3fXr2rRET8g5Ic8T6GAR2udK7/vrDmb1+4VIojIuLblOSIdzqrMMnZtgyOHanRWxuahlxExC8oyRHvFNceYtuCPQ82L6nRWyvHERHxD0pyxHudVdjL6veancvq+ASdSnNERHyZkhzxXkXtcnZ8D5n7a+y2KskREfEPSnLEe9VuWjgzuQM2Lq7BG2sWchERf6AkR7xbUQPkGqyyOl6SoyxHRMSXKckR79b+MjAs8PcvcHhnjdzyeJucGrmdiIhUEyU54t0i4qHJuc71Ghozp6gLuZIcERHfpiRHvN9ZVzmXv9VMlZVx+kNERMQHeFWSM2PGDJo2bUpwcDBdunRhxYoVZR6fm5vLQw89ROPGjQkKCqJ58+bMnj27hqKVGtN2OFgCIHUT7N9U7bfTtA4iIv7Ba5KcBQsWMH78eB566CHWrVtH3759GTp0KLt37y71nJEjR/L1118za9Ystm7dyvz582nTpk0NRi01IiQaWg5yrtdAA2RN0Cki4h+8Jsl54YUXuOmmm7j55ptp27Yt06ZNIyEhgVdffbXE47/44gsSExNZunQpAwcOpEmTJnTv3p3evXvXcORSIzoUDQy4qNobyxwvyanW24iISDXziiQnLy+PNWvWMHjw4GLbBw8ezI8//ljiOZ988gldu3blmWeeoUGDBrRq1YpJkyZx7NixUu+Tm5tLRkZGsY/4iFYXgi0YDu+A5A01ckt1IRcR8W1ekeSkpaVht9uJi4srtj0uLo6UlJQSz/nrr79YuXIlv//+O4sXL2batGl8+OGH3HHHHaXeZ+rUqURFRbk+CQkJVfocUo2CwqFlYRK86aNqvZVKckRE/INXJDlFXLM/FzJN85RtRRwOB4Zh8M4779C9e3eGDRvGCy+8wNy5c0stzZk8eTLp6emuz549e6r8GaQatb/Mudy4uFozELXJERHxD16R5NSpUwer1XpKqU1qauoppTtF6tWrR4MGDYiKinJta9u2LaZp8vfff5d4TlBQEJGRkcU+4kNaDQFbiHNQwOT11XYbleSIiPgHr0hyAgMD6dKlC8uXLy+2ffny5aU2JO7Tpw/79u3j6NGjrm1//PEHFouFhg0bVmu84iGBYdCqsMpq40fVdpvjhYfKckREfJlXJDkAEydOZObMmcyePZvNmzczYcIEdu/ezbhx4wBnVdPo0aNdx1977bXExMTwz3/+k02bNvH9999z7733cuONNxISEuKpx5DqVgNVVoYm6BQR8Qs2TwdQZNSoURw8eJApU6aQnJxMhw4dWLp0KY0bNwYgOTm52Jg54eHhLF++nLvuuouuXbsSExPDyJEj+fe//+2pR5Ca0HIwBITCkV3OKqv6nav8Fscn6BQREV9mmGfwsK4ZGRlERUWRnp6u9jm+5P0xzh5Wfe6BQVOq/PLb9mcy6MXviQ4NYN0jg09/goiI1Ch3/357TXWViNuqucpKJTkiIv5BSY74HleV1W7Yt64abqA2OSIi/kBJjviewFDnCMjgLM2pYpqgU0TEPyjJEd/UfoRzufGjKi9yKepBrhRHRMS3KckR39RiEASEQfpu2Le2Si9tqFGOiIhfUJIjvikwFFpXT5WVSnJERPyDkhzxXe1GOJcbP67SKiu1yRER8Q9KcsR3tTyhympv1VVZaYJOERH/oCRHfFdACLQe6lzfuKjKLqsJOkVE/IOSHPFt7S51Ljd/UuVZiamyHBERn6YkR3xbi4HHBwZM3lAll1RJjoiIf1CSI74tMNSZ6ABs+rhKLlnUhVw5joiIb1OSI76viqusirqQK8sREfFttsqcfOTIEb788kv27t2LYRjUq1ePIUOGEB0dXVXxiZxeqyFgDYKD2yF1M8S1q9Tljo8FqCxHRMSXVbgkZ9asWXTv3p2ffvoJh8OB3W7np59+omfPnsyaNasqYxQpW1AENB/gXN/8SaUvZ2iCThERv1DhkpxnnnmGtWvXEh4eXmz7E088QZcuXbjpppsqHZyI29pdAn98Dps+gf4PVOpSmtVBRMQ/VLgkxzAMjh49esr2o0ePHp/7R6SmtB4KFhukboS07ZW6lGtaBxXliIj4tAqX5Dz33HP069ePDh060KBBAwD+/vtvNm7cyPPPP19lAYq4JSQamp4Hf34Dmz+Gvv9X8WupJEdExC9UOMm5+OKLGTp0KD///DP79u3DNE0aNGhA9+7dsVqtVRmjiHvaXuJMcjZ9UqkkR21yRET8g9tJzrZt25g9ezb3338/tWrVAsBqtdKrV6/qik2kfNpcDJ9NhOT1cHgXRDeu0GVU2yoi4h/cbpPzn//8h61bt7oSnBPl5OSwadOmqoxLpPzCY6FxH+f65iUVvsyJOY7a5YiI+C63k5zExETuvvvuEvcFBwczbtw4nnrqqSoLTKRC2l7iXFaiK/mJDeeV44iI+C63k5y9e/fSvHnzUvffeuutfPJJ5ccoEamUthc7l3tWQca+Cl2iWElO5SMSEREPcTvJqV27NsnJyaXu7969O9u3V67rrkilRdaHht2d65s/rdAlTmyTo+oqERHf5XaSc9555zF37tzSL2SxkJubWxUxiVROu8pVWRknlOUoxRER8V1uJzmTJk3ijTfe4H//+1+J+5OSkmjWrFmVBSZSYW2HO5e7foCstPKfX6wkp2pCEhGRmud2ktOlSxdeffVVbr/9dgYNGsRHH33E7t27OXToEB9//DH3338///jHP6ozVhH3RDeBep3AdMCW8ldZFauuUlmOiIjPKte0DjfffDPfffcd6enpXH755TRt2pTY2Fguu+wyzjrrLCZMmFBdcYqUT1GV1abyV1kV70JeNeGIiEjNK/eIx+eeey4///wzW7ZsYe3atWRnZ9OhQwd69uxZHfGJVEzbS+HrKbAjEY4ddk774CbNvSYi4h/cTnIefPBBRowYQffuzp4rbdq0oU2bNtUWmEil1GkBddtB6ibY+jl0utbtU1WSIyLiH9yurkpOTubiiy+mXr163HLLLXz22WfqTSXerW3FqqzUJkdExD+4neTMmTOH/fv38/7771OrVi3+7//+jzp16nD55Zczd+5c0tIq0ItFpDoVtcv58xvIzXT7tGJdyJXjiIj4rHI1PDYMg759+/LMM8+wZcsWfv75Z3r27Mkbb7xBgwYNOO+883juuefYu3dvdcUr4r667SCmBdhz4Y8v3T6teEmOiIj4KreTnG3btjF58mSOHDni2ta2bVvuu+8+fvjhB/7++2/GjBnDihUrmD9/fnXEKlI+hlHpuaw04rGIiO9yu+Hxf/7zHw4fPlziLOS5ubkcOHCAm266iZtuuqkq4xOpnHaXwMoXYNtyyMuGwNDTnqKSHBER/1Als5AHBQVpFnLxTvU6Qa1GkJ8N279y6xS1yRER8Q+ahVz8WwWqrAxNQy4i4hc0C7n4v6IkZ+sXUHD6YQ+K5zjKckREfJVmIRf/17AbRNSDvEz467vTHn7iiMeqrhIR8V2ahVz8n8VywsCAH5/2cNVWiYj4B81CLmeGooEBt3wKBXllHlqsd5WKckREfJZmIZczQ6NeEBYLOemw8/syDy1WXVXdcYmISLXRLORyZrBYoe1wWD3bWWXVYqBbp6kgR0TEd5U7ySmiWcjF57S71JnkbPkMLnoRrKX/528YzgRHvatERHxXuaqrRHxa43MhpDZkH4RdP5R5qKvCSjmOiIjPUpIjZw6rDdpc5Fw/TS+ronY5ynFERHyXkhw5s7Qb4VxuXgIOe6mHFZXkqE2OiIjvUpIjZ5am50FwFGSlwp5VpR5W1MFKbXJERHyXkhw5s9gCofXpq6yKJulUSY6IiO9SkiNnnqKBATd9Ag5Hyce4SnJERMRXVbgLuZTu2V+eZe3+tQRaAwmyBhFkDSLEFkJ0cDQxITHEBMdQJ6QOCZEJJIQnEGAN8HTIZ5Zm50NgBGTug72rIaH7KYccb5OjNEdExFcpyakGOzN28vvB39061mpYaRDegMaRjWkb05YOMR3oUKcDsaGx1RzlGSwgGFpfCL994KyyKinJKSrJUY4jIuKzlORUgzs63cGo1qPIKcgh155Lnj2P7IJsDuUc4uCxgxzMOUhqdiq7M3aTXZDN7szd7M7czYq9K1zXiAuNo0tcF3rW60mv+r2ID4v34BP5oXaXFiY5n8DgfxefsIrjbXJERMR3KcmpBu1i2rl1nGmaHDh2gF0Zu/jzyJ9sPLiR39N+56/0v9ifvZ+lO5aydMdSAJpENqFvw74MbDSQTnU7YTHUnKpSml8AAaGQvhv2rYMG5xTbrZIcERHfpyTHgwzDoG5oXeqG1qVbfDfX9uz8bH5P+51VKav4Kfknfk/7nZ0ZO9m5aSdvb3qbmOAYBjQawNCmQ+kS10UJT0UEhkLLwbDpI2eV1clJTuFSXchFRHyXYZ7BLSszMjKIiooiPT2dyMhIT4dTqoy8DH5O/pmvd39N4p5EMvMzXfsahDfg0uaXMrz5cBpGNPRglD7o90Xw4T8huincva5YlVWHR7/kaG4B303qT5M6YR4MUkRETubu32+vKgKYMWMGTZs2JTg4mC5durBixYrTnwT88MMP2Gw2OnXqVL0BekhkYCQDGw9kat+pJI5K5LWBr3F5y8sJCwhj79G9zNgwg6GLhnLLsltI3JOIwyylW7QU13Iw2ILh8A7YX7yh+PGSHBER8VVek+QsWLCA8ePH89BDD7Fu3Tr69u3L0KFD2b17d5nnpaenM3r0aC644IIaitSzAqwB9GnQh8d7P863I79lat+p9KzXEwODpOQk7vzmTi5efDFvb3qbzLzM01/wTBYUDi0GOtdPHhjQ1SZHaY6IiK/ymuqqHj16cM455/Dqq6+6trVt25YRI0YwderUUs+7+uqradmyJVarlY8++oj169e7fU9fqa5yx96je1mwZQEfbvvQldyE2kIZ1WYUY9qNISYkxsMReqlf34dFYyGmJdz5i6vKquNjX5KRU8DX/9eP5rHhHg5SRERO5FPVVXl5eaxZs4bBgwcX2z548GB+/PHHUs+bM2cOf/75J48++mh1h+j1GoQ3YGLXiXx15Vc83PNhmkc1J7sgmzm/z+HChRfy9M9Pk5qd6ukwvU+rIWANhIPb4MAW12bXLORe8U8AERGpCK9IctLS0rDb7cTFxRXbHhcXR0pKSonnbNu2jQceeIB33nkHm829TmK5ublkZGQU+/ib0IBQRrYeyeJLF/PygJc5q85Z5NhzmLd5HhcuvJCpq6Zy8NhBT4fpPYKjoPkA5/qmT1ybj7dBVpYjIuKrvCLJKWKcNCCbaZqnbAOw2+1ce+21PP7447Rq1crt60+dOpWoqCjXJyEhodIxeyvDMOiX0I93hr3D6wNf55y655DvyOfdLe8ybNEwXt3wKtn52Z4O0zu0u9S5PKFdzvFpHWo+HBERqRpekeTUqVMHq9V6SqlNamrqKaU7AJmZmaxevZo777wTm82GzWZjypQpbNiwAZvNxjfffFPifSZPnkx6errrs2fPnmp5Hm9iGAa9G/Rm7oVzeWPwG7SPaU92QTYz1s9g2KJhLNiygAJHgafD9KzWQ8Fig9SNkLYNOKG6ypNxiYhIpXhFkhMYGEiXLl1Yvnx5se3Lly+nd+/epxwfGRnJb7/9xvr1612fcePG0bp1a9avX0+PHj1KvE9QUBCRkZHFPmcKwzDoWa8n7170Ls/2e5aEiAQO5hzk36v+zchPR/JLyi+eDtFzQqKhaT/nemFpjkpyRER8n1ckOQATJ05k5syZzJ49m82bNzNhwgR2797NuHHjAGcpzOjRowGwWCx06NCh2Kdu3boEBwfToUMHwsI0eFtpLIaFC5tcyMeXfsyDPR4kKiiKbYe3ceOXN3Lf9/exP2u/p0P0jKIqq83OdjmuaR1UliMi4rO8JskZNWoU06ZNY8qUKXTq1Invv/+epUuX0rhxYwCSk5NPO2aOuC/AGsA1ba7h0xGfMqr1KAwMPt/xOcM/Gs7s32eT78j3dIg1q83FYFgheQMc2kFRWY5KckREfJfXjJPjCf40Tk5lbTq4iamrprL+wHoAWkW3YkrvKbSv096zgdWkN4fDju9h4ON0S+zAgcxclt7dl3b1z+z/NkREvI1PjZMjntcuph1vDX2LJ899kuigaP44/AfXLr2W5355jmMFxzwdXs1oN8K53LhYE3SKiPgBJTniYhgGlzS/hI9GfMSwpsNwmA7e3PQml398OT8l/+Tp8Kpfu0sLq6zWk0AyoOoqERFfpiRHTlE7uDZPn/c0r1zwCvFh8fx99G/GLhvLkz896d+lOmF1oJmzl9UgR+kjbYuIiG9QkiOlOq/heXx06UeMaj0KgPe2vsfIJSP57cBvHo6sGrW/HIBBjpWASnJERHyZkhwpU1hAGP/q+S9eH/Q6dUPqsjNjJ9d/fj0z1s/wzx5YbS8GSwDNzd20MP5WmxwRER+mJEfc0rt+bxZduoihTYZiN+28uuFVRi8dzc70nZ4OrWqFREOLCwAYbv1JJTkiIj5MSY64LSooimf6PcPTfZ8mIjCC3w/+zshPR7LkzyWeDq1qFVZZXWxJ4gweYUFExOcpyZFyG9ZsGIsuWUS3+G4cKzjGgysf5OEfHvafCT9bDyWXQJpbkgk+uNHT0YiISAUpyZEKiQ+L541Bb3D72bdjMSx8tP0jrvnsGrYd3ubp0CovOJIk6zkA1NrxqYeDERGRilKSIxVmtVi5rdNtzBw8k9iQWP5K/4trPruGhX8s9Plqnm+t5wIQ/dcSdbESEfFRSnKk0rrFd+OD4R/Qp0Efcu25PJb0GA+tfMinx9T5ydaVLDOIoKN/w961ng5HREQqQEmOVImYkBhmXDCDCV0mYDWsLPlrCdcvvZ49mXs8HVqF5BrBfO1wVlnx+0LPBiMiIhWiJEeqjMWwcGOHG3lj8BvUDq7N1sNbufrTq1nx9wpPh1ZuhmHwqb2n88vGxeBweDYgEREpNyU5UuW6xXdjwcUL6FinIxl5Gdzx9R28vuF1HKbvJAqGAYmOsykIiIDMfbBnladDEhGRclKSI9UiPiyeORfO4apWV2Fi8vL6l7nnm3vIyMvwdGhuMYBcAjmcMMi5QVVWIiI+R0mOVJtAayCP9HqEKb2nEGgJ5Lu/v+Paz65lR/oOT4d2WhbDACCtyUXODZs+BofdgxGJiEh5KcmRandZy8t4a9hbxIfFsytjF//47B/8uNe7Z/kuzHE4EtfbOdVDVirs9L22RSIiZzIlOVIj2se0Z/5F8+kU24nM/Exu+/o23tn8jteOp2PgzHIc1kBoe4lz428feDAiEREpLyU5UmPqhNRh1pBZXNr8Uhymg//8/B8eT3qcfLv3zWZeVJJjmkDHUc4vmz6BfN8d+0dE5EyjJEdqVKA1kCf6PMGkrpMwMFi4bSFjl4/lcM5hT4dWjFGY5ThMExr1gqgEyM2AP77wcGQiIuIuJTlS4wzDYEz7Mbx8wcuEBYSxZv8ar5v3qrAgBxPAYoGzrnJu+PV9D0UkIiLlpSRHPOa8hufxzrB3aBjekL1H93Ld0utYuXelp8MCnHkNcLzNUFGV1bZlkH3IM0GJiEi5KMkRj2peqznzL5pPt/huZBdkc+fXd/LBH55v4FvU8NjVLrpuG4g/CxwFzhGQRUTE6ynJEY+rFVyL1we+ziXNL8Fu2pmSNIUX1rzg0RGSLUUNjzmh91dRaY6qrEREfIKSHPEKAdYA/t3n39ze6XYA5vw+h3sT7yWnIMczARU1PD4xz+pwJWDAnp/gkPcPaCgicqZTkiNewzAMbjv7Np469ylsFhvLdi3j5mU3cyin5tvAFGt4XCSyHjTr51z/7cMajkhERMpLSY54neHNh/O/Qf8jIjCCDQc28I/P/lHjU0G4qqtOHqzQVWW14IQGOyIi4o2U5IhX6hbfjXnD5tEgvAF/H/2b65Zex+qU1TV2/+Pj5Jy0o83FYAuBg9tg37oai0dERMpPSY54rWZRzXhn2Dt0rNORjLwMbll+C5/99VmN3NtwrZ2U5QRHQpthznU1QBYR8WpKcsSrxYTEMGvILAY1HkS+I58HVjzAnN/nVPucV0WzkJd4m6Iqq98/BHtBtcYhIiIVpyRHvF6wLZjn+j3HdW2vA+CFNS/wzC/PVG8X88KinFOqqwCaD4DQGMg6AH99W30xiIhIpSjJEZ9gMSzc3/1+JnWdBMC8zfO4N/Fecu251XQ/59I8uboKwBpQ2J0cWP9OtdxfREQqT0mO+JQx7cfwdN+nXV3Mxy0fR0ZeRpXfp2jE4xJLcgA6/8O53PKZpnkQEfFSSnLE5wxrNozXBr5GWEAYq/evZsznY0jJSqnSexildSEvUu9siDsL7Hnw+8IqvbeIiFQNJTnik3rU68GbF75JbEgs249s57ql11XpLOZFDY/LVFSas25eld1XRESqjpIc8Vmta7dm3rB5NI1qyv7s/Yz5YkyVjaVjuBoel9GL66yRYAmA5PWwf2OV3FdERKqOkhzxafXD6/P20LfpFNuJzLxMbll+C8t2Lqv0dY2yupAXCYuB1hc619epAbKIiLdRkiM+LyooijcGv8GAhAHkO/KZlDiJdzZXLukoqqwqteFxkc7XO5e/LgB7fqXuKSIiVUtJjviFYFswL/R/gVGtR2Fi8p+f/8MLa16o8Fg6p214XKT5BRAeD9lp8MeXFbqXiIhUDyU54jesFisP9XiIuzvfDcCc3+fw4MoHya9ACYtrxOPT3tQGZxeOgKwxc0REvIqSHPErhmEwtuNYnujzBFbDymd/fcbtX99OVn5W+a5TuHRr+ohOzpGY+eNLyNxfvoBFRKTaKMkRvzSixQhevuBlQmwh/JT8E//84p+kHUtz+/zj1VVuHBzbChp2A9MOG+ZXLGAREalySnLEb53b4FxmD5lN7eDabD60meuWXseO9B1unWu4W11V5JzRzuXaN8FRjXNqiYiI25TkiF/rUKcD84bOIyEigb1H9zL689FsOLDhtOcd713lZprT4QoIioRDf8GOxIoHLCIiVUZJjvi9hMgE3h76Nu1j2nMk9wg3f3kz3+35rsxzLO6Mk3OiwDDoWNgAec2cCscqIiJVR0mOnBFiQmKYPWQ25zY4lxx7Dvd8ew8f/vFhqce73YX8RF3/6Vxu+UwNkEVEvICSHDljhAaE8tKAlxjRYgQO08HjSY8zY/2MEhMZV5JTnhvEtYeG3cFRAOs1n5WIiKcpyZEzSoAlgCm9p3BLx1sAeHXDqzyW9BgFjoJix7k1rUNJikpz1qgBsoiIpynJkTOOYRjc1fkuHu75MBbDwqJti7jn23vIzs8+fkzh0u2Gx0XaXwbBUXBkF/z1TdUFLSIi5aYkR85YI1uP5MX+LxJkDeL7v7/n5mU3cyjnEFCJkpyAEDj7Guf6ajVAFhHxJCU5ckYb0GgAMwfPJCooit/SfmP056PZk7kHS2FRTrlLcgC6FFZZbf0cMpKrLlgRESkXJTlyxutUtxNvDX2LemH12JWxi+uXXk+mY2fFL1i3DTTq5RwBee2bVRaniIiUj5IcEaBZVDPmDZtH6+jWHMw5yNr8p7CG/VH+6qoi3W52LlfPhoK8KotTRETcpyRHpFDd0LrMuXAOPeJ7YCeHkIS5bMqsYOPhdpdCRD04uh82fVy1gYqIiFuU5IicICIwglcHvkq8tReG4eCrtOnM+m1W+QYFBLAGQNebnOurXqv6QEVE5LSU5IicJMAawNmBt5F3sC8A09ZOY+rPU7E77OW7UJcbwBoIe1fD36urPlARESmTVyU5M2bMoGnTpgQHB9OlSxdWrFhR6rGLFi1i0KBBxMbGEhkZSa9evfjyyy9rMFrxZxaLhdzUi+hVy9lTav6W+dz7/b3k2nPdv0h4LHS40rmu0hwRkRrnNUnOggULGD9+PA899BDr1q2jb9++DB06lN27d5d4/Pfff8+gQYNYunQpa9as4fzzz2f48OGsW7euhiMXf1Q0QefZkZfw7HnPEmAJYPmu5dyy7BbSc9Pdv1AP58jKbFys7uQiIjXMa5KcF154gZtuuombb76Ztm3bMm3aNBISEnj11VdLPH7atGncd999dOvWjZYtW/LUU0/RsmVLlixZUsORiz8qmrvK4TC5sOmFvDbwNcIDwlmbupbrll7H7oySk+9T1O8MCT2d81lpdnIRkRrlFUlOXl4ea9asYfDgwcW2Dx48mB9//NGtazgcDjIzM6ldu3Z1hChnnMIRjwu/da/X3TWWzs6Mnfxj6T9Yu3+te5fqcatzuXo25OdUfagiIlIir0hy0tLSsNvtxMXFFdseFxdHSkqKW9d4/vnnycrKYuTIkaUek5ubS0ZGRrGPSEmKRjw+sVNVy+iWvHvRu7SPac+R3CPcvOxmlv619PQXazscIhtC1gH4dUH1BCwiIqfwiiSnSNF8QUVM0zxlW0nmz5/PY489xoIFC6hbt26px02dOpWoqCjXJyEhodIxi38ySpnWoU5IHeZcOIcLGl1AviOf+1fcz+sbXi+7i7k1AHrd7lz/8SXNTi4iUkO8IsmpU6cOVqv1lFKb1NTUU0p3TrZgwQJuuukm3n//fQYOHFjmsZMnTyY9Pd312bNnT6VjF/9knFRddaIQWwjP93ueMe3GAPDy+pf51w//It+eX/oFzxntnJ384HbY6kbpj4iIVJpXJDmBgYF06dKF5cuXF9u+fPlyevfuXep58+fP54YbbuDdd9/loosuOu19goKCiIyMLPYRKUlRdVVp8zpYLVYmdZvEwz0fxmpY+eTPT7j1q1tL73kVFHF8qocfplVgenMRESkvr0hyACZOnMjMmTOZPXs2mzdvZsKECezevZtx48YBzlKY0aNHu46fP38+o0eP5vnnn6dnz56kpKSQkpJCeno5uveKlKKomtRxmlxkZOuRvHLBK4QFhPFLyi9ct/Q69mSUUkLY/VawBsHfv8Dun6o4YhEROZnXJDmjRo1i2rRpTJkyhU6dOvH999+zdOlSGjduDEBycnKxMXNef/11CgoKuOOOO6hXr57rc88993jqEcSPFLXJMUussCquT4M+vDX0LeLD4l09r1anlDDCcUQcdLrGuf7D9CqMVkRESmKY5Z6Ux39kZGQQFRVFenq6qq6kmClLNjH7hx3c1r8591/Yxq1zDmQf4K5v7mLjwY3YDBsP9XyIK1tdWfygtO3wclfAhNtXQV33ri0iIse5+/fba0pyRLyJUUIX8tOJDY1lzoVzuLDJhRSYBTye9DhPrXqKfMcJDZLrtIC2FzvXVZojIlKtlOSIlMBSjuqqE4XYQnjmvGe4q/NdgHPOq9uW38aRnCPHD+ozwbn8dQEc2lEF0YqISEmU5IiUoKjhcUUqcw3D4JaOtzD9/OmE2EJYlbKKa5dey59H/nQe0LALNL8ATDuseL4KoxYRkRMpyREpwfEe5BVvsjag0QDmDZtHg/AG7Mncwz+W/oPEPYnOnf0fcC43zIfDuyoXrIiIlEhJjkgJ3O1Cfjqtolsx/6L5dI3rSlZ+Fnd9cxczf5uJ2bAbNDvfOXGnSnNERKqFkhyRElSk4XFpooOj+d/g/zGy1UhMTKavnc6kxElk9Skc7mD9O3DEzVnNRUTEbUpyREpQ0YbHpQmwBPBwr4f5V49/YbPYWLZrGdf+No2/mvYsLM15oUruIyIixynJESmBa+6qKh5FalSbUcwZMoe6IXX5K/0vrrUe4qvQEFj3Nhz6q2pvJiJyhlOSI1KC49VVVT9WZqe6nVgwfIGznY49hwlxsbwQFU7B1/+u8nuJiJzJlOSIlMDVhbyarl8npA5vDH7DNZP5nFqRjDu4goM7V1TTHUVEzjxKckRKUNSF3FGNs57YLDYmdZvEc/2eIwQLq0KCGZV4NxsObKi2e4qInEmU5IiUwFKJwQDLa0iTIczvN50m+fnsp4AbPh/NnN/n4DAd1X9zERE/piRHpARFbXIqO06Ou5o36c/8uoMZejSLAtPBC2te4I6v7+BQzqGaCUBExA8pyREpgeFaq6EsBwg//yGePnKMxw4cJMiwsXLvSq765Cp+SfmlxmIQEfEnSnJESmCx1Fx1lUtEHMa5E7niaBbvHs6lWWQTUo+lcvOym3ltw2vYHfYaDEZExPcpyREpQ3U2PC5R7zshqhGtDu9lfmR3RrQYgcN08Mr6V7h1+a3sz9pfs/GIiPgwJTkiJajKaR3KJSAEBj8BQGjSyzzR/haeOvcp12zml39yOV/u/LKGgxIR8U1KckRKYKnmcXLK1O5SaHwuFOTA8kcY3nw4Cy5eQLuYdmTkZTApcRIPrniQzLxMT0QnIuIzlOSIlKAmxskp/eYGXDgVDAtsXAQ7vqdpVFPmDZvHLR1vwWJYWPLXEq785EpWp6yu+fhERHyEkhyRElhc9VUeCqBeR+h6o3N9yXjIzyHAEsBdne/izQvfpEF4A/Zl7ePGL2/khTUvkGfP81CgIiLeS0mOSAmOj5PjqSwHuOARCI+HQ3/CiuddmzvV7cTCSxYyosUITEzm/D6HUZ+OYmPaRs/FKiLihZTkiJTBgykOBEfB0Ked6ytfhNQtrl1hAWE80ecJXuz/ItFB0Ww/sp1/LP0H09ZMI9ee66GARUS8i5IckRLU5LQOZWp3KbS6EBz58Ol4cBSf6mFg44F8NOIjLmxyIXbTzqzfZ3HVkqtYn7reI+GKiHgTJTkiJfCK6qqiQIY9BwFhsDsJVs865ZDawbV5tt+zTOs/jZjgGHak72D056N55pdnOFZwzANBi4h4ByU5IiXwaBfyk9VKgIGPOteXPQwH/yzxsAsaX8DHIz7mkuaXYGLy9qa3ufzjy1m5d2UNBisi4j2U5IiU4PhggF6R5kC3sdC0HxQcg8W3gr2gxMOigqJ48twneeWCV6gbWpe/j/7NbV/dxv99938aLVlEzjhKckRKUDROjrfkOFgsMGIGBEXB37/ADy+Wefh5Dc/jkxGfcH2767EYFpbtWsalH1/KvE3zKHCUnCCJiPgbJTkiJTAKi3I83ibnRFENYdgzzvXv/gP71pd5eFhAGPd1u48FFy+gY52OZOVn8fQvT3PNZ9fw64Ffqz9eEREPU5IjUgKrpSjJ8XAgJ+s4CtpeAo4C+OAGyEk/7Sltarfh7WFv80ivR4gIjGDLoS1ct/Q6/rXyXxzIPlD9MYuIeIiSHJESFCU5dm/LcgwDhk+HqEZweAd8fKdbdWoWw8JVra5iyYglrobJH//5MRctvog3fn1DY+uIiF9SkiNSAlthklPgbUkOQGhtuGouWAJg8yew6nW3T40JieHJc59k3rB5dKzTkWMFx3hp3UtcsvgSvtz5pfc0tBYRqQJKckRKUFSSU2B3nOZID2nYBYY86Vxf9i/4u3wTdZ4dezZvD3ubqX2nUje0Lvuy9jEpcRI3fHGDBhIUEb9h83QA/igrKYn8lP0YFsNZvWBYwGJgWCzF1y2Wwv2n7jNsNrDZMAICMQICMAJshcsADFvxdQICXA1lpWrYLM783ytLcop0vwV2/QCbPob3/gFjv4GoBm6fbjEsXNzsYgYkDGDuxrnM+X0Oa1PXcv3n19O/YX/uOucuWkW3qsYHEBGpXkpyqsGht97m6Lff1uxNT0x+AgOxBAdjBAdhCQrGCAl2LoODS97u2h+EJSTE+QkLO/4JDXWtG1ZrzT6Xh3htm5wTGQZc8jKkbYPUTTD/arjxCwgMK9dlQgNCub3T7Vze8nJe2/Aai7cv5ru/vyPx70QubnYxt3e6nYYRDavpIUREqo+SnGoQ3L49pr3A2TXH4cA0Haeum0Xfncvi63bMAjtmfr7zU1BQbJ38/FNvWrT/mHMYf3s1PZsRHFxCAnQ8CbKGhWEJC8cSGYE1IhJrVCSWiEiskRHHl+HhzpIrLxZg9eI2OScKjoRr3oM3BkDKr7DoFhj5trOUsJziw+J5rPdjjGk/hpfXvcyyXctY8tcSPt/5OVe2vJKbzrqJ+LD4angIEZHqYZhncEvDjIwMoqKiSE9PJzIy0tPhuM00TWdSc1Ly41rPzcWRk3N8mZNTuMzFkXPMuczNwTyW41zm5GLm5uA4luPcn30MR3YW9qwszKxs7NnZJSdWFWUYWMLDsUZGYomMxBoRcTwpciVDkVija2GtdcInOtpZmlQDVXPfbU3lhjm/0KFBJJ/e1bfa71dpu1fBmxeDPQ963w2Dn6j0JTembWT62ukkJScBYLPYuKzFZdx01k00CHe/WkxEpKq5+/dbSY4PJjme4MjLw5GVVfjJPmH9hE+2c7v9aCaOzKPYM9JxZGRiz8zEkZGBPTMTMyencoEEBGCNisJaKwprrVrYoqOLJ0KFyVCx71FR5a5mW7ktjetmraJNfARfjD+vcjHXlF/fh0VjnesDH4NzJ1TJZX9O/pnXfn2NX1J+AcBm2BjefDg3n3UzjSIbVck9RETKw92/36quErdYAgOxBAZCdHSlruPIy3MmPBmZODKLL+2ZGa599ox07EeOYD98xLk8csSZIOXnY09Lw56W5v5NDcOZ7MTUxlY7puRlTAy22rWxxsQ4S5l8oU3OyTqOhMxkWP4IfPUYBEdB1xsrfdnu9brTvV531uxfw+sbXicpOYnF2xfz8Z8fc2GTCxnTfgztYtpVPn4RkSqmkhyV5PgMx7FjroTnxE/B4cMnbUvHXrjNkZlZ7vsYAQHYo6L5Mz+AvIgoendpiTUmBltMbay1T1jWcSZGRmBgNTxtJXw9BVY8Dxhw+RvQ8aoqvfyGAxt4fcPrrNi7wrWte3x3xrQfw7kNzsVieHd7KxHxfaqucoOSHP9n5uc7E6FDh7EfOkjBwUOnLg8edCZKBw/iyMoq9z2stWphi62DtU4dbHVisdWp4/zUPb5urVMHa61aNdPV3zRh6ST4ZaZzWIJLZ0Cna6r8NpsObuLNjW/y5c4vsZvOpu5No5oyut1oLm52McG24Cq/p4gIKMlxi5IcOZkjJwf7oUNs2byLKW//QBNrLg/0iiuWFBUcOog97SAFhw5BQTlm9A4IcFaLFSVBsXWwxcYWJkeFCVKsc90SElLJB3HAkrtg3Tzn94ueh243V+6apUjJSuGdze/w4R8fcjT/KABRQVGMaD6Cq1pfRePIxtVyXxE5cynJcYOSHCnNxn3pXPTSSupGBPHzQwNLPMZ0OLCnp2NPS6PgwAEK0tIoOJDmXKalUZB2wLkv9QD29NNPpHkiS1hYYSIUizW2hBKiWOd3a3R06Y2qHQ74cjKses35fdAU6HNPueIoj6N5R1m4bSHvbn6XfVn7XNt71evFqNaj6JfQD5tFzQBFpPKU5LhBSY6UZmtKJkOmfU9MWCBrHh5U6es58vKc1WKuRMiZFLkSpKLk6MABzNxyTJZptWKrXft4MlSY/DiXhSVD297D9tvrWGw4S3MufBqs1Zds2B12Vu5dyYKtC1i5dyUmzv+LqRtal0ubX8olzS+hSVSTaru/iPg/JTluUJIjpfnzwFEueD6RqJAANjw6uMbua5omjqwsCg4UlgK5kqADFKQdPKHE6AD2Q4fcmoG8iCXAgS3Yjq12FLY2fbDF1y9eZRbrLCGqyrZDf2f+zYd/fMji7Ys5lHPItf3s2LO5pPklXNj0QiID9b89ESkfJTluUJIjpdl1MIt+z35HeJCN3x8f4ulwSmQWFDjbCB044CwZKkqMikqGTkiIylU6FBBwQruhE0qGTiopstap4xxWwA159jy+2fMNn2z/hB/2/YDDdE58GmgJ5PxG5zO82XB61e9FoNXLeqqJiFdSkuMGJTlSmr8PZ3Pu098SHGBhyxNDPR1OpZimiePoUWfSs/UnCpY8QcGhdArygiio3Q27I9yVGNmPHCnXta1RUceryVxVZKcmRpaICFfpUNqxND776zM+2v4R249sd10rIiCC/gn9GdxkML3r91bCIyKlUpLjBiU5UpqU9Bx6Tv2aAKvBtieHeTqcqnU01Tky8l/fOb93vBqGPQvBkZh5eRQcPKFaLPXASVVmx0uIyjPVhxEUdErDaWtMHQ6EFvBz3h98d2wDf9kOkx4GDotBeEC4M+FpPJie9XsSYqtkbzMR8StKctygJEdKcyAzl25PfoVhwI6pF3k6nKrncMDKF+DbJ8F0QGRDuPhFaOVe+yPTNJ0DL6YVrxY7uZqsIC0NR0aG22GZBhwNtXAwzMGRMIMj4ZAZYSOqXmMaN+1E+1Z9iGvUBmudWKzh5ZttXUT8h6Z1EKkEW+G0DqbpnNqhaJoHv2GxwHmToHEf+GgcHN4J714FHUfBkKcgrE6ZpxuGgS06Glt0NEEtW5Z5rCMnp8TSIPvJJUUHD2LY7URkOYjIAij691c+sB3YzlE+5GjhVjM4iIDYugTUrVtq+yFrTAy26GiMgIBK/Vwi4puU5IiUwGo9ntQUOBxYLeWb4NNnNO4FtyU5S3R+mgG/LoCtnzsToB7jwBZU6VtYgoMJbNiQwIYNyzzOdDiwHz58UonQAdL+3s6BPVvJ3r+PgCNHqXUUQvLAyMmlYM8eCvbsOW0M1qgorLVrO+cpiy5c1i6cnqN2NNbaMVhrR2OLiXH2LivnhK4i4p1UXaXqKilBdl4B7R75EoBNU4YQGngG/Hvg79Xw6QRI+dX5PboJDHgY2l8GXpLkHck5wsp9K0na/i1/bF+FeegI0UdNamVB9FGT2GMBNMqLICbbSmh6Dkb6UWfVXHkUTehauygRqu2ar8xaO9o5qWtRQlS7tnOWe4vm6xKpSWqT4wYlOVKa3AI7rf/1BQC/PjaYyOAzpLrDYYcN7zkn+Tya4twW0xL6/h+cdVW1DiJYXqZp8ueRP1mVsoqfkn9idcpq17QSRQKx0TWkNV2CWtLWUp9mZh1CMvOwHzrsnJ7j0OFic5eVt3cZABYL1shIrNHRzuSopE908e+2WrW8b2JXER+iJMcNSnKkNHaHSfMHlwKw7uFBRIedYX+Qco86q6+SXoGcI85tkQ2h6z/hnDEQHuvR8EpS4Chg08FNrN6/mvWp69lwYEOxAQiLxIbE0i6mHW1j2tK2dlvaxbQjLjQOwzAwCwqcE7oePIT98CEKDh4snhC5Jnc9RMGhQ+VqVH0yS2joCUlQGQlSrSiskZFYIiOxRkRg2Lwn0RTxFCU5blCSI2VpOvkzTBN+eWggsRGVb5vik3IznbOZ//gyZKc5t1kCoP0I6HQtNDnPq0p3TmSaJnsy97D+wHrWpa5jfep6/jzyp2uaiRPVDq5Nm9ptaF6rOc2jmjuXtZoTERhR9j3y8ig4csTZ0+zIEeyHT1gv6XP4MPaMjPJXoZ3AEhbmTHgKkx5LlDMJskZGFG6POmE9sjBBcm4zgoOrbDRrEU9SkuMGJTlSlpYPLSXfbvLT5AuIjwr2dDielZ8DGxc7E569q49vD4uFdiOgw+XQsLvXJjxFsvOz+ePwH2w6uIlNBzex+dBm/jzyJ3bTXuLxdUPr0qJWC5pFNaNpVFMSIhJIiEggPiy+wpONmg4HjowMV+Jz2iQpIwNHejqO7OzKPDoARkCAMymKiMASGYE1PAJLeDiW8DCs4eFYwsKLf4+IKNxW+D08HEtYmBpmi8cpyXGDkhwpS5uHPycn38HK+8+nYXSop8PxHvvWwdq3YONHcOyE6qDgKGh2PrQcBM0vgMh6HguxPHLtuWw7vI0th7bw55E/nZ/0P0nNTi31HJtho0FEAxpGNCQh3Jn4NIhoQHxYPPGh8dQOrl3lJSZmQQH2zEwc6enYMzOxp2fgyMzAnp7hTISK1jMzcBRuc61nZoK95ESuIozQUKxhYYUJ0YlJUGHSFBaKJTQMS2golpCQwu/OdSO0cD30hG2qgpNyUpLjBiU5UpYuTyznYFYeS+/uS7v6+u/jFPZ856jJv30I276EY4eL76/VGBJ6QEJ35ye2TZV0Sa8pmXmZ/HnkT/5K/4vtR7azK2MXezL3sDdzL3mOvDLPDbAEEBca50x6wuJd63VC6hATEkPt4NrEBMcQFhBWI9VHzolfs3FkFCVI6TgyM3EcPYr96FEcR7NwHD2KI+uE75mZ2LNO2Hf0KGZe2c9dUUZQkDMZCg3FEhaKEVI8CSpaN0IL14NDMIKDsAQHYwQHO5eF1zCCCrcHBWMJce43AgJUTednfDLJmTFjBs8++yzJycm0b9+eadOm0bdv31KPT0xMZOLEiWzcuJH69etz3333MW7cOLfvpyRHynLhtO/ZkpLJ3H92o3/rup4Ox7s57LB3LWxfDtuWQ/J650jKJzKsUKclxLWHuu2c69FNnV3Vg33nf38O00Fqdip7MvewO2M3ezL3sCdzD/uO7mN/9n7SjqWV2O6nJEHWIGoH13YmPYXJT3RwNFGBUUQGRRIZWPg5YT0iMAKL4Zku6468PBxZx5Mee2amMwnKOnpqwpSdjePYMRzZWZjZx5zfXduc61VZulQmwzieDAUHYwkKwggJcS5L+h4chBEYhBEYeMInwLn/xG0Bx/cZgYGn7ncdpySrqvnciMcLFixg/PjxzJgxgz59+vD6668zdOhQNm3aRKNGjU45fseOHQwbNoyxY8cyb948fvjhB26//XZiY2O54oorPPAE4m/iIoPZkpJJamY5ZvA+U1mskNDN+Tn/QcjJgL1rYM/PsOcnZwKUcwQObHF+WFj8/NAYZ7ITWR/C4yA8HsLrQkThMiTaWR0WFOnxMXsshsVVQtMtvtsp+/Pt+aQeS2V/1n5SslLYn318mXYsjUM5hzh47CDZBdnk2nNJzkomOSvZ7fsbGIQHhruSnhBbCKEBoYQFhBFqCyU0ILTUZYgthGBrMIHWQIKsQa5l0frp2hlZAgOdM89HR5f7dzuZaZqY+fk4srIwT0h8HMUSomzMosQoK9u13czNwXEsx7nMycWRcwwzJxczJwdHTo5zmZt7PIkyTcxjx7AfO1bpuCuqxOSnMDkybAEYNpvzE+BcJ8B20nbnPmynbndtKzzXCHDuO779+HWLbbdawGorXFqd+y0W5/4Tl4XHGFbr8W0+wmtKcnr06ME555zDq6++6trWtm1bRowYwdSpU085/v777+eTTz5h8+bNrm3jxo1jw4YNJCUluXVPleRIWe77cAPvr/6bkV0bcuf5LTnxH2L6R1k5mSaWrBQC0zYRkLYZW9pmbOk7saXvwnrsYLku5QiMwBEUiSMoCjMoEoctFNMWfMInBIp9D8a0BGBabGCxgcWKaTjXTYsVjMJl4X7TsBYmUgYYBiZG4TqubcWWGJjFtuHGMQY59lyO5KdzOC+dI3nphetHSM/P5GjBUY4WZJFZkEVm/lHX99zTVJNVltWwEmgJINASQIAl0LUeaAnEZlixGlZshg2rYcFq2LBZnNuc263YLLYTvhc/ruh8i2HBwMBiWLBgwTAMLBgYhqVwW+F64TFGsWXRugWLYZRyHefvawAU2LHkFWDJL8DIy8fIzceSd3zdyM/HyCvclutcd33y7RgFBRj5BZBXULhuh8JtRn6Bcz2vwLWNE7cX1FAplYeYVotzehirFSxG4bJomwXzhH0xDz5A/AUXVun9faokJy8vjzVr1vDAAw8U2z548GB+/PHHEs9JSkpi8ODikwkOGTKEWbNmkZ+fT0AJc9Xk5uaSm3v8X+UZlRjjQvxfUWPj91f/zfur//ZwNP7CArQv/DiFk02CcYBGRip1jcPEGkeoyxHqGkeINY4Qa6QTSTahhvN/u5a8TCx5mZC51zOP4EF5QIbVQobFQrrFQpbFQpZhkG2xkG0xOGY4l1mFy+wT9mUbFo5ZDHINgzzDucw1DApOyNjtpp1jdjvH7Dmee8iaZCv8VHm/AgPDtGKzQ0ABBNjBVrgsvm5iKwCbA6wOsNnBaj/+/ZR1u1nGvuLfrY7C73bTeV3X9+PrFgdYTOfSWrhuLdxuO80oB4bdAXYH5BeU8gscl7RmMZdVcZLjLq9IctLS0rDb7cTFxRXbHhcXR0pKSonnpKSklHh8QUEBaWlp1Kt3as+OqVOn8vjjj1dd4OLXru3RiBXbDrBxXwZF5Z0mJt5R9uk/8gnnL8L5i6bFd5hwYtOWAAoIJ5tIsogkmwjDuQwhh2DyCCKfYPIINvIIIq9wm3Npw44NO1YcWIvWDUfhNud2W7Glvaj8BVxlOSZGsXUK9526v7TtxskPVQEhBRACxBX7kSo+7o4DyDdwJT/OBAhXIlT03W4YFBhQgIHd4KR1gwJwrlN4nGFgp3CfgWvdXhSx4fw17Dhnn3fg/NUchokDo/C785pm0ffC6zif2Cj+/cTjCp/NBNdfW/Pk7a6lcXy/UXz/yceefD4nnWOe8Ke96N5mANgDnHHmVLAE+Pj93LtA8f/CTn9Oaf9FGqbpTIROSoAM03AlRa4EqZTvVgcMbeK5No1ekeQUOblhlmmaZTbWKun4krYXmTx5MhMnTnR9z8jIICEhoaLhip+rEx7EB+N6ezoMERGpIK9IcurUqYPVaj2l1CY1NfWU0poi8fHxJR5vs9mIiYkp8ZygoCCCgnynC6uIiIhUnFc0kQ4MDKRLly4sX7682Pbly5fTu3fJ/5Lu1avXKccvW7aMrl27ltgeR0RERM4sXpHkAEycOJGZM2cye/ZsNm/ezIQJE9i9e7dr3JvJkyczevRo1/Hjxo1j165dTJw4kc2bNzN79mxmzZrFpEmTPPUIIiIi4kW8oroKYNSoURw8eJApU6aQnJxMhw4dWLp0KY0bNwYgOTmZ3bt3u45v2rQpS5cuZcKECbzyyivUr1+fl156SWPkiIiICOBF4+R4gsbJERER8T3u/v32muoqERERkaqkJEdERET8kpIcERER8UtKckRERMQvKckRERERv6QkR0RERPySkhwRERHxS0pyRERExC8pyRERERG/5DXTOnhC0WDPGRkZHo5ERERE3FX0d/t0kzac0UlOZmYmAAkJCR6ORERERMorMzOTqKioUvef0XNXORwO9u3bR0REBIZhVNl1MzIySEhIYM+ePX47J5a/P6Oez/f5+zP6+/OB/z+jnq/iTNMkMzOT+vXrY7GU3vLmjC7JsVgsNGzYsNquHxkZ6Zf/4Z7I359Rz+f7/P0Z/f35wP+fUc9XMWWV4BRRw2MRERHxS0pyRERExC8pyakGQUFBPProowQFBXk6lGrj78+o5/N9/v6M/v584P/PqOerfmd0w2MRERHxXyrJEREREb+kJEdERET8kpIcERER8UtKckRERMQvKcmpoBkzZtC0aVOCg4Pp0qULK1asKPP4xMREunTpQnBwMM2aNeO1116roUgrpjzP991332EYximfLVu21GDE7vv+++8ZPnw49evXxzAMPvroo9Oe42vvr7zP6GvvcOrUqXTr1o2IiAjq1q3LiBEj2Lp162nP85X3WJHn87V3+Oqrr9KxY0fXQHG9evXi888/L/McX3l/UP7n87X3d7KpU6diGAbjx48v87iafodKcipgwYIFjB8/noceeoh169bRt29fhg4dyu7du0s8fseOHQwbNoy+ffuybt06HnzwQe6++24WLlxYw5G7p7zPV2Tr1q0kJye7Pi1btqyhiMsnKyuLs88+m5dfftmt433t/UH5n7GIr7zDxMRE7rjjDn766SeWL19OQUEBgwcPJisrq9RzfOk9VuT5ivjKO2zYsCH/+c9/WL16NatXr2bAgAFceumlbNy4scTjfen9Qfmfr4ivvL8T/fLLL/zvf/+jY8eOZR7nkXdoSrl1797dHDduXLFtbdq0MR944IESj7/vvvvMNm3aFNt26623mj179qy2GCujvM/37bffmoB5+PDhGoiuagHm4sWLyzzG197fydx5Rl9+h6ZpmqmpqSZgJiYmlnqML79Hd57P19+haZpmdHS0OXPmzBL3+fL7K1LW8/nq+8vMzDRbtmxpLl++3OzXr595zz33lHqsJ96hSnLKKS8vjzVr1jB48OBi2wcPHsyPP/5Y4jlJSUmnHD9kyBBWr15Nfn5+tcVaERV5viKdO3emXr16XHDBBXz77bfVGWaN8qX3V1m++g7T09MBqF27dqnH+PJ7dOf5ivjiO7Tb7bz33ntkZWXRq1evEo/x5ffnzvMV8bX3d8cdd3DRRRcxcODA0x7riXeoJKec0tLSsNvtxMXFFdseFxdHSkpKieekpKSUeHxBQQFpaWnVFmtFVOT56tWrx//+9z8WLlzIokWLaN26NRdccAHff/99TYRc7Xzp/VWUL79D0zSZOHEi5557Lh06dCj1OF99j+4+ny++w99++43w8HCCgoIYN24cixcvpl27diUe64vvrzzP54vv77333mPt2rVMnTrVreM98Q7P6FnIK8MwjGLfTdM8Zdvpji9pu7coz/O1bt2a1q1bu7736tWLPXv28Nxzz3HeeedVa5w1xdfeX3n58ju88847+fXXX1m5cuVpj/XF9+ju8/niO2zdujXr16/nyJEjLFy4kDFjxpCYmFhqIuBr7688z+dr72/Pnj3cc889LFu2jODgYLfPq+l3qJKccqpTpw5Wq/WUUo3U1NRTMtQi8fHxJR5vs9mIiYmptlgroiLPV5KePXuybdu2qg7PI3zp/VUlX3iHd911F5988gnffvstDRs2LPNYX3yP5Xm+knj7OwwMDKRFixZ07dqVqVOncvbZZzN9+vQSj/XF91ee5yuJN7+/NWvWkJqaSpcuXbDZbNhsNhITE3nppZew2WzY7fZTzvHEO1SSU06BgYF06dKF5cuXF9u+fPlyevfuXeI5vXr1OuX4ZcuW0bVrVwICAqot1oqoyPOVZN26ddSrV6+qw/MIX3p/Vcmb36Fpmtx5550sWrSIb775hqZNm572HF96jxV5vpJ48zssiWma5ObmlrjPl95facp6vpJ48/u74IIL+O2331i/fr3r07VrV/7xj3+wfv16rFbrKed45B1WW5NmP/bee++ZAQEB5qxZs8xNmzaZ48ePN8PCwsydO3eapmmaDzzwgHn99de7jv/rr7/M0NBQc8KECeamTZvMWbNmmQEBAeaHH37oqUcoU3mf78UXXzQXL15s/vHHH+bvv/9uPvDAAyZgLly40FOPUKbMzExz3bp15rp160zAfOGFF8x169aZu3btMk3T99+faZb/GX3tHd52221mVFSU+d1335nJycmuT3Z2tusYX36PFXk+X3uHkydPNr///ntzx44d5q+//mo++OCDpsViMZctW2aapm+/P9Ms//P52vsrycm9q7zhHSrJqaBXXnnFbNy4sRkYGGiec845xbp2jhkzxuzXr1+x47/77juzc+fOZmBgoNmkSRPz1VdfreGIy6c8z/f000+bzZs3N4ODg83o6Gjz3HPPNT/77DMPRO2eoq6aJ3/GjBljmqZ/vL/yPqOvvcOSng0w58yZ4zrGl99jRZ7P197hjTfe6Pr/mNjYWPOCCy5wJQCm6dvvzzTL/3y+9v5KcnKS4w3v0DDNwlY/IiIiIn5EbXJERETELynJEREREb+kJEdERET8kpIcERER8UtKckRERMQvKckRERERv6QkR0RERPySkhwRERHxS0pyRERExC8pyRERv5eXl+fpEETEA2yeDkBEpKr179+fDh06EBgYyFtvvUX79u1JTEz0dFgiUsOU5IiIX3rzzTe57bbb+OGHH9AUfSJnJk3QKSJ+p3///qSnp7Nu3TpPhyIiHqQ2OSLil7p27erpEETEw5TkiIhfCgsL83QIIuJhSnJERETELynJEREREb+kJEdERET8knpXiYiIiF9SSY6IiIj4JSU5IiIi4peU5IiIiIhfUpIjIiIifklJjoiIiPglJTkiIiLil5TkiIiIiF9SkiMiIiJ+SUmOiIiI+CUlOSIiIuKXlOSIiIiIX1KSIyIiIn7p/wGPEX/R2NGfdwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rr = np.linspace(0.001,4,1000)\n", "plt.figure(2)\n", "plt.clf()\n", "plt.title(f'analytic solution for $a = {a}$, $D = {D_val}$')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.00001, a, D_val), label='t~0')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.0625, a, D_val), label='t=0.0625')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.25, a, D_val), label='t=0.25')\n", "plt.plot(rr, C_sphere_infmed(rr, 1.0, a, D_val), label='t=1.0')\n", "plt.ylabel('$C / C_0$')\n", "plt.xlabel('r')\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The figure above is consistent with Figure 3.1 from <PERSON><PERSON>'s book, demonstrating probable correctness of our code for evaluation of the analytic solution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparison with numerical solution by PyFVTool"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we will reproduce the curves at the different time points using PyFVTool. \n", "\n", "It was observed that a very fine grid needs to be used. 50 cells is woofully insufficient (very imprecise result), 100 is slightly better, 500 seems to do OK, 1000 cells on 10 units width better still. We finally used 2000 cells over 10 length units. We use small time-steps (0.0625/20 instead of 0.0625/10), but this does not change the final result much."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["## Define the domain and create a mesh structure\n", "# Here we work in a 1D spherical coordinate system (r coordinate)\n", "R = 10.0   # domain radius (this models an infinite medium)\n", "Nr = 2000  # number of cells\n", "m = pf.SphericalGrid1D(Nr, R)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["D = pf.CellVariable(m, D_val)\n", "alfa = pf.CellVariable(m, 1.0)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["c_fvm = pf.CellVariable(m, 0)\n", "r_fvm = c_fvm.cellcenters.r\n", "c_fvm.value[r_fvm < a] = C_0"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Implicit 'no-flux' boundary conditions (<PERSON>)\n", "# TO DO: in this case, it should not change much if we switch to Dirichlet"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 0.0 4.188764024847613\n"]}], "source": ["t = 0.0 # total time\n", "deltat = 0.0625/20 # time step\n", "\n", "# output total mass in the system\n", "print(0, t, c_fvm.domainIntegral())\n", "\n", "# store initial condition in list\n", "pyfvtool_c = [c_fvm.value]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 0.06250000000000001 4.188767980524447\n", "0 0.2499999999999996 4.188778049013295\n", "0 1.0000000000000058 4.188786238173347\n"]}], "source": ["## loop for \"time-stepping\" the solution\n", "# It outputs the spatial profile C(r) after\n", "# 20, 80 and 320 time-steps\n", "# This corresponds to t=0.0625, t=0.25 and t=1, respectively.\n", "for sdi in [20,60,240]:\n", "    for n in range(sdi):\n", "        transientterm = pf.transientTerm(c_fvm, deltat, alfa)\n", "        Dave = pf.<PERSON><PERSON><PERSON>(D)\n", "        diffusionterm = pf.diffusionTerm(<PERSON>)\n", "        pf.solvePDE(c_fvm, [ transientterm,\n", "                            -diffusionterm])\n", "        t += deltat\n", "\n", "    print(0, t, c_fvm.domainIntegral())\n", "    pyfvtool_c.append(c_fvm.value)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rr = np.linspace(0.001,4,1000)\n", "plt.figure(2)\n", "plt.clf()\n", "plt.title(f'PyFVTool FVM solution for $a = {a}$, $D = {D_val}$')\n", "plt.plot(r_fvm, pyfvtool_c[0], label='t=0')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.00001, a, D_val), 'k:')\n", "plt.plot(r_fvm, pyfvtool_c[1], label='t=0.0625')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.0625, a, D_val), 'k:')\n", "plt.plot(r_fvm, pyfvtool_c[2], label='t=0.25')\n", "plt.plot(rr, C_sphere_infmed(rr, 0.25, a, D_val), 'k:')\n", "plt.plot(r_fvm, pyfvtool_c[3], label='t=1.0')\n", "plt.plot(rr, C_sphere_infmed(rr, 1.0, a, D_val), 'k:', label = 'analytic')\n", "plt.ylabel('$C / C_0$')\n", "plt.xlabel('r')\n", "plt.xlim(0, 4)\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# time hard-coded for now (t = 1.0)\n", "fvmdiff = pyfvtool_c[3] - C_sphere_infmed(r_fvm, 1.0, a, D_val)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(3)\n", "plt.clf()\n", "plt.title('difference between FVM and analytic')\n", "plt.plot(r_fvm, fvmdiff, label='t=1.0')\n", "plt.ylabel('abs. diff. between FVM and analytic')\n", "plt.xlabel('r')\n", "plt.xlim(0, 4)\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Conclusion\n", "\n", "This notebook shows that once the computational parameters PyFVTool have suitable values, close agreement is obtained with the analytic solution from [<PERSON><PERSON> 1975] for this particular diffusion problem. It also shows how to how to set up a simple calculation in spherical symmetry with PyFVtool.\n", "\n", "It might be of interest to analyze the subtle differences between the analytic and numerical solutions. The PyFVTool calculation may be made more efficient by using unevenly sized cells: smaller cells near the initial sphere, larger cells farther away from the origin."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}