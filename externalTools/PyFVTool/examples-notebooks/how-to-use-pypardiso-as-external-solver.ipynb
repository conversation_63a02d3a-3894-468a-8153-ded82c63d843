{"cells": [{"cell_type": "markdown", "id": "f5ac2f81-49ec-48e8-9968-4d5f3c882a61", "metadata": {}, "source": ["# Using pypardiso as an external sparse direct solver for PyFVTool\n", "\n", "MW, 230908, 240503\n", "\n", "This is an example of how to use an external sparse solver with PyFVTool. It is also a draft for future documentation on the subject, as part of the PyFVTool documentation.\n", "\n", "An external solver can be supplied as an optional argument to `pyfvtool.solvePDE()`, *e.g.*\n", "\n", "```python\n", "pf.solvePDE(phi, eqnterms,\n", "            externalsolver = solveur)\n", "```\n", "\n", "where `solveur` is a function that will be called instead of `scipy.sparse.linalg.spsolve(A, b)`. In this example, we use `pypardiso.spsolve(A, b)`."]}, {"cell_type": "markdown", "id": "ec93bdd4-096c-46e1-8258-60118a4e29f2", "metadata": {}, "source": ["\n", "## Install pypardiso\n", "\n", "[`pypardiso`](https://github.com/haasad/PyPardisoProject) is a simple Python interface to the [Intel MKL PARDISO solver](https://www.intel.com/content/www/us/en/docs/onemkl/developer-reference-c/2023-2/onemkl-pardiso-parallel-direct-sparse-solver-iface.html)\n", "\n", "Using `miniconda` with exclusively the Conda-forge repositories, `pypardiso` is readily installed (tested only on Windows 10).\n", "\n", "```\n", "conda install pypardiso\n", "```\n"]}, {"cell_type": "markdown", "id": "c827dee8-ae1e-4d35-894a-49bca4c3caf1", "metadata": {}, "source": ["## Test drive pypardiso\n", "\n", "Using the example from the pypardiso README. This example is not ideal for the present PyFVTool testing infrastructure, because of the random numbers."]}, {"cell_type": "code", "execution_count": 1, "id": "0e77a132-0f37-491c-a58e-6d5a39b0c60e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import scipy.sparse as sp\n", "import matplotlib.pyplot as plt\n", "import pypardiso"]}, {"cell_type": "markdown", "id": "01afb5ed-9735-45ea-a40d-fdfeb2f8f171", "metadata": {}, "source": ["pypardiso version 0.4.6 or later is required!"]}, {"cell_type": "code", "execution_count": 2, "id": "3b468728-8a4e-4614-8d7d-bbdc0da11c74", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.4.6'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pypardiso.__version__"]}, {"cell_type": "code", "execution_count": 3, "id": "fd00e51e-2bc8-4e55-abdf-1445977a2c72", "metadata": {}, "outputs": [], "source": ["A = sp.rand(10, 10, density=0.5, format='csr')"]}, {"cell_type": "code", "execution_count": 4, "id": "29c6c0a7-6f4f-4728-ac02-6e6f3b5c3ca8", "metadata": {}, "outputs": [], "source": ["b = np.random.rand(10)"]}, {"cell_type": "code", "execution_count": 5, "id": "66613514-0885-456a-932f-c40d10a84297", "metadata": {}, "outputs": [], "source": ["x = pypardiso.spsolve(A, b)"]}, {"cell_type": "code", "execution_count": 6, "id": "6d20b1a3-09af-43d4-9d05-3c93a6980e39", "metadata": {}, "outputs": [], "source": ["# do not output result for now.\n", "# x"]}, {"cell_type": "markdown", "id": "ba7f0702-7886-4df5-92fd-585a3af98374", "metadata": {}, "source": ["## PyFVTool finite-volume (from cylindrical2D_convection notebook)"]}, {"cell_type": "code", "execution_count": 7, "id": "3062d8af-4a6f-4285-b910-b50b3f72027c", "metadata": {}, "outputs": [], "source": ["from time import time\n", "import pyfvtool as pf"]}, {"cell_type": "markdown", "id": "df8e1325-198a-47a6-8e71-06b3604a6ed5", "metadata": {}, "source": ["#### Utility functions"]}, {"cell_type": "code", "execution_count": 8, "id": "b56afb82-9f77-406f-829c-321b741b3c16", "metadata": {}, "outputs": [], "source": ["# visualization routine (imshow-based)\n", "def phi_visualize():\n", "    print(f't = {t:.1f} s')\n", "    # avoid ghost cells\n", "    plt.imshow(phi.value, origin = 'lower',\n", "               extent = [zz[0], zz[-1], rr[0]*rzoom, rr[-1]*rzoom])"]}, {"cell_type": "code", "execution_count": 9, "id": "1cd0611d-f786-4fa2-a278-9b8cb158b03b", "metadata": {}, "outputs": [], "source": ["# calculate simple finite-volume integral over r\n", "def integral_dr(phi0):\n", "    v = phi0.cellvolume\n", "    c = phi0.value\n", "    return (v*c).sum(axis=0)"]}, {"cell_type": "markdown", "id": "44b77e8a-d5fb-4229-8d1b-741447fb32fd", "metadata": {}, "source": ["#### FVM settings"]}, {"cell_type": "code", "execution_count": 10, "id": "b70cedd2-baae-40e6-a123-65007b605c93", "metadata": {}, "outputs": [], "source": ["Lr = 7.5e-05 # [m] radius of cylinder\n", "Lz = 0.3 # [m] length of cylinder\n", "umax = 2*9.4314e-3 # [m s^-1] max flow velocity = 2 time average flow velocity"]}, {"cell_type": "code", "execution_count": 11, "id": "b331b3d2-e52d-4852-8233-5b6794632859", "metadata": {}, "outputs": [], "source": ["# regular grid parameters\n", "Nr = 40\n", "Nz = 500"]}, {"cell_type": "code", "execution_count": 12, "id": "7a1af730-d1d4-49c1-b567-a82aa5f4014a", "metadata": {}, "outputs": [], "source": ["# initial condition parameters (cell indices)\n", "loadix0 = 20\n", "loadix1 = 40"]}, {"cell_type": "code", "execution_count": 13, "id": "2addc814-63d0-4e00-b3b1-f85fc3fd8b25", "metadata": {}, "outputs": [], "source": ["# timestep parameters\n", "deltat = 0.01 # [s] per time step"]}, {"cell_type": "code", "execution_count": 14, "id": "fbd7f090-6a9f-4510-9c64-364ab2e72c43", "metadata": {}, "outputs": [], "source": ["# visualization parameters\n", "rzoom = 1000"]}, {"cell_type": "markdown", "id": "e0c228f5-ad35-40e0-817e-55ea640b5cfe", "metadata": {}, "source": ["#### 2D cylindrical mesh"]}, {"cell_type": "code", "execution_count": 15, "id": "7831e5d0-d129-482d-9b26-590cc5f97a5a", "metadata": {}, "outputs": [], "source": ["msh = pf.CylindricalGrid2D(Nr, Nz, Lr, Lz)"]}, {"cell_type": "markdown", "id": "32216205-b7a3-49eb-9fca-feb9cd98a637", "metadata": {}, "source": ["#### Set up Poiseuille flow velocity field"]}, {"cell_type": "code", "execution_count": 16, "id": "0264a95e-db00-4441-b988-9be0c8bf26f3", "metadata": {}, "outputs": [], "source": ["rr = msh.cellcenters.r\n", "zz = msh.facecenters.z"]}, {"cell_type": "code", "execution_count": 17, "id": "399ea439-8a21-45ff-afe9-034f8e410164", "metadata": {}, "outputs": [], "source": ["uu =  umax*(1 - (rr**2)/(Lr**2)) # does not depend on zz"]}, {"cell_type": "code", "execution_count": 18, "id": "0f0dfe6b-95f0-4532-a6c9-8d63e863661b", "metadata": {}, "outputs": [], "source": ["u =  pf.FaceVariable(msh, 1.0)"]}, {"cell_type": "code", "execution_count": 19, "id": "e141e300-0b88-4fb8-a3d4-251e9fa98f4a", "metadata": {}, "outputs": [], "source": ["u.rvalue[:] = 0\n", "u.zvalue[:] = uu[:, np.newaxis]"]}, {"cell_type": "markdown", "id": "149e0f7b-f14e-4507-98b1-729df06739bc", "metadata": {}, "source": ["#### Solution variable"]}, {"cell_type": "code", "execution_count": 20, "id": "efb9f15b-93ef-496f-8941-e507faaed92e", "metadata": {}, "outputs": [], "source": ["bc = pf.BoundaryConditions(msh)"]}, {"cell_type": "code", "execution_count": 21, "id": "2563b8d6-a57b-4ed5-a958-5ffbc478fc66", "metadata": {}, "outputs": [], "source": ["phi = pf.CellVariable(msh, 0.0 , bc)"]}, {"cell_type": "markdown", "id": "ae0736a3-107c-4f6a-b093-b5d06cb8d54b", "metadata": {}, "source": ["#### Initial condition"]}, {"cell_type": "code", "execution_count": 22, "id": "89b7fda9-871d-4ad3-afff-2723f108ff79", "metadata": {}, "outputs": [], "source": ["t=0."]}, {"cell_type": "code", "execution_count": 23, "id": "bc57e0e8-7fa2-45fb-b422-9b6a38e98c57", "metadata": {}, "outputs": [], "source": ["# initial condition\n", "for i in range(loadix0, loadix1):\n", "    phi.value[:, i] = 1.0"]}, {"cell_type": "code", "execution_count": 24, "id": "5c596a4c-c983-48cb-888f-5338c66b106b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 0.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "code", "execution_count": 25, "id": "73f8ace3-dd40-4b75-9410-6ef40c8cda29", "metadata": {}, "outputs": [], "source": ["initInt = phi.domainIntegral()\n", "# print(initInt)"]}, {"cell_type": "code", "execution_count": 26, "id": "f27415f5-5a94-41c8-9f7e-a35e57f61ccf", "metadata": {}, "outputs": [], "source": ["phiprofs = []\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "markdown", "id": "37b32d38-17c1-4e69-8657-f1ec723d8fea", "metadata": {}, "source": ["### Solve the convection PDE with time-stepping"]}, {"cell_type": "code", "execution_count": 27, "id": "98594df8-a52a-416d-820a-5ee83c3a514c", "metadata": {}, "outputs": [], "source": ["exect0 = time()"]}, {"cell_type": "code", "execution_count": 28, "id": "6ca90110-c48c-46c2-83c1-c6ace789e13f", "metadata": {}, "outputs": [], "source": ["# The convection term only needs to be constructed once, since it\n", "# will be constant during all time steps.\n", "# convectionterm = pf.convectionTerm(u) # really ugly results?\n", "convectionterm = pf.convectionUpwindTerm(u) # numerical diffusion\n", "\n", "def step_solver(Nstp):\n", "    global t\n", "\n", "    for i in range(Nstp):\n", "        # Transient term needs to be re-evaluated at each time step\n", "        transientterm = pf.transientTerm(phi, deltat, 1.0)\n", "        eqnterms = [transientterm,\n", "                    convectionterm]\n", "        # solve the PDE using pypardiso.spsolve as the external solver\n", "        pf.solvePDE(phi, eqnterms,\n", "                    externalsolver = pypardiso.spsolve)\n", "        t += deltat"]}, {"cell_type": "code", "execution_count": 29, "id": "50f94ce2-cd02-4849-a4b0-c287ee87205e", "metadata": {}, "outputs": [], "source": ["step_solver(200)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 30, "id": "ad8521f0-72d0-4a1b-b529-34ac0563aa26", "metadata": {}, "outputs": [], "source": ["# print(t, initInt, pf.domainInt(phi))\n", "# test conservation of mass\n", "assert np.isclose(initInt, phi.domainIntegral())"]}, {"cell_type": "code", "execution_count": 31, "id": "ec265066-a81f-4e47-a6d6-d63c50c0e622", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 2.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "code", "execution_count": 32, "id": "2048546e-02c5-440e-84b7-91abcf43732e", "metadata": {}, "outputs": [], "source": ["step_solver(300)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 33, "id": "79e2c6d4-bc8c-4e8e-9faf-60719118371f", "metadata": {}, "outputs": [], "source": ["step_solver(500)\n", "phiprofs.append((t, integral_dr(phi)))"]}, {"cell_type": "code", "execution_count": 34, "id": "f69518d9-b522-4a0c-ae99-15dc36398106", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9.999999999999831 2.1205750411731096e-10 2.1205750411730108e-10\n"]}], "source": ["print(t, initInt, phi.domainIntegral())"]}, {"cell_type": "code", "execution_count": 35, "id": "14ea960f-2d57-4c5f-912d-758cd3b03617", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t = 10.0 s\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["phi_visualize()"]}, {"cell_type": "code", "execution_count": 36, "id": "81ba399b-f716-4e59-b8b7-9b20fc74c86b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for ix, (tprof, phiprof) in enumerate(phiprofs):\n", "    if ix == 2:\n", "        lbl1 = 'FVM'\n", "    else:\n", "        lbl1 = None\n", "    plt.plot(phi.domain.cellcenters.z, phiprof, \n", "             label=lbl1)\n", "plt.xlabel('z / m')\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": 37, "id": "976a4cd4-5b88-4f0b-a184-dbe4dcde8aeb", "metadata": {}, "outputs": [], "source": ["exect1 = time()"]}, {"cell_type": "code", "execution_count": 38, "id": "1488bee9-d24f-411f-8886-ba0a105f309c", "metadata": {}, "outputs": [], "source": ["# print('Elapsed time ', exect1 - exect0, 's')"]}, {"cell_type": "code", "execution_count": 39, "id": "b7abecb1-1ca3-4e19-9631-175063ff8ae8", "metadata": {}, "outputs": [], "source": ["# Execution time benchmark results \n", "#    - Werts' computer (in 'best performance' power management mode)\n", "#\n", "# Elapsed time  2.36826229095459  s (pypardiso.spsolve via solveur function)\n", "# Elapsed time  5.680087327957153 s (scipy.sparse.linalg.spsolve SuperLU)"]}, {"cell_type": "code", "execution_count": null, "id": "69ae543d-5902-4c57-a431-5aefe2e7c16a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}