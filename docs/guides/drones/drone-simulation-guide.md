# Quick Guide: Running Drone Rotor Simulations

## Overview
If we implement all what tin the file "drone-simulation-implementation.md" in the framework. This Julia CFD framework now has all the capabilities needed to simulate multi-rotor drones with the same features as OpenFOAM:

- ✅ **AMI (Arbitrary Mesh Interface)** for rotating rotor zones
- ✅ **Moving mesh** support with solid body rotation
- ✅ **Q-criterion** visualization for vortex dynamics
- ✅ **PIMPLE algorithm** for transient simulations
- ✅ **Multi-rotor support** with contra-rotating rotors
- ✅ **ParaView output** with time series animation

## Quick Start

```julia
using FlowSolver

# 1. Generate drone mesh with rotating zones
mesh = generateDroneMesh(
    baseResolution = 40,        # Base mesh resolution
    rotorRadius = 0.15,         # 150mm rotors
    droneSTL = "drone.stl"      # Your drone geometry
)

# 2. Run simulation
simulateDrone(
    totalTime = 0.05,           # 50ms simulation
    dt = 1e-5,                  # 10 microsecond timestep
    outputInterval = 0.001      # Write every 1ms
)

# Results will be in ParaView format with Q-criterion
```

## Key Features

### 1. AMI (Arbitrary Mesh Interface)
- Handles interpolation between rotating and stationary mesh zones
- Each rotor has master/slave AMI patches
- Automatic weight calculation and interpolation

### 2. Rotating Zones
- Each rotor rotates at specified RPM
- Contra-rotating rotors (alternating direction)
- Mesh points move with rotation

### 3. Q-Criterion Visualization
- Automatically calculated at each time step
- Reveals vortex structures from rotor tips
- Colored by velocity magnitude

### 4. PIMPLE Solver
- Merged PISO-SIMPLE for transient flows
- Handles moving meshes
- Pressure-velocity coupling

## Simulation Setup

### Mesh Generation
```julia
# Customize drone configuration
mesh = generateDroneMesh(
    domain = (Vec3(-3, -3, -2), Vec3(3, 3, 2)),  # Domain bounds
    baseResolution = 50,                           # Higher = finer mesh
    rotorPositions = [                             # Rotor locations
        Vec3(0.3, 0.3, 0),    # Front-right
        Vec3(-0.3, 0.3, 0),   # Front-left  
        Vec3(-0.3, -0.3, 0),  # Rear-left
        Vec3(0.3, -0.3, 0)    # Rear-right
    ],
    rotorRadius = 0.15                             # Rotor radius
)
```

### Simulation Parameters
```julia
# Create simulation with custom parameters
simulation = setupDroneSimulation(
    "meshFile",
    droneConfig = Dict(
        "rotorRPM" => 6000.0,      # Rotor speed
        "cruiseSpeed" => 15.0,     # Forward flight speed
        "nu" => 1.5e-5,            # Air viscosity
        "rho" => 1.225             # Air density
    )
)
```

### Boundary Conditions
- **Inlet**: Freestream velocity
- **Outlet**: Zero gradient velocity, fixed pressure
- **Drone body**: No-slip wall
- **Rotor blades**: No-slip rotating wall
- **AMI patches**: Cyclic AMI boundaries

## Visualization in ParaView

1. **Open the series file**: `paraview/drone_simulation.series`

2. **Apply Q-criterion filter**:
   - Filters → Alphabetical → Contour
   - Contour By: Q
   - Value: 1000-5000 (adjust for your case)

3. **Color by velocity**:
   - Coloring: U_magnitude
   - Color Map: Cool to Warm

4. **Add drone geometry**:
   - File → Open → drone.stl
   - Display as Surface with Edges

5. **Animate**:
   - Click Play button to see rotor rotation
   - Adjust speed with Animation View

## Example: Complete Simulation

```julia
using FlowSolver

function simulate_quadcopter()
    # Generate mesh
    println("Generating quadcopter mesh...")
    mesh = generateDroneMesh(
        baseResolution = 40,
        rotorRadius = 0.15
    )
    
    # Setup simulation
    println("Setting up simulation...")
    config = Dict(
        "rotorRPM" => 5000.0,
        "cruiseSpeed" => 10.0,
        "altitude" => 100.0
    )
    
    simulation = setupDroneSimulation("mesh", droneConfig=config)
    
    # Run PIMPLE solver
    println("Running PIMPLE simulation...")
    pimpleFoam(
        simulation,
        0.0,           # Start time
        0.05,          # End time (50ms)
        1e-5,          # Time step
        writeInterval = 0.001
    )
    
    # Post-process
    println("Post-processing...")
    for t in [0.01, 0.02, 0.03, 0.04, 0.05]
        Q = QCriterion(simulation.U)
        writeVTK("results/Q_$(t)", simulation.mesh, 
                Dict("Q" => Q, "U" => simulation.U), time=t)
    end
    
    println("Simulation complete!")
end

# Run it
simulate_quadcopter()
```

## Performance Tips

1. **Mesh Resolution**: Start coarse (30-40 base resolution) for testing
2. **Time Step**: Use Co < 1 for stability (typically 1e-5 to 1e-4)
3. **Parallel**: Use MPI for large meshes:
   ```bash
   mpirun -np 4 julia drone_simulation.jl
   ```
4. **Output Frequency**: Balance between file size and temporal resolution

## Troubleshooting

### Simulation Diverges
- Reduce time step
- Increase under-relaxation
- Check mesh quality near AMI interfaces

### Poor Vortex Capture
- Increase mesh resolution near rotor tips
- Reduce numerical dissipation
- Use higher-order schemes

### AMI Interpolation Issues
- Ensure AMI patches have similar face sizes
- Check rotation axis alignment
- Verify face normal directions

## steps need to be also  added are, if they are not already implemented :  

1. **Add Turbulence Models**: k-ε or k-ω SST for better wake prediction
2. **Force Calculation**: Integrate pressure/shear for thrust/torque
3. **Rotor Models**: Add actuator disk or blade element theory
4. **Control System**: Implement PID controllers for stability
5. **Acoustic Analysis**: Add Ffowcs Williams-Hawkings for noise

the core design concepts and constraints of the framework are :

1. **Modular Architecture**: Clear separation between mesh handling, numerics, physics, and solvers
2. **Type Safety**: Leverages Julia's type system for performance and correctness
3. **Multiple Dispatch**: Easy to extend with new schemes and models
4. **HPC Ready**: Built-in support for MPI parallelization and GPU acceleration
5. **Memory Efficient**: Lazy evaluation and sparse matrix operations
6. **OpenFOAM Compatibility**: Can read OpenFOAM meshes and case files
7. **Modern Design**: Clean API inspired by OpenFOAM but with Julia's elegance

