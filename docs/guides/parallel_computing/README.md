# Parallel Computing in CFD.jl

CFD.jl provides comprehensive high-performance computing capabilities through advanced linear solvers, GPU acceleration, and parallel algorithms.

## 🚀 Linear Solvers

### Preconditioned Conjugate Gradient (PCG)
Optimal for symmetric positive definite systems:

```julia
using CFD.Solvers

# Basic PCG solver
solver = PCG(tol=1e-8, maxiter=1000)
result = solve(solver, A, b)

# PCG with ILU preconditioning
solver_ilu = PCG(tol=1e-8, preconditioner=ILU(fill_in=2))
result_ilu = solve(solver_ilu, A, b)
```

### BiCGSTAB
Robust solver for nonsymmetric systems:

```julia
# BiCGSTAB for nonsymmetric matrices
solver = BiCGSTAB(tol=1e-8, maxiter=1000)
result = solve(solver, A, b)

# With Jacobi preconditioning
solver_jacobi = BiCGSTAB(preconditioner=JacobiPreconditioner(A))
result_jacobi = solve(solver_jaco<PERSON>, <PERSON>, b)
```

### GMRES
Flexible Krylov subspace method:

```julia
# GMRES with restart
solver = GMRES(tol=1e-8, restart=30, maxiter=1000)
result = solve(solver, A, b)

# GMRES with ILU preconditioning
solver_gmres_ilu = GMRES(tol=1e-8, restart=30, preconditioner=ILU())
result_gmres = solve(solver_gmres_ilu, A, b)
```

### Algebraic Multigrid (AMG)
Scalable solver for large systems:

```julia
# V-cycle AMG
solver_v = AMG(tol=1e-8, cycle_type=:V, n_levels=6)
result_v = solve(solver_v, A, b)

# W-cycle AMG for better convergence
solver_w = AMG(tol=1e-8, cycle_type=:W, n_levels=6)
result_w = solve(solver_w, A, b)
```

## 🔥 GPU Acceleration

### CUDA Support
Automatic GPU acceleration when CUDA is available:

```julia
using CUDA

# Check GPU availability
if CUDA.functional()
    println("GPU acceleration available")
    
    # GPU PCG solver
    gpu_solver = GPUPCG(tol=1e-6, preconditioner=:jacobi)
    
    # Transfer data to GPU
    A_gpu = CuArray(A)
    b_gpu = CuArray(b)
    
    # Solve on GPU
    result_gpu = solve(gpu_solver, A_gpu, b_gpu)
    
    # Transfer result back to CPU
    x_cpu = Array(result_gpu.x)
else
    @warn "CUDA not available, using CPU computation"
end
```

### GPU Performance Tips
1. **Memory Management**: Minimize CPU-GPU transfers
2. **Problem Size**: GPU acceleration effective for large systems (>10,000 unknowns)
3. **Preconditioning**: Use simple preconditioners (Jacobi) for GPU

## 🔧 Solver Selection Guide

### Automatic Solver Selection
```julia
function select_optimal_solver(A, b)
    n = size(A, 1)
    
    if n > 100000
        # Large systems - use AMG
        return AMG(cycle_type=:V, n_levels=6)
    elseif CUDA.functional() && n > 10000
        # GPU available for medium-large systems
        return GPUPCG(preconditioner=:jacobi)
    elseif issymmetric(A)
        # Symmetric systems - use PCG
        return PCG(preconditioner=ILU())
    else
        # Nonsymmetric systems - use BiCGSTAB
        return BiCGSTAB(preconditioner=JacobiPreconditioner(A))
    end
end

# Use automatic selection
solver = select_optimal_solver(A, b)
result = solve(solver, A, b)
```

### Performance Comparison
```julia
# Benchmark different solvers
solvers = [
    ("PCG", PCG(tol=1e-8)),
    ("PCG+ILU", PCG(tol=1e-8, preconditioner=ILU())),
    ("BiCGSTAB", BiCGSTAB(tol=1e-8)),
    ("GMRES(30)", GMRES(tol=1e-8, restart=30)),
    ("AMG V-cycle", AMG(tol=1e-8, cycle_type=:V)),
    ("AMG W-cycle", AMG(tol=1e-8, cycle_type=:W))
]

for (name, solver) in solvers
    time = @elapsed result = solve(solver, A, b)
    if result.converged
        println("$name: $(round(time, digits=3))s, $(result.iterations) iterations")
    else
        println("$name: Failed to converge")
    end
end
```

## 📊 Performance Monitoring

### Solver Diagnostics
```julia
# Detailed solver information
result = solve(solver, A, b)

println("Convergence: $(result.converged)")
println("Iterations: $(result.iterations)")
println("Final residual: $(result.residual)")
println("Residual history: $(length(result.residual_history))")

# Plot convergence history
if length(result.residual_history) > 0
    using Plots
    plot(result.residual_history, yscale=:log10, 
         xlabel="Iteration", ylabel="Residual", 
         title="Convergence History")
end
```

### Memory Usage Analysis
```julia
# Monitor memory usage
function analyze_memory_usage(A, solver_type)
    initial_memory = Base.gc_live_bytes()
    
    solver = solver_type(tol=1e-8)
    result = solve(solver, A, b)
    
    final_memory = Base.gc_live_bytes()
    memory_increase = final_memory - initial_memory
    
    println("Memory increase: $(memory_increase ÷ 1024^2) MB")
    return memory_increase
end

# Compare memory usage
pcg_memory = analyze_memory_usage(A, PCG)
amg_memory = analyze_memory_usage(A, AMG)
```

## 🎯 Best Practices

### 1. Problem-Specific Solver Selection
- **Poisson equations**: PCG with AMG preconditioning
- **Convection-diffusion**: BiCGSTAB or GMRES
- **Stokes flow**: Block preconditioned GMRES
- **Large systems (>100k)**: AMG solvers

### 2. Preconditioning Strategies
- **Jacobi**: Simple, good for GPU
- **ILU**: Effective for moderate fill-in
- **AMG**: Best for elliptic operators
- **Physics-based**: Problem-specific preconditioners

### 3. Convergence Criteria
```julia
# Relative tolerance for well-conditioned systems
solver = PCG(tol=1e-8)

# Absolute tolerance for poorly conditioned systems
solver = PCG(tol=1e-6, atol=1e-10)

# Maximum iterations to prevent hanging
solver = PCG(tol=1e-8, maxiter=1000)
```

### 4. GPU Optimization
```julia
# Optimal GPU solver configuration
function create_gpu_solver(problem_size)
    if problem_size > 50000
        return GPUPCG(tol=1e-6, maxiter=2000, preconditioner=:jacobi)
    else
        @warn "Problem too small for GPU acceleration"
        return PCG(tol=1e-8, preconditioner=ILU())
    end
end
```

## 🧪 Testing and Validation

Run parallel solver validation:
```bash
# Test all parallel solvers
./validation/run_validation.sh --phase 7

# Test GPU solvers specifically
CFD_TEST_GPU=true julia --project=. test/integration/solvers/test_linear_solvers.jl

# Benchmark performance
julia --project=. examples/parallel_solvers_demo.jl
```

## 📚 Further Reading

- [Linear Solver Theory](linear_solver_theory.md)
- [GPU Programming Guide](gpu_programming.md)
- [Performance Optimization](performance_optimization.md)
- [Parallel I/O](parallel_io.md)