# Moving Mesh and Dynamic Geometry in CFD.jl

CFD.jl provides comprehensive moving mesh capabilities for simulating flows with moving boundaries, rotating machinery, and complex dynamic geometries.

## 🔄 Rigid Body Motion

### Translation and Rotation
Basic rigid body transformations:

```julia
using CFD.Mesh

# Simple translation
displacement = SVector(1.0, 0.0, 0.0)  # Move 1m in x-direction
translate_mesh!(mesh, displacement)

# Simple rotation
angle = π/6  # 30 degrees
axis = SVector(0, 0, 1)  # z-axis
origin = SVector(0, 0, 0)  # Rotation center
rotate_mesh!(mesh, angle, axis, origin)

# Combined motion
transform = RigidBodyTransform(
    translation = SVector(0.5, 0.0, 0.0),
    rotation_angle = π/4,
    rotation_axis = SVector(0, 0, 1),
    rotation_center = SVector(0, 0, 0)
)
apply_transform!(mesh, transform)
```

### Time-Dependent Motion
Prescribed motion with time dependency:

```julia
# Define motion function
function rotor_motion(t, omega)
    angle = omega * t
    return RigidBodyTransform(
        rotation_angle = angle,
        rotation_axis = SVector(0, 0, 1),
        rotation_center = SVector(0, 0, 0)
    )
end

# Time loop with motion
for t in time_steps
    # Update mesh position
    transform = rotor_motion(t, 100.0)  # 100 rad/s
    apply_transform!(rotor_mesh, transform)
    
    # Update mesh metrics
    update_mesh_metrics!(rotor_mesh)
    
    # Solve flow equations
    solve_time_step!(U, p, dt)
end
```

### 6-DOF Motion
Full six degrees of freedom motion:

```julia
# 6-DOF rigid body motion
body_motion = SixDOFMotion(
    mass = 1000.0,           # kg
    moment_of_inertia = I_tensor,  # 3x3 inertia tensor
    initial_position = SVector(0, 0, 0),
    initial_velocity = SVector(10, 0, 0),
    initial_orientation = quaternion_identity(),
    initial_angular_velocity = SVector(0, 0, 0)
)

# Solve motion equations
function solve_6dof_motion!(body, forces, moments, dt)
    # Linear motion: F = ma
    acceleration = forces / body.mass
    body.velocity += acceleration * dt
    body.position += body.velocity * dt
    
    # Angular motion: M = I⋅α
    angular_acceleration = body.inertia_inv * moments
    body.angular_velocity += angular_acceleration * dt
    body.orientation = update_quaternion(body.orientation, body.angular_velocity, dt)
end
```

## 🌪️ Arbitrary Mesh Interface (AMI)

### AMI Setup and Configuration
Conservative interpolation between non-conformal meshes:

```julia
using CFD.Mesh

# Create AMI interface between rotating and stationary domains
ami = ArbitraryMeshInterface(
    source_patch = "rotor_interface",
    target_patch = "stator_interface",
    interpolation_method = :conservative,  # Conservative interpolation
    search_method = :octree,               # Spatial search algorithm
    tolerance = 1e-6                       # Geometric tolerance
)

# Calculate interpolation weights
weights = calculate_ami_weights(ami, source_mesh, target_mesh)

# Store for efficient reuse
cache_ami_weights!(ami, weights)
```

### AMI Field Interpolation
Transfer fields between meshes:

```julia
# Interpolate scalar field
p_target = ami_interpolate(p_source, ami, weights)

# Interpolate vector field with proper rotation
U_target = ami_interpolate_vector(U_source, ami, weights, rotation_tensor)

# Interpolate with gradient preservation
phi_target, grad_phi_target = ami_interpolate_with_gradient(
    phi_source, grad_phi_source, ami, weights
)

# Flux conservation check
flux_source = sum(U_source ⋅ face_normals_source)
flux_target = sum(U_target ⋅ face_normals_target)
conservation_error = abs(flux_source - flux_target) / abs(flux_source)
@assert conservation_error < 1e-6 "Flux not conserved through AMI"
```

### Dynamic AMI Updates
Update AMI weights during simulation:

```julia
# Update AMI weights when mesh moves
function update_ami_for_motion!(ami, source_mesh, target_mesh, dt)
    # Check if update is needed (based on rotation angle)
    rotation_since_update = accumulated_rotation(ami)
    
    if rotation_since_update > ami.update_threshold
        @info "Updating AMI weights due to large rotation"
        
        # Recalculate intersection geometry
        new_weights = calculate_ami_weights(ami, source_mesh, target_mesh)
        
        # Smooth transition from old to new weights
        blend_factor = 0.1  # Gradual transition
        ami.weights = (1 - blend_factor) * ami.weights + blend_factor * new_weights
        
        # Reset rotation counter
        reset_rotation_counter!(ami)
    end
end
```

## ⚙️ Rotor Dynamics and Turbomachinery

### Rotor Definition and Setup
Define rotating machinery components:

```julia
using CFD.Physics

# Define helicopter rotor
rotor = Rotor(
    center = SVector(0, 0, 1),      # 1m above ground
    axis = SVector(0, 0, 1),        # Vertical rotation axis
    radius = 2.0,                   # 2m rotor radius
    omega = 27.0,                   # 27 rad/s (260 RPM)
    num_blades = 4,                 # 4-bladed rotor
    blade_chord = 0.2,              # 0.2m chord length
    blade_twist = deg2rad(8),       # 8° collective pitch
    airfoil_cl_alpha = 2π,          # Lift curve slope
    airfoil_cd0 = 0.01             # Zero-lift drag coefficient
)

# Define rotor zones in mesh
rotor_zone = MeshZone(
    name = "rotor_disk",
    center = rotor.center,
    radius = rotor.radius,
    thickness = 0.1,                # Actuator disk thickness
    cells = get_cells_in_cylinder(mesh, rotor.center, rotor.radius, 0.1)
)
```

### Blade Element Momentum Theory
Calculate rotor forces using BEM theory:

```julia
# Blade element momentum theory implementation
function compute_rotor_forces(rotor, flow_field, mesh)
    forces = zeros(SVector{3,Float64}, length(rotor_zone.cells))
    
    for (i, cell) in enumerate(rotor_zone.cells)
        # Local flow conditions
        U_local = interpolate_velocity(flow_field, cell.center)
        
        # Radial position and local blade properties
        r = norm(cell.center - rotor.center)
        local_chord = rotor.blade_chord * (1 - 0.3 * r/rotor.radius)  # Taper
        local_twist = rotor.blade_twist * r/rotor.radius
        
        # Compute local inflow angle and forces
        V_tangential = rotor.omega * r
        V_axial = dot(U_local, rotor.axis)
        phi = atan(V_axial, V_tangential)  # Inflow angle
        
        # Angle of attack
        alpha = local_twist - phi
        
        # Lift and drag coefficients (simplified)
        Cl = rotor.airfoil_cl_alpha * alpha
        Cd = rotor.airfoil_cd0 + 0.1 * Cl^2  # Induced drag
        
        # Local dynamic pressure
        V_rel = sqrt(V_axial^2 + V_tangential^2)
        q = 0.5 * density * V_rel^2
        
        # Forces per unit span
        dL = q * local_chord * Cl  # Lift per unit span
        dD = q * local_chord * Cd  # Drag per unit span
        
        # Convert to thrust and torque contributions
        dT = dL * cos(phi) - dD * sin(phi)  # Thrust contribution
        dQ = (dL * sin(phi) + dD * cos(phi)) * r  # Torque contribution
        
        # Distribute force over cell volume
        cell_volume = get_cell_volume(mesh, cell)
        thrust_density = dT * rotor.num_blades / (2π * r * cell_volume)
        
        # Force vector (thrust direction)
        forces[i] = thrust_density * rotor.axis
    end
    
    return forces
end
```

### Actuator Disk Implementation
Apply rotor forces to flow field:

```julia
# Apply rotor forces as source terms
function apply_rotor_source_terms!(momentum_eq, rotor, forces)
    for (i, cell) in enumerate(rotor_zone.cells)
        # Add source term to momentum equation
        add_source_term!(momentum_eq, cell, forces[i])
    end
    
    # Update rotor performance
    total_thrust = sum(forces[i] ⋅ rotor.axis for i in 1:length(forces))
    total_torque = sum(cross(cell.center - rotor.center, forces[i]) ⋅ rotor.axis 
                      for (i, cell) in enumerate(rotor_zone.cells))
    
    # Rotor power
    power = total_torque * rotor.omega
    
    @info "Rotor performance: Thrust = $(total_thrust) N, Power = $(power) W"
end
```

## 🌊 Mesh Deformation and Morphing

### Laplacian Smoothing
Smooth mesh deformation for boundary conforming:

```julia
using CFD.Mesh

# Laplacian mesh smoothing
function laplacian_smoothing!(mesh, boundary_displacement, iterations=5)
    # Initialize internal point displacement
    displacement = zeros(SVector{3,Float64}, length(mesh.points))
    
    # Set boundary displacements
    for (point_id, disp) in boundary_displacement
        displacement[point_id] = disp
    end
    
    # Iterative Laplacian smoothing
    for iter in 1:iterations
        new_displacement = copy(displacement)
        
        for point in interior_points(mesh)
            # Average displacement of neighboring points
            neighbors = get_neighboring_points(mesh, point)
            avg_disp = sum(displacement[n] for n in neighbors) / length(neighbors)
            new_displacement[point] = avg_disp
        end
        
        displacement = new_displacement
    end
    
    # Apply displacement to mesh
    for (i, point) in enumerate(mesh.points)
        mesh.points[i] = point + displacement[i]
    end
    
    # Update mesh metrics
    update_mesh_metrics!(mesh)
end
```

### Spring-Based Deformation
Spring analogy for more robust deformation:

```julia
# Spring-based mesh deformation
function spring_mesh_deformation!(mesh, boundary_displacement)
    # Create spring system
    springs = create_spring_system(mesh)
    
    # Set up system of equations: K⋅x = f
    K = assemble_stiffness_matrix(springs)
    f = assemble_force_vector(boundary_displacement)
    
    # Solve for internal point displacements
    displacement = solve(K, f)
    
    # Apply displacements
    apply_displacement!(mesh, displacement)
    
    # Check mesh quality
    quality = check_mesh_quality(mesh)
    if quality.min_volume < 0
        @warn "Negative volume cells detected after deformation"
        # Possibly reduce displacement or use different method
    end
end
```

### Radial Basis Function (RBF) Deformation
High-quality deformation using RBF interpolation:

```julia
# RBF mesh deformation (advanced method)
function rbf_mesh_deformation!(mesh, boundary_displacement)
    # Choose RBF type
    rbf = ThinPlateSpline()  # or Wendland, Gaussian, etc.
    
    # Control points (boundary points)
    control_points = get_boundary_points(mesh)
    control_displacements = [boundary_displacement[p] for p in control_points]
    
    # Build RBF interpolation
    rbf_interpolant = build_rbf_interpolation(
        control_points, control_displacements, rbf
    )
    
    # Interpolate displacement for all mesh points
    for (i, point) in enumerate(mesh.points)
        if !(i in control_points)
            mesh.points[i] = point + evaluate_rbf(rbf_interpolant, point)
        end
    end
    
    update_mesh_metrics!(mesh)
end
```

## 🎯 Advanced Moving Mesh Techniques

### Sliding Mesh Interface
For rotating machinery with multiple rotating parts:

```julia
# Sliding mesh interface setup
sliding_interface = SlidingMeshInterface(
    stationary_zone = "stator",
    rotating_zone = "rotor",
    interface_patches = ("stator_interface", "rotor_interface"),
    rotation_axis = SVector(0, 0, 1),
    rotation_center = SVector(0, 0, 0)
)

# Time loop with sliding mesh
for t in time_steps
    # Rotate mesh
    angle = omega * dt
    rotate_mesh_zone!(rotating_zone, angle, sliding_interface.rotation_axis)
    
    # Update interface connectivity
    update_sliding_interface!(sliding_interface, mesh)
    
    # Solve with updated connectivity
    solve_time_step!(U, p, dt)
end
```

### Overset/Chimera Meshes
Multiple overlapping meshes:

```julia
# Overset mesh system (in development)
overset_system = OversetMeshSystem([
    OversetMesh("background", background_mesh),
    OversetMesh("body1", body1_mesh),
    OversetMesh("body2", body2_mesh)
])

# Hole cutting and connectivity
perform_hole_cutting!(overset_system)
establish_connectivity!(overset_system)

# Interpolation between meshes
function interpolate_overset_boundaries!(overset_system, fields)
    for mesh in overset_system.meshes
        for boundary in mesh.overset_boundaries
            interpolate_from_donor_meshes!(boundary, fields, overset_system)
        end
    end
end
```

### Immersed Boundary Methods
Complex geometries without body-fitted meshes:

```julia
# Immersed boundary method (in development)
immersed_body = ImmersedBody(
    geometry = read_stl_geometry("complex_body.stl"),
    velocity = body_velocity_function,
    force_calculation = :direct_forcing
)

# Apply immersed boundary conditions
function apply_immersed_boundary!(U, p, immersed_body, mesh)
    # Identify cells intersected by body
    intersected_cells = find_intersected_cells(mesh, immersed_body.geometry)
    
    # Apply direct forcing
    for cell in intersected_cells
        # Calculate body velocity at cell center
        U_body = immersed_body.velocity(cell.center, t)
        
        # Apply forcing term to drive velocity to body velocity
        forcing = (U_body - U[cell]) / dt
        add_source_term!(momentum_eq, cell, forcing)
    end
end
```

## 📊 Performance and Optimization

### Efficient AMI Algorithms
Optimize AMI performance for large meshes:

```julia
# Optimized AMI with spatial acceleration
function optimized_ami_search(source_mesh, target_mesh)
    # Build octree for efficient spatial searches
    octree = build_octree(source_mesh.faces)
    
    # Parallel weight calculation
    weights = @distributed (vcat) for target_face in target_mesh.faces
        # Find candidate source faces using octree
        candidates = octree_search(octree, target_face.bounding_box)
        
        # Calculate intersection weights only for candidates
        face_weights = calculate_intersection_weights(target_face, candidates)
        face_weights
    end
    
    return weights
end
```

### Memory Management for Moving Meshes
Efficient memory usage:

```julia
# Memory-efficient mesh motion
struct EfficientMeshMotion
    original_positions::Vector{SVector{3,Float64}}
    displacement_cache::Vector{SVector{3,Float64}}
    update_frequency::Int
    last_update::Int
end

function update_mesh_efficiently!(motion::EfficientMeshMotion, mesh, t)
    if t - motion.last_update >= motion.update_frequency
        # Calculate new displacements
        for (i, original_pos) in enumerate(motion.original_positions)
            motion.displacement_cache[i] = calculate_displacement(original_pos, t)
        end
        
        # Apply cached displacements
        for (i, point) in enumerate(mesh.points)
            mesh.points[i] = motion.original_positions[i] + motion.displacement_cache[i]
        end
        
        motion.last_update = t
        update_mesh_metrics!(mesh)
    end
end
```

## 🧪 Validation and Testing

### Moving Mesh Validation Cases
Standard test cases for moving mesh validation:

```julia
# Oscillating cylinder validation
function validate_oscillating_cylinder()
    # Set up oscillating cylinder case
    cylinder_motion(t) = SVector(0.2 * sin(2π * t), 0.0, 0.0)
    
    mesh = generate_cylinder_mesh()
    
    # Time loop with prescribed motion
    for t in time_steps
        # Move cylinder
        displacement = cylinder_motion(t)
        translate_mesh_region!(mesh, "cylinder", displacement)
        
        # Solve flow
        solve_time_step!(U, p, dt)
    end
    
    # Validate against experimental data
    cl_computed = compute_lift_coefficient(U, p, cylinder_patch)
    cl_reference = reference_oscillating_cylinder_data(t)
    
    @test abs(cl_computed - cl_reference) < 0.1
end

# Rotor in hover validation
function validate_rotor_hover()
    rotor = setup_helicopter_rotor()
    mesh = generate_rotor_mesh()
    
    # Steady-state hover simulation
    converged_solution = solve_steady_hover(rotor, mesh)
    
    # Check momentum theory predictions
    thrust_theory = π * rotor.radius^2 * disk_loading
    thrust_computed = compute_total_thrust(converged_solution)
    
    error = abs(thrust_computed - thrust_theory) / thrust_theory
    @test error < 0.05  # 5% tolerance
end
```

### AMI Conservation Testing
Verify conservation properties:

```julia
# Test AMI conservation
function test_ami_conservation()
    # Create test case with known flux
    source_mesh, target_mesh = create_ami_test_meshes()
    
    # Set up uniform flow field
    U_uniform = VectorField(:U, source_mesh, SVector(1.0, 0.0, 0.0))
    
    # Calculate flux through source interface
    flux_source = compute_interface_flux(U_uniform, source_interface)
    
    # Interpolate to target
    ami = ArbitraryMeshInterface(source_interface, target_interface)
    U_interpolated = ami_interpolate(U_uniform, ami)
    
    # Calculate flux through target interface
    flux_target = compute_interface_flux(U_interpolated, target_interface)
    
    # Check conservation
    conservation_error = abs(flux_source - flux_target) / abs(flux_source)
    @test conservation_error < 1e-10  # Very tight tolerance for conservation
end
```

## 🎯 Best Practices

### 1. Mesh Quality Management
```julia
# Monitor mesh quality during deformation
function monitor_mesh_quality(mesh, tolerance=0.1)
    quality = check_mesh_quality(mesh)
    
    if quality.min_orthogonality < tolerance
        @warn "Poor mesh orthogonality: $(quality.min_orthogonality)"
        # Consider reducing time step or displacement
    end
    
    if quality.max_skewness > 0.8
        @warn "High mesh skewness: $(quality.max_skewness)"
        # Consider mesh regeneration
    end
    
    if any(quality.cell_volumes .< 0)
        @error "Negative cell volumes detected!"
        # Emergency mesh recovery needed
    end
end
```

### 2. Time Step Selection
```julia
# Adaptive time stepping for moving mesh
function adaptive_time_step(mesh_velocity, CFL_max=0.5)
    # Compute characteristic length scales
    min_cell_size = minimum(compute_cell_sizes(mesh))
    
    # Maximum velocity in domain
    max_velocity = maximum(norm.(mesh_velocity))
    
    # CFL-based time step
    dt_cfl = CFL_max * min_cell_size / max_velocity
    
    # Additional constraint for mesh motion
    max_displacement = maximum(norm.(mesh_velocity)) * dt_cfl
    dt_mesh = 0.1 * min_cell_size / max_displacement
    
    return min(dt_cfl, dt_mesh)
end
```

### 3. AMI Weight Caching
```julia
# Efficient AMI weight management
mutable struct AMICache
    weights::Vector{Float64}
    geometry_hash::UInt64
    last_update::Float64
    update_threshold::Float64
end

function get_ami_weights(ami_cache, source_mesh, target_mesh, current_time)
    current_hash = hash_mesh_geometry(source_mesh, target_mesh)
    
    if (current_hash != ami_cache.geometry_hash || 
        current_time - ami_cache.last_update > ami_cache.update_threshold)
        
        # Recalculate weights
        ami_cache.weights = calculate_ami_weights(source_mesh, target_mesh)
        ami_cache.geometry_hash = current_hash
        ami_cache.last_update = current_time
    end
    
    return ami_cache.weights
end
```

## 🧪 Testing and Validation

Run moving mesh validation:
```bash
# Test all moving mesh capabilities
./validation/run_validation.sh --phase 9

# Test specific components
CFD_MOVING_MESH_TEST=rotor julia --project=. validation/phase9_moving_mesh.jl
CFD_MOVING_MESH_TEST=ami julia --project=. validation/phase9_moving_mesh.jl

# Performance benchmarks
julia --project=. examples/moving_mesh_performance.jl
```

## 📚 Further Reading

- [AMI Theory and Implementation](ami_theory.md)
- [Rotor Aerodynamics](rotor_aerodynamics.md)
- [Mesh Deformation Algorithms](mesh_deformation.md)
- [Turbomachinery Applications](turbomachinery_guide.md)