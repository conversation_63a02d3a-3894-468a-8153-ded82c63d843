# High-Order Numerical Methods in CFD.jl

CFD.jl provides advanced high-order numerical schemes for enhanced accuracy and sophisticated flow physics simulation, including shock capturing, flux limiters, and spectral methods.

## 🎯 High-Order Spatial Discretization

### QUICK Scheme (3rd Order)
Quadratic Upstream Interpolation for Convective Kinematics:

```julia
using CFD.Numerics

# QUICK scheme for convection terms
quick_scheme = QUICK(
    upwind_factor = 0.75,    # Upwind weighting
    central_factor = 0.25,   # Central differencing contribution
    limiter = :none          # Optional flux limiter
)

# Apply to convection term
convection_term = ∇⋅(𝐮⊗𝐮)
quick_discretization = apply_scheme(convection_term, quick_scheme, mesh)

# Automatic scheme selection based on local Peclet number
function select_convection_scheme(Pe_local)
    if Pe_local < 2.0
        return CentralDifferencing()   # Low Peclet - use central
    elseif Pe_local < 10.0
        return QUICK()                 # Moderate Peclet - use QUICK
    else
        return FirstOrderUpwind()      # High Peclet - use upwind
    end
end
```

### MUSCL Reconstruction
Monotonic Upstream-centered Scheme for Conservation Laws:

```julia
# MUSCL scheme with van <PERSON> limiter
muscl_scheme = MUSCL(
    limiter = VanLeerLimiter(),
    reconstruction_order = 2,
    gradient_limiter = true
)

# Reconstruct face values
phi_face_left, phi_face_right = muscl_reconstruct(
    phi_cell_values, 
    cell_gradients, 
    muscl_scheme, 
    face
)

# Compute numerical flux
numerical_flux = compute_roe_flux(
    phi_face_left, phi_face_right, 
    face_normal, face_velocity
)
```

### Total Variation Diminishing (TVD) Limiters
Prevent spurious oscillations while maintaining accuracy:

```julia
# Available flux limiters
limiters = [
    MinModLimiter(),              # Most diffusive, most stable
    VanLeerLimiter(),            # Good balance of accuracy/stability  
    SuperbeeLimiter(),           # Less diffusive, higher accuracy
    MonotonizedCentralLimiter(), # High accuracy in smooth regions
    VanAlbadaLimiter(),          # Smooth variation, good for complex flows
    KorenLimiter()               # Third-order accurate in smooth regions
]

# Apply limiter to gradient
function apply_flux_limiter(gradient, limiter_type, r_factor)
    r = compute_gradient_ratio(gradient, r_factor)
    limiter_function = get_limiter_function(limiter_type)
    return gradient * limiter_function(r)
end

# Limiter function implementations
van_leer_limiter(r) = (r + abs(r)) / (1 + abs(r))
superbee_limiter(r) = max(0, min(2*r, 1), min(r, 2))
minmod_limiter(r) = max(0, min(1, r))
```

### WENO Reconstruction
Weighted Essentially Non-Oscillatory schemes for shock capturing:

```julia
# 5th order WENO scheme
weno5_scheme = WENO5(
    epsilon = 1e-6,          # Small parameter to avoid division by zero
    power = 2,               # Power in smoothness indicator
    mapping = :js            # Jiang-Shu or Z mapping
)

# WENO reconstruction for high-order accuracy
function weno5_reconstruct(cell_values, stencil)
    # Three candidate stencils (left-biased, central, right-biased)
    stencils = [
        stencil[1:3],   # Left stencil: i-2, i-1, i
        stencil[2:4],   # Central stencil: i-1, i, i+1  
        stencil[3:5]    # Right stencil: i, i+1, i+2
    ]
    
    # Compute smoothness indicators
    smoothness = [compute_smoothness_indicator(s, cell_values) for s in stencils]
    
    # Compute non-linear weights
    alpha = [(1.0 / (weno5_scheme.epsilon + smoothness[k])^weno5_scheme.power) 
             for k in 1:3]
    weights = alpha ./ sum(alpha)
    
    # Combine reconstructions
    reconstructions = [polynomial_reconstruction(s, cell_values) for s in stencils]
    return sum(weights[k] * reconstructions[k] for k in 1:3)
end

# Smoothness indicators for WENO5
function compute_smoothness_indicator(stencil, values)
    # Compute derivatives at stencil points
    v = [values[i] for i in stencil]
    
    # Standard WENO5 smoothness indicators
    if length(stencil) == 3
        d1 = v[2] - v[1]
        d2 = v[3] - v[2]
        return 13/12 * (d1 - d2)^2 + 1/4 * (d1 - 3*d2)^2
    end
end
```

## 🌊 Spectral Methods

### Fourier Spectral Differentiation
For periodic domains with high accuracy:

```julia
using FFTW, CFD.Numerics

# Spectral derivative calculation
function spectral_derivative(field_values, domain_length, direction)
    N = length(field_values)
    
    # Wavenumber vector
    k = fftfreq(N, N/domain_length) * 2π
    
    # Forward FFT
    field_hat = fft(field_values)
    
    # Spectral differentiation (multiply by ik)
    if direction == :x
        derivative_hat = (1im * k) .* field_hat
    elseif direction == :y
        derivative_hat = (1im * k) .* field_hat
    end
    
    # Inverse FFT
    derivative = real(ifft(derivative_hat))
    
    return derivative
end

# 2D spectral derivatives
function spectral_gradient_2d(phi, Lx, Ly)
    Nx, Ny = size(phi)
    
    # Wavenumber vectors
    kx = fftfreq(Nx, Nx/Lx) * 2π
    ky = fftfreq(Ny, Ny/Ly) * 2π
    
    # 2D FFT
    phi_hat = fft(phi)
    
    # Compute derivatives
    dphi_dx_hat = (1im * kx') .* phi_hat
    dphi_dy_hat = (1im * ky) .* phi_hat
    
    # Inverse transform
    dphi_dx = real(ifft(dphi_dx_hat))
    dphi_dy = real(ifft(dphi_dy_hat))
    
    return dphi_dx, dphi_dy
end
```

### Dealiasing for Nonlinear Terms
Prevent aliasing errors in spectral methods:

```julia
# 2/3 rule dealiasing
function dealias_2_3_rule(field_hat, N)
    # Zero out high wavenumbers (upper 1/3)
    dealias_cutoff = N ÷ 3
    
    field_hat_dealiased = copy(field_hat)
    field_hat_dealiased[dealias_cutoff+1:end-dealias_cutoff] .= 0
    
    return field_hat_dealiased
end

# Dealiased nonlinear term computation
function compute_nonlinear_spectral(u, v, Lx, Ly)
    # Forward transforms
    u_hat = fft(u)
    v_hat = fft(v)
    
    # Compute product in physical space (3/2 padding for dealiasing)
    Nx_pad = 3 * size(u, 1) ÷ 2
    Ny_pad = 3 * size(u, 2) ÷ 2
    
    u_pad = real(ifft(zero_pad(u_hat, Nx_pad, Ny_pad)))
    v_pad = real(ifft(zero_pad(v_hat, Nx_pad, Ny_pad)))
    
    # Nonlinear term in physical space
    nonlinear_pad = u_pad .* v_pad
    
    # Transform back and truncate
    nonlinear_hat = fft(nonlinear_pad)
    return truncate_spectrum(nonlinear_hat, size(u))
end
```

### Chebyshev Spectral Methods
For non-periodic domains:

```julia
# Chebyshev differentiation matrix
function chebyshev_diff_matrix(N)
    # Chebyshev points
    x = cos.(π * (0:N) / N)
    
    # Differentiation matrix
    D = zeros(N+1, N+1)
    
    for i in 0:N, j in 0:N
        if i != j
            D[i+1, j+1] = (-1)^(i+j) * c_weight(i) / (c_weight(j) * (x[i+1] - x[j+1]))
        elseif i == j && 0 < i < N
            D[i+1, j+1] = -x[i+1] / (2 * (1 - x[i+1]^2))
        elseif i == j == 0
            D[1, 1] = (2*N^2 + 1) / 6
        elseif i == j == N
            D[N+1, N+1] = -(2*N^2 + 1) / 6
        end
    end
    
    return D
end

# Chebyshev weight function
c_weight(i) = i == 0 || i == N ? 2 : 1
```

## ⚡ Shock Capturing Methods

### Artificial Viscosity
Add numerical dissipation in shock regions:

```julia
# Artificial viscosity for shock capturing
function compute_artificial_viscosity(U, pressure, mesh, C_av=0.1)
    artificial_visc = zeros(length(mesh.cells))
    
    for (i, cell) in enumerate(mesh.cells)
        # Compute velocity divergence
        div_U = compute_divergence(U, cell, mesh)
        
        # Shock indicator based on velocity divergence
        if div_U < 0  # Compression
            # Cell size and sound speed
            h = cell_size(cell)
            c = sqrt(gamma * pressure[i] / density[i])
            
            # Artificial viscosity coefficient
            artificial_visc[i] = C_av * h^2 * abs(div_U) * density[i]
        end
    end
    
    return artificial_visc
end

# Apply artificial viscosity to momentum equation
function apply_artificial_viscosity!(momentum_eq, artificial_visc, U, mesh)
    for (i, cell) in enumerate(mesh.cells)
        if artificial_visc[i] > 0
            # Add viscous term: ∇⋅(μ_av ∇U)
            visc_term = compute_viscous_term(artificial_visc[i], U, cell, mesh)
            add_source_term!(momentum_eq, i, visc_term)
        end
    end
end
```

### Slope Limiters for Discontinuities
Preserve monotonicity near shocks:

```julia
# Slope limiter for scalar transport
function apply_slope_limiter(phi, gradient, limiter_type, mesh)
    limited_gradient = copy(gradient)
    
    for (i, cell) in enumerate(mesh.cells)
        neighbors = get_cell_neighbors(cell, mesh)
        
        # Compute gradient ratios with neighbors
        min_ratio = Inf
        max_ratio = -Inf
        
        for neighbor in neighbors
            delta_phi = phi[neighbor] - phi[i]
            if abs(gradient[i]) > 1e-12
                ratio = delta_phi / (gradient[i] ⋅ direction_vector(cell, neighbor))
                min_ratio = min(min_ratio, ratio)
                max_ratio = max(max_ratio, ratio)
            end
        end
        
        # Apply limiter
        if limiter_type == :minmod
            limiter_factor = max(0, min(1, min_ratio, 1/max_ratio))
        elseif limiter_type == :van_leer
            r_avg = (min_ratio + max_ratio) / 2
            limiter_factor = (r_avg + abs(r_avg)) / (1 + abs(r_avg))
        end
        
        limited_gradient[i] *= limiter_factor
    end
    
    return limited_gradient
end
```

### Discontinuous Galerkin (DG) Methods
High-order methods for complex geometries:

```julia
# DG scheme framework (advanced implementation)
struct DGScheme
    polynomial_degree::Int
    basis_functions::Vector{Function}
    quadrature_points::Vector{Float64}
    quadrature_weights::Vector{Float64}
    flux_function::Function
end

# DG spatial discretization
function dg_spatial_discretization(phi, dg_scheme, mesh)
    residual = zeros(length(mesh.cells), dg_scheme.polynomial_degree + 1)
    
    for (i, cell) in enumerate(mesh.cells)
        # Volume integral
        vol_integral = compute_dg_volume_integral(phi, dg_scheme, cell)
        
        # Surface integral (numerical fluxes)
        surf_integral = compute_dg_surface_integral(phi, dg_scheme, cell, mesh)
        
        # Combine volume and surface contributions
        residual[i, :] = vol_integral + surf_integral
    end
    
    return residual
end
```

## 🎛️ Adaptive Methods

### Adaptive Mesh Refinement (AMR)
Dynamic mesh refinement based on solution features:

```julia
# AMR criterion based on gradient
function amr_refinement_criterion(phi, gradient_threshold=0.1)
    refinement_flag = zeros(Bool, length(mesh.cells))
    
    for (i, cell) in enumerate(mesh.cells)
        # Compute solution gradient magnitude
        grad_magnitude = norm(compute_gradient(phi, cell, mesh))
        
        # Refinement criterion
        characteristic_length = cell_size(cell)
        normalized_gradient = grad_magnitude * characteristic_length / phi[i]
        
        if normalized_gradient > gradient_threshold
            refinement_flag[i] = true
        end
    end
    
    return refinement_flag
end

# Octree-based mesh refinement
function octree_refinement!(mesh, refinement_flag)
    for (i, cell) in enumerate(mesh.cells)
        if refinement_flag[i] && can_refine(cell)
            # Split cell into 8 children (3D) or 4 children (2D)
            child_cells = split_cell(cell)
            
            # Add children to mesh
            for child in child_cells
                push!(mesh.cells, child)
            end
            
            # Mark parent as inactive
            cell.active = false
        end
    end
    
    # Update mesh connectivity
    update_mesh_connectivity!(mesh)
end
```

### p-Refinement
Increase polynomial order locally:

```julia
# Adaptive polynomial order selection
function adaptive_p_refinement(solution_error, target_accuracy)
    optimal_order = zeros(Int, length(mesh.cells))
    
    for (i, cell) in enumerate(mesh.cells)
        # Estimate error for different polynomial orders
        orders = 1:5
        estimated_errors = [estimate_error(solution, order, cell) for order in orders]
        
        # Find minimum order that achieves target accuracy
        for (j, error) in enumerate(estimated_errors)
            if error < target_accuracy
                optimal_order[i] = orders[j]
                break
            end
        end
        
        # Default to highest order if target not achieved
        if optimal_order[i] == 0
            optimal_order[i] = maximum(orders)
        end
    end
    
    return optimal_order
end
```

### hp-Adaptive Methods
Combined mesh and order refinement:

```julia
# hp-refinement strategy
function hp_adaptive_refinement!(mesh, solution, error_estimate)
    for (i, cell) in enumerate(mesh.cells)
        error = error_estimate[i]
        current_order = cell.polynomial_order
        
        if error > refinement_threshold
            # Decide between h-refinement and p-refinement
            if solution_is_smooth(solution, cell)
                # Smooth solution: increase polynomial order
                cell.polynomial_order = min(current_order + 1, max_polynomial_order)
            else
                # Non-smooth solution: refine mesh
                if can_refine(cell)
                    refine_cell!(mesh, cell)
                end
            end
        elseif error < coarsening_threshold
            # Consider coarsening or order reduction
            if current_order > 1
                cell.polynomial_order = current_order - 1
            elseif can_coarsen(cell)
                coarsen_cell!(mesh, cell)
            end
        end
    end
end
```

## 📊 Advanced Numerical Features

### Multi-Resolution Analysis
Wavelet-based adaptive methods:

```julia
# Wavelet-based grid adaptation
function wavelet_adaptation(solution, wavelet_threshold)
    # Compute wavelet coefficients
    coefficients = compute_wavelet_transform(solution)
    
    # Identify significant coefficients
    significant = abs.(coefficients) .> wavelet_threshold
    
    # Mark cells for refinement/coarsening
    refinement_flag = zeros(Bool, length(mesh.cells))
    coarsening_flag = zeros(Bool, length(mesh.cells))
    
    for (i, cell) in enumerate(mesh.cells)
        if significant[i] && can_refine(cell)
            refinement_flag[i] = true
        elseif !significant[i] && can_coarsen(cell)
            coarsening_flag[i] = true
        end
    end
    
    return refinement_flag, coarsening_flag
end
```

### Multi-Grid Methods
Accelerate convergence using multiple grid levels:

```julia
# Geometric multigrid implementation
function multigrid_v_cycle!(solution, rhs, mg_levels, v1=2, v2=2)
    n_levels = length(mg_levels)
    
    # Downward sweep
    for level in 1:n_levels-1
        # Pre-smoothing
        for i in 1:v1
            gauss_seidel_smooth!(solution[level], rhs[level])
        end
        
        # Compute residual
        residual = compute_residual(solution[level], rhs[level])
        
        # Restrict to coarser level
        rhs[level+1] = restrict(residual, mg_levels[level], mg_levels[level+1])
        solution[level+1] .= 0  # Initial guess for correction
    end
    
    # Coarsest level solve
    solve_coarse_level!(solution[n_levels], rhs[n_levels])
    
    # Upward sweep
    for level in n_levels-1:-1:1
        # Interpolate correction
        correction = interpolate(solution[level+1], mg_levels[level+1], mg_levels[level])
        solution[level] += correction
        
        # Post-smoothing
        for i in 1:v2
            gauss_seidel_smooth!(solution[level], rhs[level])
        end
    end
end
```

## 🧪 Verification and Validation

### Method of Manufactured Solutions
Systematic verification of numerical schemes:

```julia
# Manufactured solution for accuracy testing
function manufactured_solution_test(scheme, mesh_sizes)
    errors = Float64[]
    
    # Define manufactured solution
    u_exact(x, y, t) = sin(π*x) * cos(π*y) * exp(-2*π^2*t)
    
    for h in mesh_sizes
        # Generate mesh
        mesh = generate_uniform_mesh(h)
        
        # Compute source term that gives manufactured solution
        source_term = compute_manufactured_source(u_exact, mesh)
        
        # Solve with source term
        u_numerical = solve_with_scheme(scheme, mesh, source_term)
        
        # Compute error
        u_exact_values = [u_exact(cell.center..., final_time) for cell in mesh.cells]
        error = norm(u_numerical - u_exact_values, 2) / norm(u_exact_values, 2)
        push!(errors, error)
    end
    
    # Verify convergence order
    convergence_order = compute_convergence_order(mesh_sizes, errors)
    return convergence_order, errors
end

# Convergence order calculation
function compute_convergence_order(h_values, errors)
    n = length(h_values)
    orders = Float64[]
    
    for i in 2:n
        order = log(errors[i]/errors[i-1]) / log(h_values[i]/h_values[i-1])
        push!(orders, order)
    end
    
    return mean(orders)
end
```

### Benchmark Problem Testing
Standard CFD test cases:

```julia
# Taylor-Green vortex for high-order scheme validation
function taylor_green_vortex_test(scheme)
    # Initial conditions
    u0(x, y) = sin(x) * cos(y)
    v0(x, y) = -cos(x) * sin(y)
    p0(x, y) = 0.25 * (cos(2*x) + cos(2*y))
    
    # Analytical solution at later times
    u_exact(x, y, t) = u0(x, y) * exp(-2*ν*t)
    v_exact(x, y, t) = v0(x, y) * exp(-2*ν*t)
    
    # Run simulation
    U_final, p_final = solve_taylor_green(scheme, final_time)
    
    # Compare with analytical solution
    U_exact = [SVector(u_exact(cell.center..., final_time), 
                      v_exact(cell.center..., final_time), 0.0) 
               for cell in mesh.cells]
    
    error = norm(U_final - U_exact) / norm(U_exact)
    return error
end
```

## 🎯 Best Practices

### 1. Scheme Selection Guidelines
```julia
# Automatic scheme selection based on flow characteristics
function select_optimal_scheme(flow_analysis)
    if flow_analysis.has_shocks
        return WENO5(epsilon=1e-6)  # Shock capturing
    elseif flow_analysis.requires_high_accuracy
        return spectral_method()     # Spectral accuracy
    elseif flow_analysis.has_strong_convection
        return QUICK(limiter=VanLeerLimiter())  # Stable convection
    else
        return CentralDifferencing()  # Standard case
    end
end
```

### 2. Stability Considerations
```julia
# CFL condition for high-order schemes
function compute_cfl_limit(scheme_order, mesh, velocity)
    base_cfl = 0.5  # Conservative base CFL
    
    # Reduce CFL for higher order schemes
    order_factor = 2.0 / scheme_order
    
    # Account for mesh non-uniformity
    mesh_factor = minimum_cell_size(mesh) / maximum_cell_size(mesh)
    
    # Maximum velocity consideration
    max_velocity = maximum(norm.(velocity))
    
    cfl_limit = base_cfl * order_factor * mesh_factor
    return min(cfl_limit, 0.9)  # Never exceed 0.9
end
```

### 3. Error Estimation
```julia
# Richardson extrapolation for error estimation
function richardson_error_estimate(solution_fine, solution_coarse, refinement_ratio, order)
    # Richardson extrapolation formula
    error_estimate = (solution_fine - solution_coarse) / (refinement_ratio^order - 1)
    return norm(error_estimate)
end
```

## 🧪 Testing and Validation

Run high-order methods validation:
```bash
# Test all high-order schemes
./validation/run_validation.sh --phase 10

# Test specific methods
CFD_HIGH_ORDER_TEST=weno julia --project=. validation/phase10_advanced_numerics.jl
CFD_HIGH_ORDER_TEST=spectral julia --project=. validation/phase10_advanced_numerics.jl

# Convergence studies
julia --project=. examples/convergence_study_demo.jl
```

## 📚 Further Reading

- [Numerical Analysis Theory](numerical_theory.md)
- [WENO Implementation Details](weno_implementation.md)
- [Spectral Methods Guide](spectral_methods.md)
- [Shock Capturing Techniques](shock_capturing.md)