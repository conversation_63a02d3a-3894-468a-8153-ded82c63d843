# CFD.jl Solver Development & Usage Framework Guide

## 🎯 Overview

CFD.jl now provides a **dual-mode architecture** that caters to both:
1. **Users**: Simple one-liner usage similar to OpenFOAM
2. **Developers**: Full mathematical control for creating custom solvers

## 📦 Quick Start for Users

### Simple Usage

```julia
using CFD

# List available solvers
CFD.list_solvers()

# Get solver recommendations
CFD.suggest_solver("heat transfer with natural convection")

# Run a solver on your case
CFD.solve("myCase", solver=:heatTransferFoam, time=10.0)
CFD.solve("myCase", solver=:simpleFoam, parallel=8)
CFD.solve("myCase", solver=:pimpleFoam, writeInterval=0.1)
```

That's it! No need to understand the internals - just use the solvers like OpenFOAM.

## 🔧 Developer Mode: Creating Custom Solvers

### 1. Mathematical DSL for Solver Definition

```julia
using CFD
using CFD.SolverDSL
using CFD.MathematicalPhysics

@solver MyHeatSolver begin
    
    # Define required fields
    @fields begin
        𝐮 = VectorField("U", required=false)
        T = ScalarField("T", required=true)
        k = ScalarField("thermalConductivity", properties=true)
    end
    
    # Define governing equations with Unicode
    @equations begin
        # Energy equation
        energy: ∂T/∂t + ∇⋅(𝐮T) = ∇⋅(α∇T) + Q
        
        # Optional momentum with buoyancy
        momentum: ∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮 + β𝐠(T-Tᵣₑf)
        
        continuity: ∇⋅𝐮 = 0
    end
    
    # Configure algorithm
    @algorithm begin
        type = PIMPLE
        nOuterCorrectors = 3
        relaxation = Dict(:T => 0.9, :U => 0.7)
    end
    
    # Add custom functions
    @customize begin
        function initialize!(fields, mesh)
            # Custom initialization
            T = fields[:T]
            for (i, cell) in enumerate(mesh.cells)
                x, y, z = cell.center
                T[i] = 300.0 + 100.0 * exp(-((x-0.5)^2 + (y-0.5)^2)/0.1)
            end
        end
        
        function compute_source_term(T, x, y, z)
            # Heat generation
            return 1000.0 * exp(-((x-0.5)^2 + (y-0.5)^2)/0.01)
        end
    end
end

# Your solver is now registered and ready to use!
CFD.solve("heatedCavity", solver=:MyHeatSolver)
```

### 2. Extending Existing Solvers

```julia
# Extend an existing solver with new physics
@extend_solver TurbulentHeatSolver from :heatTransferFoam add begin
    # Add turbulence
    @import_physics TurbulenceModels.kEpsilon
    
    # Modify energy equation for turbulent diffusion
    @modify_equation energy begin
        ∂T/∂t + ∇⋅(𝐮T) = ∇⋅((α + αₜ)∇T) + Q
        # where αₜ = νₜ/Prₜ
    end
    
    @add_property Prₜ = 0.85  # Turbulent Prandtl number
end
```

### 3. Quick Solver Creation from Templates

```julia
# Use templates for common solver types
@create_solver TransonicFoam based_on=:rhoPimpleFoam modify=begin
    @add_equation shock_capturing
    @modify_algorithm time_stepping=:local_adaptive
end

# Or use the interactive wizard
CFD.create_solver_interactive()
```

## 📐 Mathematical Physics System

### Defining Physics with Full Unicode Support

```julia
using CFD.MathematicalPhysics

@physics IncompressibleTurbulent begin
    # Momentum equation
    @equation momentum begin
        ∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ∇⋅((ν + νₜ)∇𝐮) + 𝐟
    end
    
    # Continuity
    @equation continuity begin
        ∇⋅𝐮 = 0
    end
    
    # Turbulence model (k-ε)
    @equation tke begin
        ∂k/∂t + ∇⋅(𝐮k) = ∇⋅((ν + νₜ/σₖ)∇k) + 𝒫ₖ - ε
    end
    
    @equation dissipation begin
        ∂ε/∂t + ∇⋅(𝐮ε) = ∇⋅((ν + νₜ/σₑ)∇ε) + C₁ₑ(ε/k)𝒫ₖ - C₂ₑε²/k
    end
    
    # Model constants
    @constants begin
        Cμ = 0.09
        C₁ₑ = 1.44
        C₂ₑ = 1.92
        σₖ = 1.0
        σₑ = 1.3
    end
end
```

## 🔬 FVM Workflow System

### Define Mathematical Workflows

```julia
using CFD.FVMWorkflow

@fvm_workflow TurbulentChannelFlow begin
    
    @stage MeshAnalysis begin
        # Automatic mesh topology analysis
        analyze_mesh_topology(mesh)
        # Identifies structured regions, clustering, quality
    end
    
    @stage Discretization begin
        # Show mathematical discretization
        analyze_discretization(
            "∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ∇⋅((ν+νₜ)∇𝐮)",
            "linearUpwind"
        )
    end
    
    @stage MatrixAssembly begin
        # Visualize matrix structure
        show_matrix_structure(A)
        # Shows sparsity, bandwidth, condition number
    end
    
    @stage ConvergenceMonitoring begin
        # Real-time convergence visualization
        monitor = ConvergenceMonitor(tol=1e-6)
        display_convergence(monitor)
    end
    
    @stage PerformanceAnalysis begin
        # Profile and optimize
        profile_performance(operations)
        # Suggests optimizations
    end
end

# Execute workflow interactively or in batch
execute_workflow(workflow, interactive=true)
```

## 🧪 Testing Your Solver

### Automated Testing Framework

```julia
@test_solver MyHeatSolver begin
    @test_case "heated_cavity" begin
        mesh = UnitCube(20, 20, 1)
        BC = Dict(
            :T => Dict(:bottom => 400, :top => 300),
            :U => Dict(:all => :noSlip)
        )
        time = 10.0
    end
    
    @verify begin
        energy_conservation < 1e-10
        max_temperature < 450
        heat_flux_balance < 1e-8
    end
    
    @benchmark_against analytical_solution
end
```

## 📊 Performance Monitoring

The framework automatically profiles your solver:

```julia
CFD.profile_solver(:MyHeatSolver, "testCase")
```

Output:
```
🎯 Solver Profiling: MyHeatSolver
════════════════════════════════

📊 Performance Metrics:
┌─────────────────────────────────────┐
│ Stage              Time    % Total  │
├─────────────────────────────────────┤
│ Matrix Assembly    0.23s   12.1%    │
│ Linear Solve       1.45s   76.3%    │
│ BC Updates         0.08s    4.2%    │
│ Custom Functions   0.14s    7.4%    │
└─────────────────────────────────────┘

🚀 Optimization Suggestions:
- Linear solver is bottleneck → Try AMG
- Custom function allocating → Pre-allocate
```

## 📦 Sharing Your Solver

### Package and Share

```julia
# Package your solver
CFD.package_solver(:MyHeatSolver)

# Publish to community repository
CFD.publish_solver()

# Others can now install it
CFD.install_solver("MyHeatSolver")
```

## 🎮 Interactive Development Mode

Start the interactive terminal:

```julia
CFD.terminal()
```

```
CFD » develop solver

🛠️ Solver Development Mode
════════════════════════

CFD/dev » new solver from scratch
Name: ShockCapturingSolver

CFD/dev » add equation momentum
Enter equation: ∂(ρ𝐮)/∂t + ∇⋅(ρ𝐮⊗𝐮) = -∇p + ∇⋅𝛕

CFD/dev » test discretization
Testing: ∂(ρ𝐮)/∂t + ∇⋅(ρ𝐮⊗𝐮) = -∇p + ∇⋅𝛕
- Time: Backward Euler ✓
- Convection: Upwind ⚠️
  Suggestion: Use MUSCL for shocks

CFD/dev » save solver
✅ Saved to: solvers/ShockCapturingSolver/
```

## 📚 OpenFOAM-Style Case Structure

Cases follow familiar OpenFOAM structure with Julia files:

```
myCase/
├── 0/
│   ├── U.jl         # 𝐮 field with math BCs
│   ├── p.jl         # Pressure
│   └── T.jl         # Temperature
├── constant/
│   ├── physics.jl   # Mathematical physics
│   └── polyMesh/
├── system/
│   ├── controlDict.jl
│   ├── fvSchemes.jl
│   └── fvmWorkflow.jl
└── run.jl          # Master run script
```

Example field file (0/U.jl):
```julia
𝐮 = VectorField(
    name = "U",
    internalField = (x, y, z) -> begin
        # Parabolic inlet profile
        u_max = 1.5
        return [u_max * 4 * y * (1 - y), 0, 0]
    end,
    
    boundaryField = Dict(
        :inlet => (x, y, z, t) -> [parabolic_profile(y), 0, 0],
        :wall => noSlip,
        :outlet => ∂𝐮/∂n = 0
    )
)
```

## 🚀 Advanced Features

### 1. Mathematical Experimentation

```julia
@experimental_solver TestIdea begin
    @algorithm CustomProjection begin
        # Try new mathematical ideas
        𝐮_star = solve_momentum(𝐮ⁿ, pⁿ)
        Π = projection_operator(∇²)
        𝐮ⁿ⁺¹ = (I - Π)𝐮_star
        pⁿ⁺¹ = pⁿ + Π·𝐮_star
        
        @monitor orthogonality = |∇×(∇p)|
    end
end
```

### 2. Multi-Solver Composition

```julia
@composite_solver MultiphysicsSolver combine=[
    :incompressibleFlow,
    :heatTransfer,
    :speciesTransport
] coupling=:implicit
```

### 3. Learning Mode

```julia
CFD.learn(:SIMPLE_algorithm)
# Shows step-by-step mathematical explanation
```

## 🎯 Summary

The new framework provides:

1. **For Users**: Simple `solve()` interface - no complexity
2. **For Developers**: Full mathematical control with DSL
3. **For Researchers**: Rapid prototyping and experimentation
4. **For Everyone**: Unicode math notation throughout

### Getting Started

**As a User:**
```julia
CFD.solve("myCase", solver=:pimpleFoam)
```

**As a Developer:**
```julia
@solver MySolver begin
    @equation momentum ∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮
end
```

The framework handles all the complexity behind the scenes!