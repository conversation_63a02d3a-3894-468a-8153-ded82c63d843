# Simple CFD.jl Mesh Resolution Example
# Shows the basic workflow for setting domain resolution

using CFD

println("=== Simple Mesh Resolution Example ===\n")

# 1. Quick domain setup
domain = (
    dimensions = (2.0, 1.0, 0.1),    # 2m x 1m x 0.1m
    resolution = (100, 50, 1),       # 100x50x1 cells
    patches = ["inlet", "outlet", "wall", "symmetry"]
)

println("Domain: $(domain.dimensions) m")
println("Resolution: $(domain.resolution) cells")
println("Total cells: $(prod(domain.resolution))")

# 2. Set flow conditions
velocity_inlet = 5.0    # m/s
pressure_ref = 0.0      # Pa
viscosity = 1e-5        # m²/s

# Calculate time step
cell_size = minimum(domain.dimensions ./ domain.resolution)
dt = 0.1 * cell_size / velocity_inlet  # CFL = 0.1

println("Inlet velocity: $(velocity_inlet) m/s")
println("Cell size: $(cell_size) m")
println("Time step: $(dt) s")

# 3. Define physics and solver
@physics FlowPhysics begin
    @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮)
    @equation continuity (∇⋅𝐮 = 0)
end

@bc inlet = 𝐮 → (5.0, 0, 0)     # 5 m/s inlet
@bc outlet = p → 0.0           # 0 Pa reference
@bc wall = 𝐮 → (0, 0, 0)       # No-slip wall
@bc symmetry = 𝐮 → :symmetry   # Symmetry condition

@solver FlowSolver begin
    @physics FlowPhysics
    @algorithm PISO(correctors=2)
end

# 4. Run with mesh information
mesh_info = Dict(:ncells => prod(domain.resolution), 
                :nfaces => 2*sum(collect(domain.resolution)), 
                :domain => domain)

result = solve("flow.foam", FlowSolver, time=1.0, dt=dt, 
              mesh=mesh_info, output_interval=10)

println("\nSimulation completed!")
println("This shows how to set:")
println("- Domain dimensions and resolution")
println("- Flow conditions (velocity, pressure)")
println("- CFL-stable time step")
println("- Mesh-aware boundary conditions")