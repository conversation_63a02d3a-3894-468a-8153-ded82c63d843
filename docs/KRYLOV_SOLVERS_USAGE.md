# Quick Start Guide: <PERSON><PERSON><PERSON> in CFD.jl

## 🚀 Basic Usage

### Simple Example
```julia
using CFD.Solvers.LinearSolvers
using LinearAlgebra, SparseArrays

# Create a test system (tridiagonal)
n = 100
A = spdiagm(-1 => -ones(n-1), 0 => 4*ones(n), 1 => -ones(n-1))
b = ones(n)

# Choose the best solver for your problem
solver = CGS(tol=1e-8, maxiter=1000)  # Fast for symmetric
result = solve!(solver, A, b)

println("Converged in $(result.iterations) iterations")
println("Final residual: $(result.residual)")
```

## 🎯 Solver Selection

### For CFD Applications

```julia
# Pressure-Poisson equation (symmetric, well-conditioned)
pressure_solver = CGS(tol=1e-10, maxiter=1000, 
                     preconditioner=JacobiPreconditioner(A))

# Momentum equations (non-symmetric, convection-dominated)
momentum_solver = BiCG(tol=1e-8, maxiter=1500,
                      preconditioner=ILUPreconditioner(A))

# Turbulence equations (ill-conditioned, source terms)
turbulence_solver = TFQMR(tol=1e-6, maxiter=2000, verbose=true)
```

### Quick Decision Guide

| Problem Type | Recommended Solver | Why? |
|-------------|-------------------|------|
| Pressure equations | `CGS` | Symmetric, fast convergence |
| Momentum equations | `BiCG` | Handles convection well |
| Turbulence k-ε | `TFQMR` | Most robust for difficult problems |
| General purpose | `BiCG` | Good balance of speed and robustness |

## 🛠️ Preconditioning

### Automatic Preconditioner Selection
```julia
function auto_preconditioner(A)
    # Estimate condition number
    if size(A, 1) < 10000
        return ILUPreconditioner(A)  # More expensive but better
    else
        return JacobiPreconditioner(A)  # Faster for large systems
    end
end

# Usage
precond = auto_preconditioner(A)
solver = BiCG(tol=1e-8, preconditioner=precond)
```

## 📊 Performance Monitoring

### Track Convergence
```julia
solver = TFQMR(tol=1e-8, maxiter=1000, verbose=true)
result = solve!(solver, A, b)

# Plot convergence history
using Plots
plot(result.residual_history, yscale=:log10, 
     title="Solver Convergence", xlabel="Iteration", ylabel="Residual")
```

### Performance Comparison
```julia
solvers = [
    ("CGS", CGS(tol=1e-8, maxiter=1000)),
    ("BiCG", BiCG(tol=1e-8, maxiter=1000)),
    ("TFQMR", TFQMR(tol=1e-8, maxiter=1000))
]

for (name, solver) in solvers
    time_start = time()
    result = solve!(solver, A, b)
    elapsed = time() - time_start
    
    println("$name: $(result.iterations) iterations, $(elapsed:.3f)s")
end
```

## 🔧 Integration with CFD

### In PISO Algorithm
```julia
function solve_piso_step!(U, p, mesh, dt)
    # 1. Momentum predictor (non-symmetric)
    A_mom, b_mom = assemble_momentum(mesh, U, dt)
    mom_solver = BiCG(tol=1e-6, preconditioner=ILUPreconditioner(A_mom))
    U_star = solve!(mom_solver, A_mom, b_mom).x
    
    # 2. Pressure correction (symmetric)
    A_p, b_p = assemble_pressure_poisson(mesh, U_star, dt)
    p_solver = CGS(tol=1e-10, preconditioner=JacobiPreconditioner(A_p))
    p_corr = solve!(p_solver, A_p, b_p).x
    
    # 3. Velocity correction
    correct_velocity!(U, U_star, p_corr, mesh, dt)
    
    return U, p
end
```

### Error Handling
```julia
function robust_solve(A, b; max_attempts=3)
    solvers = [
        CGS(tol=1e-8, maxiter=1000),
        BiCG(tol=1e-8, maxiter=1500), 
        TFQMR(tol=1e-6, maxiter=2000)
    ]
    
    for (attempt, solver) in enumerate(solvers)
        try
            result = solve!(solver, A, b)
            if result.converged
                @info "Converged with $(typeof(solver).name.name) in $(result.iterations) iterations"
                return result.x
            end
        catch e
            @warn "Solver $(typeof(solver).name.name) failed: $e"
        end
        
        if attempt == max_attempts
            error("All solvers failed to converge")
        end
    end
end
```

## 🎯 Common Patterns

### Multiple Right-Hand Sides
```julia
# For solving multiple systems with same matrix
B = [b1 b2 b3]  # Multiple RHS vectors
solutions = []

# Reuse expensive preconditioner setup
precond = ILUPreconditioner(A)
solver = BiCG(tol=1e-8, preconditioner=precond)

for i in 1:size(B, 2)
    result = solve!(solver, A, B[:, i])
    push!(solutions, result.x)
end
```

### Adaptive Tolerance
```julia
function solve_with_adaptive_tolerance(solver_type, A, b; initial_tol=1e-6)
    tolerance = initial_tol
    max_attempts = 5
    
    for attempt in 1:max_attempts
        solver = solver_type(tol=tolerance, maxiter=2000)
        result = solve!(solver, A, b)
        
        # Check actual residual
        true_residual = norm(A * result.x - b) / norm(b)
        
        if true_residual < initial_tol
            return result.x
        end
        
        # Tighten tolerance
        tolerance /= 10
        @info "Attempt $attempt: tightening tolerance to $tolerance"
    end
    
    error("Failed to achieve target accuracy")
end
```

## ⚡ Performance Tips

### Memory Management
```julia
# Pre-allocate for repeated solves
solver = BiCG(tol=1e-8, maxiter=1000)
x = zeros(size(A, 1))  # Pre-allocate solution vector

# In time loop
for timestep in 1:nsteps
    update_matrix!(A, current_state)  # Update in-place
    update_rhs!(b, current_state)     # Update in-place
    
    result = solve!(solver, A, b, x)  # Reuse solution vector
    x .= result.x  # Copy result
end
```

### Choosing Iteration Limits
```julia
function optimal_maxiter(problem_size, solver_type)
    base_iters = Dict(
        CGS => problem_size ÷ 100,
        BiCG => problem_size ÷ 50,
        TFQMR => problem_size ÷ 25
    )
    
    return max(100, min(5000, base_iters[solver_type]))
end
```

---

*This guide covers the essential usage patterns. For detailed API documentation, see [`docs/LINEAR_SOLVERS.md`](LINEAR_SOLVERS.md).*