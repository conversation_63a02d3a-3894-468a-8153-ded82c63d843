"""
Phase 9: Moving Mesh and AMI Validation

This phase validates moving mesh capabilities, AMI (Arbitrary Mesh Interface), and rotor dynamics.
"""

using CFD
using CFD.CFDCore
using CFD.MovingMesh
using CFD.AMI
using CFD.Physics
using CFD.RotorDynamics
using CFD.Utilities
using LinearAlgebra
using StaticArrays
using Test

# Test rigid body mesh motion
function test_rigid_body_motion()
    println("\n" * "="^60)
    println("TEST 9.1: Rigid Body Mesh Motion")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Create a simple mesh
    mesh = Utilities.create_unit_cube_mesh(5, 5, 5)
    
    # Test translation
    println("\n9.1.1 Mesh Translation")
    try
        # Store original positions
        original_centers = [copy(cell.center) for cell in mesh.cells]
        
        # Apply translation
        translation = SVector(1.0, 0.5, -0.3)
        MovingMesh.translate_mesh!(mesh, translation)
        
        # Check translation
        translation_correct = true
        for (i, cell) in enumerate(mesh.cells)
            expected = original_centers[i] + translation
            if !isapprox(cell.center, expected, atol=1e-10)
                translation_correct = false
                break
            end
        end
        
        test_results["mesh_translation"] = translation_correct
        println("  Translation vector: $translation")
        println("  Translation correct: $translation_correct")
        
    catch e
        println("  Failed: $e")
        test_results["mesh_translation"] = false
    end
    
    # Test rotation
    println("\n9.1.2 Mesh Rotation")
    try
        # Reset mesh
        mesh = Utilities.create_unit_cube_mesh(5, 5, 5)
        
        # Apply rotation around z-axis
        angle = π/4  # 45 degrees
        axis = SVector(0.0, 0.0, 1.0)
        origin = SVector(0.5, 0.5, 0.5)  # Center of unit cube
        
        MovingMesh.rotate_mesh!(mesh, angle, axis, origin)
        
        # Check that distances from rotation axis are preserved
        rotation_correct = true
        for cell in mesh.cells
            r = cell.center - origin
            r_perp = r - dot(r, axis) * axis
            dist = norm(r_perp)
            
            # Distance from axis should be preserved
            original_dist = norm(SVector(cell.center[1] - 0.5, cell.center[2] - 0.5, 0.0))
            if !isapprox(dist, original_dist, atol=1e-10)
                rotation_correct = false
                break
            end
        end
        
        test_results["mesh_rotation"] = rotation_correct
        println("  Rotation angle: $(angle) rad")
        println("  Rotation axis: $axis")
        println("  Rotation correct: $rotation_correct")
        
    catch e
        println("  Failed: $e")
        test_results["mesh_rotation"] = false
    end
    
    return test_results
end

# Test AMI interface
function test_ami_interface()
    println("\n" * "="^60)
    println("TEST 9.2: Arbitrary Mesh Interface (AMI)")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Create two meshes with a sliding interface
    println("\n9.2.1 AMI Weight Calculation")
    try
        # Create simple test patches
        source_patch = [
            (center=SVector(0.0, 0.0, 0.0), area=1.0),
            (center=SVector(1.0, 0.0, 0.0), area=1.0),
        ]
        
        target_patch = [
            (center=SVector(0.5, 0.0, 0.0), area=1.0),
            (center=SVector(1.5, 0.0, 0.0), area=1.0),
        ]
        
        # Calculate AMI weights
        weights = AMI.calculate_ami_weights(source_patch, target_patch)
        
        # Check weight conservation
        weight_sum_correct = true
        for i in 1:length(source_patch)
            weight_sum = sum(weights[i, :])
            if !isapprox(weight_sum, 1.0, atol=1e-10)
                weight_sum_correct = false
                break
            end
        end
        
        test_results["ami_weight_conservation"] = weight_sum_correct
        println("  AMI weights calculated")
        println("  Weight conservation: $weight_sum_correct")
        
    catch e
        println("  Failed: $e")
        test_results["ami_weight_conservation"] = false
    end
    
    # Test interpolation across AMI
    println("\n9.2.2 AMI Interpolation")
    try
        # Create test field values
        source_values = [1.0, 2.0]
        
        # Interpolate to target
        target_values = AMI.ami_interpolate(source_values, weights)
        
        # Check interpolation (should be average due to overlap)
        expected_values = [1.5, 1.5]  # Due to 50% overlap
        
        interpolation_correct = isapprox(target_values, expected_values, atol=0.1)
        test_results["ami_interpolation"] = interpolation_correct
        
        println("  Source values: $source_values")
        println("  Target values: $target_values")
        println("  Interpolation correct: $interpolation_correct")
        
    catch e
        println("  Failed: $e")
        test_results["ami_interpolation"] = false
    end
    
    return test_results
end

# Test rotor dynamics
function test_rotor_dynamics()
    println("\n" * "="^60)
    println("TEST 9.3: Rotor Dynamics")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Test rotor force calculation
    println("\n9.3.1 Blade Element Momentum Theory")
    try
        # Rotor parameters
        radius = 1.0
        omega = 100.0  # rad/s
        num_blades = 4
        chord = 0.1
        
        # Create rotor
        rotor = RotorDynamics.RotorConfiguration(
            position = SVector(0.0, 0.0, 0.0),
            axis = SVector(0.0, 0.0, 1.0),
            radius = radius,
            omega = omega,
            nBlades = num_blades,
            collective = 0.1,
            cellZone = "rotor"
        )
        
        # Calculate forces at a blade element
        r = 0.7 * radius  # 70% span
        V_inf = SVector(0.0, 0.0, -5.0)  # Downward flow
        
        # Blade velocity
        V_blade = omega * r
        
        # Relative velocity
        V_rel = sqrt(V_inf[3]^2 + V_blade^2)
        
        # Check velocity calculation
        test_results["rotor_velocity_calc"] = V_rel > 0
        
        println("  Rotor radius: $radius m")
        println("  Rotation speed: $omega rad/s")
        println("  Blade velocity at 70% span: $V_blade m/s")
        println("  Relative velocity: $V_rel m/s")
        
    catch e
        println("  Failed: $e")
        test_results["rotor_velocity_calc"] = false
    end
    
    # Test thrust calculation
    println("\n9.3.2 Rotor Thrust Calculation")
    try
        # Simple momentum theory thrust
        rho = 1.225  # Air density
        A = π * radius^2  # Rotor disk area
        V_induced = 2.5  # Induced velocity
        
        # Momentum theory thrust
        T = 2 * rho * A * V_induced^2
        
        # Check thrust is positive
        test_results["rotor_thrust_positive"] = T > 0
        
        # Check thrust coefficient
        C_T = T / (rho * A * (omega * radius)^2)
        test_results["rotor_CT_reasonable"] = 0.001 < C_T < 0.1
        
        println("  Thrust: $T N")
        println("  Thrust coefficient: $C_T")
        
    catch e
        println("  Failed: $e")
        test_results["rotor_thrust_positive"] = false
        test_results["rotor_CT_reasonable"] = false
    end
    
    return test_results
end

# Test mesh deformation
function test_mesh_deformation()
    println("\n" * "="^60)
    println("TEST 9.4: Mesh Deformation")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Test Laplacian smoothing
    println("\n9.4.1 Laplacian Mesh Smoothing")
    try
        # Create mesh
        mesh = Utilities.create_unit_cube_mesh(4, 4, 4)
        
        # Apply boundary displacement
        boundary_displacement = Dict{Int, SVector{3, Float64}}()
        
        # Move top boundary
        for (i, node) in enumerate(mesh.nodes)
            if node.coords[3] ≈ 1.0  # Top boundary
                boundary_displacement[i] = SVector(0.0, 0.0, 0.1)
            end
        end
        
        # Apply Laplacian smoothing
        MovingMesh.laplacian_smooth!(mesh, boundary_displacement, iterations=10)
        
        # Check that internal nodes moved smoothly
        smoothing_correct = true
        for node in mesh.nodes
            if 0.1 < node.coords[3] < 0.9  # Internal nodes
                # Check that z-displacement is between 0 and 0.1
                original_z = node.coords[3]
                if node.coords[3] < original_z || node.coords[3] > original_z + 0.1
                    smoothing_correct = false
                    break
                end
            end
        end
        
        test_results["mesh_smoothing"] = smoothing_correct
        println("  Laplacian smoothing applied")
        println("  Smooth deformation: $smoothing_correct")
        
    catch e
        println("  Failed: $e")
        test_results["mesh_smoothing"] = false
    end
    
    return test_results
end