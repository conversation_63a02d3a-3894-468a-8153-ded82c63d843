# CFD.jl Validation Suite

This directory contains comprehensive validation tests for the CFD.jl framework, organized into 10 phases that test different aspects of computational fluid dynamics capabilities, from basic operations to advanced features.

## Validation Phases

The validation suite is organized into 10 phases:

### Core CFD Validation (Phases 1-6)
1. **Phase 1: Basic Mesh and Field Operations**
   - Mesh construction and quality checks
   - Field initialization and boundary conditions

2. **Phase 2: Gradient and Divergence Operators**
   - Finite volume gradient calculations
   - Divergence operator validation

3. **Phase 3: Laplacian and Diffusion**
   - Laplacian operator accuracy
   - Steady-state diffusion problems

4. **Phase 4: Time-Dependent Problems**
   - Unsteady diffusion equations
   - Time integration schemes

5. **Phase 5: Convection-Diffusion**
   - 1D convection-diffusion equation
   - Peclet number effects

6. **Phase 6: Navier-Stokes**
   - Taylor-Green vortex
   - Incompressible flow validation

### Advanced Features Validation (Phases 7-10)
7. **Phase 7: Parallel Solvers**
   - Parallel linear solver validation
   - GPU solver testing (if CUDA available)
   - AMG (Algebraic Multigrid) solver validation

8. **Phase 8: Turbulence Models**
   - k-epsilon model validation
   - k-omega SST model (placeholder)
   - Wall functions testing
   - LES (<PERSON><PERSON><PERSON><PERSON><PERSON>) model validation

9. **Phase 9: Moving Mesh and AMI**
   - Rigid body mesh motion
   - AMI (Arbitrary Mesh Interface) validation
   - Rotor dynamics and blade element momentum theory
   - Mesh deformation and smoothing

10. **Phase 10: Advanced Numerics**
    - High-order schemes (QUICK, MUSCL)
    - Flux limiters (MinMod, Van Leer, Superbee, MC)
    - WENO5 reconstruction
    - Spectral methods and dealiasing

## Running Validation Tests

### Using the validation runner script (recommended)
```bash
# Run all phases
./validation/run_validation.sh

# Run specific phase
./validation/run_validation.sh --phase 7

# Run with options
./validation/run_validation.sh --phase 7 --parallel --gpu --verbose

# Show help
./validation/run_validation.sh --help
```

### Using Julia directly
```bash
# Run all phases
julia --project=. validation/runner.jl

# Run specific phase
julia --project=. -e "
    include(\"validation/ValidationWorkflow.jl\")
    using .ValidationWorkflow
    results = run_phase(8)
"
```

### Interactive usage
```julia
using CFD
include("validation/ValidationWorkflow.jl")
using .ValidationWorkflow

# Run all tests
results = run_all_tests()

# Run specific phase
phase8_results = run_phase(8)

# Run individual test groups
parallel_results = test_parallel_solvers()
turbulence_results = test_k_epsilon_model()
```

## Files

- `ValidationWorkflow.jl` - Main validation framework module
- `runner.jl` - Standalone validation runner
- `run_validation.sh` - Convenient shell script wrapper
- `phase2_operators.jl` - Gradient and divergence operator tests
- `phase3_laplacian.jl` - Laplacian and diffusion tests
- `phase4_time_dependent.jl` - Time-dependent problem tests
- `phase5_convection_diffusion.jl` - Convection-diffusion tests
- `phase6_navier_stokes.jl` - Navier-Stokes validation
- `phase7_parallel_solvers.jl` - Parallel and GPU solver tests
- `phase8_turbulence_models.jl` - Turbulence model validation
- `phase9_moving_mesh.jl` - Moving mesh and AMI tests
- `phase10_advanced_numerics.jl` - Advanced numerical methods

## Test Structure

Each phase includes multiple test cases that validate specific functionality:

- **Analytical Solutions**: Comparison against known analytical solutions
- **Method of Manufactured Solutions**: Verification using constructed solutions
- **Benchmark Problems**: Validation against established CFD benchmarks
- **Convergence Studies**: Order of accuracy verification
- **Performance Tests**: Parallel and GPU acceleration validation
- **Stability Tests**: Time integration and solver stability

## Expected Results

The validation tests check:
- Solution accuracy against analytical solutions
- Proper convergence rates for numerical schemes
- Conservation properties (mass, momentum, energy)
- Boundary condition implementation
- Stability of time integration schemes
- Parallel solver efficiency and accuracy
- Turbulence model behavior and physics
- Moving mesh capabilities
- High-order scheme accuracy and stability

All tests include tolerance checks and report pass/fail status along with error metrics.

## Environment Variables

Set these environment variables to control validation behavior:
- `CFD_VALIDATION_PARALLEL=true` - Enable parallel solver tests
- `CFD_VALIDATION_GPU=true` - Enable GPU tests (requires CUDA)
- `CFD_VALIDATION_VERBOSE=true` - Enable verbose output

## Requirements

- CFD.jl package
- Julia 1.6 or newer
- Core dependencies: LinearAlgebra, Test, Printf, Statistics, StaticArrays
- Optional: Plots.jl (for visualizations), CUDA.jl (for GPU tests), FFTW.jl (for spectral methods)