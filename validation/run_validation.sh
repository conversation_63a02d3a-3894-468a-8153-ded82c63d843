#!/bin/bash

# CFD.jl Validation Runner Script

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}CFD.jl Validation Suite${NC}"
echo "========================"

# Parse command line arguments
PHASE=""
PARALLEL=false
GPU=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--phase)
            PHASE="$2"
            shift 2
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        --gpu)
            GPU=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  -p, --phase N     Run specific phase (1-10)"
            echo "  --parallel        Enable parallel solver tests"
            echo "  --gpu             Enable GPU tests"
            echo "  -v, --verbose     Enable verbose output"
            echo "  -h, --help        Show this help"
            echo ""
            echo "Validation Phases:"
            echo "  1  - Basic Mesh and Field Operations"
            echo "  2  - Gradient and Divergence Operators"
            echo "  3  - Laplacian and Diffusion"
            echo "  4  - Time-Dependent Problems"
            echo "  5  - Convection-Diffusion"
            echo "  6  - Navier-Stokes"
            echo "  7  - Parallel Solvers"
            echo "  8  - Turbulence Models"
            echo "  9  - Moving Mesh and AMI"
            echo "  10 - Advanced Numerics"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use -h for help"
            exit 1
            ;;
    esac
done

# Set environment variables
export CFD_VALIDATION_PARALLEL=$PARALLEL
export CFD_VALIDATION_GPU=$GPU
export CFD_VALIDATION_VERBOSE=$VERBOSE

# Change to project directory
cd "$(dirname "$0")/.."

# Run validation based on phase
if [[ -n "$PHASE" ]]; then
    if [[ "$PHASE" =~ ^[1-9]$|^10$ ]]; then
        echo -e "${YELLOW}Running validation phase $PHASE...${NC}"
        julia --project=. -e "
            include(\"validation/ValidationWorkflow.jl\")
            using .ValidationWorkflow
            results = run_phase($PHASE)
            println(\"\\n\" * \"=\"^60)
            println(\"PHASE $PHASE RESULTS:\")
            println(\"=\"^60)
            for (group, tests) in results
                println(\"$group: \", tests)
            end
        "
    else
        echo -e "${RED}Invalid phase number: $PHASE (must be 1-10)${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}Running all validation phases...${NC}"
    julia --project=. -e "
        include(\"validation/ValidationWorkflow.jl\")
        using .ValidationWorkflow
        results = run_all_tests()
    "
fi

echo -e "${GREEN}Validation complete!${NC}"