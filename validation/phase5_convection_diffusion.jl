# ============================================================================
# Phase 5: Convection-Diffusion
# ============================================================================

"""
Test 5.1: 1D Convection-Diffusion
Test combined convection and diffusion with analytical solution
"""
# Import required modules
using CFD
using CFD.CFDCore
using CFD.Utilities
using CFD.Numerics
using CFD.Solvers
using LinearAlgebra
using StaticArrays
using Printf

function scientific_notation(x)
    if x == 0.0
        return "0.0"
    else
        exp = floor(Int, log10(abs(x)))
        coef = x / 10.0^exp
        return @sprintf("%.3fe%d", coef, exp)
    end
end

function test_convection_diffusion_1d()
    println("\n" * "="^60)
    println("TEST 5.1: 1D Convection-Diffusion")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Steady convection-diffusion: ∇⋅(Uφ) = α∇²φ 
    # BC: φ(0) = 0, φ(1) = 1
    # Analytical: φ(x) = (exp(Pe*x) - 1)/(exp(Pe) - 1), Pe = U/α
    
    n = 100
    mesh = CFD.Utilities.create_unit_cube_mesh(n, 2, 2)
    
    # Test different Peclet numbers
    test_cases = [
        (U=0.1, α=0.1, name="Pe=1"),
        (U=1.0, α=0.1, name="Pe=10"),
        (U=10.0, α=0.1, name="Pe=100")
    ]
    
    for case in test_cases
        println("\n$(case.name): U=$(case.U), α=$(case.α)")
        
        # Velocity field (constant in x-direction)
        U_data = [SVector(case.U, 0.0, 0.0) for _ in mesh.cells]
        U_bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
        U_field = CFDCore.Field{SVector{3,Float64}, 3, typeof(mesh)}(:U, mesh, U_data, U_bcs, nothing, nothing)
        
        # Scalar field with proper boundary conditions for mesh patches
        φ_data = zeros(length(mesh.cells))
        φ_bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
        
        # Set boundary conditions for actual mesh patches
        φ_bcs["inlet"] = CFDCore.DirichletBC((x,y,z,t) -> 0.0)
        φ_bcs["outlet"] = CFDCore.DirichletBC((x,y,z,t) -> 1.0)
        φ_bcs["walls"] = CFDCore.NeumannBC((x,y,z,t) -> 0.0)
        
        φ = CFDCore.Field{Float64, 3, typeof(mesh)}(:phi, mesh, φ_data, φ_bcs, nothing, nothing)
        
        # Assemble system: div(U*φ) - div(α*grad(φ)) = 0
        # First compute face flux from velocity field
        face_flux_data = zeros(length(mesh.faces))
        for (i, face) in enumerate(mesh.faces)
            # Simple face flux: U dot face_normal * face_area
            U_owner = U_field.data[face.owner]
            flux = (U_owner ⋅ face.normal) * face.area
            face_flux_data[i] = flux
        end
        face_flux_bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
        face_flux = CFDCore.Field{Float64, 3, typeof(mesh)}(:face_flux, mesh, face_flux_data, face_flux_bcs, nothing, nothing)
        
        # Using upwind for convection
        conv_matrix = Numerics.fvm.div(face_flux, φ, scheme=Numerics.fvm.UpwindScheme())
        diff_matrix = Numerics.fvm.laplacian(case.α, φ)
        
        # Extract matrices and vectors from FvMatrix objects
        A = conv_matrix.A - diff_matrix.A
        b = conv_matrix.b - diff_matrix.b
        
        # Solve
        solver = CFD.Solvers.LinearSolvers.BiCGSTAB(tol=1e-10)
        result = CFD.Solvers.LinearSolvers.solve(solver, A, b)
        
        test_results["conv_diff_$(case.name)_converged"] = result.converged
        
        # Compare with analytical
        φ.data .= result.x
        Pe = case.U / case.α
        
        errors = Float64[]
        for i in 1:length(mesh.cells)
            x = mesh.cells[i].center[1]
            if abs(Pe) < 1e-10
                analytical = x # Linear profile for Pe→0
            else
                analytical = (exp(Pe*x) - 1) / (exp(Pe) - 1)
            end
            push!(errors, abs(φ.data[i] - analytical))
        end
        
        max_error = maximum(errors)
        test_results["conv_diff_$(case.name)_accuracy"] = max_error < 0.1
        
        println(" Solver converged: $(result.converged)")
        println(" Max error: $(scientific_notation(max_error))")
    end
    
    return test_results
end
