"""
Phase 7: Parallel Solvers Validation

This phase validates the parallel and GPU solver implementations.
"""

using CFD
using CFD.<PERSON><PERSON>
using LinearAlgebra
using SparseArrays
using Test
try
    using CUDA
catch
    # CUDA not available
end

# Test parallel linear solvers
function test_parallel_solvers()
    println("\n" * "="^60)
    println("TEST 7.1: Parallel Linear Solvers")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Create test problem
    n = 100
    A = create_2d_poisson_matrix(n)
    x_true = rand(n^2)
    b = A * x_true
    
    println("\nTesting parallel solvers on $(n)×$(n) Poisson problem...")
    
    # Test Parallel PCG
    println("\n7.1.1 Parallel PCG Solver")
    try
        solver = Solvers.PCG(tol=1e-8, maxiter=1000)
        result = Solvers.solve(solver, A, b)
        
        error_norm = norm(result.x - x_true) / norm(x_true)
        test_results["parallel_pcg_convergence"] = result.converged
        test_results["parallel_pcg_accuracy"] = error_norm < 1e-6
        
        println("  Converged: $(result.converged)")
        println("  Iterations: $(result.iterations)")
        println("  Relative error: $(error_norm)")
    catch e
        println("  Failed: $e")
        test_results["parallel_pcg_convergence"] = false
        test_results["parallel_pcg_accuracy"] = false
    end
    
    # Test AMG solver
    println("\n7.1.2 Algebraic Multigrid (AMG)")
    try
        solver = Solvers.AMG(tol=1e-8, cycle_type=:V)
        result = Solvers.solve(solver, A, b)
        
        error_norm = norm(result.x - x_true) / norm(x_true)
        test_results["amg_convergence"] = result.converged
        test_results["amg_accuracy"] = error_norm < 1e-6
        
        println("  Converged: $(result.converged)")
        println("  Iterations: $(result.iterations)")
        println("  Relative error: $(error_norm)")
    catch e
        println("  Failed: $e")
        test_results["amg_convergence"] = false
        test_results["amg_accuracy"] = false
    end
    
    return test_results
end

# Test GPU solvers if available
function test_gpu_solvers()
    println("\n" * "="^60)
    println("TEST 7.2: GPU Solvers")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # Check if GPU is available
    gpu_available = false
    try
        gpu_available = isdefined(Main, :CUDA) && CUDA.functional()
    catch
        println("  CUDA.jl not available - skipping GPU tests")
        test_results["gpu_available"] = false
        return test_results
    end
    
    if !gpu_available
        println("  No GPU detected - skipping GPU tests")
        test_results["gpu_available"] = false
        return test_results
    end
    
    test_results["gpu_available"] = true
    
    # Create test problem
    n = 50
    A = create_2d_poisson_matrix(n)
    x_true = rand(n^2)
    b = A * x_true
    
    println("\nTesting GPU solvers on $(n)×$(n) Poisson problem...")
    
    # Test GPU PCG
    println("\n7.2.1 GPU PCG Solver")
    try
        solver = Solvers.GPUPCG(tol=1e-6, preconditioner=:jacobi)
        A_gpu = CUDA.CUSPARSE.CuSparseMatrixCSC(A)
        b_gpu = CuArray(b)
        
        result = Solvers.solve(solver, A_gpu, b_gpu)
        x_cpu = Array(result.x)
        
        error_norm = norm(x_cpu - x_true) / norm(x_true)
        test_results["gpu_pcg_convergence"] = result.converged
        test_results["gpu_pcg_accuracy"] = error_norm < 1e-4
        
        println("  Converged: $(result.converged)")
        println("  Iterations: $(result.iterations)")
        println("  Relative error: $(error_norm)")
    catch e
        println("  Failed: $e")
        test_results["gpu_pcg_convergence"] = false
        test_results["gpu_pcg_accuracy"] = false
    end
    
    return test_results
end

# Helper function to create 2D Poisson matrix
function create_2d_poisson_matrix(n::Int)
    N = n^2
    A = spzeros(N, N)
    
    for i in 1:n, j in 1:n
        idx = (i-1)*n + j
        
        # Diagonal
        A[idx, idx] = 4.0
        
        # Off-diagonals
        if i > 1
            A[idx, idx-n] = -1.0
        end
        if i < n
            A[idx, idx+n] = -1.0
        end
        if j > 1
            A[idx, idx-1] = -1.0
        end
        if j < n
            A[idx, idx+1] = -1.0
        end
    end
    
    return A
end