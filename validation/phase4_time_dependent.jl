# ============================================================================
# Phase 4: Time-Dependent Problems
# ============================================================================

# Import required modules
using CFD
using CFD.CFDCore
using CFD.Utilities
using CFD.Numerics
using CFD.Solvers
using LinearAlgebra
using Printf

# Helper functions for field creation
function create_scalar_field(name::Symbol, mesh, data)
    bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
    return CFDCore.Field{Float64, 3, typeof(mesh)}(name, mesh, data, bcs, nothing, nothing)
end

function scientific_notation(x)
    if x == 0.0
        return "0.0"
    else
        exp = floor(Int, log10(abs(x)))
        coef = x / 10.0^exp
        return @sprintf("%.3fe%d", coef, exp)
    end
end

"""
Test 4.1: Unsteady Diffusion
Test time integration schemes with analytical solution
"""
function test_unsteady_diffusion()
    println("\n" * "="^60)
    println("TEST 4.1: Unsteady Diffusion")
    println("="^60)
    
    test_results = Dict{String, Bool}()
    
    # 1D heat equation: ∂T/∂t = α∇²T
    # IC: T(x,0) = sin(πx)
    # BC: T(0,t) = T(1,t) = 0
    # Analytical: T(x,t) = sin(πx)exp(-απ²t)
    
    n = 40
    mesh = CFD.Utilities.create_unit_cube_mesh(n, 2, 2)
    α = 0.01
    
    # Initial condition
    T_data = [sin(π * c.center[1]) for c in mesh.cells]
    T_bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
    
    # Boundary conditions for all mesh patches
    T_bcs["inlet"] = CFDCore.DirichletBC((x,y,z,t) -> 0.0)
    T_bcs["outlet"] = CFDCore.DirichletBC((x,y,z,t) -> 0.0)
    T_bcs["walls"] = CFDCore.DirichletBC((x,y,z,t) -> 0.0)
    
    T = CFDCore.Field{Float64, 3, typeof(mesh)}(:T, mesh, T_data, T_bcs, nothing, nothing)
    T_old = copy(T.data)
    
    # Time stepping
    t_end = 0.1
    dt = 0.001
    n_steps = Int(t_end / dt)
    
    println("\nTime stepping: dt=$dt, steps=$n_steps")
    
    errors_time = Float64[]
    times = Float64[]
    
    t = 0.0
    for step in 1:n_steps
        # Store old solution
        T_old .= T.data
        
        # Assemble system: (T^{n+1} - T^n)/dt = α∇²T^{n+1} (backward Euler)
        # Rearrange: T^{n+1} - dt*α∇²T^{n+1} = T^n
        
        # Time derivative part
        A_time = (1.0/dt) * I(length(T.data))
        b_time = (1.0/dt) * T_old
        
        # Diffusion part
        diff_matrix = CFD.Numerics.fvm.laplacian(α, T)
        A_diff = diff_matrix.A
        b_diff = diff_matrix.b
        
        # Combined system
        A = A_time - A_diff
        b = b_time - b_diff
        
        # Solve
        solver = CFD.Solvers.LinearSolvers.PCG(tol=1e-10, verbose=false)
        result = CFD.Solvers.LinearSolvers.solve(solver, A, b)
        T.data .= result.x
        
        t += dt
        
        # Check against analytical solution
        if step % 10 == 0
            max_error = 0.0
            for i in 1:length(mesh.cells)
                x = mesh.cells[i].center[1]
                analytical = sin(π*x) * exp(-α*π^2*t)
                max_error = max(max_error, abs(T.data[i] - analytical))
            end
            push!(errors_time, max_error)
            push!(times, t)
            
            println(" t=$t: max error = $(scientific_notation(max_error))")
        end
    end
    
    # Check that error remains bounded
    test_results["unsteady_bounded"] = all(e -> e < 0.01, errors_time)
    test_results["unsteady_stable"] = !any(isnan, T.data) && !any(isinf, T.data)
    
    return test_results, times, errors_time
end
