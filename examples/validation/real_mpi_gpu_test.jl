# Real MPI + GPU Testing - No Mocking!
# Test with: mpirun -np 2 julia real_mpi_gpu_test.jl

# Add the CFD package to the path
push!(LOAD_PATH, "../../src")

using CFD

# Load UnicodeHPC or create minimal version
include("../basic/unicode_elegance_demo.jl")  # This will define UnicodeHPC if needed
using LinearAlgebra
using Printf

# ============================================================================
# REAL MPI TESTING
# ============================================================================

function test_real_mpi()
    println("🌐 Real MPI Testing")
    println("="^25)
    
    # Check if actually running with MPI
    if haskey(ENV, "OMPI_COMM_WORLD_SIZE") || haskey(ENV, "PMI_SIZE")
        try
            using MPI
            if !MPI.Initialized()
                MPI.Init()
            end
            
            rank = MPI.Comm_rank(MPI.COMM_WORLD)
            size = MPI.Comm_size(MPI.COMM_WORLD)
            
            println("Process $rank of $size")
            
            if size == 1
                println("⚠️  Running on single process - not a real MPI test")
                println("   Run with: mpirun -np 2 julia real_mpi_gpu_test.jl")
                return false
            end
            
            # Real MPI communication test
            n = 100
            local_data = randn(n) .+ rank  # Each rank has different data
            
            # Test MPI reduction
            t1 = time()
            global_sum = MPI.Allreduce(sum(local_data), MPI.SUM, MPI.COMM_WORLD)
            mpi_time = time() - t1
            
            # Test with our Unicode operators
            t1 = time()
            φ = randn(n*n)
            ∇²φ = ∇²(φ)  # This should use MPI path if large enough
            unicode_time = time() - t1
            
            if rank == 0
                @printf "✓ MPI working with %d processes\n" size
                @printf "  • MPI reduction: %.6fs\n" mpi_time
                @printf "  • Unicode ∇²: %.6fs\n" unicode_time
                @printf "  • Global sum: %.3f\n" global_sum
            end
            
            MPI.Barrier(MPI.COMM_WORLD)
            return true
            
        catch e
            println("❌ MPI test failed: $e")
            return false
        end
    else
        println("❌ Not running under MPI")
        println("   Run with: mpirun -np 2 julia real_mpi_gpu_test.jl")
        return false
    end
end

# ============================================================================
# REAL GPU TESTING
# ============================================================================

function test_real_gpu()
    println("\n🚀 Real GPU Testing")
    println("="^25)
    
    # Try to load CUDA and test
    try
        using CUDA
        
        if !CUDA.functional()
            println("❌ CUDA not functional")
            return false, "CUDA not functional"
        end
        
        device_name = CUDA.name(CUDA.device())
        memory_info = CUDA.MemoryInfo()
        free_mem = memory_info[1] ÷ 1024^2  # MB
        total_mem = memory_info[2] ÷ 1024^2  # MB
        
        println("✓ GPU detected: $device_name")
        println("  • Memory: $(free_mem) MB free / $(total_mem) MB total")
        
        # Real GPU computation test
        n = 1000
        cpu_data = randn(Float32, n)
        
        # CPU version
        t1 = time()
        cpu_result = cpu_data.^2 .+ sin.(cpu_data)
        cpu_time = time() - t1
        
        # GPU version
        t1 = time()
        gpu_data = cu(cpu_data)
        gpu_result = gpu_data.^2 .+ sin.(gpu_data)
        CUDA.synchronize()
        gpu_time = time() - t1
        
        # Transfer back and check
        gpu_result_cpu = Array(gpu_result)
        error = norm(cpu_result - gpu_result_cpu)
        
        @printf "  • CPU time: %.6fs\n" cpu_time
        @printf "  • GPU time: %.6fs\n" gpu_time
        @printf "  • Speedup: %.1fx\n" (cpu_time / gpu_time)
        @printf "  • Error: %.2e\n" error
        
        # Test with our Unicode operators
        println("\n  Testing Unicode operators on GPU:")
        φ = randn(Float32, n)
        
        t1 = time()
        ∇²φ_result = ∇²(φ)  # Should use GPU if available
        unicode_time = time() - t1
        
        @printf "  • Unicode ∇² time: %.6fs\n" unicode_time
        
        return true, device_name
        
    catch e
        println("❌ GPU test failed: $e")
        return false, string(e)
    end
end

# ============================================================================
# COMBINED MPI + GPU TEST
# ============================================================================

function test_mpi_gpu_combined()
    println("\n🔥 Combined MPI + GPU Test")
    println("="^30)
    
    # Check MPI first
    mpi_available = false
    rank = 0
    size = 1
    
    if haskey(ENV, "OMPI_COMM_WORLD_SIZE") || haskey(ENV, "PMI_SIZE")
        try
            using MPI
            if !MPI.Initialized()
                MPI.Init()
            end
            
            rank = MPI.Comm_rank(MPI.COMM_WORLD)
            size = MPI.Comm_size(MPI.COMM_WORLD)
            mpi_available = (size > 1)
        catch
            mpi_available = false
        end
    end
    
    # Check GPU
    gpu_available = false
    gpu_name = "None"
    try
        using CUDA
        if CUDA.functional()
            gpu_available = true
            gpu_name = CUDA.name(CUDA.device())
        end
    catch
        gpu_available = false
    end
    
    if rank == 0
        println("Configuration:")
        println("  • MPI: $(mpi_available ? "✓" : "❌") ($size processes)")
        println("  • GPU: $(gpu_available ? "✓" : "❌") ($gpu_name)")
    end
    
    if !mpi_available && !gpu_available
        println("  ⚠️  Neither MPI nor GPU available for testing")
        return
    end
    
    # Combined test: distributed computation
    n = 64  # Grid size
    local_size = div(n*n, size) + (rank < (n*n % size) ? 1 : 0)
    
    # Create local portion of the data
    local_φ = randn(Float32, local_size)
    
    println("Process $rank: working with $(local_size) elements")
    
    # Each process computes on its local data
    start_time = time()
    
    if gpu_available && local_size > 100
        # Use GPU for local computation
        using CUDA
        local_φ_gpu = cu(local_φ)
        local_result_gpu = local_φ_gpu.^2 .+ sin.(local_φ_gpu)
        CUDA.synchronize()
        local_result = Array(local_result_gpu)
        
        if rank == 0
            println("  Using GPU for local computation")
        end
    else
        # CPU computation
        local_result = local_φ.^2 .+ sin.(local_φ)
        
        if rank == 0
            println("  Using CPU for local computation")
        end
    end
    
    local_time = time() - start_time
    
    if mpi_available
        using MPI
        
        # Gather timing results
        all_times = MPI.Gather(local_time, 0, MPI.COMM_WORLD)
        
        # Gather partial results for validation
        local_sum = sum(local_result)
        global_sum = MPI.Allreduce(local_sum, MPI.SUM, MPI.COMM_WORLD)
        
        if rank == 0
            avg_time = sum(all_times) / length(all_times)
            max_time = maximum(all_times)
            min_time = minimum(all_times)
            
            @printf "Results:\n"
            @printf "  • Average time: %.6fs\n" avg_time
            @printf "  • Load balance: %.1f%% (max/min ratio)\n" (min_time/max_time*100)
            @printf "  • Global sum: %.6f\n" global_sum
            
            # Report configuration used
            config = []
            if mpi_available
                push!(config, "MPI($(size))")
            end
            if gpu_available
                push!(config, "GPU")
            end
            println("  • Configuration: $(join(config, " + "))")
        end
    else
        @printf "Single process result: %.6fs, sum = %.6f\n" local_time sum(local_result)
    end
end

# ============================================================================
# HARDWARE BENCHMARK WITH REAL DETECTION
# ============================================================================

function real_hardware_benchmark()
    println("\n⚡ Real Hardware Benchmark")
    println("Actual measurements from your system")
    println("="^40)
    
    # System info
    println("System information:")
    println("  • CPU: $(Sys.CPU_NAME)")
    println("  • Cores: $(Sys.CPU_THREADS)")
    println("  • Julia threads: $(Threads.nthreads())")
    println("  • RAM: $(round(Sys.total_memory()/1024^3, digits=1)) GB")
    
    # MPI info
    if haskey(ENV, "OMPI_COMM_WORLD_SIZE") || haskey(ENV, "PMI_SIZE")
        try
            using MPI
            if MPI.Initialized()
                rank = MPI.Comm_rank(MPI.COMM_WORLD)
                size = MPI.Comm_size(MPI.COMM_WORLD)
                println("  • MPI: Process $rank of $size")
            end
        catch
        end
    end
    
    # GPU info
    try
        using CUDA
        if CUDA.functional()
            println("  • GPU: $(CUDA.name(CUDA.device()))")
            mem_info = CUDA.MemoryInfo()
            println("  • GPU RAM: $(round(mem_info[2]/1024^3, digits=1)) GB")
        else
            println("  • GPU: CUDA not functional")
        end
    catch
        println("  • GPU: Not available")
    end
    
    # Run actual benchmark
    benchmark_hardware!(128)  # Real measurements
end

# ============================================================================
# MAIN TEST SUITE
# ============================================================================

function main()
    println("🧪 CFD.jl Real MPI + GPU Test Suite")
    println("No mocking - actual hardware testing only!")
    println("="^50)
    
    # Test MPI
    mpi_success = test_real_mpi()
    
    # Test GPU
    gpu_success, gpu_info = test_real_gpu()
    
    # Combined test
    test_mpi_gpu_combined()
    
    # Hardware benchmark
    real_hardware_benchmark()
    
    # Summary
    println("\n" * "="^50)
    println("🎯 TEST SUMMARY")
    println("="^50)
    
    println("Capabilities tested:")
    println("  • MPI: $(mpi_success ? "✅ Working" : "❌ Not available")")
    println("  • GPU: $(gpu_success ? "✅ Working ($gpu_info)" : "❌ Not available")")
    
    if mpi_success || gpu_success
        println("\n✅ HPC capabilities confirmed!")
        println("Your system supports real parallel/GPU acceleration.")
    else
        println("\n⚠️  No HPC acceleration available")
        println("Consider installing CUDA.jl or running with MPI")
    end
    
    # Usage instructions
    println("\n📝 To run with HPC:")
    if !mpi_success
        println("  MPI: mpirun -np 4 julia real_mpi_gpu_test.jl")
    end
    if !gpu_success
        println("  GPU: Install CUDA.jl and ensure CUDA drivers")
    end
    
    println("\nAll measurements are from actual hardware - no fake data!")
end

# Run tests
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end