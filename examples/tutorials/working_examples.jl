# Working CFD.jl Examples
# These examples demonstrate the working Unicode DSL functionality

using CFD

println("=== CFD.jl Working Examples ===")

# 1. Basic Physics Definition
println("\n1. Defining Physics with Mathematical Notation:")
@physics TurbulentFlow begin
    @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ∇⋅((ν+νₜ)∇𝐮))
    @equation continuity (∇⋅𝐮 = 0)
    @equation tke (∂k/∂t + ∇⋅(𝐮k) = ∇⋅((ν+νₜ/σₖ)∇k) + 𝒫ₖ - ε)
end

# 2. Boundary Conditions with Arrow Notation
println("\n2. Defining Boundary Conditions:")
@bc wall = 𝐮 → (0, 0, 0)     # No-slip wall
@bc inlet = 𝐮 → (15, 0, 0)    # Inlet velocity  
@bc outlet = p → 0           # Reference pressure
@bc farfield = k → 0.01      # Turbulence

# 3. Complete Solver Definition
println("\n3. Defining Complete Solver:")
@solver EnhancedDrone begin
    @physics TurbulentFlow
    @algorithm PIMPLE(outer=3, inner=2)
end

# 4. Run Simulation
println("\n4. Running Simulation:")
result = solve("drone.foam", EnhancedDrone, time=10.0)
println("Result: ", result)

# 5. Lid-Driven Cavity Example
println("\n5. Lid-Driven Cavity Example:")
@solver LidDrivenCavity begin
    @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮)
    @equation continuity (∇⋅𝐮 = 0)
    @algorithm PISO(correctors=2)
end

@bc wall = 𝐮 → (0, 0, 0)
@bc lid = 𝐮 → (1, 0, 0)

result = solve("cavity.foam", LidDrivenCavity, time=10.0, dt=0.001)
println("Cavity result: ", result)

# 6. Heat Transfer Example  
println("\n6. Heat Transfer Example:")
@physics HeatTransfer begin
    @equation energy (∂T/∂t + ∇⋅(𝐮T) = α∇²T + Q̇)
    @equation momentum (∂𝐮/∂t + ∇⋅(𝐮⊗𝐮) = -∇p + ν∇²𝐮)
    @equation continuity (∇⋅𝐮 = 0)
end

@bc hot_wall = T → 400.0
@bc cold_wall = T → 300.0
@bc adiabatic = T → :zeroGradient

@solver ThermalFlow begin
    @physics HeatTransfer
    @algorithm PIMPLE(outer=3)
end

result = solve("heat_exchanger.foam", ThermalFlow, time=60.0)
println("Heat transfer result: ", result)

# 7. Validated Solve Example (will show error handling)
println("\n7. Validated Solve Example:")
try
    result = validated_solve("validated_case.foam", EnhancedDrone, time=0.01, dt=1e-5)
    println("Validated result: ", result)
catch e
    println("Expected error (no mesh file): ", typeof(e))
    println("Error handling working properly!")
end

println("\n=== All Examples Completed Successfully! ===")