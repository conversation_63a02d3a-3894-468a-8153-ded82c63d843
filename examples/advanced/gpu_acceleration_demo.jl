#!/usr/bin/env julia

"""
GPU Acceleration Demo for CFD.jl

Demonstrates the automatic GPU acceleration features with fallback to CPU.
Shows performance comparison between CPU and GPU implementations.
"""

using Pkg
Pkg.activate(".")

using CFD
using CFD.MinimalCFD: read_mesh, auto_mesh, set_bc!, PISO, 𝐮, φ
using CFD.MinimalCFD: predictor_step!, corrector_step!
using CFD.Solvers: gpu_available, get_gpu_backend, create_gpu_fields, gpu_solve!
using CFD.Solvers: benchmark_gpu_performance
using StaticArrays
using LinearAlgebra
using Printf

println("🚀 CFD.jl GPU Acceleration Demo")
println("="^50)

function check_gpu_status()
    """Check and display GPU acceleration status."""
    println("\n📋 GPU Status Check")
    println("-"^30)
    
    if gpu_available()
        backend = get_gpu_backend()
        println("  ✅ GPU acceleration: AVAILABLE")
        println("  🖥️  Backend: $backend")
        
        if backend == :CUDA
            try
                # Try to access CUDA through Base.require
                CUDA = Base.require(Main, :CUDA)
                println("  🚀 CUDA device: $(CUDA.name())")
                println("  💾 GPU memory: $(round(CUDA.total_memory()/1024^3, digits=1)) GB")
            catch e
                println("  ⚠️  CUDA info unavailable: $e")
            end
        end
    else
        println("  ℹ️  GPU acceleration: NOT AVAILABLE")
        println("  🖥️  Using CPU backend")
    end
    
    return gpu_available()
end

function create_test_case(case_name::String, resolution::Tuple{Int,Int,Int})
    """Create a test case for GPU/CPU comparison."""
    println("\n🏗️ Creating test case: $case_name")
    println("  📐 Resolution: $(resolution)")
    
    # Create case
    mesh_path = auto_mesh(case_name, resolution)
    mesh = read_mesh(mesh_path)
    
    # Create fields
    U = 𝐮(:U, mesh)
    p = φ(:p, mesh)
    
    # Set boundary conditions
    set_bc!(U, :inlet, (2.0, 0.0, 0.0))    # 2 m/s inlet
    set_bc!(U, :outlet, (1.0, 0.0, 0.0))   # 1 m/s outlet  
    set_bc!(U, :wall, (0.0, 0.0, 0.0))     # No-slip walls
    set_bc!(p, :outlet, 0.0)               # Reference pressure
    
    ncells = prod(resolution)
    println("  ✅ Case created: $ncells cells")
    
    return mesh, U, p
end

function run_cpu_simulation(mesh, U, p, time_steps::Int=10)
    """Run CPU-only simulation for benchmarking."""
    println("\n🖥️ CPU Simulation")
    println("-"^20)
    
    solver = PISO(mesh)
    dt = 0.001
    
    start_time = time_ns()
    
    # Simple time loop
    for step in 1:time_steps
        # PISO predictor/corrector
        predictor_step!(U, p, dt, solver)
        corrector_step!(U, p, dt, solver, 1)
        
        if step % max(1, div(time_steps, 5)) == 0
            @printf "  Step %2d/%d completed\n" step time_steps
        end
    end
    
    cpu_time = (time_ns() - start_time) / 1e9
    
    @printf "  ✅ CPU time: %.3f s (%.3f s/step)\n" cpu_time (cpu_time/time_steps)
    
    return cpu_time
end

function run_gpu_simulation(mesh, U, p, time_steps::Int=10)
    """Run GPU-accelerated simulation if available."""
    if !gpu_available()
        println("\n⚠️ GPU not available, skipping GPU simulation")
        return Inf
    end
    
    println("\n🚀 GPU Simulation")
    println("-"^20)
    
    try
        # Create GPU fields
        U_gpu, p_gpu = create_gpu_fields(U, p)
        
        # GPU-accelerated solve
        start_time = time_ns()
        
        solver_info = gpu_solve!(
            PISO(mesh), U_gpu, p_gpu,
            time = time_steps * 0.001,
            dt = 0.001
        )
        
        gpu_time = (time_ns() - start_time) / 1e9
        
        @printf "  ✅ GPU time: %.3f s (%.3f s/step)\n" gpu_time (gpu_time/time_steps)
        @printf "  📊 GPU backend: %s\n" solver_info[:backend]
        
        return gpu_time
        
    catch e
        @printf "  ❌ GPU simulation failed: %s\n" e
        return Inf
    end
end

function performance_comparison(ncells_list=[64, 400, 1600])
    """Compare CPU vs GPU performance across different problem sizes."""
    println("\n📊 Performance Comparison")
    println("="^50)
    
    results = []
    
    for ncells in ncells_list
        n = Int(sqrt(ncells))
        resolution = (n, n, 1)  # 2D case
        
        println("\n🔬 Problem size: $ncells cells")
        println("-"^30)
        
        # Create test case
        mesh, U, p = create_test_case("perf_test_$(ncells)", resolution)
        
        # CPU benchmark
        cpu_time = run_cpu_simulation(mesh, U, p, 5)
        
        # GPU benchmark
        gpu_time = run_gpu_simulation(mesh, U, p, 5)
        
        # Calculate speedup
        if gpu_time < Inf && cpu_time > 0
            speedup = cpu_time / gpu_time
            @printf "  🏃 Speedup: %.2fx\n" speedup
        else
            speedup = 0.0
            println("  📝 Speedup: N/A")
        end
        
        push!(results, (ncells, cpu_time, gpu_time, speedup))
    end
    
    return results
end

function display_results_summary(results)
    """Display a summary table of performance results."""
    println("\n📈 Performance Summary")
    println("="^50)
    
    @printf "%-10s %-12s %-12s %-10s\n" "Cells" "CPU (s)" "GPU (s)" "Speedup"
    println("-"^50)
    
    for (ncells, cpu_time, gpu_time, speedup) in results
        if gpu_time < Inf
            @printf "%-10d %-12.3f %-12.3f %-10.2fx\n" ncells cpu_time gpu_time speedup
        else
            @printf "%-10d %-12.3f %-12s %-10s\n" ncells cpu_time "N/A" "N/A"
        end
    end
    
    println()
    
    # Analyze trends
    gpu_results = filter(x -> x[3] < Inf, results)
    if length(gpu_results) >= 2
        println("🔍 Analysis:")
        
        # Find best speedup
        best_speedup = maximum(x -> x[4], gpu_results)
        best_case = filter(x -> x[4] == best_speedup, gpu_results)[1]
        @printf "  • Best speedup: %.2fx at %d cells\n" best_speedup best_case[1]
        
        # Efficiency trend
        if length(gpu_results) >= 3
            early_speedup = gpu_results[1][4]
            late_speedup = gpu_results[end][4]
            if late_speedup > early_speedup
                println("  • GPU efficiency improves with problem size ✅")
            else
                println("  • GPU efficiency decreases with problem size ⚠️")
            end
        end
    end
end

function demonstrate_gpu_features()
    """Demonstrate specific GPU features and capabilities."""
    println("\n🎯 GPU Features Demonstration")
    println("="^50)
    
    # Feature 1: Automatic backend detection
    println("\n1️⃣ Automatic Backend Detection")
    backend = get_gpu_backend()
    println("  Current backend: $backend")
    
    # Feature 2: Graceful fallback
    println("\n2️⃣ Graceful CPU Fallback")
    if gpu_available()
        println("  ✅ GPU available - would use GPU acceleration")
        println("  🔄 Automatic fallback to CPU if GPU operations fail")
    else
        println("  ✅ GPU not available - automatically using CPU")
        println("  🔄 No code changes needed, transparent fallback")
    end
    
    # Feature 3: Benchmark if GPU available
    if gpu_available()
        println("\n3️⃣ GPU Performance Benchmark")
        println("  📊 GPU benchmark available (detailed benchmarks in full demo)")
        # try
        #     benchmark_gpu_performance(1000)
        # catch e
        #     @warn "Benchmark failed: $e"
        # end
    end
    
    # Feature 4: Memory management
    println("\n4️⃣ Automatic Memory Management")
    println("  🔄 Automatic CPU ↔ GPU data transfers")
    println("  💾 Transparent memory allocation")
    println("  🛡️ Error handling for memory issues")
end

function main()
    """Main demo function."""
    println("Running comprehensive GPU acceleration demonstration...\n")
    
    # Check GPU status
    has_gpu = check_gpu_status()
    
    # Demonstrate GPU features
    demonstrate_gpu_features()
    
    # Run performance comparison
    if has_gpu
        # Larger problems for GPU testing
        problem_sizes = [100, 400, 1600]
    else
        # Smaller problems for CPU-only
        problem_sizes = [64, 256, 1000]
    end
    
    results = performance_comparison(problem_sizes)
    display_results_summary(results)
    
    # Final recommendations
    println("\n🎉 GPU Acceleration Demo Complete!")
    println("="^50)
    
    if has_gpu
        println("✅ GPU acceleration is available and ready to use!")
        println("🚀 Key benefits:")
        println("  • Automatic GPU detection and usage")
        println("  • Transparent CPU fallback")
        println("  • Real CFD acceleration for large problems")
        println("  • No code changes required")
        
        gpu_results = filter(x -> x[3] < Inf, results)
        if !isempty(gpu_results)
            avg_speedup = sum(x -> x[4], gpu_results) / length(gpu_results)
            @printf "  • Average speedup: %.1fx\n" avg_speedup
        end
    else
        println("ℹ️  GPU acceleration not available")
        println("📋 To enable GPU acceleration:")
        println("  1. Install CUDA-compatible GPU")
        println("  2. Install CUDA toolkit")
        println("  3. Julia CUDA.jl package is already configured")
        println("  4. Framework will automatically detect and use GPU")
    end
    
    println("\n🔧 Usage in your code:")
    println("```julia")
    println("using CFD")
    println("mesh, U, p = setup_case()")
    println("# Framework automatically uses GPU if available")
    println("solve!(PISO(mesh), U, p, time=10.0)")
    println("```")
    
    return has_gpu
end

# Run the demo
if abspath(PROGRAM_FILE) == @__FILE__
    success = main()
    exit(success ? 0 : 1)
end