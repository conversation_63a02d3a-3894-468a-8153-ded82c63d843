# Demo Usage Examples for CFD_LLM Agentic Tool - Updated for Current CFD.jl
# This demo showcases integration with the latest CFD.jl implementation
# featuring 13+ production solvers, auto-case generation, and dual-mode architecture

using CFD_LLM

# Example 1: Initialize with OpenRouter (recommended for production)
function example_openrouter_setup()
    println("=== Example 1: OpenRouter Setup ===")
    
    # Set up API key (you can also set OPENROUTER_API_KEY environment variable)
    api_key = "your-openrouter-api-key-here"  # Replace with actual key
    
    # Initialize with high-quality model for code generation
    success = initialize_with_openrouter(api_key, model="anthropic/claude-3-sonnet")
    
    if success
        println("✓ CFD_LLM initialized with OpenRouter")
        return true
    else
        println("✗ Failed to initialize with OpenRouter")
        return false
    end
end

# Example 2: Initialize with local LLM (good for development/privacy)
function example_local_llm_setup()
    println("=== Example 2: Local LLM Setup ===")
    
    # Initialize with local Ollama server
    success = initialize_with_local("http://localhost:11434/api/generate", model="llama2:70b")
    
    if success
        println("✓ CFD_LLM initialized with local LLM")
        return true
    else
        println("✗ Failed to initialize with local LLM")
        println("Make sure Ollama is running with: ollama serve")
        return false
    end
end

# Example 3: Generate boundary conditions
function example_boundary_conditions()
    println("\n=== Example 3: Boundary Condition Generation ===")
    
    # Generate a wall boundary condition with heat transfer
    wall_bc = @extend """
    Create a boundary condition for a heated wall in a channel flow:
    - Wall temperature varies sinusoidally: T = 350 + 25*sin(2π*t/10)
    - No-slip condition for velocity
    - Zero gradient for pressure
    - Include proper wall function treatment for turbulence
    """
    
    println("Generated Wall BC:")
    println(wall_bc.content)
    
    # Generate an inlet boundary condition
    inlet_bc = @extend """
    Create an inlet boundary condition for supersonic flow:
    - Mach number: 2.5
    - Total temperature: 500 K
    - Total pressure: 150000 Pa
    - Flow direction: 15 degrees from horizontal
    - Include turbulence specification
    """
    
    println("\nGenerated Inlet BC:")
    println(inlet_bc.content)
end

# Example 4: Solver configuration
function example_solver_setup()
    println("\n=== Example 4: Solver Configuration ===")
    
    # Generate PISO solver for LES
    piso_config = @extend """
    Configure a PISO solver for Large Eddy Simulation of flow around a cylinder:
    - Reynolds number: 3900 (based on cylinder diameter)
    - Subgrid-scale model: Smagorinsky
    - Time step: adaptive based on CFL < 0.5
    - Pressure-velocity coupling: 2 corrector steps
    - Spatial schemes: second-order accurate
    """
    
    println("Generated PISO Configuration:")
    println(piso_config.content)
    
    # Generate SIMPLE solver for steady RANS
    simple_config = @extend """
    Set up a SIMPLE solver for steady turbulent flow in a diffuser:
    - Turbulence model: k-ω SST
    - Under-relaxation factors for robust convergence
    - Convergence criteria: 1e-6 for momentum, 1e-4 for turbulence
    - Maximum 1000 iterations
    """
    
    println("\nGenerated SIMPLE Configuration:")
    println(simple_config.content)
end

# Example 5: Physics analysis and advice
function example_physics_analysis()
    println("\n=== Example 5: Physics Analysis ===")
    
    # Analyze a complex flow problem
    analysis = @ask """
    I'm simulating hypersonic flow over a blunt body at Mach 8 and altitude 30 km.
    The flow features strong shock waves, high-temperature effects, and potential 
    chemical reactions. What physics should I consider and what are the key 
    modeling challenges?
    """
    
    println("Physics Analysis:")
    println(analysis.content)
    
    # Get troubleshooting advice
    troubleshooting = @ask """
    My k-ε simulation of separated flow over a backward-facing step is not 
    converging. The residuals oscillate and the reattachment length seems 
    wrong compared to experiments. What could be the issues and how can I fix them?
    """
    
    println("\nTroubleshooting Advice:")
    println(troubleshooting.content)
end

# Example 6: Current CFD.jl Integration Examples
function example_current_cfd_integration()
    println("\n=== Example 6: Current CFD.jl Integration ===")
    
    # Generate code using current CFD.jl SolverRegistry
    solver_registry_code = @generate """
    Create a function that demonstrates the current CFD.jl SolverRegistry system:
    - List all 13+ available production solvers
    - Show solver information for key solvers (PISO, SIMPLE, interFoam)
    - Demonstrate auto-case generation
    - Run a simple incompressible flow simulation
    Use the latest CFD.jl API with CFD.solve(), CFD.list_solvers(), etc.
    """
    
    println("Generated Current CFD.jl Code:")
    if solver_registry_code.generated_code !== nothing
        println(solver_registry_code.generated_code)
    else
        println(solver_registry_code.content)
    end
    
    # Generate HPC-optimized simulation code
    hpc_code = @generate """
    Create an HPC-optimized CFD workflow using current CFD.jl features:
    - Use default HPC optimizations (enabled by default)
    - Demonstrate parallel execution
    - Show performance monitoring
    - Include ghost cell optimization and matrix assembly improvements
    Target: 3-7x performance improvement over standard solvers
    """
    
    println("\nGenerated HPC-Optimized Code:")
    if hpc_code.generated_code !== nothing
        println(hpc_code.generated_code)
    else
        println(hpc_code.content)
    end
end

# Example 7: Current CFD.jl Advanced Features
function example_current_advanced_features()
    println("\n=== Example 7: Current CFD.jl Advanced Features ===")
    
    # Dual-mode architecture demonstration
    dual_mode_demo = @generate """
    Demonstrate CFD.jl's dual-mode architecture:
    
    USER MODE (Simple Interface):
    - Show how to run simulations with just CFD.solve()
    - Demonstrate auto-case generation
    - Use built-in solvers from SolverRegistry
    
    DEVELOPER MODE (Full Control):
    - Use CFD.Development module
    - Create custom solvers with @quick_solver
    - Register new solvers with @register_solver
    - Access solver benchmarking tools
    
    Include examples of both modes working with the same case.
    """
    
    println("Dual-Mode Architecture Demo:")
    if dual_mode_demo.generated_code !== nothing
        println(dual_mode_demo.generated_code)
    else
        println(dual_mode_demo.content)
    end
    
    # Multiphase with current interFoam
    multiphase_current = @generate """
    Set up a multiphase simulation using current CFD.jl interFoam solver:
    - Use CFD.solve() with solver=:interFoam
    - Leverage auto-case generation for VOF setup
    - Include interPhaseChangeFoam for phase change
    - Show how CFD.jl handles multiphase physics automatically
    - Demonstrate the 13+ solver ecosystem
    """
    
    println("\nCurrent Multiphase Implementation:")
    if multiphase_current.generated_code !== nothing
        println(multiphase_current.generated_code)
    else
        println(multiphase_current.content)
    end
end

# Example 8: Real CFD.jl Integration Testing
function example_real_cfd_integration()
    println("\n=== Example 8: Real CFD.jl Integration Testing ===")
    
    # Check current CFD.jl capabilities
    capabilities = CFD_LLM.CFDInterface.list_current_cfd_capabilities()
    println("Current CFD.jl Capabilities:")
    for (key, value) in capabilities
        println("  $key: $value")
    end
    
    # Test solver registry integration
    available_solvers = CFD_LLM.CFDInterface.get_available_solvers()
    println("\n📋 Available Solvers in Registry:")
    for solver in available_solvers
        println("  • $solver")
    end
    
    if length(available_solvers) > 0
        println("\n✅ CFD.jl SolverRegistry integration working")
        
        # Generate real integration code
        real_integration = @generate """
        Create a complete workflow using the ACTUAL current CFD.jl API:
        
        1. Use CFD.solve() with auto-case generation
        2. Test multiple solvers from the registry (PISO, SIMPLE, interFoam)
        3. Demonstrate HPC optimization (enabled by default)
        4. Show solver information retrieval
        5. Include error handling for real scenarios
        
        Make this code executable with the current CFD.jl implementation.
        """
        
        println("\n🔧 Real CFD.jl Integration Code:")
        if real_integration.generated_code !== nothing
            println(real_integration.generated_code)
        else
            println(real_integration.content)
        end
    else
        println("\n⚠️ CFD.jl not fully available - generating compatibility code")
    end
end

# Example 9: Learning and adaptation
function example_learning_workflow()
    println("\n=== Example 9: Learning Workflow ===")
    
    # Simulate a successful workflow to demonstrate learning
    request = "Create inlet BC for turbulent pipe flow"
    
    # Generate response
    response = @extend request
    
    # Simulate successful outcome (normally this would come from actual simulation)
    using CFD_LLM.OrchestratorAgent: SimulationOutcome, learn_from_outcome
    
    if CFD_LLM.ORCHESTRATOR[] !== nothing
        successful_outcome = SimulationOutcome(
            true,  # success
            Dict("convergence_rate" => 0.95, "accuracy" => 0.98),  # metrics
            String[],  # no errors
            Dict("cpu_time" => 45.2, "memory_usage" => "2.1 GB"),  # performance
            Dict("physics_valid" => true, "conservation_error" => 1e-7)  # validation
        )
        
        # Learn from the successful outcome
        learn_from_outcome(CFD_LLM.ORCHESTRATOR[], request, response, successful_outcome)
        println("✓ Learning recorded - this pattern will improve future recommendations")
    end
end

# Main demo function
function run_complete_demo()
    println("🚀 CFD_LLM Agentic Tool Demo")
    println("=" ^ 50)
    
    # Try to initialize (prefer OpenRouter, fallback to local)
    initialized = false
    
    # First try OpenRouter if API key is available
    if haskey(ENV, "OPENROUTER_API_KEY") && !isempty(ENV["OPENROUTER_API_KEY"])
        initialized = example_openrouter_setup()
    end
    
    # Fallback to local LLM
    if !initialized
        initialized = example_local_llm_setup()
    end
    
    if !initialized
        println("❌ Could not initialize CFD_LLM")
        println("Please either:")
        println("1. Set OPENROUTER_API_KEY environment variable, or")
        println("2. Start local Ollama server with: ollama serve")
        return
    end
    
    # Run all examples
    try
        example_boundary_conditions()
        example_solver_setup()
        example_physics_analysis()
        example_current_cfd_integration()
        example_current_advanced_features()
        example_real_cfd_integration()
        example_learning_workflow()
        
        println("\n🎉 Demo completed successfully!")
        println("Explore the generated code and adapt it for your specific CFD needs.")
        
    catch e
        println("❌ Demo encountered an error: $e")
        println("This might be due to LLM connectivity issues or rate limits.")
    end
end

# Run individual examples for testing
function run_single_example(example_name::String)
    examples = Dict(
        "boundary_conditions" => example_boundary_conditions,
        "solver_setup" => example_solver_setup,
        "physics_analysis" => example_physics_analysis,
        "current_cfd_integration" => example_current_cfd_integration,
        "current_advanced_features" => example_current_advanced_features,
        "real_cfd_integration" => example_real_cfd_integration,
        "learning_workflow" => example_learning_workflow
    )
    
    if haskey(examples, example_name)
        # Initialize first
        if initialize_with_local() || (haskey(ENV, "OPENROUTER_API_KEY") && initialize_with_openrouter(ENV["OPENROUTER_API_KEY"]))
            examples[example_name]()
        else
            println("Failed to initialize CFD_LLM")
        end
    else
        println("Available examples: ", join(keys(examples), ", "))
    end
end

# Uncomment to run the complete demo
# run_complete_demo()

# Or run a specific example:
# run_single_example("boundary_conditions")