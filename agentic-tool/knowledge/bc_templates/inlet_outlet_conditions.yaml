# Inlet and Outlet Boundary Condition Templates
inlet_boundary_conditions:
  velocity_inlet:
    name: "Velocity Inlet"
    description: "Specified velocity at inlet boundary"
    physics_requirements:
      - known_velocity_profile
      - incompressible_or_subsonic
    field_specifications:
      velocity:
        type: "dirichlet"
        value: "inlet_velocity_vector"
        units: "m/s"
      pressure:
        type: "neumann"
        value: 0.0
        units: "Pa/m"
      temperature:
        type: "dirichlet"
        value: "inlet_temperature"
        units: "K"
    turbulence_specifications:
      turbulence_intensity:
        typical_values: [0.01, 0.1]  # 1-10%
        formula: "I = u'/U_avg"
      hydraulic_diameter:
        formula: "Dh = 4*Area/Perimeter"
      turbulent_length_scale:
        formula: "L = 0.07 * Dh"
    code_template: |
      function apply_velocity_inlet!(field::Field, patch_name::String, inlet_velocity::SVector, inlet_temp::Float64=300.0)
          if field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = DirichletBC(inlet_velocity)
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          elseif field.name == :temperature || field.name == :T
              field.boundary_conditions[patch_name] = DirichletBC(inlet_temp)
          elseif field.name == :k
              # Turbulent kinetic energy: k = 1.5*(I*U)²
              I = 0.05  # 5% turbulence intensity
              U_mag = norm(inlet_velocity)
              k_inlet = 1.5 * (I * U_mag)^2
              field.boundary_conditions[patch_name] = DirichletBC(k_inlet)
          elseif field.name == :epsilon
              # Turbulent dissipation: ε = Cμ^(3/4) * k^(3/2) / L
              I = 0.05
              U_mag = norm(inlet_velocity)
              k_inlet = 1.5 * (I * U_mag)^2
              L = 0.1  # Turbulent length scale
              Cmu = 0.09
              eps_inlet = Cmu^0.75 * k_inlet^1.5 / L
              field.boundary_conditions[patch_name] = DirichletBC(eps_inlet)
          end
      end

  mass_flow_inlet:
    name: "Mass Flow Inlet"
    description: "Specified mass flow rate at inlet"
    physics_requirements:
      - known_mass_flow_rate
      - uniform_density
    field_specifications:
      velocity:
        type: "calculated"
        formula: "U = mdot / (rho * A)"
      pressure:
        type: "neumann"
        value: 0.0
    code_template: |
      function apply_mass_flow_inlet!(field::Field, patch_name::String, mass_flow::Float64, inlet_area::Float64, density::Float64)
          if field.name == :velocity || field.name == :U
              velocity_magnitude = mass_flow / (density * inlet_area)
              inlet_velocity = SVector(velocity_magnitude, 0.0, 0.0)  # Assume x-direction
              field.boundary_conditions[patch_name] = DirichletBC(inlet_velocity)
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          end
      end

  pressure_inlet:
    name: "Pressure Inlet"
    description: "Specified total pressure at inlet"
    physics_requirements:
      - compressible_flow
      - known_total_conditions
    field_specifications:
      pressure:
        type: "dirichlet"
        value: "total_pressure"
      temperature:
        type: "dirichlet"
        value: "total_temperature"
      velocity:
        type: "calculated"
        note: "Calculated from pressure ratio"

outlet_boundary_conditions:
  pressure_outlet:
    name: "Pressure Outlet"
    description: "Specified static pressure at outlet"
    physics_requirements:
      - subsonic_outflow
      - known_downstream_pressure
    field_specifications:
      pressure:
        type: "dirichlet"
        value: "outlet_pressure"
        units: "Pa"
      velocity:
        type: "neumann"
        value: 0.0
        units: "(m/s)/m"
      temperature:
        type: "neumann"
        value: 0.0
        units: "K/m"
    code_template: |
      function apply_pressure_outlet!(field::Field, patch_name::String, outlet_pressure::Float64)
          if field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = DirichletBC(outlet_pressure)
          elseif field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = NeumannBC(SVector(0.0, 0.0, 0.0))
          elseif field.name == :temperature || field.name == :T
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          elseif field.name in [:k, :epsilon, :omega]
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          end
      end

  outflow:
    name: "Outflow"
    description: "Zero gradient conditions at outlet"
    physics_requirements:
      - fully_developed_flow
      - long_domain_downstream
    field_specifications:
      velocity:
        type: "neumann"
        value: 0.0
      pressure:
        type: "dirichlet"
        value: "reference_pressure"
      temperature:
        type: "neumann"
        value: 0.0
    code_template: |
      function apply_outflow!(field::Field, patch_name::String, reference_pressure::Float64=101325.0)
          if field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = NeumannBC(SVector(0.0, 0.0, 0.0))
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = DirichletBC(reference_pressure)
          else
              # Zero gradient for all other fields
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          end
      end

boundary_condition_validation:
  mass_conservation:
    check: "inlet_mass_flow == outlet_mass_flow"
    tolerance: 1e-6
    
  pressure_reference:
    requirement: "At least one pressure boundary condition required for incompressible flow"
    
  energy_conservation:
    check: "inlet_energy_flow == outlet_energy_flow + wall_heat_transfer"
    
  realistic_values:
    velocity_range: [0.0, 500.0]  # m/s
    pressure_range: [0.0, 1e8]    # Pa
    temperature_range: [0.1, 5000.0]  # K

common_inlet_patterns:
  pipe_flow:
    velocity_profile: "parabolic for laminar, 1/7th power for turbulent"
    turbulence_intensity: 0.04  # 4%
    length_scale: "0.07 * hydraulic_diameter"
    
  external_flow:
    velocity_profile: "uniform"
    turbulence_intensity: 0.01  # 1%
    length_scale: "characteristic_length / 20"
    
  wind_tunnel:
    velocity_profile: "uniform core with boundary layer"
    turbulence_intensity: 0.005  # 0.5%
    length_scale: "tunnel_height / 10"

outlet_guidelines:
  outlet_distance:
    minimum: "10 * characteristic_length from last geometric feature"
    recommendation: "Extend domain until results converge"
    
  pressure_level:
    incompressible: "Set one pressure reference (usually atmospheric)"
    compressible: "Set static pressure based on operating conditions"
    
  backflow_prevention:
    check: "Monitor for reverse flow at outlets"
    action: "Extend domain or use pressure outlet with backflow temperature"