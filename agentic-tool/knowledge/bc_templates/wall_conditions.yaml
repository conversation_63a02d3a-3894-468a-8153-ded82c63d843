# Wall Boundary Condition Templates
wall_boundary_conditions:
  no_slip_wall:
    name: "No-slip Wall"
    description: "Standard viscous wall with zero velocity"
    physics_requirements:
      - viscous_flow
      - attached_boundary_layer
    field_specifications:
      velocity:
        type: "dirichlet"
        value: [0.0, 0.0, 0.0]
        units: "m/s"
      pressure:
        type: "neumann"
        value: 0.0
        units: "Pa/m"
      temperature:
        type: "dirichlet"  # or neumann
        value: "user_specified"
        units: "K"
    turbulence_treatment:
      k_epsilon:
        k: "wall_function"
        epsilon: "wall_function"
        y_plus_range: [30, 300]
      k_omega:
        k: "wall_function"
        omega: "specified_value"
        y_plus_range: [1, 300]
    code_template: |
      function apply_no_slip_wall!(field::Field, patch_name::String)
          if field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = DirichletBC(SVector(0.0, 0.0, 0.0))
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = <PERSON>BC(0.0)
          elseif field.name == :temperature || field.name == :T
              # User must specify wall temperature
              field.boundary_conditions[patch_name] = DirichletBC(wall_temperature)
          end
      end

  slip_wall:
    name: "Slip Wall"
    description: "Inviscid wall with tangential slip allowed"
    physics_requirements:
      - inviscid_flow
      - euler_equations
    field_specifications:
      velocity:
        type: "mixed"
        normal_component: 0.0
        tangential_component: "free"
      pressure:
        type: "neumann"
        value: 0.0
    code_template: |
      function apply_slip_wall!(field::Field, patch_name::String, wall_normal::SVector)
          if field.name == :velocity || field.name == :U
              # Slip condition: U·n = 0, tangential component free
              field.boundary_conditions[patch_name] = SlipBC(wall_normal)
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          end
      end

  heated_wall:
    name: "Heated Wall"
    description: "Wall with specified heat flux or temperature"
    physics_requirements:
      - heat_transfer
      - energy_equation
    field_specifications:
      velocity:
        type: "dirichlet"
        value: [0.0, 0.0, 0.0]
      pressure:
        type: "neumann"
        value: 0.0
      temperature:
        type: "dirichlet"  # or heat flux
        value: "user_specified"
    variants:
      constant_temperature:
        temperature:
          type: "dirichlet"
          value: "T_wall"
      constant_heat_flux:
        temperature:
          type: "neumann"
          value: "q_wall / k"
      time_varying_temperature:
        temperature:
          type: "dirichlet"
          value: "T_base + A*sin(2π*f*t)"
    code_template: |
      function apply_heated_wall!(field::Field, patch_name::String, wall_temp::Float64)
          if field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = DirichletBC(SVector(0.0, 0.0, 0.0))
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          elseif field.name == :temperature || field.name == :T
              field.boundary_conditions[patch_name] = DirichletBC(wall_temp)
          end
      end

  moving_wall:
    name: "Moving Wall"
    description: "Wall with specified velocity (e.g., rotating cylinder)"
    physics_requirements:
      - viscous_flow
      - relative_motion
    field_specifications:
      velocity:
        type: "dirichlet"
        value: "wall_velocity"
      pressure:
        type: "neumann"
        value: 0.0
    code_template: |
      function apply_moving_wall!(field::Field, patch_name::String, wall_velocity::SVector)
          if field.name == :velocity || field.name == :U
              field.boundary_conditions[patch_name] = DirichletBC(wall_velocity)
          elseif field.name == :pressure || field.name == :p
              field.boundary_conditions[patch_name] = NeumannBC(0.0)
          end
      end

wall_function_guidelines:
  y_plus_requirements:
    wall_functions: "30 < y+ < 300"
    low_re_models: "y+ < 1"
    enhanced_wall_treatment: "y+ < 1 or 30 < y+ < 300"
    
  turbulence_models:
    k_epsilon:
      wall_treatment: "wall_functions"
      k_boundary: "wall_function"
      epsilon_boundary: "wall_function"
    k_omega_sst:
      wall_treatment: "low_re"
      k_boundary: "zero_value"
      omega_boundary: "high_value"
      
  implementation_notes:
    - "Always check y+ distribution after mesh generation"
    - "Use wall functions for high-Re flows to save computational cost"
    - "Use low-Re models for accurate near-wall physics"
    - "Ensure smooth wall function transition"