# Current CFD.jl Boundary Condition Types - Updated for Latest Implementation
# This file documents the 50+ boundary condition types available in CFD.jl

boundary_condition_categories:
  dirichlet_conditions:
    fixedValue:
      description: "Constant value boundary condition"
      field_types: ["scalar", "vector", "tensor"]
      physics_usage: ["all_physics"]
      code_template: |
        @bc wall_bc begin
            patch = "wall"
            type = :fixedValue
            value = SVector(0.0, 0.0, 0.0)  # For velocity
            # value = 300.0  # For temperature/scalar
        end
      examples:
        velocity: "value uniform (0 0 0)  # No-slip wall"
        pressure: "value uniform 101325   # Fixed pressure"
        temperature: "value uniform 300   # Fixed temperature"

    uniformFixedValue:
      description: "Spatially uniform fixed value"
      field_types: ["scalar", "vector"]
      physics_usage: ["inlet_conditions", "initial_conditions"]
      code_template: |
        @bc inlet_bc begin
            patch = "inlet"
            type = :uniformFixedValue
            uniformValue = SVector(1.0, 0.0, 0.0)  # Inlet velocity
        end

    timeVaryingMappedFixedValue:
      description: "Time-varying mapped boundary condition"
      field_types: ["scalar", "vector"]
      physics_usage: ["transient_inlets", "pulsating_flow"]
      parameters:
        - setAverage: "true/false"
        - offset: "constant offset value"
        - interpolationScheme: "linear/spline"

    codedFixedValue:
      description: "Programmable boundary condition"
      field_types: ["scalar", "vector", "tensor"]
      physics_usage: ["custom_profiles", "complex_conditions"]
      code_template: |
        # Requires custom C++ code generation
        # CFD.jl provides Julia-based programmable BCs
        function custom_bc(x, y, z, t)
            # Custom logic here
            return SVector(sin(t), 0.0, 0.0)
        end

  neumann_conditions:
    zeroGradient:
      description: "Zero normal gradient"
      field_types: ["scalar", "vector", "tensor"]
      physics_usage: ["outlets", "symmetry", "far_field"]
      code_template: |
        @bc outlet_bc begin
            patch = "outlet"
            type = :zeroGradient
        end
      common_usage:
        velocity_outlet: "Fully developed flow assumption"
        pressure_wall: "Impermeable wall for incompressible flow"
        temperature_adiabatic: "Adiabatic (no heat transfer) wall"

    fixedGradient:
      description: "Fixed normal gradient"
      field_types: ["scalar", "vector", "tensor"]
      physics_usage: ["heat_flux", "mass_flux", "stress"]
      code_template: |
        @bc heat_flux_bc begin
            patch = "heated_wall"
            type = :fixedGradient
            gradient = 1000.0  # Heat flux [W/m²]
        end

    uniformFixedGradient:
      description: "Spatially uniform fixed gradient"
      field_types: ["scalar", "vector"]
      physics_usage: ["uniform_heat_flux", "pressure_gradient"]

  mixed_conditions:
    mixed:
      description: "Mixed Dirichlet/Neumann condition"
      field_types: ["scalar", "vector"]
      physics_usage: ["Robin_BC", "convective_heat_transfer"]
      parameters:
        refValue: "reference value"
        refGradient: "reference gradient"
        valueFraction: "mixing coefficient (0=Neumann, 1=Dirichlet)"
      code_template: |
        @bc convective_bc begin
            patch = "convective_wall"
            type = :mixed
            refValue = 300.0  # Ambient temperature
            refGradient = 0.0
            valueFraction = 0.8  # Convective mixing
        end

    directionMixed:
      description: "Direction-dependent mixed condition"
      field_types: ["vector"]
      physics_usage: ["partially_permeable_walls", "directional_flow"]

  wall_conditions:
    noSlip:
      description: "No-slip wall condition for velocity"
      field_types: ["vector"]
      physics_usage: ["viscous_walls", "solid_boundaries"]
      code_template: |
        @bc wall_velocity_bc begin
            patch = "wall"
            type = :noSlip
        end
      automatic_application: "CFD.jl automatically applies to wall patches"

    slip:
      description: "Free-slip wall condition"
      field_types: ["vector"]
      physics_usage: ["inviscid_walls", "symmetry_planes"]
      code_template: |
        @bc slip_wall_bc begin
            patch = "symmetry"
            type = :slip
        end

    partialSlip:
      description: "Partial slip with friction coefficient"
      field_types: ["vector"]
      physics_usage: ["rough_walls", "porous_boundaries"]
      parameters:
        valueFraction: "slip coefficient (0=free slip, 1=no slip)"

    movingWallVelocity:
      description: "Moving wall with specified velocity"
      field_types: ["vector"]
      physics_usage: ["rotating_machinery", "moving_boundaries"]
      code_template: |
        @bc moving_wall_bc begin
            patch = "rotor_surface"
            type = :movingWallVelocity
            value = SVector(0.0, 10.0, 0.0)  # Wall velocity
        end

    rotatingWallVelocity:
      description: "Rotating wall with angular velocity"
      field_types: ["vector"]
      physics_usage: ["turbomachinery", "rotating_cylinders"]
      parameters:
        origin: "rotation center"
        axis: "rotation axis"
        omega: "angular velocity [rad/s]"

  inlet_conditions:
    pressureInletVelocity:
      description: "Velocity calculated from pressure gradient"
      field_types: ["vector"]
      physics_usage: ["pressure_driven_flow", "natural_circulation"]
      code_template: |
        @bc pressure_inlet_bc begin
            patch = "inlet"
            type = :pressureInletVelocity
            value = SVector(0.0, 0.0, 0.0)  # Initial guess
        end

    velocityInlet:
      description: "Specified velocity profile at inlet"
      field_types: ["vector"]
      physics_usage: ["forced_convection", "wind_tunnels"]
      profiles:
        uniform: "constant velocity"
        parabolic: "Poiseuille profile"
        power_law: "turbulent profile"
        custom: "user-defined function"

    massFlowInlet:
      description: "Specified mass flow rate"
      field_types: ["vector"]
      physics_usage: ["pump_boundaries", "mass_conservation"]
      parameters:
        massFlowRate: "total mass flow [kg/s]"
        flowDirection: "flow direction vector"

    totalPressureInlet:
      description: "Specified total pressure"
      field_types: ["scalar", "vector"]
      physics_usage: ["compressible_inlets", "reservoir_boundaries"]
      parameters:
        p0: "total pressure"
        T0: "total temperature"
        flowDirection: "flow direction"

  outlet_conditions:
    pressureOutlet:
      description: "Specified static pressure at outlet"
      field_types: ["scalar", "vector"]
      physics_usage: ["atmospheric_outlets", "pressure_boundaries"]
      code_template: |
        @bc pressure_outlet_bc begin
            patch = "outlet"
            type = :pressureOutlet
            value = 101325.0  # Atmospheric pressure
        end

    outletPhaseMeanVelocity:
      description: "Velocity adjusted for mass conservation"
      field_types: ["vector"]
      physics_usage: ["multiphase_outlets", "recirculation_prevention"]

    advective:
      description: "Advective (wave-transmissive) outlet"
      field_types: ["scalar", "vector"]
      physics_usage: ["open_boundaries", "far_field"]
      parameters:
        lInf: "far-field length scale"
        fieldInf: "far-field value"

  turbulence_wall_functions:
    kqRWallFunction:
      description: "Wall function for turbulent kinetic energy"
      field_types: ["scalar"]
      physics_usage: ["k_equation", "wall_treatment"]
      turbulence_models: ["k-epsilon", "k-omega", "Reynolds stress"]

    epsilonWallFunction:
      description: "Wall function for turbulent dissipation"
      field_types: ["scalar"]
      physics_usage: ["epsilon_equation", "k_epsilon_models"]
      parameters:
        Cmu: "turbulence constant"
        kappa: "von Karman constant"
        E: "wall roughness parameter"

    omegaWallFunction:
      description: "Wall function for specific dissipation rate"
      field_types: ["scalar"]
      physics_usage: ["omega_equation", "k_omega_models"]

    nutkWallFunction:
      description: "Wall function for turbulent viscosity (k-based)"
      field_types: ["scalar"]
      physics_usage: ["turbulent_viscosity", "wall_treatment"]

    nutUWallFunction:
      description: "Wall function for turbulent viscosity (velocity-based)"
      field_types: ["scalar"]
      physics_usage: ["spalart_allmaras", "one_equation_models"]

  multiphase_conditions:
    constantAlphaContactAngle:
      description: "Contact angle for VOF simulations"
      field_types: ["scalar"]
      physics_usage: ["free_surface", "wetting"]
      parameters:
        theta0: "equilibrium contact angle [degrees]"

    dynamicAlphaContactAngle:
      description: "Dynamic contact angle model"
      field_types: ["scalar"]
      physics_usage: ["moving_contact_lines", "dynamic_wetting"]

    alphaInletOutlet:
      description: "Phase fraction inlet/outlet"
      field_types: ["scalar"]
      physics_usage: ["multiphase_flow", "injection"]

  compressible_conditions:
    totalPressure:
      description: "Total pressure boundary condition"
      field_types: ["scalar"]
      physics_usage: ["compressible_inlets", "stagnation_conditions"]

    totalTemperature:
      description: "Total temperature boundary condition"
      field_types: ["scalar"]
      physics_usage: ["compressible_flow", "energy_equation"]

    supersonicFreestream:
      description: "Supersonic freestream condition"
      field_types: ["scalar", "vector"]
      physics_usage: ["supersonic_flow", "far_field"]
      parameters:
        UInf: "freestream velocity"
        pInf: "freestream pressure"
        TInf: "freestream temperature"

# CFD.jl Boundary Condition Application
cfd_jl_bc_system:
  automatic_detection:
    description: "CFD.jl automatically detects patch types and suggests appropriate BCs"
    patch_types:
      wall: "Automatically applies noSlip for velocity, zeroGradient for pressure"
      inlet: "Suggests velocity or pressure inlet based on solver"
      outlet: "Suggests pressure outlet or zeroGradient"
      symmetry: "Applies symmetry conditions"

  dsl_integration:
    description: "Boundary conditions integrated with CFD.jl DSL"
    macro_usage: "@bc macro for clean BC definition"
    unicode_support: "Mathematical symbols in BC definitions"
    
  smart_validation:
    description: "Automatic validation of BC combinations"
    checks:
      - "Mass conservation at inlets/outlets"
      - "Pressure reference point for incompressible flow"
      - "Energy conservation for heat transfer"
      - "Realistic value ranges"

  template_system:
    description: "Pre-defined BC templates for common cases"
    templates:
      - "Pipe flow inlet/outlet"
      - "External aerodynamics"
      - "Heat exchanger"
      - "Natural convection cavity"
      - "Multiphase dam break"

# Physics-Specific BC Guidelines
physics_bc_guidelines:
  incompressible_flow:
    required_bcs:
      velocity: "At least one velocity BC (inlet/wall)"
      pressure: "Exactly one pressure reference point"
    recommendations:
      inlet: "velocityInlet or pressureInletVelocity"
      outlet: "pressureOutlet or zeroGradient"
      walls: "noSlip for velocity, zeroGradient for pressure"

  compressible_flow:
    required_bcs:
      density: "Calculated from equation of state"
      pressure: "totalPressure or pressureOutlet"
      temperature: "totalTemperature or fixedValue"
    recommendations:
      supersonic_inlet: "All variables fixed"
      subsonic_inlet: "Total conditions + flow direction"
      supersonic_outlet: "All variables extrapolated"
      subsonic_outlet: "Static pressure fixed"

  heat_transfer:
    thermal_bcs:
      isothermal_wall: "fixedValue temperature"
      adiabatic_wall: "zeroGradient temperature"
      heat_flux_wall: "fixedGradient temperature"
      convective_wall: "mixed condition with heat transfer coefficient"

  multiphase_flow:
    phase_bcs:
      free_surface: "Contact angle conditions"
      inlet: "Phase fraction specification"
      outlet: "zeroGradient for phase fractions"
      walls: "Contact angle for wetting surfaces"

  turbulent_flow:
    turbulence_bcs:
      inlet: "Specify turbulence intensity and length scale"
      outlet: "zeroGradient for all turbulence variables"
      walls: "Wall functions or resolved boundary layer"