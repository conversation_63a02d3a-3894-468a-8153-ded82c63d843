# Conservation Laws for CFD Physics Validation
conservation_laws:
  mass_conservation:
    name: "Conservation of Mass"
    equation: "∂ρ/∂t + ∇·(ρU) = 0"
    description: "Mass cannot be created or destroyed in a control volume"
    violations:
      - "Mass source/sink without physical justification"
      - "Inconsistent density specification"
      - "Boundary condition mass imbalance"
    validation_checks:
      - check: "inlet_outlet_mass_balance"
        tolerance: 1e-6
      - check: "total_mass_conservation"
        tolerance: 1e-8
    
  momentum_conservation:
    name: "Conservation of Momentum"
    equation: "∂(ρU)/∂t + ∇·(ρU⊗U) = -∇p + ∇·τ + ρg"
    description: "Newton's second law for fluid motion"
    violations:
      - "Forces not balanced"
      - "Non-physical pressure gradients"
      - "Inconsistent viscous stress specification"
    validation_checks:
      - check: "force_balance"
        tolerance: 1e-6
      - check: "pressure_consistency"
        tolerance: 1e-5

  energy_conservation:
    name: "Conservation of Energy"
    equation: "∂(ρE)/∂t + ∇·(ρEU) = ∇·(k∇T) + Φ + Q"
    description: "First law of thermodynamics for fluid flow"
    violations:
      - "Energy source/sink without justification"
      - "Inconsistent thermal properties"
      - "Temperature boundary condition violations"
    validation_checks:
      - check: "energy_balance"
        tolerance: 1e-6
      - check: "temperature_bounds"
        min_temp: 0.1  # Kelvin
        max_temp: 5000.0

physics_constraints:
  density:
    positivity: true
    typical_range: [0.1, 10000.0]  # kg/m³
    
  velocity:
    typical_range: [0.0, 1000.0]  # m/s
    realizable: true
    
  pressure:
    absolute_positivity: true
    gauge_pressure_range: [-1e5, 1e8]  # Pa
    
  temperature:
    absolute_positivity: true
    typical_range: [1.0, 5000.0]  # K

turbulence_constraints:
  turbulent_kinetic_energy:
    positivity: true
    realizability: "k ≥ 0"
    
  turbulent_dissipation:
    positivity: true
    realizability: "ε > 0"
    
  turbulent_viscosity:
    positivity: true
    upper_limit: "νt/ν < 1e6"

boundary_condition_physics:
  wall:
    velocity: "no_slip or slip"
    pressure: "zero_gradient"
    temperature: "specified or zero_gradient"
    turbulence: "wall_functions or low_re"
    
  inlet:
    velocity: "specified_value or mass_flow"
    pressure: "zero_gradient or total_pressure"
    temperature: "specified_value"
    turbulence: "specified_k_epsilon or intensity"
    
  outlet:
    velocity: "zero_gradient"
    pressure: "specified_value"
    temperature: "zero_gradient"
    turbulence: "zero_gradient"

dimensional_analysis:
  reynolds_number:
    formula: "Re = ρUL/μ"
    laminar_threshold: 2300
    turbulent_threshold: 4000
    
  mach_number:
    formula: "Ma = U/c"
    incompressible_limit: 0.3
    subsonic_limit: 1.0
    
  prandtl_number:
    formula: "Pr = μcp/k"
    typical_gases: 0.7
    typical_liquids: [1.0, 10.0]