# Current CFD.jl Solver Capabilities - Updated for Latest Implementation
# This file documents the 13+ production solvers available in CFD.jl

production_solvers:
  incompressible_solvers:
    PISO:
      name: "PISO"
      full_name: "Pressure-Implicit with Splitting of Operators"
      description: "Transient solver for incompressible flow using PISO algorithm"
      physics: ["incompressible", "transient"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U"
        continuity: "∇·U = 0"
      required_fields: ["U", "p"]
      optional_fields: ["phi"]
      algorithm: "PISO"
      parameters:
        nCorrectors: 2
        nNonOrthogonalCorrectors: 1
      use_cases:
        - "Unsteady incompressible flow"
        - "LES simulations"
        - "Transient flow analysis"
      code_template: |
        # CFD.jl PISO solver usage
        case_result = CFD.solve("myCase", solver=:PISO, time=10.0)
        
        # Or using HPC-optimized version (default)
        solver = CFD.PISO(mesh)  # Automatically HPC-optimized
        CFD.solve!(solver, time=10.0)

    SIMPLE:
      name: "SIMPLE"
      full_name: "Semi-Implicit Method for Pressure-Linked Equations"
      description: "Steady-state solver for incompressible flow using SIMPLE algorithm"
      physics: ["incompressible", "steady"]
      equations:
        momentum: "∇·(UU) = -∇p + ν∇²U"
        continuity: "∇·U = 0"
      required_fields: ["U", "p"]
      optional_fields: ["phi", "nu"]
      algorithm: "SIMPLE"
      parameters:
        relaxation:
          U: 0.7
          p: 0.3
      use_cases:
        - "Steady incompressible flow"
        - "Industrial flow analysis"
        - "Design optimization"
      code_template: |
        # CFD.jl SIMPLE solver usage
        case_result = CFD.solve("myCase", solver=:SIMPLE)
        
        # With custom relaxation
        case_result = CFD.solve("myCase", solver=:SIMPLE, 
                               relaxation=Dict(:U => 0.8, :p => 0.4))

    PIMPLE:
      name: "PIMPLE"
      full_name: "Merged PISO-SIMPLE Algorithm"
      description: "Transient solver combining PISO and SIMPLE for robust convergence"
      physics: ["incompressible", "transient"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U"
        continuity: "∇·U = 0"
      required_fields: ["U", "p"]
      optional_fields: ["phi"]
      algorithm: "PIMPLE"
      parameters:
        nOuterCorrectors: 3
        nCorrectors: 2
        nNonOrthogonalCorrectors: 1
      use_cases:
        - "Large time-step transient flow"
        - "Complex geometry flow"
        - "Coupled physics simulations"

    icoFoam:
      name: "icoFoam"
      description: "Transient solver for incompressible, laminar flow using PISO"
      physics: ["incompressible", "laminar", "transient"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U"
        continuity: "∇·U = 0"
      required_fields: ["U", "p"]
      optional_fields: ["phi"]
      algorithm: "PISO"
      use_cases:
        - "Laminar flow validation"
        - "Educational examples"
        - "Simple transient flow"

    simpleFoam:
      name: "simpleFoam"
      description: "Steady-state solver for incompressible flow using SIMPLE"
      physics: ["incompressible", "steady"]
      required_fields: ["U", "p"]
      optional_fields: ["phi", "nu"]
      algorithm: "SIMPLE"
      use_cases:
        - "Steady flow analysis"
        - "External aerodynamics"
        - "Internal flow systems"

    pisoFoam:
      name: "pisoFoam"
      description: "Transient solver for incompressible flow using PISO"
      physics: ["incompressible", "transient"]
      required_fields: ["U", "p"]
      optional_fields: ["phi", "nu"]
      algorithm: "PISO"
      use_cases:
        - "Unsteady flow phenomena"
        - "Vortex shedding"
        - "Flow transients"

    pimpleFoam:
      name: "pimpleFoam"
      description: "Transient solver for incompressible flow using PIMPLE"
      physics: ["incompressible", "transient", "turbulent"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U + ∇·(νₜ∇U)"
        continuity: "∇·U = 0"
      required_fields: ["U", "p"]
      optional_fields: ["phi", "nu", "nut"]
      algorithm: "PIMPLE"
      use_cases:
        - "Turbulent transient flow"
        - "Complex unsteady flows"
        - "Large-scale simulations"

  multiphase_solvers:
    interFoam:
      name: "interFoam"
      description: "Two-phase flow solver using Volume of Fluid (VOF) method"
      physics: ["incompressible", "multiphase", "transient", "VOF"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U + σκ∇α"
        continuity: "∇·U = 0"
        vof: "∂α/∂t + ∇·(Uα) + ∇·(Uᵣα(1-α)) = 0"
      required_fields: ["U", "p", "alpha"]
      optional_fields: ["phi", "nu1", "nu2", "rho1", "rho2"]
      algorithm: "PIMPLE"
      parameters:
        nOuterCorrectors: 3
        nCorrectors: 2
        interface_compression: 1.0
      use_cases:
        - "Free surface flows"
        - "Dam break analysis"
        - "Sloshing simulations"
        - "Bubble dynamics"

    interPhaseChangeFoam:
      name: "interPhaseChangeFoam"
      description: "Two-phase flow solver with phase change (evaporation/condensation)"
      physics: ["incompressible", "multiphase", "transient", "VOF", "phase_change"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U + σκ∇α + Sₘ"
        continuity: "∇·U = 0"
        vof: "∂α/∂t + ∇·(Uα) = Sₐ"
        energy: "∂T/∂t + ∇·(UT) = α∇²T + Q_phase"
      required_fields: ["U", "p", "alpha", "T"]
      optional_fields: ["phi", "nu1", "nu2", "rho1", "rho2", "Cp1", "Cp2"]
      algorithm: "PIMPLE"
      use_cases:
        - "Boiling simulations"
        - "Condensation processes"
        - "Evaporation studies"

  compressible_solvers:
    rhoPimpleFoam:
      name: "rhoPimpleFoam"
      description: "Transient solver for compressible flow using PIMPLE"
      physics: ["compressible", "transient"]
      equations:
        momentum: "∂(ρU)/∂t + ∇·(ρUU) = -∇p + ∇·τ"
        continuity: "∂ρ/∂t + ∇·(ρU) = 0"
        energy: "∂(ρe)/∂t + ∇·(ρUe) = ∇·(k∇T) + Φ"
      required_fields: ["U", "p", "T", "rho"]
      optional_fields: ["phi", "mu", "k", "Cp"]
      algorithm: "PIMPLE"
      parameters:
        nOuterCorrectors: 3
        nCorrectors: 2
        energy_coupling: "explicit"
      use_cases:
        - "High-speed flows"
        - "Gas dynamics"
        - "Compressible turbulence"

    sonicFoam:
      name: "sonicFoam"
      description: "Transient solver for compressible flows from subsonic to supersonic"
      physics: ["compressible", "transient", "supersonic"]
      equations:
        momentum: "∂(ρU)/∂t + ∇·(ρUU) = -∇p + ∇·τ"
        continuity: "∂ρ/∂t + ∇·(ρU) = 0"
        energy: "∂(ρe)/∂t + ∇·(ρUe) = ∇·(k∇T) + Φ"
      required_fields: ["U", "p", "T", "rho"]
      optional_fields: ["phi", "mu", "k", "Cp", "gamma"]
      algorithm: "PISO"
      parameters:
        nCorrectors: 2
        shock_capturing: true
      use_cases:
        - "Supersonic flows"
        - "Shock wave interactions"
        - "High-speed aerodynamics"

  heat_transfer_solvers:
    buoyantBoussinesqPimpleFoam:
      name: "buoyantBoussinesqPimpleFoam"
      description: "Transient solver for buoyant incompressible flow using Boussinesq approximation"
      physics: ["incompressible", "buoyant", "heat_transfer", "transient"]
      equations:
        momentum: "∂U/∂t + ∇·(UU) = -∇p + ν∇²U + βg(T-T₀)"
        continuity: "∇·U = 0"
        energy: "∂T/∂t + ∇·(UT) = α∇²T"
      required_fields: ["U", "p", "T"]
      optional_fields: ["phi", "nu", "alpha", "beta"]
      algorithm: "PIMPLE"
      parameters:
        nOuterCorrectors: 3
        nCorrectors: 2
        energy_coupling: "implicit"
      use_cases:
        - "Natural convection"
        - "Mixed convection"
        - "Thermal plumes"
        - "Building ventilation"

    heatTransferFoam:
      name: "heatTransferFoam"
      description: "Steady-state heat transfer solver for solid regions"
      physics: ["heat_transfer", "steady", "solid"]
      equations:
        energy: "∇·(k∇T) = Q"
      required_fields: ["T"]
      optional_fields: ["k", "Q"]
      algorithm: "SIMPLE"
      parameters:
        relaxation:
          T: 0.9
      use_cases:
        - "Solid heat conduction"
        - "Steady thermal analysis"
        - "Heat exchanger design"

# Physics Types Available in CFD.jl
physics_types:
  flow_characteristics:
    - IncompressibleFlow
    - CompressibleFlow
    - LaminarFlow
    - TurbulentFlow
    - TransientFlow
    - SteadyFlow
  
  heat_and_mass_transfer:
    - HeatTransfer
    - MultiphaseFlow
    - TurbulentHeatTransfer
    - IncompressibleHeatTransfer
  
  combined_physics:
    - IncompressibleTurbulentFlow
    - CompressibleTurbulentFlow

# Algorithm Types Available
algorithm_types:
  pressure_velocity_coupling:
    - SIMPLEAlgorithm
    - PISOAlgorithm
    - PIMPLEAlgorithm
    - SIMPLECAlgorithm
    - PIMPLECAlgorithm
  
  time_stepping:
    - ForwardEulerAlgorithm
    - BackwardEulerAlgorithm
    - CrankNicolsonAlgorithm
  
  parallel_algorithms:
    - ParallelSIMPLEAlgorithm
    - ParallelPISOAlgorithm
    - ParallelPIMPLEAlgorithm

# Boundary Condition Types (50+ available)
boundary_condition_types:
  dirichlet_conditions:
    - fixedValue
    - uniformFixedValue
    - timeVaryingMappedFixedValue
    - codedFixedValue
  
  neumann_conditions:
    - zeroGradient
    - fixedGradient
    - uniformFixedGradient
    - codedFixedGradient
  
  mixed_conditions:
    - mixed
    - directionMixed
    - fixedNormalSlip
  
  wall_conditions:
    - noSlip
    - slip
    - partialSlip
    - movingWallVelocity
    - rotatingWallVelocity
  
  inlet_conditions:
    - pressureInletVelocity
    - velocityInlet
    - massFlowInlet
    - totalPressureInlet
  
  outlet_conditions:
    - pressureOutlet
    - outletPhaseMeanVelocity
    - advective
  
  turbulence_wall_functions:
    - kqRWallFunction
    - epsilonWallFunction
    - omegaWallFunction
    - nutkWallFunction
    - nutUWallFunction

# HPC Features (Default in CFD.jl)
hpc_features:
  automatic_optimizations:
    - ghost_cell_communication: "30-40% speedup"
    - cache_optimized_matrix_assembly: "20-25% speedup"
    - simd_field_interpolation: "15-20% speedup"
    - automatic_parallelization: "variable speedup"
  
  default_behavior:
    description: "All optimizations enabled by default"
    disable_option: "optimizations=false parameter"
  
  hpc_solvers:
    - HPCOptimizedPISO
    - HPCOptimizedSIMPLE
    - HPCPerformanceMonitor
    - OptimizedCFDSolver

# Dual-Mode Architecture
user_interface:
  simple_usage:
    description: "High-level interface for end users"
    examples:
      - "CFD.solve('myCase', solver=:PISO)"
      - "CFD.list_solvers()"
      - "CFD.solver_help(:SIMPLE)"
  
  developer_interface:
    description: "Full control for CFD developers"
    examples:
      - "CFD.Development.create_custom_solver()"
      - "CFD.@register_solver MySolver definition"
      - "CFD.benchmark_solvers()"

# Auto-Case Generation
auto_case_features:
  capabilities:
    - "Automatic mesh generation"
    - "Default field initialization"
    - "Boundary condition templates"
    - "Solver-specific configuration"
    - "Physics-aware setup"
  
  usage:
    description: "Cases are auto-generated when running solvers on non-existent directories"
    example: "CFD.solve('nonExistentCase', solver=:PISO)  # Auto-generates case structure"

# Development Module Features
development_module:
  capabilities:
    - "Custom solver creation"
    - "Solver benchmarking"
    - "Performance optimization"
    - "Equation building DSL"
    - "Solver templates"
  
  tools:
    - SolverWizard
    - EquationBuilder
    - "@quick_solver macro"
    - "@test_idea macro"
    - "Performance profiling"