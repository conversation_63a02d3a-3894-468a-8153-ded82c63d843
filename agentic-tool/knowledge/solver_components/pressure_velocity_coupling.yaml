# Pressure-Velocity Coupling Algorithms
pressure_velocity_algorithms:
  SIMPLE:
    name: "Semi-Implicit Method for Pressure-Linked Equations"
    description: "Iterative algorithm for pressure-velocity coupling in steady flows"
    best_for:
      - steady_state_problems
      - RANS_simulations
      - memory_constrained_systems
    algorithm_steps:
      1: "Guess pressure field p*"
      2: "Solve momentum equations with p* → U*"
      3: "Solve pressure correction equation → p'"
      4: "Update pressure: p = p* + αp * p'"
      5: "Update velocity: U = U* + correction"
      6: "Solve other scalars (energy, turbulence)"
      7: "Check convergence, repeat if needed"
    
    relaxation_factors:
      pressure: [0.1, 0.3]
      velocity: [0.3, 0.7]
      turbulence: [0.5, 0.8]
      energy: [0.8, 1.0]
      
    convergence_criteria:
      residuals: 1e-6
      imbalances: 1e-4
      
    code_template: |
      function solve_simple!(fields, mesh, flow_model, max_iter=1000, tolerance=1e-6)
          for iter in 1:max_iter
              # 1. Solve momentum with current pressure
              solve_momentum!(fields[:U], fields[:p], flow_model, mesh)
              
              # 2. Solve pressure correction
              solve_pressure_correction!(fields[:p], fields[:U], mesh)
              
              # 3. Update fields with relaxation
              fields[:p].data .= fields[:p].data .+ 0.3 .* pressure_correction
              fields[:U].data .= fields[:U].data .+ velocity_correction
              
              # 4. Solve turbulence
              if haskey(fields, :k)
                  solve_turbulence!(fields[:k], fields[:epsilon], fields[:U], mesh)
              end
              
              # 5. Check convergence
              residual = calculate_residuals(fields)
              if residual < tolerance
                  println("SIMPLE converged in $iter iterations")
                  break
              end
          end
      end

  PISO:
    name: "Pressure-Implicit Split-Operator"
    description: "Algorithm for transient incompressible flows"
    best_for:
      - transient_simulations
      - LES_simulations
      - DNS_simulations
      - small_time_steps
    algorithm_steps:
      1: "Momentum predictor with previous time step pressure"
      2: "First pressure corrector (similar to SIMPLE)"
      3: "Second pressure corrector for better coupling"
      4: "Optional third corrector for high accuracy"
      
    typical_correctors: 2
    time_step_requirements:
      CFL_condition: "CFL < 1"
      stability: "Small time steps required"
      
    code_template: |
      function solve_piso!(fields, mesh, flow_model, dt, n_correctors=2)
          # Momentum predictor
          solve_momentum_predictor!(fields[:U], fields[:p], flow_model, mesh, dt)
          
          # Pressure-velocity corrections
          for corrector in 1:n_correctors
              # Pressure correction
              solve_pressure_correction!(fields[:p], fields[:U], mesh)
              
              # Velocity correction
              correct_velocity!(fields[:U], fields[:p], mesh)
              
              # Additional explicit corrections for higher-order accuracy
              if corrector < n_correctors
                  apply_explicit_corrections!(fields[:U], fields[:p], mesh)
              end
          end
          
          # Update other scalars
          solve_scalars!(fields, mesh, dt)
      end

  PIMPLE:
    name: "PISO-SIMPLE Hybrid"
    description: "Combination allowing larger time steps with outer iterations"
    best_for:
      - transient_with_large_time_steps
      - complex_geometries
      - multiphase_flows
      - heat_transfer_problems
    algorithm_features:
      outer_iterations: "SIMPLE-like iterations"
      inner_corrections: "PISO-like corrections"
      larger_time_steps: "CFL > 1 possible"
      
    typical_settings:
      outer_iterations: [2, 5]
      inner_correctors: [1, 3]
      max_CFL: [5.0, 20.0]
      
    code_template: |
      function solve_pimple!(fields, mesh, flow_model, dt, n_outer=3, n_correctors=2)
          for outer in 1:n_outer
              # Momentum equation
              solve_momentum!(fields[:U], fields[:p], flow_model, mesh, dt)
              
              # PISO-like corrections
              for corrector in 1:n_correctors
                  solve_pressure_correction!(fields[:p], fields[:U], mesh)
                  correct_velocity!(fields[:U], fields[:p], mesh)
              end
              
              # Check outer convergence
              if calculate_outer_residual(fields) < 1e-4
                  break
              end
          end
          
          # Solve other equations
          solve_turbulence!(fields, mesh, dt)
          solve_energy!(fields, mesh, dt)
      end

algorithm_selection_guide:
  flow_type:
    steady_incompressible: "SIMPLE"
    transient_incompressible: "PISO or PIMPLE"
    steady_compressible: "Coupled or density-based"
    transient_compressible: "Density-based"
    
  time_step_size:
    small_CFL_less_than_1: "PISO"
    moderate_CFL_1_to_10: "PIMPLE"
    large_CFL_greater_than_10: "PIMPLE with more outer iterations"
    
  computational_resources:
    limited_memory: "SIMPLE (segregated)"
    high_memory: "Coupled solver"
    parallel_efficiency: "PIMPLE"

numerical_stability:
  under_relaxation:
    purpose: "Improve stability and convergence"
    pressure: "Typically 0.1-0.3"
    velocity: "Typically 0.3-0.7"
    adaptive: "Increase factors as solution converges"
    
  time_stepping:
    explicit_CFL: "CFL < 1 for stability"
    implicit_accuracy: "Small time steps for accuracy"
    adaptive_time_step: "Adjust based on residuals and CFL"
    
  pressure_reference:
    incompressible_closed: "Required - set one cell or boundary"
    incompressible_open: "Pressure outlet provides reference"
    compressible: "Not needed - density provides compressibility"

convergence_monitoring:
  residuals:
    momentum: "< 1e-3 for engineering accuracy"
    pressure: "< 1e-3 (note: relative to largest source)"
    energy: "< 1e-6"
    turbulence: "< 1e-3"
    
  imbalances:
    mass: "< 1% of inlet mass flow"
    momentum: "< 1% of dominant momentum source"
    energy: "< 1% of heat input"
    
  monitoring_quantities:
    - "Forces and moments on bodies"
    - "Mass flow rates through boundaries"
    - "Heat transfer rates"
    - "Pressure drops"

performance_optimization:
  memory_usage:
    segregated: "Lower memory per equation"
    coupled: "Higher memory but faster convergence"
    
  parallelization:
    domain_decomposition: "Divide mesh between processors"
    load_balancing: "Equal cells per processor"
    communication_overhead: "Minimize with good partitioning"
    
  multigrid:
    algebraic_multigrid: "Accelerates pressure equation"
    geometric_multigrid: "Good for structured meshes"
    
common_problems_solutions:
  divergence:
    cause: "Too aggressive relaxation factors"
    solution: "Reduce under-relaxation factors"
    
  slow_convergence:
    cause: "Conservative relaxation factors"
    solution: "Gradually increase factors"
    
  pressure_velocity_decoupling:
    cause: "Poor mesh quality or algorithm choice"
    solution: "Improve mesh or use PIMPLE"
    
  oscillations:
    cause: "Numerical instability"
    solution: "Reduce time step or increase dissipation"