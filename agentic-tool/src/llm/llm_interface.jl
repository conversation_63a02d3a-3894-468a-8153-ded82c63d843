# src/llm/llm_interface.jl
module LLMInterface

using HTTP
using JSON3

export LLMConfig, create_llm_config, create_config_for_task
export test_llm_connection, get_available_models

struct LLMConfig
    provider::String  # "openrouter" or "local"
    model::String
    api_key::Union{String, Nothing}
    endpoint::Union{String, Nothing}
    temperature::Float64
    max_tokens::Int
    timeout::Int
    
    function LLMConfig(provider, model, api_key, endpoint, temperature, max_tokens, timeout)
        new(provider, model, api_key, endpoint, temperature, max_tokens, timeout)
    end
end

"""
    create_llm_config(; kwargs...)

Create an LLM configuration for the CFD_LLM system.

# Arguments
- `provider`: "openrouter" or "local" (default: "openrouter")
- `model`: Model name (default: "anthropic/claude-3-sonnet")  
- `api_key`: API key for OpenRouter (default: ENV["OPENROUTER_API_KEY"])
- `endpoint`: Local LLM endpoint (default: "http://localhost:11434/api/generate")
- `temperature`: Sampling temperature (default: 0.7)
- `max_tokens`: Maximum response tokens (default: 4000)
- `timeout`: Request timeout in seconds (default: 60)

# Examples
```julia
# OpenRouter configuration
config = create_llm_config(
    provider="openrouter",
    api_key="your-api-key",
    model="anthropic/claude-3-sonnet"
)

# Local Ollama configuration
config = create_llm_config(
    provider="local", 
    endpoint="http://localhost:11434/api/generate",
    model="llama2:70b"
)
```
"""
function create_llm_config(; 
    provider="openrouter",
    model="anthropic/claude-3-sonnet",
    api_key=get(ENV, "OPENROUTER_API_KEY", nothing),
    endpoint="http://localhost:11434/api/generate",
    temperature=0.7,
    max_tokens=4000,
    timeout=60
)
    # Validate provider
    if provider ∉ ["openrouter", "local"]
        error("Provider must be 'openrouter' or 'local'")
    end
    
    # Validate API key for OpenRouter
    if provider == "openrouter" && (api_key === nothing || isempty(api_key))
        @warn "OpenRouter API key not provided. Set OPENROUTER_API_KEY environment variable or pass api_key parameter."
    end
    
    # Set endpoint to nothing for OpenRouter
    if provider == "openrouter"
        endpoint = nothing
    end
    
    # Validate parameters
    if !(0.0 ≤ temperature ≤ 2.0)
        error("Temperature must be between 0.0 and 2.0")
    end
    
    if max_tokens ≤ 0
        error("max_tokens must be positive")
    end
    
    return LLMConfig(provider, model, api_key, endpoint, temperature, max_tokens, timeout)
end

"""
    create_config_for_task(task_type::Symbol)

Create optimized LLM configuration for specific CFD tasks.

# Task Types
- `:code_generation`: Uses stronger models with lower temperature for code
- `:physics_reasoning`: Uses models good at scientific reasoning  
- `:general`: Fast general-purpose model
- `:boundary_conditions`: Specialized for BC generation
- `:solver_design`: Optimized for solver configuration
"""
function create_config_for_task(task_type::Symbol)
    base_api_key = get(ENV, "OPENROUTER_API_KEY", nothing)
    
    if task_type == :code_generation
        # Use strongest model for code generation
        return create_llm_config(
            provider="openrouter",
            model="anthropic/claude-3-opus",
            api_key=base_api_key,
            temperature=0.3,  # Lower temperature for code
            max_tokens=6000,
            timeout=90
        )
    elseif task_type == :physics_reasoning
        # Good reasoning model for physics analysis
        return create_llm_config(
            provider="openrouter", 
            model="openai/gpt-4-turbo",
            api_key=base_api_key,
            temperature=0.5,
            max_tokens=4000,
            timeout=60
        )
    elseif task_type == :boundary_conditions
        # Specialized for boundary condition generation
        return create_llm_config(
            provider="openrouter",
            model="anthropic/claude-3-sonnet",
            api_key=base_api_key,
            temperature=0.4,
            max_tokens=3000,
            timeout=45
        )
    elseif task_type == :solver_design
        # For solver configuration and optimization
        return create_llm_config(
            provider="openrouter",
            model="anthropic/claude-3-sonnet",
            api_key=base_api_key,
            temperature=0.6,
            max_tokens=5000,
            timeout=75
        )
    elseif task_type == :general
        # Fast general-purpose model (try local first)
        if test_local_llm_available()
            return create_llm_config(
                provider="local",
                endpoint="http://localhost:11434/api/generate",
                model="llama2:70b",
                temperature=0.7,
                max_tokens=2000,
                timeout=45
            )
        else
            return create_llm_config(
                provider="openrouter",
                model="anthropic/claude-3-haiku",
                api_key=base_api_key,
                temperature=0.7,
                max_tokens=2000,
                timeout=30
            )
        end
    else
        @warn "Unknown task type: $task_type, using default configuration"
        return create_llm_config()
    end
end

"""
    test_llm_connection(config::LLMConfig)

Test connectivity to the configured LLM service.

Returns a dictionary with connection status and details.
"""
function test_llm_connection(config::LLMConfig)
    
    result = Dict{String,Any}(
        "connected" => false,
        "provider" => config.provider,
        "model" => config.model,
        "error" => nothing,
        "response_time" => nothing,
        "test_response" => nothing
    )
    
    test_prompt = "Hello! Please respond with 'CFD_LLM connection test successful' to confirm connectivity."
    
    try
        start_time = time()
        
        if config.provider == "openrouter"
            response = test_openrouter_connection(config, test_prompt)
        else
            response = test_local_connection(config, test_prompt)
        end
        
        end_time = time()
        
        result["connected"] = true
        result["response_time"] = end_time - start_time
        result["test_response"] = response[1:min(100, length(response))]
        
    catch e
        result["error"] = string(e)
        @warn "LLM connection test failed: $e"
    end
    
    return result
end

function test_openrouter_connection(config::LLMConfig, test_prompt::String)
    
    if config.api_key === nothing
        error("API key required for OpenRouter")
    end
    
    headers = [
        "Authorization" => "Bearer $(config.api_key)",
        "Content-Type" => "application/json",
        "HTTP-Referer" => "https://github.com/CFD_LLM",
        "X-Title" => "CFD_LLM Connection Test"
    ]
    
    body = JSON3.write(Dict(
        "model" => config.model,
        "messages" => [
            Dict("role" => "user", "content" => test_prompt)
        ],
        "max_tokens" => 100,
        "temperature" => 0.1
    ))
    
    response = HTTP.post(
        "https://openrouter.ai/api/v1/chat/completions",
        headers,
        body;
        timeout=config.timeout
    )
    
    result = JSON3.read(response.body)
    
    if haskey(result, "choices") && !isempty(result["choices"])
        return result["choices"][1]["message"]["content"]
    else
        error("Unexpected response format from OpenRouter")
    end
end

function test_local_connection(config::LLMConfig, test_prompt::String)
    
    if config.endpoint === nothing
        error("Endpoint required for local LLM")
    end
    
    # Try Ollama format first
    body = JSON3.write(Dict(
        "model" => config.model,
        "prompt" => test_prompt,
        "stream" => false,
        "options" => Dict(
            "temperature" => 0.1,
            "num_predict" => 50
        )
    ))
    
    response = HTTP.post(
        config.endpoint,
        ["Content-Type" => "application/json"],
        body;
        timeout=config.timeout
    )
    
    result = JSON3.read(response.body)
    return get(result, "response", "No response field in local LLM response")
end

"""
    test_local_llm_available()

Quick test to see if a local LLM server is available.
"""
function test_local_llm_available()
    try
        response = HTTP.get("http://localhost:11434/api/tags"; timeout=5)
        return response.status == 200
    catch
        return false
    end
end

"""
    get_available_models(config::LLMConfig)

Get list of available models from the LLM provider.
"""
function get_available_models(config::LLMConfig)
    if config.provider == "openrouter"
        return get_openrouter_models(config)
    else
        return get_local_models(config)
    end
end

function get_openrouter_models(config::LLMConfig)
    
    if config.api_key === nothing
        return ["API key required"]
    end
    
    try
        headers = [
            "Authorization" => "Bearer $(config.api_key)",
            "HTTP-Referer" => "https://github.com/CFD_LLM"
        ]
        
        response = HTTP.get(
            "https://openrouter.ai/api/v1/models",
            headers;
            timeout=30
        )
        
        result = JSON3.read(response.body)
        
        if haskey(result, "data")
            return [model["id"] for model in result["data"]]
        else
            return ["Error retrieving models"]
        end
    catch e
        @warn "Failed to retrieve OpenRouter models: $e"
        return ["Error: $e"]
    end
end

function get_local_models(config::LLMConfig)
    
    if config.endpoint === nothing
        return ["Endpoint not configured"]
    end
    
    try
        # Try Ollama API
        base_url = replace(config.endpoint, "/api/generate" => "")
        models_url = "$base_url/api/tags"
        
        response = HTTP.get(models_url; timeout=10)
        result = JSON3.read(response.body)
        
        if haskey(result, "models")
            return [model["name"] for model in result["models"]]
        else
            return ["Local server responding but no models found"]
        end
    catch e
        @warn "Failed to retrieve local models: $e"
        return ["Error: $e"]
    end
end

"""
    validate_config(config::LLMConfig)

Validate LLM configuration and return detailed status.
"""
function validate_config(config::LLMConfig)
    validation = Dict{String,Any}(
        "valid" => true,
        "warnings" => String[],
        "errors" => String[]
    )
    
    # Check provider
    if config.provider ∉ ["openrouter", "local"]
        push!(validation["errors"], "Invalid provider: $(config.provider)")
        validation["valid"] = false
    end
    
    # Check OpenRouter specific settings
    if config.provider == "openrouter"
        if config.api_key === nothing || isempty(config.api_key)
            push!(validation["errors"], "OpenRouter requires API key")
            validation["valid"] = false
        end
        
        if config.endpoint !== nothing
            push!(validation["warnings"], "Endpoint ignored for OpenRouter provider")
        end
    end
    
    # Check local LLM specific settings
    if config.provider == "local"
        if config.endpoint === nothing || isempty(config.endpoint)
            push!(validation["errors"], "Local provider requires endpoint")
            validation["valid"] = false
        end
        
        if config.api_key !== nothing
            push!(validation["warnings"], "API key ignored for local provider")
        end
    end
    
    # Check parameter ranges
    if !(0.0 ≤ config.temperature ≤ 2.0)
        push!(validation["errors"], "Temperature must be between 0.0 and 2.0")
        validation["valid"] = false
    end
    
    if config.max_tokens ≤ 0
        push!(validation["errors"], "max_tokens must be positive")
        validation["valid"] = false
    end
    
    if config.timeout ≤ 0
        push!(validation["errors"], "timeout must be positive")
        validation["valid"] = false
    end
    
    # Recommendations
    if config.temperature > 1.0
        push!(validation["warnings"], "High temperature ($(config.temperature)) may produce less coherent responses")
    end
    
    if config.max_tokens > 8000
        push!(validation["warnings"], "Very high max_tokens ($(config.max_tokens)) may be slow or expensive")
    end
    
    return validation
end

# Configuration presets for common scenarios
const PRESET_CONFIGS = Dict{Symbol,Function}(
    :development => () -> create_llm_config(
        provider="local",
        model="llama2:7b", 
        temperature=0.8,
        max_tokens=2000
    ),
    :production => () -> create_llm_config(
        provider="openrouter",
        model="anthropic/claude-3-sonnet",
        temperature=0.6,
        max_tokens=4000
    ),
    :high_accuracy => () -> create_llm_config(
        provider="openrouter", 
        model="anthropic/claude-3-opus",
        temperature=0.3,
        max_tokens=6000
    ),
    :fast => () -> create_llm_config(
        provider="openrouter",
        model="anthropic/claude-3-haiku",
        temperature=0.7,
        max_tokens=1500
    )
)

"""
    get_preset_config(preset::Symbol)

Get a predefined configuration for common use cases.

Available presets: :development, :production, :high_accuracy, :fast
"""
function get_preset_config(preset::Symbol)
    if haskey(PRESET_CONFIGS, preset)
        return PRESET_CONFIGS[preset]()
    else
        available = join(keys(PRESET_CONFIGS), ", ")
        error("Unknown preset: $preset. Available presets: $available")
    end
end

end # module LLMInterface