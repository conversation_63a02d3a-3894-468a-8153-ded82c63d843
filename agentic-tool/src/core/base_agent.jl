# src/core/base_agent.jl
module BaseAgent

using HTTP
using JSON3

export Agent, AgentCapability, AgentMemory, BaseAgentImpl
export think, query_llm, build_context
export query_openrouter, query_local_llm

abstract type Agent end

# Agent capabilities
struct AgentCapability
    name::Symbol
    description::String
    input_type::Type
    output_type::Type
end

# Agent memory for context and learning
mutable struct AgentMemory
    short_term::Vector{Any}  # Current task context
    long_term::Dict{String, Any}  # Learned patterns and knowledge
    max_short_term::Int
    
    function AgentMemory(max_short_term::Int=20)
        new(Vector{Any}(), Dict{String, Any}(), max_short_term)
    end
end

# Base agent implementation
mutable struct BaseAgentImpl <: Agent
    name::String
    capabilities::Vector{AgentCapability}
    memory::AgentMemory
    llm_config::Dict{String, Any}
    
    function BaseAgentImpl(name::String, capabilities::Vector{AgentCapability}, 
                          memory::AgentMemory, llm_config::Dict{String, Any})
        new(name, capabilities, memory, llm_config)
    end
end

# Core agent functions
function think(agent::Agent, input::Any)
    # Add to short-term memory
    push!(agent.memory.short_term, input)
    if length(agent.memory.short_term) > agent.memory.max_short_term
        popfirst!(agent.memory.short_term)
    end
    
    # Build context from memory
    context = build_context(agent)
    
    # Process with LLM
    response = query_llm(agent, context, string(input))
    
    # Store successful interactions in long-term memory
    if !isempty(response)
        patterns = get!(agent.memory.long_term, "successful_patterns", Vector{Tuple{Any,String}}())
        push!(patterns, (input, response))
        # Keep only recent successful patterns
        if length(patterns) > 100
            agent.memory.long_term["successful_patterns"] = patterns[end-50:end]
        end
    end
    
    return response
end

function build_context(agent::Agent)
    context_parts = String[]
    
    # Agent identity and capabilities
    push!(context_parts, "You are $(agent.name), a specialized CFD assistant.")
    push!(context_parts, "Your capabilities include:")
    for cap in agent.capabilities
        push!(context_parts, "- $(cap.name): $(cap.description)")
    end
    
    # Recent context from short-term memory
    if !isempty(agent.memory.short_term)
        push!(context_parts, "\nRecent context:")
        for (i, item) in enumerate(agent.memory.short_term[max(1, end-5):end])
            push!(context_parts, "$(i). $(string(item))")
        end
    end
    
    # Learned patterns from long-term memory
    if haskey(agent.memory.long_term, "successful_patterns")
        patterns = agent.memory.long_term["successful_patterns"]
        if !isempty(patterns)
            push!(context_parts, "\nRelevant successful patterns:")
            for (input, output) in patterns[max(1, end-3):end]
                push!(context_parts, "Input: $(string(input)[1:min(100, length(string(input)))])")
                push!(context_parts, "Output: $(output[1:min(200, length(output))])")
            end
        end
    end
    
    # CFD-specific guidelines
    push!(context_parts, "\nCFD Guidelines:")
    push!(context_parts, "- Always consider physics validity and conservation laws")
    push!(context_parts, "- Ensure boundary conditions are physically meaningful")
    push!(context_parts, "- Consider numerical stability and convergence")
    push!(context_parts, "- Use appropriate turbulence models for the flow regime")
    push!(context_parts, "- Validate dimensionless numbers (Re, Ma, Pr, etc.)")
    
    return join(context_parts, "\n")
end

function query_llm(agent::Agent, context::String, query::String)
    try
        if agent.llm_config["provider"] == "openrouter"
            return query_openrouter(
                agent.llm_config["api_key"],
                agent.llm_config["model"],
                context,
                query
            )
        else
            return query_local_llm(
                agent.llm_config["endpoint"],
                context,
                query
            )
        end
    catch e
        @warn "LLM query failed: $e"
        return "I apologize, but I'm unable to process your request at the moment due to a connection issue."
    end
end

function query_openrouter(api_key::String, model::String, context::String, query::String)
    headers = [
        "Authorization" => "Bearer $api_key",
        "Content-Type" => "application/json",
        "HTTP-Referer" => "https://github.com/CFD_LLM",
        "X-Title" => "CFD_LLM"
    ]
    
    # Prepare messages for chat completion
    messages = [
        Dict("role" => "system", "content" => context),
        Dict("role" => "user", "content" => query)
    ]
    
    body = JSON3.write(Dict(
        "model" => model,
        "messages" => messages,
        "temperature" => get(agent.llm_config, "temperature", 0.7),
        "max_tokens" => get(agent.llm_config, "max_tokens", 4000)
    ))
    
    try
        response = HTTP.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers,
            body;
            timeout=30
        )
        
        result = JSON3.read(response.body)
        
        if haskey(result, "choices") && !isempty(result["choices"])
            return result["choices"][1]["message"]["content"]
        else
            @warn "Unexpected OpenRouter response format: $result"
            return "I received an unexpected response format from the AI service."
        end
    catch e
        if isa(e, HTTP.Exceptions.StatusError)
            @warn "OpenRouter API error: $(e.status) - $(String(e.response.body))"
            return "I'm currently unable to connect to the AI service. Please check your API key and try again."
        else
            @warn "OpenRouter request failed: $e"
            return "I encountered an error connecting to the AI service: $e"
        end
    end
end

function query_local_llm(endpoint::String, context::String, query::String)
    # Support for Ollama and other local LLM servers
    prompt = "$context\n\nUser: $query\nAssistant:"
    
    # Try Ollama format first
    try
        body = JSON3.write(Dict(
            "model" => get(agent.llm_config, "model", "llama2"),
            "prompt" => prompt,
            "stream" => false,
            "options" => Dict(
                "temperature" => get(agent.llm_config, "temperature", 0.7),
                "num_predict" => get(agent.llm_config, "max_tokens", 2000)
            )
        ))
        
        response = HTTP.post(
            endpoint,
            ["Content-Type" => "application/json"],
            body;
            timeout=60
        )
        
        result = JSON3.read(response.body)
        return get(result, "response", "No response from local LLM")
        
    catch e
        @warn "Local LLM query failed: $e"
        
        # Try alternative format (e.g., LlamaCpp server)
        try
            alt_body = JSON3.write(Dict(
                "prompt" => prompt,
                "temperature" => 0.7,
                "n_predict" => 2000
            ))
            
            alt_response = HTTP.post(
                endpoint,
                ["Content-Type" => "application/json"],
                alt_body;
                timeout=60
            )
            
            alt_result = JSON3.read(alt_response.body)
            return get(alt_result, "content", "No response from local LLM")
            
        catch alt_e
            @warn "Alternative local LLM format also failed: $alt_e"
            return "Unable to connect to local LLM server. Please check the endpoint and model configuration."
        end
    end
end

# Utility functions for agent management
function add_capability!(agent::Agent, capability::AgentCapability)
    push!(agent.capabilities, capability)
end

function has_capability(agent::Agent, capability_name::Symbol)
    return any(cap -> cap.name == capability_name, agent.capabilities)
end

function store_knowledge!(agent::Agent, key::String, value::Any)
    agent.memory.long_term[key] = value
end

function retrieve_knowledge(agent::Agent, key::String, default=nothing)
    return get(agent.memory.long_term, key, default)
end

# Learning and adaptation functions
function learn_from_feedback!(agent::Agent, input::Any, output::String, feedback::Dict{String,Any})
    feedback_key = "feedback_$(hash(input))"
    agent.memory.long_term[feedback_key] = Dict(
        "input" => input,
        "output" => output,
        "feedback" => feedback,
        "timestamp" => now()
    )
    
    # Update success patterns based on feedback
    if get(feedback, "success", false)
        patterns = get!(agent.memory.long_term, "successful_patterns", Vector{Tuple{Any,String}}())
        push!(patterns, (input, output))
    elseif get(feedback, "error", false)
        error_patterns = get!(agent.memory.long_term, "error_patterns", Vector{Tuple{Any,String}}())
        push!(error_patterns, (input, output))
    end
end

function get_learning_stats(agent::Agent)
    return Dict(
        "short_term_memory_size" => length(agent.memory.short_term),
        "long_term_keys" => collect(keys(agent.memory.long_term)),
        "successful_patterns" => length(get(agent.memory.long_term, "successful_patterns", [])),
        "error_patterns" => length(get(agent.memory.long_term, "error_patterns", []))
    )
end

end # module BaseAgent