# src/agents/orchestrator_agent.jl
module OrchestratorAgent

using ..BaseAgent
using ..PhysicsAgent
using ..CodeSynthesisAgent
using ..RetrievalEngine
using JSON3

# Define types first
struct AgentTask
    id::String
    type::Symbol  # :understand_physics, :generate_code, :validate, :analyze, :integrate
    description::String
    input::Any
    output::Union{Any, Nothing}
    status::Symbol  # :pending, :in_progress, :completed, :failed
    depends_on::Vector{String}
    assigned_agent::Symbol  # :physics, :code, :orchestrator
    priority::Int
    estimated_time::Float64
end

struct Response
    type::Symbol  # :code, :explanation, :analysis, :error, :question
    content::String
    metadata::Dict{String,Any}
    confidence::Float64
    suggestions::Vector{String}
    generated_code::Union{String, Nothing}
    validation_results::Union{Dict, Nothing}
end

struct TaskPlan
    id::String
    tasks::Vector{AgentTask}
    dependencies::Dict{String, Vector{String}}
    estimated_duration::Float64
    complexity_level::Symbol  # :simple, :moderate, :complex, :expert
end

struct SimulationOutcome
    success::Bool
    metrics::Dict{String, Float64}
    errors::Vector{String}
    performance_data::Dict{String, Any}
    physics_validation::Dict{String, Any}
end

export Orchestrator, create_orchestrator
export Response, TaskPlan, AgentTask, SimulationOutcome
export handle_user_request, learn_from_outcome, coordinate_agents

mutable struct Orchestrator <: Agent
    base::BaseAgentImpl
    physics_agent::PhysicsUnderstandingAgent
    code_agent::CodeSynthesizer
    retriever::PhysicsAwareRetriever
    active_tasks::Dict{String, AgentTask}
    session_history::Vector{Dict{String,Any}}
end

function create_orchestrator(llm_config::Union{Dict, Any})
    # Convert LLMConfig to Dict if needed
    config_dict = if isa(llm_config, Dict)
        llm_config
    else
        Dict{String, Any}(
            "provider" => llm_config.provider,
            "model" => llm_config.model,
            "api_key" => llm_config.api_key,
            "endpoint" => llm_config.endpoint,
            "temperature" => llm_config.temperature,
            "max_tokens" => llm_config.max_tokens,
            "timeout" => llm_config.timeout
        )
    end
    
    base = BaseAgentImpl(
        "Orchestrator",
        [
            AgentCapability(:handle_request, "Process user requests", String, Response),
            AgentCapability(:coordinate_agents, "Coordinate specialist agents", TaskPlan, Dict),
            AgentCapability(:plan_tasks, "Create execution plans", String, TaskPlan),
            AgentCapability(:validate_results, "Validate and integrate results", Dict, Response),
            AgentCapability(:learn_patterns, "Learn from user interactions", Dict, Nothing)
        ],
        AgentMemory(30),
        config_dict
    )
    
    # Create specialist agents
    physics = create_physics_agent(config_dict)
    code = create_code_synthesizer(config_dict)
    retriever = create_retriever()
    
    return Orchestrator(base, physics, code, retriever, Dict{String,AgentTask}(), Vector{Dict{String,Any}}())
end

function handle_user_request(orchestrator::Orchestrator, request::String)
    # Record request in session history
    request_record = Dict{String,Any}(
        "timestamp" => time(),
        "request" => request,
        "type" => "user_input"
    )
    push!(orchestrator.session_history, request_record)
    
    try
        # 1. Analyze and understand the request
        analysis = analyze_request(orchestrator, request)
        
        # 2. Retrieve relevant knowledge
        relevant_docs = retrieve(orchestrator.retriever, request, 8)
        context = build_context_from_docs(relevant_docs)
        
        # 3. Create task plan
        task_plan = create_task_plan(orchestrator, request, analysis, context)
        
        # 4. Execute task plan
        result = execute_task_plan(orchestrator, task_plan, context)
        
        # 5. Validate and format response
        response = validate_and_format_response(orchestrator, result, task_plan)
        
        # 6. Record response in history
        response_record = Dict{String,Any}(
            "timestamp" => time(),
            "response" => response,
            "task_plan_id" => task_plan.id,
            "type" => "response"
        )
        push!(orchestrator.session_history, response_record)
        
        return response
        
    catch e
        @warn "Error handling user request: $e"
        error_response = Response(
            :error,
            "I apologize, but I encountered an error processing your request: $e",
            Dict("error" => string(e)),
            0.1,
            ["Please try rephrasing your request", "Check if all required parameters are provided"],
            nothing,
            nothing
        )
        return error_response
    end
end

function analyze_request(orchestrator::Orchestrator, request::String)
    analysis_prompt = """
    Analyze this CFD-related request and determine the appropriate response strategy:

    User Request: "$request"

    Classify the request type and provide a structured analysis:

    {
        "request_type": "boundary_condition|solver_config|physics_analysis|code_generation|troubleshooting|explanation|optimization",
        "complexity": "simple|moderate|complex|expert",
        "required_expertise": ["physics", "coding", "numerical_methods", "post_processing"],
        "key_concepts": ["list of CFD concepts mentioned"],
        "deliverables": ["what the user expects as output"],
        "information_gaps": ["what additional info might be needed"],
        "suggested_approach": "step-by-step approach to address the request",
        "estimated_time": "time estimate in minutes",
        "agent_coordination": {
            "primary_agent": "physics|code|orchestrator",
            "supporting_agents": ["list of other agents needed"],
            "task_sequence": ["ordered list of major tasks"]
        }
    }

    Focus on understanding what the user truly needs and the best way to deliver it.
    """
    
    response = think(orchestrator.base, analysis_prompt)
    
    try
        return JSON3.read(response)
    catch e
        @warn "Failed to parse request analysis: $e"
        # Fallback analysis
        return Dict(
            "request_type" => "general",
            "complexity" => "moderate",
            "required_expertise" => ["physics"],
            "key_concepts" => [],
            "deliverables" => ["explanation"],
            "information_gaps" => [],
            "suggested_approach" => "direct response",
            "estimated_time" => 5,
            "agent_coordination" => Dict(
                "primary_agent" => "orchestrator",
                "supporting_agents" => [],
                "task_sequence" => ["respond"]
            )
        )
    end
end

function create_task_plan(orchestrator::Orchestrator, request::String, analysis::Dict, context::String)
    task_plan_id = "plan_$(hash(request) % 10000)"
    tasks = Task[]
    dependencies = Dict{String, Vector{String}}()
    
    # Determine task sequence based on analysis
    request_type = get(analysis, "request_type", "general")
    complexity = Symbol(get(analysis, "complexity", "moderate"))
    
    if request_type == "boundary_condition"
        # BC generation workflow
        task1 = AgentTask("understand_physics", :understand_physics, "Analyze BC physics requirements", 
                    request, nothing, :pending, String[], :physics, 1, 2.0)
        task2 = AgentTask("generate_bc_code", :generate_code, "Generate boundary condition code",
                    nothing, nothing, :pending, ["understand_physics"], :code, 2, 5.0)
        task3 = AgentTask("validate_bc", :validate, "Validate generated BC",
                    nothing, nothing, :pending, ["generate_bc_code"], :physics, 3, 2.0)
        
        tasks = [task1, task2, task3]
        dependencies = Dict(
            "generate_bc_code" => ["understand_physics"],
            "validate_bc" => ["generate_bc_code"]
        )
        
    elseif request_type == "solver_config"
        # Solver configuration workflow
        task1 = AgentTask("analyze_flow", :analyze, "Analyze flow regime and requirements",
                    request, nothing, :pending, String[], :physics, 1, 3.0)
        task2 = AgentTask("design_solver", :generate_code, "Design solver configuration",
                    nothing, nothing, :pending, ["analyze_flow"], :code, 2, 4.0)
        task3 = AgentTask("validate_config", :validate, "Validate solver configuration",
                    nothing, nothing, :pending, ["design_solver"], :physics, 3, 2.0)
        
        tasks = [task1, task2, task3]
        dependencies = Dict(
            "design_solver" => ["analyze_flow"],
            "validate_config" => ["design_solver"]
        )
        
    elseif request_type == "physics_analysis"
        # Physics analysis workflow
        task1 = AgentTask("physics_understanding", :understand_physics, "Analyze physics problem",
                    request, nothing, :pending, String[], :physics, 1, 4.0)
        task2 = AgentTask("provide_analysis", :analyze, "Provide detailed physics analysis",
                    nothing, nothing, :pending, ["physics_understanding"], :orchestrator, 2, 3.0)
        
        tasks = [task1, task2]
        dependencies = Dict(
            "provide_analysis" => ["physics_understanding"]
        )
        
    elseif request_type == "code_generation"
        # General code generation workflow
        task1 = AgentTask("understand_requirements", :understand_physics, "Understand code requirements",
                    request, nothing, :pending, String[], :physics, 1, 2.0)
        task2 = AgentTask("generate_code", :generate_code, "Generate CFD code",
                    nothing, nothing, :pending, ["understand_requirements"], :code, 2, 6.0)
        task3 = AgentTask("test_integration", :integrate, "Test CFD.jl integration",
                    nothing, nothing, :pending, ["generate_code"], :code, 3, 3.0)
        
        tasks = [task1, task2, task3]
        dependencies = Dict(
            "generate_code" => ["understand_requirements"],
            "test_integration" => ["generate_code"]
        )
        
    else
        # Default workflow for general requests
        task1 = AgentTask("process_request", :analyze, "Process and respond to request",
                    request, nothing, :pending, String[], :orchestrator, 1, 3.0)
        tasks = [task1]
        dependencies = Dict{String, Vector{String}}()
    end
    
    # Estimate total duration
    total_duration = sum(task.estimated_time for task in tasks)
    
    return TaskPlan(task_plan_id, tasks, dependencies, total_duration, complexity)
end

function execute_task_plan(orchestrator::Orchestrator, plan::TaskPlan, context::String)
    results = Dict{String, Any}()
    
    # Execute tasks in dependency order
    completed_tasks = Set{String}()
    
    while length(completed_tasks) < length(plan.tasks)
        # Find tasks ready to execute
        ready_tasks = filter(plan.tasks) do task
            task.status == :pending && 
            all(dep -> dep in completed_tasks, task.depends_on)
        end
        
        if isempty(ready_tasks)
            @warn "No ready tasks found, possible circular dependency"
            break
        end
        
        # Execute ready tasks
        for task in ready_tasks
            @info "Executing task: $(task.id) - $(task.description)"
            task.status = :in_progress
            orchestrator.active_tasks[task.id] = task
            
            try
                # Execute task based on type and assigned agent
                task_result = execute_task(orchestrator, task, results, context)
                task.output = task_result
                task.status = :completed
                results[task.id] = task_result
                push!(completed_tasks, task.id)
                
                delete!(orchestrator.active_tasks, task.id)
                @info "Completed task: $(task.id)"
                
            catch e
                @warn "Task $(task.id) failed: $e"
                task.status = :failed
                task.output = Dict("error" => string(e))
                results[task.id] = task.output
                delete!(orchestrator.active_tasks, task.id)
            end
        end
    end
    
    return results
end

function execute_task(orchestrator::Orchestrator, task::Task, previous_results::Dict, context::String)
    if task.assigned_agent == :physics
        return execute_physics_task(orchestrator.physics_agent, task, previous_results, context)
    elseif task.assigned_agent == :code
        return execute_code_task(orchestrator.code_agent, task, previous_results, context)
    else
        return execute_orchestrator_task(orchestrator, task, previous_results, context)
    end
end

function execute_physics_task(physics_agent::PhysicsUnderstandingAgent, task::Task, 
                             previous_results::Dict, context::String)
    if task.type == :understand_physics
        return understand_problem(physics_agent, string(task.input))
    elseif task.type == :analyze
        # Convert input to appropriate format for flow analysis
        conditions = Dict{Symbol,Any}(:description => string(task.input))
        return analyze_flow_regime(physics_agent, conditions)
    elseif task.type == :validate
        # Use previous result as validation input
        validation_input = Dict{Symbol,Any}()
        for (key, result) in previous_results
            if result isa PhysicsProblem
                validation_input[:physics_problem] = result
            elseif isa(result, String) && occursin("function", result)
                validation_input[:generated_code] = result
            end
        end
        return validate_physics_consistency(physics_agent, validation_input)
    else
        error("Unknown physics task type: $(task.type)")
    end
end

function execute_code_task(code_agent::CodeSynthesizer, task::Task, 
                          previous_results::Dict, context::String)
    if task.type == :generate_code
        # Determine what type of code to generate based on previous results
        for (key, result) in previous_results
            if result isa PhysicsProblem
                # Generate boundary condition based on physics problem
                bc_spec = create_bc_spec_from_problem(result)
                return generate_boundary_condition(code_agent, bc_spec)
            elseif isa(result, Dict) && haskey(result, "regime_analysis")
                # Generate solver based on flow analysis
                solver_spec = create_solver_spec_from_analysis(result)
                return compose_solver(code_agent, solver_spec)
            end
        end
        
        # Fallback: general code generation
        return generate_cfd_integration_code(code_agent, Dict(:request => task.input))
        
    elseif task.type == :integrate
        # Test CFD.jl integration
        code = ""
        for (key, result) in previous_results
            if isa(result, String) && (occursin("function", result) || occursin("using", result))
                code = result
                break
            end
        end
        
        return test_cfd_integration(code)
        
    else
        error("Unknown code task type: $(task.type)")
    end
end

function execute_orchestrator_task(orchestrator::Orchestrator, task::Task, 
                                  previous_results::Dict, context::String)
    if task.type == :analyze
        # Comprehensive analysis using all available information
        analysis_prompt = """
        Provide a comprehensive analysis for this CFD request:
        
        Original Request: $(task.input)
        
        Available Context: $context
        
        Previous Analysis Results:
        $(format_previous_results(previous_results))
        
        Please provide:
        1. Summary of the physics involved
        2. Key considerations and challenges
        3. Recommended approach
        4. Important parameters and settings
        5. Common pitfalls to avoid
        6. Expected outcomes and validation criteria
        
        Be specific and actionable in your recommendations.
        """
        
        return think(orchestrator.base, analysis_prompt)
        
    else
        error("Unknown orchestrator task type: $(task.type)")
    end
end

function validate_and_format_response(orchestrator::Orchestrator, results::Dict, plan::TaskPlan)
    # Determine response type based on results
    response_type = :explanation
    content = ""
    generated_code = nothing
    validation_results = nothing
    suggestions = String[]
    confidence = 0.8
    
    # Process results to create unified response
    for (task_id, result) in results
        if isa(result, String) && (occursin("function", result) || occursin("using", result))
            response_type = :code
            generated_code = result
            content = "Generated CFD code based on your requirements:\n\n```julia\n$result\n```"
        elseif result isa PhysicsProblem
            if isempty(content)
                content = "Physics Analysis:\n"
            end
            content *= "\nDomain: $(result.domain)\n"
            content *= "Phenomena: $(join(result.phenomena, ", "))\n"
            content *= "Boundary Conditions: $(join(result.boundary_conditions, ", "))\n"
        elseif result isa ValidationResult
            validation_results = Dict(
                "passed" => result.passed,
                "score" => result.score,
                "violations" => result.violations,
                "suggestions" => result.suggestions
            )
            confidence = result.confidence
            suggestions = result.suggestions
        elseif isa(result, Dict) && haskey(result, "regime_analysis")
            if isempty(content)
                content = "Flow Analysis:\n"
            end
            content *= "\n$(result["regime_analysis"])\n"
        elseif isa(result, String)
            if isempty(content)
                content = result
            else
                content *= "\n\n$result"
            end
        end
    end
    
    # Add suggestions based on task complexity
    if plan.complexity_level == :complex
        push!(suggestions, "Consider running validation tests before deployment")
        push!(suggestions, "Monitor convergence carefully for complex physics")
    end
    
    metadata = Dict{String,Any}(
        "task_plan_id" => plan.id,
        "complexity" => plan.complexity_level,
        "execution_time" => plan.estimated_duration,
        "tasks_completed" => length([t for t in plan.tasks if t.status == :completed])
    )
    
    return Response(response_type, content, metadata, confidence, suggestions, 
                   generated_code, validation_results)
end

function build_context_from_docs(relevant_docs::Vector{Document})
    if isempty(relevant_docs)
        return "No relevant documentation found."
    end
    
    context_parts = String["Relevant CFD Knowledge:"]
    
    for (i, doc) in enumerate(relevant_docs[1:min(5, length(relevant_docs))])
        push!(context_parts, "\n$(i). $(doc.type) - $(doc.content[1:min(300, length(doc.content))])")
        if haskey(doc.metadata, :category)
            push!(context_parts, "   Category: $(doc.metadata[:category])")
        end
    end
    
    return join(context_parts, "\n")
end

function learn_from_outcome(orchestrator::Orchestrator, 
                           request::String, 
                           response::Response, 
                           outcome::SimulationOutcome)
    # Create learning record
    learning_record = Dict{String,Any}(
        "timestamp" => time(),
        "request" => request,
        "response_type" => response.type,
        "response_confidence" => response.confidence,
        "outcome_success" => outcome.success,
        "metrics" => outcome.metrics,
        "type" => "learning_record"
    )
    
    # Add to session history
    push!(orchestrator.session_history, learning_record)
    
    # Update agent memories based on outcome
    if outcome.success
        # Store successful pattern
        successful_pattern = (request, response.content)
        patterns = get!(orchestrator.base.memory.long_term, "successful_patterns", Vector{Tuple{String,String}}())
        push!(patterns, successful_pattern)
        
        # Update physics agent if physics-related
        if response.type == :code && response.generated_code !== nothing
            physics_patterns = get!(orchestrator.physics_agent.base.memory.long_term, "successful_code_patterns", Vector{Tuple{String,String}}())
            push!(physics_patterns, (request, response.generated_code))
        end
        
        # Update code agent
        if response.generated_code !== nothing
            code_patterns = get!(orchestrator.code_agent.base.memory.long_term, "successful_generations", Vector{Tuple{String,String}}())
            push!(code_patterns, (request, response.generated_code))
        end
    else
        # Store failure for learning
        failure_pattern = (request, outcome.errors)
        failures = get!(orchestrator.base.memory.long_term, "failure_patterns", Vector{Tuple{String,Vector{String}}}())
        push!(failures, failure_pattern)
    end
    
    # Add example to retriever if successful
    if outcome.success && response.generated_code !== nothing
        doc_content = """
        Request: $request
        Generated Code: $(response.generated_code)
        Outcome: Success
        Performance: $(outcome.metrics)
        """
        
        add_document!(
            orchestrator.retriever,
            doc_content,
            :example,
            Dict(:success => true, :request_type => response.type),
            extract_physics_concepts(request)
        )
    end
end

# Helper functions
function create_bc_spec_from_problem(problem::PhysicsProblem)
    # Extract BC specification from physics problem
    physics_type = :velocity  # Default
    if :heat_transfer in problem.phenomena
        physics_type = :temperature
    elseif :pressure in problem.phenomena
        physics_type = :pressure
    end
    
    boundary_type = :dirichlet  # Default
    if any(bc -> occursin("wall", bc), problem.boundary_conditions)
        boundary_type = :wall
    elseif any(bc -> occursin("inlet", bc), problem.boundary_conditions)
        boundary_type = :inlet
    end
    
    return BCSpec(
        physics_type,
        boundary_type,
        string(physics_type),
        problem.description,
        nothing,  # Will be determined by agent
        problem.boundary_conditions,
        false,    # time_dependent
        false,    # space_dependent
        [:mass, :momentum],  # conservation_requirements
        "stable",
        1000      # mesh_size_estimate
    )
end

function create_solver_spec_from_analysis(analysis::Dict)
    physics = [:incompressible]  # Default
    algorithms = [:simple]       # Default
    
    if haskey(analysis, "turbulence_required") && analysis["turbulence_required"]
        push!(physics, :turbulent)
    end
    
    if haskey(analysis, "compressibility_effects") && analysis["compressibility_effects"]
        physics[1] = :compressible
    end
    
    return SolverSpec(
        physics,
        algorithms,
        "balanced accuracy and performance",
        :steady,  # time_scheme
        Dict(:convection => :upwind, :diffusion => :central),  # spatial_schemes
        BCSpec[]  # boundary_conditions - empty for now
    )
end

function test_cfd_integration(code::String)
    # Simplified integration test
    integration_result = Dict{String,Any}(
        "syntax_valid" => check_julia_syntax(code),
        "cfd_imports" => occursin("CFD", code),
        "has_functions" => occursin("function", code),
        "integration_score" => 0.8
    )
    
    return integration_result
end

function format_previous_results(results::Dict)
    formatted = String[]
    for (task_id, result) in results
        result_summary = string(result)[1:min(200, length(string(result)))]
        push!(formatted, "Task $task_id: $result_summary")
    end
    return join(formatted, "\n")
end

function check_julia_syntax(code::String)
    try
        Meta.parse(code)
        return true
    catch
        return false
    end
end

end # module OrchestratorAgent