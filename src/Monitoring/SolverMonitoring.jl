"""
# SolverMonitoring.jl
# 
# Comprehensive solver monitoring module for CFD.jl
# Provides residual tracking, progress visualization, and convergence detection
# 
# Architecture:
# - MonitoringSystem: Main interface and state management
# - ResidualTracker: Handles residual computation and history
# - ProgressDisplay: Progress bars and real-time feedback
# - ConvergenceDetector: Adaptive convergence criteria
# - PlottingInterface: Real-time plotting (optional)
"""

module SolverMonitoring

using Printf
using LinearAlgebra
using Statistics

# Optional plotting support (graceful fallback if not available)
const HAS_PLOTS = try
    @eval using Plots
    true
catch
    false
end

# Core monitoring types
export MonitoringSystem, ResidualTracker, ProgressDisplay, ConvergenceDetector
export monitor!, update_residuals!, check_convergence!, show_progress!
export create_monitoring_system, finalize_monitoring!, register_residual!

# ============================================================================
# Core Data Structures
# ============================================================================

"""
Stores residual history for a specific field/equation
"""
mutable struct ResidualHistory
    name::String
    values::Vector{Float64}
    times::Vector{Float64}
    target_tolerance::Float64
    converged::Bool
    
    function ResidualHistory(name::String, tolerance::Float64=1e-6)
        new(name, Float64[], Float64[], tolerance, false)
    end
end

"""
Real-time progress tracking with performance metrics
"""
mutable struct ProgressMetrics
    start_time::Float64
    iteration::Int
    max_iterations::Int
    residual_reduction::Float64
    convergence_rate::Float64
    estimated_time_remaining::Float64
    
    function ProgressMetrics(max_iter::Int)
        new(time(), 0, max_iter, 1.0, 0.0, 0.0)
    end
end

"""
Adaptive convergence detection with multiple criteria
"""
mutable struct ConvergenceState
    absolute_tolerance::Float64
    relative_tolerance::Float64
    stagnation_threshold::Int
    min_iterations::Int
    max_iterations::Int
    
    # Internal state
    stagnation_count::Int
    initial_residual::Float64
    converged::Bool
    convergence_reason::String
    
    function ConvergenceState(;
        abs_tol=1e-6,
        rel_tol=1e-8,
        stagnation=50,
        min_iter=10,
        max_iter=1000
    )
        new(abs_tol, rel_tol, stagnation, min_iter, max_iter,
            0, 0.0, false, "")
    end
end

"""
Main monitoring system that orchestrates all components
"""
mutable struct MonitoringSystem
    residuals::Dict{String, ResidualHistory}
    progress::ProgressMetrics
    convergence::ConvergenceState
    display_enabled::Bool
    plotting_enabled::Bool
    output_frequency::Int
    
    # Internal state
    iteration_count::Int
    last_output_time::Float64
    
    function MonitoringSystem(max_iterations::Int; 
                            display=true, 
                            plotting=HAS_PLOTS,
                            output_freq=10,
                            convergence_options...)
        residuals = Dict{String, ResidualHistory}()
        progress = ProgressMetrics(max_iterations)
        convergence = ConvergenceState(; max_iter=max_iterations, convergence_options...)
        
        new(residuals, progress, convergence, display, plotting, output_freq,
            0, time())
    end
end

# ============================================================================
# Core Interface Functions
# ============================================================================

"""
    create_monitoring_system(max_iterations; options...)

Create a comprehensive monitoring system for CFD solvers.

# Arguments
- `max_iterations::Int`: Maximum number of solver iterations
- `display::Bool=true`: Enable progress display
- `plotting::Bool=auto`: Enable real-time plotting (auto-detected)
- `output_freq::Int=10`: Output frequency for progress updates
- `abs_tol::Float64=1e-6`: Absolute convergence tolerance
- `rel_tol::Float64=1e-8`: Relative convergence tolerance
- `min_iter::Int=10`: Minimum iterations before convergence check
- `stagnation::Int=50`: Iterations before declaring stagnation

# Returns
- `MonitoringSystem`: Configured monitoring system

# Example
# ```julia
# monitor = create_monitoring_system(1000, abs_tol=1e-8, display=true)
# register_residual!(monitor, "U", 1e-6) 
# register_residual!(monitor, "p", 1e-8)
#
# for iter in 1:1000
#     # Solve timestep...
#     
#     update_residuals!(monitor, "U", velocity_residual)
#     update_residuals!(monitor, "p", pressure_residual)
#     
#     if check_convergence!(monitor)
#         println("Converged at iteration \$iter")
#         break
#     end
#     
#     show_progress!(monitor)
# end
#
# finalize_monitoring!(monitor)
# ```
"""
function create_monitoring_system(max_iterations::Int; kwargs...)
    system = MonitoringSystem(max_iterations; kwargs...)
    
    if system.display_enabled
        println("┌" * "─"^60 * "┐")
        println("│" * " "^18 * "CFD Solver Monitor" * " "^18 * "│")
        println("├" * "─"^60 * "┤")
        println("│ Max Iterations: $(lpad(max_iterations, 8))                      │")
        println("│ Convergence Tol: $(system.convergence.absolute_tolerance)                     │")
        println("│ Progress Updates: Every $(system.output_frequency) iterations            │")
        if system.plotting_enabled
            println("│ Real-time Plotting: Enabled                         │")
        end
        println("└" * "─"^60 * "┘")
        println()
    end
    
    return system
end

"""
Register a residual field for monitoring
"""
function register_residual!(system::MonitoringSystem, name::String, tolerance::Float64=1e-6)
    system.residuals[name] = ResidualHistory(name, tolerance)
    
    if system.display_enabled
        println("📊 Registered residual monitor: '$name' (tolerance: $tolerance)")
    end
end

"""
Update residual value and check for convergence
"""
function update_residuals!(system::MonitoringSystem, name::String, residual_value::Float64, time_value::Float64=time())
    if !haskey(system.residuals, name)
        register_residual!(system, name)
    end
    
    history = system.residuals[name]
    push!(history.values, residual_value)
    push!(history.times, time_value)
    
    # Check individual field convergence
    if residual_value < history.target_tolerance
        history.converged = true
    end
    
    # Update global convergence state
    if length(history.values) == 1
        system.convergence.initial_residual = max(system.convergence.initial_residual, residual_value)
    end
end

"""
Comprehensive convergence check with multiple criteria
"""
function check_convergence!(system::MonitoringSystem)
    system.iteration_count += 1
    system.progress.iteration = system.iteration_count
    
    # Don't check convergence before minimum iterations
    if system.iteration_count < system.convergence.min_iterations
        return false
    end
    
    # Check maximum iterations
    if system.iteration_count >= system.convergence.max_iterations
        system.convergence.converged = true
        system.convergence.convergence_reason = "Maximum iterations reached"
        return true
    end
    
    # Check if all residuals converged
    all_converged = true
    max_residual = 0.0
    
    for (name, history) in system.residuals
        if !isempty(history.values)
            current_residual = history.values[end]
            max_residual = max(max_residual, current_residual)
            
            # Absolute tolerance check
            if current_residual > history.target_tolerance
                all_converged = false
            end
        else
            all_converged = false
        end
    end
    
    # Check relative tolerance (reduction from initial)
    if system.convergence.initial_residual > 0
        relative_reduction = max_residual / system.convergence.initial_residual
        if relative_reduction < system.convergence.relative_tolerance
            system.convergence.converged = true
            system.convergence.convergence_reason = "Relative tolerance achieved"
            return true
        end
    end
    
    # Check absolute tolerance
    if all_converged
        system.convergence.converged = true
        system.convergence.convergence_reason = "All residuals converged"
        return true
    end
    
    # Check for stagnation
    if length(system.residuals) > 0
        # Get the primary residual (first one)
        primary_residual = first(values(system.residuals))
        if length(primary_residual.values) > system.convergence.stagnation_threshold
            recent_values = primary_residual.values[end-system.convergence.stagnation_threshold+1:end]
            if maximum(recent_values) - minimum(recent_values) < 1e-12
                system.convergence.converged = true
                system.convergence.convergence_reason = "Solution stagnated"
                return true
            end
        end
    end
    
    return false
end

"""
Display progress with intuitive progress bar and metrics
"""
function show_progress!(system::MonitoringSystem)
    if !system.display_enabled
        return
    end
    
    # Limit output frequency
    current_time = time()
    if current_time - system.last_output_time < 1.0 && system.iteration_count % system.output_frequency != 0
        return
    end
    system.last_output_time = current_time
    
    # Calculate progress metrics
    progress_percent = 100.0 * system.iteration_count / system.progress.max_iterations
    elapsed_time = current_time - system.progress.start_time
    
    # Estimate time remaining
    if system.iteration_count > 0
        time_per_iter = elapsed_time / system.iteration_count
        remaining_iters = system.progress.max_iterations - system.iteration_count
        estimated_remaining = time_per_iter * remaining_iters
    else
        estimated_remaining = 0.0
    end
    
    # Create progress bar
    bar_width = 30
    filled_width = Int(round(bar_width * progress_percent / 100))
    bar = "█"^filled_width * "░"^(bar_width - filled_width)
    
    # Display header periodically
    if system.iteration_count == 1 || system.iteration_count % (system.output_frequency * 5) == 0
        println()
        println("┌" * "─"^80 * "┐")
        println("│ Iter │ Progress [" * " "^bar_width * "] │ Residuals │ Time │ ETA  │")
        println("├" * "─"^80 * "┤")
    end
    
    # Format residuals display
    residual_str = ""
    if !isempty(system.residuals)
        max_residual = 0.0
        for history in values(system.residuals)
            if !isempty(history.values)
                max_residual = max(max_residual, history.values[end])
            end
        end
        sci_str = scientific_notation(max_residual)
        residual_str = length(sci_str) >= 9 ? sci_str[1:9] : rpad(sci_str, 9)
    else
        residual_str = "N/A      "
    end
    
    # Format time
    time_str = format_time(elapsed_time)
    eta_str = format_time(estimated_remaining)
    
    # Display progress line
    @printf("│%5d │ %5.1f%% [%s] │ %s │ %4s │ %4s │\n",
        system.iteration_count, progress_percent, bar, residual_str, time_str, eta_str)
    
    # Show individual residuals in detail mode
    if system.iteration_count % (system.output_frequency * 2) == 0
        for (name, history) in system.residuals
            if !isempty(history.values)
                current = history.values[end]
                status = history.converged ? "✓" : "○"
                current_str = scientific_notation(current)
                target_str = scientific_notation(history.target_tolerance)
                current_display = length(current_str) >= 9 ? current_str[1:9] : rpad(current_str, 9)
                target_display = length(target_str) >= 9 ? target_str[1:9] : rpad(target_str, 9)
                @printf("│      │ %s %-8s: %s (target: %s)     │\n",
                    status, name, current_display, target_display)
            end
        end
        println("├" * "─"^80 * "┤")
    end
    
    flush(stdout)
end

"""
Finalize monitoring and display summary
"""
function finalize_monitoring!(system::MonitoringSystem)
    if !system.display_enabled
        return
    end
    
    elapsed_time = time() - system.progress.start_time
    
    println()
    println("┌" * "─"^60 * "┐")
    println("│" * " "^18 * "Convergence Summary" * " "^19 * "│")
    println("├" * "─"^60 * "┤")
    
    if system.convergence.converged
        println("│ Status: ✓ CONVERGED                                │")
        println("│ Reason: $(system.convergence.convergence_reason)")
        println("│$(repeat(" ", 60 - length(system.convergence.convergence_reason) - 9))│")
    else
        println("│ Status: ○ NOT CONVERGED                            │")
    end
    
    println("│ Iterations: $(lpad(system.iteration_count, 8))                           │")
    println("│ Total Time: $(lpad(format_time(elapsed_time), 8))                           │")
    
    if system.iteration_count > 0
        avg_time = elapsed_time / system.iteration_count
        println("│ Avg Time/Iter: $(lpad(format_time(avg_time), 6))                        │")
    end
    
    println("├" * "─"^60 * "┤")
    
    # Final residual summary
    for (name, history) in system.residuals
        if !isempty(history.values)
            final_residual = history.values[end]
            status = history.converged ? "✓" : "✗"
            reduction = length(history.values) > 1 ? 
                history.values[1] / history.values[end] : 1.0
            
            final_str = scientific_notation(final_residual)
            final_display = length(final_str) >= 9 ? final_str[1:9] : rpad(final_str, 9)
            @printf("│ %s %-8s: %s (reduction: %.1e)      │\n",
                status, name, final_display, reduction)
        end
    end
    
    println("└" * "─"^60 * "┘")
    
    # Generate plots if enabled
    if system.plotting_enabled && HAS_PLOTS
        plot_residuals(system)
    end
end

"""
Generate real-time residual plots (optional)
"""
function plot_residuals(system::MonitoringSystem)
    if !HAS_PLOTS
        println("⚠️  Plotting not available - install Plots.jl for visualization")
        return
    end
    
    try
        if isempty(system.residuals)
            return
        end
        
        # Create residual plot
        p = Plots.plot(title="Residual Convergence History", 
                xlabel="Iteration", ylabel="Residual", 
                yscale=:log10, legend=:topright,
                size=(800, 500))
        
        for (name, history) in system.residuals
            if length(history.values) > 1
                iterations = 1:length(history.values)
                Plots.plot!(p, iterations, history.values, 
                     label=name, linewidth=2, marker=:circle, markersize=2)
                
                # Add tolerance line
                Plots.hline!(p, [history.target_tolerance], 
                      linestyle=:dash, color=:gray, 
                      label="$(name) tolerance", alpha=0.7)
            end
        end
        
        # Save plot
        Plots.savefig(p, "residual_convergence.png")
        println("📈 Residual plot saved: residual_convergence.png")
        
    catch e
        println("⚠️  Plotting failed: $e")
    end
end

# ============================================================================
# Utility Functions
# ============================================================================

"""
Format scientific notation for display
"""
function scientific_notation(x::Float64)
    if x == 0.0
        return "0.00e+00"
    else
        exp_val = floor(Int, log10(abs(x)))
        coef = x / 10.0^exp_val
        return @sprintf("%.2fe%+03d", coef, exp_val)
    end
end

"""
Format time duration for display
"""
function format_time(seconds::Float64)
    if seconds < 60
        return @sprintf("%.1fs", seconds)
    elseif seconds < 3600
        minutes = floor(Int, seconds / 60)
        return @sprintf("%dm%ds", minutes, Int(seconds % 60))
    else
        hours = floor(Int, seconds / 3600)
        minutes = floor(Int, (seconds % 3600) / 60)
        return @sprintf("%dh%dm", hours, minutes)
    end
end

end # module SolverMonitoring