# src/PostProcessing/VortexIdentification.jl - Q-criterion and vortex methods

module VortexIdentification

using ..CFDCore
using ..CFDCore: AbstractMesh, Field, ScalarField, VectorField, AbstractField
using ..Numerics
using ..Numerics.fvc
using LinearAlgebra
using StaticArrays

export QCriterion, Lambda2, vorticity
export calculateQ, calculateLambda2, calculateVorticity
export helicity, enstrophy

# Define aliases for compatibility
const Vec3{T} = SVector{3,T}
const Scalar = Float64

# Calculate velocity gradient tensor
function velocityGradientTensor(U::VectorField{SVector{N,T},N,M}) where {N,T,M<:AbstractMesh}
    # Use fvc.grad to calculate gradient of each velocity component
    mesh = U.mesh
    num_cells = length(mesh.cells)
    
    # Create tensor field to store velocity gradient
    tensor_data = [SMatrix{N,N,T}(zeros(T, N, N)) for _ in 1:num_cells]
    
    # Calculate gradient of each velocity component
    for i in 1:N
        # Extract scalar component
        U_component = CFDCore.extract_scalar_component(U, i)
        
        # Calculate gradient
        grad_U_component = fvc.grad(U_component, Numerics.GaussGradient())
        
        # Store in tensor field
        for j in 1:num_cells
            grad_vec = grad_U_component.data[j]
            for k in 1:N
                if k <= length(grad_vec)
                    tensor_data[j] = setindex(tensor_data[j], grad_vec[k], k, i)
                end
            end
        end
    end
    
    # Create and return tensor field
    return Field{SMatrix{N,N,T},N,M}(Symbol(string(U.name)*"_gradTensor"), mesh, tensor_data, U.boundary_conditions)
end

# Calculate Q-criterion
function QCriterion(U::VectorField{SVector{N,T},N,M}) where {N,T,M<:AbstractMesh}
    mesh = U.mesh
    gradU = velocityGradientTensor(U)
    
    # Create Q-criterion field
    Q_data = zeros(T, length(mesh.cells))
    
    for i in 1:length(mesh.cells)
        # Velocity gradient tensor
        L = gradU.data[i]
        
        # Symmetric and antisymmetric parts
        S = T(0.5) * (L + L')  # Strain rate tensor
        Omega = T(0.5) * (L - L')  # Rotation rate tensor
        
        # Q = 0.5 * (||Ω||² - ||S||²)
        Q_data[i] = T(0.5) * (norm(Omega)^2 - norm(S)^2)
    end
    
    return ScalarField(Symbol("Q"), mesh, Q_data, Dict{String, CFDCore.AbstractBoundaryCondition}())
end

# Calculate Lambda2 criterion
function Lambda2(U::VectorField{SVector{N,T},N,MeshType}) where {N,T,MeshType<:AbstractMesh}
    mesh = U.mesh
    gradU = velocityGradientTensor(U)
    
    # Create Lambda2 field
    lambda2_data = zeros(T, length(mesh.cells))
    
    for i in 1:length(mesh.cells)
        L = gradU.data[i]
        
        # S and Omega tensors
        S = T(0.5) * (L + L')
        Omega = T(0.5) * (L - L')
        
        # S² + Ω²
        M = S^2 + Omega^2
        
        # Eigenvalues of M
        eigenvals = eigvals(M)
        
        # Lambda2 is the second eigenvalue (sorted)
        sorted_eigenvals = sort(real.(eigenvals))
        lambda2_data[i] = length(sorted_eigenvals) >= 2 ? sorted_eigenvals[2] : T(0)
    end
    
    return ScalarField(Symbol("Lambda2"), mesh, lambda2_data, Dict{String, CFDCore.AbstractBoundaryCondition}())
end

# Calculate vorticity using curl operation
function vorticity(U::VectorField{SVector{N,T},N,M}) where {N,T,M<:AbstractMesh}
    mesh = U.mesh
    
    if N != 3
        error("Vorticity calculation currently only supported for 3D velocity fields")
    end
    
    # Calculate vorticity as curl of velocity field
    # ω = ∇ × U
    vorticity_data = [SVector{3,T}(0, 0, 0) for _ in 1:length(mesh.cells)]
    
    # Extract velocity components
    U1 = CFDCore.extract_scalar_component(U, 1)  # u component
    U2 = CFDCore.extract_scalar_component(U, 2)  # v component  
    U3 = CFDCore.extract_scalar_component(U, 3)  # w component
    
    # Calculate gradients
    grad_U1 = fvc.grad(U1, Numerics.GaussGradient())
    grad_U2 = fvc.grad(U2, Numerics.GaussGradient())
    grad_U3 = fvc.grad(U3, Numerics.GaussGradient())
    
    # Calculate curl components
    for i in 1:length(mesh.cells)
        grad_u = grad_U1.data[i]
        grad_v = grad_U2.data[i]
        grad_w = grad_U3.data[i]
        
        # ω = (∂w/∂y - ∂v/∂z, ∂u/∂z - ∂w/∂x, ∂v/∂x - ∂u/∂y)
        omega_x = grad_w[2] - grad_v[3]  # ∂w/∂y - ∂v/∂z
        omega_y = grad_u[3] - grad_w[1]  # ∂u/∂z - ∂w/∂x
        omega_z = grad_v[1] - grad_u[2]  # ∂v/∂x - ∂u/∂y
        
        vorticity_data[i] = SVector{3,T}(omega_x, omega_y, omega_z)
    end
    
    return VectorField(Symbol("vorticity"), mesh, vorticity_data, U.boundary_conditions)
end

# Calculate helicity (U · ω)
function helicity(U::VectorField{SVector{N,T},N,M}) where {N,T,M<:AbstractMesh}
    mesh = U.mesh
    omega = vorticity(U)
    
    # Create helicity field
    H_data = zeros(T, length(mesh.cells))
    
    for i in 1:length(mesh.cells)
        H_data[i] = dot(U.data[i], omega.data[i])
    end
    
    return ScalarField(Symbol("helicity"), mesh, H_data, Dict{String, CFDCore.AbstractBoundaryCondition}())
end

# Calculate enstrophy (0.5 * ω · ω)
function enstrophy(U::VectorField{SVector{N,T},N,M}) where {N,T,M<:AbstractMesh}
    mesh = U.mesh
    omega = vorticity(U)
    
    # Create enstrophy field
    xi_data = zeros(T, length(mesh.cells))
    
    for i in 1:length(mesh.cells)
        xi_data[i] = T(0.5) * dot(omega.data[i], omega.data[i])
    end
    
    return ScalarField(Symbol("enstrophy"), mesh, xi_data, Dict{String, CFDCore.AbstractBoundaryCondition}())
end

# Convenient aliases
const calculateQ = QCriterion
const calculateLambda2 = Lambda2
const calculateVorticity = vorticity

end # module VortexIdentification