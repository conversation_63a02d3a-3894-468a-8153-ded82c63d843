# src/Mesh/AMI.jl - Arbitrary Mesh Interface for rotating zones

module AMI

using ..CFDCore
using ..CFDCore: AbstractMesh, Field, ScalarField, VectorField, DirichletBC, NeumannBC
using LinearAlgebra
using StaticArrays

# Optional dependency
const HAS_NEAREST_NEIGHBORS = try
    using NearestNeighbors
    true
catch
    @warn "NearestNeighbors not available, using simplified AMI interpolation"
    false
end

export AMIInterface, AMIPatch, createAMI
export interpolateAMI!, updateAMI!
export RotatingZone, MRFZone

# Define Vec3 and Scalar aliases for compatibility with the implementation
const Vec3{T} = SVector{3,T}
const Scalar = Float64

# AMI Patch definition
struct AMIPatch{T}
    name::String
    masterPatch::String
    slavePatch::String
    masterFaces::Vector{Int}
    slaveFaces::Vector{Int}
    rotationAxis::Vec3{T}
    rotationCenter::Vec3{T}
    omega::T  # Angular velocity (rad/s)
end

# AMI Interface with interpolation weights
mutable struct AMIInterface{T}
    masterPatch::AMIPatch{T}
    slavePatch::AMIPatch{T}
    
    # Interpolation data
    masterWeights::Vector{Vector{T}}
    slaveWeights::Vector{Vector{T}}
    masterIndices::Vector{Vector{Int}}
    slaveIndices::Vector{Vector{Int}}
    
    # Current rotation angle
    theta::T
    
    # Transformation matrix
    R::SMatrix{3,3,T}
end

# Rotating zone for MRF or moving mesh
struct RotatingZone{T}
    name::String
    cellZone::Vector{Int}
    axis::Vec3{T}
    origin::Vec3{T}
    omega::T
    patches::Vector{AMIPatch{T}}
end

# MRF Zone alias
const MRFZone{T} = RotatingZone{T}

# Patch structure for mesh boundary patches
struct Patch{T}
    name::String
    faces::Vector{Int}
    centers::Vector{Vec3{T}}
    areas::Vector{T}
    nFaces::Int
end

# Create AMI interface
function createAMI(mesh::AbstractMesh{T,N}, masterPatch::String, slavePatch::String,
                   axis::Vec3{T}, origin::Vec3{T}, omega::T) where {T,N}
    
    # Find patches in mesh boundaries
    masterFaces = get(mesh.boundaries, masterPatch, Int[])
    slaveFaces = get(mesh.boundaries, slavePatch, Int[])
    
    if isempty(masterFaces) || isempty(slaveFaces)
        error("AMI patches not found: $masterPatch, $slavePatch")
    end
    
    # Create patch structures
    masterPatchData = createPatchData(mesh, masterPatch, masterFaces)
    slavePatchData = createPatchData(mesh, slavePatch, slaveFaces)
    
    # Create AMI patches
    masterAMI = AMIPatch{T}(masterPatch, masterPatch, slavePatch,
                         masterFaces, slaveFaces, axis, origin, omega)
    slaveAMI = AMIPatch{T}(slavePatch, slavePatch, masterPatch,
                        slaveFaces, masterFaces, axis, origin, -omega)
    
    # Calculate initial weights
    masterWeights, masterIndices = calculateAMIWeights(mesh, masterPatchData, slavePatchData)
    slaveWeights, slaveIndices = calculateAMIWeights(mesh, slavePatchData, masterPatchData)
    
    # Initial rotation matrix (identity)
    R = SMatrix{3,3,T}(I)
    
    return AMIInterface{T}(masterAMI, slaveAMI,
                       masterWeights, slaveWeights,
                       masterIndices, slaveIndices,
                       T(0), R)
end

# Create patch data from mesh
function createPatchData(mesh::AbstractMesh{T,N}, patchName::String, faceIndices::Vector{Int}) where {T,N}
    centers = Vec3{T}[]
    areas = T[]
    
    for faceIdx in faceIndices
        if faceIdx <= length(mesh.faces)
            face = mesh.faces[faceIdx]
            # Convert face center to Vec3 if needed
            center = N == 3 ? Vec3{T}(face.center) : Vec3{T}(face.center[1], face.center[2], T(0))
            push!(centers, center)
            push!(areas, face.area)
        end
    end
    
    return Patch{T}(patchName, faceIndices, centers, areas, length(faceIndices))
end

# Calculate AMI interpolation weights using area-weighted interpolation
function calculateAMIWeights(mesh::AbstractMesh{T,N}, sourcePatch::Patch{T}, targetPatch::Patch{T}) where {T,N}
    nSourceFaces = sourcePatch.nFaces
    nTargetFaces = targetPatch.nFaces
    
    weights = Vector{Vector{T}}(undef, nSourceFaces)
    indices = Vector{Vector{Int}}(undef, nSourceFaces)
    
    # Get face centers and areas
    sourceCenters = sourcePatch.centers
    targetCenters = targetPatch.centers
    sourceAreas = sourcePatch.areas
    targetAreas = targetPatch.areas
    
    # Build search structure for target faces
    if nTargetFaces > 0 && HAS_NEAREST_NEIGHBORS
        targetMatrix = hcat([SVector{3,T}(c[1], c[2], c[3]) for c in targetCenters]...)
        targetTree = KDTree(targetMatrix)
        
        # For each source face, find overlapping target faces
        for i in 1:nSourceFaces
            center = sourceCenters[i]
            area = sourceAreas[i]
            
            # Find nearby target faces (use reasonable search radius)
            searchRadius = 2.0 * sqrt(area / π)
            k = min(10, nTargetFaces)  # Don't search for more neighbors than exist
            idxs, dists = knn(targetTree, SVector{3,T}(center), k, true)
            
            # Calculate overlap weights
            faceWeights = T[]
            faceIndices = Int[]
            totalWeight = T(0)
            
            for (j, idx) in enumerate(idxs)
                if dists[j] < searchRadius
                    # Simple distance-based weight (can be improved with actual overlap calculation)
                    weight = exp(-dists[j]^2 / (T(0.5) * searchRadius^2))
                    push!(faceWeights, weight)
                    push!(faceIndices, idx)
                    totalWeight += weight
                end
            end
            
            # Normalize weights
            if totalWeight > T(0)
                faceWeights ./= totalWeight
            else
                # Fallback to nearest neighbor
                if !isempty(idxs)
                    push!(faceWeights, T(1))
                    push!(faceIndices, idxs[1])
                end
            end
            
            weights[i] = faceWeights
            indices[i] = faceIndices
        end
    elseif nTargetFaces > 0
        # Fallback: simple distance-based search without KDTree
        for i in 1:nSourceFaces
            center = sourceCenters[i]
            area = sourceAreas[i]
            searchRadius = 2.0 * sqrt(area / π)
            
            faceWeights = T[]
            faceIndices = Int[]
            totalWeight = T(0)
            
            # Linear search through target faces
            for j in 1:nTargetFaces
                targetCenter = targetCenters[j]
                dist = norm(center - targetCenter)
                
                if dist < searchRadius
                    weight = exp(-dist^2 / (T(0.5) * searchRadius^2))
                    push!(faceWeights, weight)
                    push!(faceIndices, j)
                    totalWeight += weight
                end
            end
            
            # Normalize weights
            if totalWeight > T(0)
                faceWeights ./= totalWeight
            elseif nTargetFaces > 0
                # Fallback to nearest face
                minDist = Inf
                nearestIdx = 1
                for j in 1:nTargetFaces
                    dist = norm(center - targetCenters[j])
                    if dist < minDist
                        minDist = dist
                        nearestIdx = j
                    end
                end
                push!(faceWeights, T(1))
                push!(faceIndices, nearestIdx)
            end
            
            weights[i] = faceWeights
            indices[i] = faceIndices
        end
    else
        # Handle empty target patch
        for i in 1:nSourceFaces
            weights[i] = T[]
            indices[i] = Int[]
        end
    end
    
    return weights, indices
end

# Update AMI for new rotation angle
function updateAMI!(ami::AMIInterface{T}, dt::T) where T
    # Update rotation angle
    ami.theta += ami.masterPatch.omega * dt
    
    # Update rotation matrix
    axis = ami.masterPatch.rotationAxis
    theta = ami.theta
    
    # Rodrigues' rotation formula
    K = SMatrix{3,3,T}(0, -axis[3], axis[2],
                       axis[3], 0, -axis[1],
                       -axis[2], axis[1], 0)
    
    ami.R = SMatrix{3,3,T}(I) + sin(theta) * K + (1 - cos(theta)) * K^2
    
    # Could recalculate weights here if mesh deforms significantly
end

# Interpolate field values across AMI
function interpolateAMI!(field::Field{TF,N,M}, ami::AMIInterface{T}) where {TF,T,N,M}
    mesh = field.mesh
    
    # Interpolate from master to slave
    interpolateAMISide!(field, ami.masterPatch, ami.slavePatch, 
                       ami.masterWeights, ami.masterIndices, ami.R)
    
    # Interpolate from slave to master  
    interpolateAMISide!(field, ami.slavePatch, ami.masterPatch,
                       ami.slaveWeights, ami.slaveIndices, ami.R')
end

function interpolateAMISide!(field::Field{TF,N,M}, 
                            sourcePatch::AMIPatch{T}, targetPatch::AMIPatch{T},
                            weights::Vector{Vector{T}}, 
                            indices::Vector{Vector{Int}},
                            R::SMatrix{3,3,T}) where {TF,T,N,M}
    
    # Get boundary conditions
    sourceBoundary = get(field.boundary_conditions, sourcePatch.name, nothing)
    targetBoundary = get(field.boundary_conditions, targetPatch.name, nothing)
    
    if sourceBoundary === nothing || targetBoundary === nothing
        @warn "Missing boundary conditions for AMI interpolation: $(sourcePatch.name) or $(targetPatch.name)"
        return
    end
    
    # Interpolate each target face
    for i in 1:length(targetPatch.masterFaces)
        if i <= length(weights) && !isempty(weights[i])
            value = zero(TF)
            
            for j in 1:length(weights[i])
                if j <= length(indices[i])
                    sourceIdx = indices[i][j]
                    weight = weights[i][j]
                    
                    # Get source value (simplified - would need proper boundary value extraction)
                    sourceValue = getBoundaryValue(sourceBoundary, sourceIdx, field)
                    
                    # Rotate vector/tensor fields
                    if TF <: SVector{3}
                        sourceValue = R * sourceValue
                    elseif TF <: SMatrix
                        sourceValue = R * sourceValue * R'
                    end
                    
                    value += weight * sourceValue
                end
            end
            
            # Set interpolated value (simplified - would need proper boundary value setting)
            setBoundaryValue!(targetBoundary, i, value, field)
        end
    end
end

# Helper functions for boundary value access (simplified implementations)
function getBoundaryValue(bc::DirichletBC{TF}, idx::Int, field::Field{TF,N,M}) where {TF,N,M}
    return bc.value
end

function getBoundaryValue(bc::NeumannBC{TF}, idx::Int, field::Field{TF,N,M}) where {TF,N,M}
    # For Neumann BC, return current field value (simplified)
    return zero(TF)
end

function setBoundaryValue!(bc::DirichletBC{TF}, idx::Int, value::TF, field::Field{TF,N,M}) where {TF,N,M}
    # Update BC value (simplified)
    bc.value = value
end

function setBoundaryValue!(bc::NeumannBC{TF}, idx::Int, value::TF, field::Field{TF,N,M}) where {TF,N,M}
    # For Neumann BC, might update gradient (simplified)
    bc.gradient = value
end

end # module AMI