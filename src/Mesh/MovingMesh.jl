# src/Mesh/MovingMesh.jl - Dynamic mesh motion

module MovingMesh

using ..CFDCore
using ..CFDCore: AbstractMesh, Cell, Face, Node
using LinearAlgebra
using StaticArrays

export MeshMotionSolver, SolidBodyMotion, MorphingMesh
export moveMesh!, updateMeshGeometry!
export rotatingMotion, oscillatingMotion

# Define aliases for compatibility
const Vec3{T} = SVector{3,T}
const Scalar = Float64

# Abstract mesh motion solver
abstract type MeshMotionSolver end

# Solid body motion (for rotating zones)
struct SolidBodyMotion{T} <: MeshMotionSolver
    cellZone::Vector{Int}
    motionFunction::Function  # (t) -> (translation, rotation)
end

# Mesh morphing with point displacement
struct MorphingMesh{T} <: MeshMotionSolver
    pointDisplacement::Vector{Vec3{T}}
    cellMotion::Bool
end

# Predefined motion functions
function rotatingMotion(axis::Vec3{T}, origin::Vec3{T}, omega::T) where T
    return (t) -> begin
        theta = omega * t
        
        # Rotation matrix using <PERSON>rig<PERSON>' formula
        K = SMatrix{3,3,T}(0, -axis[3], axis[2],
                           axis[3], 0, -axis[1],
                           -axis[2], axis[1], 0)
        R = SMatrix{3,3,T}(I) + sin(theta) * K + (1 - cos(theta)) * K^2
        
        translation = SVector{3,T}(0, 0, 0)
        rotation = R
        
        return translation, rotation
    end
end

function oscillatingMotion(amplitude::Vec3{T}, frequency::T, phase::T=T(0)) where T
    return (t) -> begin
        translation = amplitude * sin(2π * frequency * t + phase)
        rotation = SMatrix{3,3,T}(I)
        return translation, rotation
    end
end

# Move mesh points
function moveMesh!(mesh::AbstractMesh{T,N}, motion::SolidBodyMotion{T}, t::T, dt::T) where {T,N}
    # Get motion at current time
    translation, rotation = motion.motionFunction(t)
    
    # Get motion at previous time for velocity calculation
    translation_old, rotation_old = motion.motionFunction(t - dt)
    
    # Move cells in the cell zone
    for cellI in motion.cellZone
        if cellI <= length(mesh.cells)
            cell = mesh.cells[cellI]
            
            # Update cell center
            center_old = cell.center
            new_center = rotation * center_old + translation
            
            # Update the cell (need to create new cell due to immutability)
            mesh.cells[cellI] = Cell{T,N}(cell.id, cell.nodes, cell.faces, 
                                          new_center, cell.volume)
            
            # Update face centers and areas for cell faces
            for faceI in cell.faces
                if faceI <= length(mesh.faces)
                    face = mesh.faces[faceI]
                    
                    # Only update if this is the owner cell (avoid double updates)
                    if face.owner == cellI
                        # Update face center
                        fc_old = face.center
                        new_fc = rotation * fc_old + translation
                        
                        # Update face area vector (normal direction)
                        area_vector = face.area * face.normal
                        new_area_vector = rotation * area_vector
                        new_normal = normalize(new_area_vector)
                        new_area = norm(new_area_vector)
                        
                        # Update the face
                        mesh.faces[faceI] = Face{T,N}(face.id, face.nodes, new_fc,
                                                      new_area, new_normal, 
                                                      face.owner, face.neighbor, face.boundary)
                    end
                end
            end
        end
    end
    
    # Update mesh points
    updateMeshPoints!(mesh, motion.cellZone, translation, rotation)
end

function updateMeshPoints!(mesh::AbstractMesh{T,N}, cellZone::Vector{Int}, 
                          translation::Vec3{T}, rotation::SMatrix{3,3,T}) where {T,N}
    # Get unique points for the cell zone
    pointSet = Set{Int}()
    
    for cellI in cellZone
        if cellI <= length(mesh.cells)
            cell = mesh.cells[cellI]
            for faceI in cell.faces
                if faceI <= length(mesh.faces)
                    face = mesh.faces[faceI]
                    # Add face points
                    for nodeI in face.nodes
                        push!(pointSet, nodeI)
                    end
                end
            end
        end
    end
    
    # Update unique points
    for pointI in pointSet
        if pointI <= length(mesh.nodes)
            node = mesh.nodes[pointI]
            old_coords = node.coords
            new_coords = rotation * old_coords + translation
            
            # Update the node
            mesh.nodes[pointI] = Node{T,N}(node.id, new_coords, node.boundary)
        end
    end
end

# Update mesh geometry after motion
function updateMeshGeometry!(mesh::AbstractMesh{T,N}) where {T,N}
    # Recalculate cell volumes
    for i in 1:length(mesh.cells)
        cell = mesh.cells[i]
        new_volume = calculateCellVolume(mesh, i)
        mesh.cells[i] = Cell{T,N}(cell.id, cell.nodes, cell.faces, 
                                  cell.center, new_volume)
    end
    
    # Recalculate face areas and centers
    for i in 1:length(mesh.faces)
        face = mesh.faces[i]
        new_area, new_center, new_normal = calculateFaceGeometry(mesh, i)
        mesh.faces[i] = Face{T,N}(face.id, face.nodes, new_center,
                                  new_area, new_normal, 
                                  face.owner, face.neighbor, face.boundary)
    end
    
    # Recalculate cell centers
    for i in 1:length(mesh.cells)
        cell = mesh.cells[i]
        new_center = calculateCellCenter(mesh, i)
        mesh.cells[i] = Cell{T,N}(cell.id, cell.nodes, cell.faces, 
                                  new_center, cell.volume)
    end
end

# Helper functions for geometry calculation
function calculateCellVolume(mesh::AbstractMesh{T,N}, cellI::Int) where {T,N}
    # Simplified volume calculation - for polyhedron would use proper calculation
    if cellI <= length(mesh.cells)
        cell = mesh.cells[cellI]
        # Simple approximation - in practice would compute proper polyhedron volume
        return cell.volume  # Keep existing volume for now
    else
        return T(0)
    end
end

function calculateFaceGeometry(mesh::AbstractMesh{T,N}, faceI::Int) where {T,N}
    # Calculate face geometry from face points
    if faceI <= length(mesh.faces)
        face = mesh.faces[faceI]
        
        if length(face.nodes) >= 3
            # Get face points
            points = [mesh.nodes[nodeI].coords for nodeI in face.nodes if nodeI <= length(mesh.nodes)]
            
            if length(points) >= 3
                # Calculate centroid
                center = sum(points) / length(points)
                
                # Calculate area and normal for triangular/polygonal face
                if length(points) == 3
                    # Triangle
                    v1 = points[2] - points[1]
                    v2 = points[3] - points[1]
                    area_vector = 0.5 * cross(v1, v2)
                    area = norm(area_vector)
                    normal = area > T(0) ? normalize(area_vector) : SVector{N,T}(0, 0, 1)
                else
                    # Polygon - sum triangular areas
                    total_area_vector = SVector{N,T}(0, 0, 0)
                    for i in 2:length(points)-1
                        v1 = points[i] - points[1]
                        v2 = points[i+1] - points[1]
                        total_area_vector += 0.5 * cross(v1, v2)
                    end
                    area = norm(total_area_vector)
                    normal = area > T(0) ? normalize(total_area_vector) : SVector{N,T}(0, 0, 1)
                end
                
                return area, center, normal
            end
        end
        
        # Fallback to existing values
        return face.area, face.center, face.normal
    else
        return T(0), SVector{N,T}(0, 0, 0), SVector{N,T}(0, 0, 1)
    end
end

function calculateCellCenter(mesh::AbstractMesh{T,N}, cellI::Int) where {T,N}
    # Volume-weighted average of face centers
    if cellI <= length(mesh.cells)
        cell = mesh.cells[cellI]
        center = SVector{N,T}(0, 0, 0)
        totalVolume = T(0)
        
        for faceI in cell.faces
            if faceI <= length(mesh.faces)
                face = mesh.faces[faceI]
                # Pyramid volume from face to current cell center estimate
                pyramidVolume = face.area * T(0.1)  # Simplified
                center += face.center * pyramidVolume
                totalVolume += pyramidVolume
            end
        end
        
        return totalVolume > T(0) ? center / totalVolume : cell.center
    else
        return SVector{N,T}(0, 0, 0)
    end
end

# Cross product for SVector{3,T}
function cross(a::SVector{3,T}, b::SVector{3,T}) where T
    return SVector{3,T}(
        a[2]*b[3] - a[3]*b[2],
        a[3]*b[1] - a[1]*b[3],
        a[1]*b[2] - a[2]*b[1]
    )
end

end # module MovingMesh