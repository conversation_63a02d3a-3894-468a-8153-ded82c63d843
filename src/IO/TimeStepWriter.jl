# OpenFOAM-style Time Step Output System
# Writes field data to time directories like OpenFOAM

module TimeStepWriter

using Printf
using ..CFDCore

# Structure to manage time series output
mutable struct TimeSeriesWriter
    base_path::String
    current_time::Float64
    write_precision::Int
    fields::Dict{Symbol, Any}
    
    function TimeSeriesWriter(base_path::String; precision::Int=6)
        mkpath(base_path)
        new(base_path, 0.0, precision, Dict{Symbol, Any}())
    end
end

# Write fields to time directory (OpenFOAM style)
function write_time_step!(writer::TimeSeriesWriter, time::Float64, fields::Dict{Symbol, Any})
    writer.current_time = time
    
    # Create time directory name (OpenFOAM format)
    time_dir = time_directory_name(time, writer.write_precision)
    time_path = joinpath(writer.base_path, time_dir)
    
    # Create time directory
    mkpath(time_path)
    
    # Write each field to the time directory
    for (field_name, field_data) in fields
        write_field_file(time_path, field_name, field_data, time)
    end
    
    # Update latest symlink (OpenFOAM convention)
    update_latest_link(writer.base_path, time_dir)
    
    return time_path
end

# Generate OpenFOAM-style time directory name
function time_directory_name(time::Float64, precision::Int)
    if time == 0.0
        return "0"
    elseif abs(time) >= 1.0
        # For times >= 1, use minimal decimal places
        time_str = @sprintf("%.6g", time)  # Use fixed precision for now
    else
        # For small times, use scientific notation if needed
        if abs(time) < 10.0^(-precision)
            time_str = @sprintf("%.5e", time)  # Use fixed precision for now
        else
            time_str = @sprintf("%.6f", time)  # Use fixed precision for now
        end
    end
    
    # Remove trailing zeros and decimal point if integer
    time_str = rstrip(time_str, '0')
    time_str = rstrip(time_str, '.')
    
    return time_str
end

# Write individual field file in OpenFOAM format
function write_field_file(time_path::String, field_name::Symbol, field_data, time::Float64)
    field_file = joinpath(time_path, string(field_name))
    
    open(field_file, "w") do io
        write_openfoam_header(io, field_name, time)
        write_field_data(io, field_data)
        write_openfoam_footer(io)
    end
end

# Write OpenFOAM-style field file header
function write_openfoam_header(io::IO, field_name::Symbol, time::Float64)
    println(io, "/*--------------------------------*- C++ -*----------------------------------*\\")
    println(io, "| =========                 |                                                 |")
    println(io, "|  \\      /  F ield         | CFD.jl: Enhanced Julia CFD Framework          |")
    println(io, "|   \\    /   O peration     | Version:  2.0                                  |")
    println(io, "|    \\  /    A nd           | Web:      https://github.com/your/CFD.jl      |")
    println(io, "|     \\/     M anipulation  |                                                 |")
    println(io, "\\*---------------------------------------------------------------------------*/")
    println(io, "FoamFile")
    println(io, "{")
    println(io, "    version     2.0;")
    println(io, "    format      ascii;")
    println(io, "    class       vol$(field_type_name(field_name))Field;")
    println(io, "    object      $(field_name);")
    println(io, "}")
    println(io, "// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //")
    println(io)
    println(io, "dimensions      $(get_field_dimensions(field_name));")
    println(io)
    println(io, "internalField   uniform $(get_default_value(field_name));")
    println(io)
    println(io, "boundaryField")
    println(io, "{")
end

# Write field boundary conditions (placeholder)
function write_field_data(io::IO, field_data)
    # This would write the actual field data
    # For now, write placeholder boundary conditions
    
    patches = ["inlet", "outlet", "wall", "top", "bottom", "front", "back"]
    
    for patch in patches
        println(io, "    $patch")
        println(io, "    {")
        println(io, "        type            zeroGradient;")
        println(io, "    }")
        println(io)
    end
end

# Write OpenFOAM file footer
function write_openfoam_footer(io::IO)
    println(io, "}")
    println(io)
    println(io, "// ************************************************************************* //")
end

# Determine field type name for OpenFOAM header
function field_type_name(field_name::Symbol)
    if field_name in [:U, :velocity]
        return "Vector"
    elseif field_name in [:p, :pressure, :T, :temperature, :k, :epsilon, :nut]
        return "Scalar"
    else
        return "Scalar"  # Default
    end
end

# Get field dimensions in OpenFOAM format
function get_field_dimensions(field_name::Symbol)
    dimensions = Dict(
        :U => "[0 1 -1 0 0 0 0]",          # m/s
        :p => "[0 2 -2 0 0 0 0]",          # m²/s² (kinematic pressure)
        :T => "[0 0 0 1 0 0 0]",           # K
        :k => "[0 2 -2 0 0 0 0]",          # m²/s²
        :epsilon => "[0 2 -3 0 0 0 0]",    # m²/s³
        :nut => "[0 2 -1 0 0 0 0]"         # m²/s
    )
    
    return get(dimensions, field_name, "[0 0 0 0 0 0 0]")
end

# Get default field values
function get_default_value(field_name::Symbol)
    defaults = Dict(
        :U => "(0 0 0)",
        :p => "0",
        :T => "300",
        :k => "0.1",
        :epsilon => "0.01",
        :nut => "0"
    )
    
    return get(defaults, field_name, "0")
end

# Update 'latest' symlink to current time (OpenFOAM convention)
function update_latest_link(base_path::String, time_dir::String)
    latest_link = joinpath(base_path, "latest")
    
    # Remove existing symlink
    if islink(latest_link)
        rm(latest_link)
    end
    
    # Create new symlink
    try
        symlink(time_dir, latest_link)
    catch
        # If symlinks not supported, create a text file instead
        open(latest_link, "w") do io
            println(io, time_dir)
        end
    end
end

# Write controlDict-style time information
function write_time_info(base_path::String, start_time::Float64, end_time::Float64, 
                        dt::Float64, write_interval::Int)
    
    system_dir = joinpath(base_path, "system")
    mkpath(system_dir)
    
    controlDict_file = joinpath(system_dir, "controlDict")
    
    open(controlDict_file, "w") do io
        println(io, "/*--------------------------------*- C++ -*----------------------------------*\\")
        println(io, "| CFD.jl Enhanced Unicode Framework - Control Dictionary                    |")
        println(io, "\\*---------------------------------------------------------------------------*/")
        println(io, "FoamFile")
        println(io, "{")
        println(io, "    version     2.0;")
        println(io, "    format      ascii;")
        println(io, "    class       dictionary;")
        println(io, "    object      controlDict;")
        println(io, "}")
        println(io, "// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //")
        println(io)
        println(io, "application     CFD.jl;")
        println(io)
        println(io, "startFrom       startTime;")
        println(io, "startTime       $start_time;")
        println(io, "stopAt          endTime;")
        println(io, "endTime         $end_time;")
        println(io, "deltaT          $dt;")
        println(io, "writeControl    timeStep;")
        println(io, "writeInterval   $write_interval;")
        println(io, "purgeWrite      0;")
        println(io, "writeFormat     ascii;")
        println(io, "writePrecision  6;")
        println(io, "writeCompression off;")
        println(io, "timeFormat      general;")
        println(io, "timePrecision   6;")
        println(io, "runTimeModifiable true;")
        println(io)
        println(io, "// ************************************************************************* //")
    end
end

# Initialize case directory structure (OpenFOAM style)
function initialize_case_structure(case_path::String)
    # Create standard OpenFOAM directories
    directories = ["0", "constant", "system", "postProcessing"]
    
    for dir in directories
        mkpath(joinpath(case_path, dir))
    end
    
    # Create constant/polyMesh directory
    mkpath(joinpath(case_path, "constant", "polyMesh"))
    
    return case_path
end

# Copy initial conditions from 0 to time directories
function copy_initial_conditions(case_path::String, time::Float64)
    zero_dir = joinpath(case_path, "0")
    time_dir_name = time_directory_name(time, 6)
    time_dir = joinpath(case_path, time_dir_name)
    
    if isdir(zero_dir) && time != 0.0
        # Copy all files from 0/ to time directory
        for file in readdir(zero_dir)
            src = joinpath(zero_dir, file)
            dst = joinpath(time_dir, file)
            
            if isfile(src)
                cp(src, dst, force=true)
            end
        end
    end
end

# Main function to setup time step writing
function setup_time_step_output(case_path::String, fields::Vector{Symbol}; 
                               start_time::Float64=0.0, end_time::Float64=1.0, 
                               dt::Float64=0.01, write_interval::Int=10)
    
    # Initialize case structure
    initialize_case_structure(case_path)
    
    # Write control information
    write_time_info(case_path, start_time, end_time, dt, write_interval)
    
    # Create time series writer
    writer = TimeSeriesWriter(case_path)
    
    # Write initial time step
    initial_fields = Dict{Symbol, Any}()
    for field in fields
        initial_fields[field] = nothing  # Placeholder data
    end
    
    write_time_step!(writer, start_time, initial_fields)
    
    return writer
end

export TimeSeriesWriter, write_time_step!, setup_time_step_output
export time_directory_name, initialize_case_structure, write_time_info

end # module TimeStepWriter