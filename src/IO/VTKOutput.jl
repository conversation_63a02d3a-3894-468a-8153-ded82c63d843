# src/IO/VTKOutput.jl - Enhanced VTK output with time series

module VTKOutput

using ..CFDCore
using ..CFDCore: AbstractMesh, Field, ScalarField, VectorField, AbstractField
using LinearAlgebra
using StaticArrays
using Printf

export writeVTK, writeTimeStep, writeVTKSeries
export VTKSeries, createVTKSeries

# Define aliases
const Vec3{T} = SVector{3,T}
const Scalar = Float64

# VTK Series structure for time animation
mutable struct VTKSeries
    basename::String
    timesteps::Vector{Float64}
    filenames::Vector{String}
    
    function VTKSeries(basename::String)
        new(basename, Float64[], String[])
    end
end

# Write single VTK file
function writeVTK(filename::String, mesh::AbstractMesh{T,N}, 
                 fields::Dict{String, F}; time::Float64=0.0) where {T,N,F<:AbstractField}
    
    println("Writing VTK file: $filename.vtu")
    
    # Create VTK file
    open("$filename.vtu", "w") do io
        writeVTKHeader(io)
        writeVTKMesh(io, mesh)
        writeVTKFields(io, mesh, fields)
        writeVTKFooter(io, time)
    end
    
    return filename
end

# Write VTK header
function writeVTKHeader(io::IO)
    write(io, "<?xml version=\"1.0\"?>\n")
    write(io, "<VTKFile type=\"UnstructuredGrid\" version=\"0.1\" byte_order=\"LittleEndian\">\n")
    write(io, "  <UnstructuredGrid>\n")
end

# Write mesh data
function writeVTKMesh(io::IO, mesh::AbstractMesh{T,N}) where {T,N}
    nPoints = length(mesh.nodes)
    nCells = length(mesh.cells)
    
    write(io, "    <Piece NumberOfPoints=\"$nPoints\" NumberOfCells=\"$nCells\">\n")
    
    # Write points
    write(io, "      <Points>\n")
    write(io, "        <DataArray type=\"Float64\" NumberOfComponents=\"3\" format=\"ascii\">\n")
    for node in mesh.nodes
        coords = node.coords
        if N == 2
            write(io, "          $(coords[1]) $(coords[2]) 0.0\n")
        else
            write(io, "          $(coords[1]) $(coords[2]) $(coords[3])\n")
        end
    end
    write(io, "        </DataArray>\n")
    write(io, "      </Points>\n")
    
    # Write cells
    write(io, "      <Cells>\n")
    
    # Cell connectivity
    write(io, "        <DataArray type=\"Int32\" Name=\"connectivity\" format=\"ascii\">\n")
    for cell in mesh.cells
        # VTK uses 0-based indexing
        connectivity = join([string(nodeId - 1) for nodeId in cell.nodes], " ")
        write(io, "          $connectivity\n")
    end
    write(io, "        </DataArray>\n")
    
    # Cell offsets
    write(io, "        <DataArray type=\"Int32\" Name=\"offsets\" format=\"ascii\">\n")
    offset = 0
    for cell in mesh.cells
        offset += length(cell.nodes)
        write(io, "          $offset\n")
    end
    write(io, "        </DataArray>\n")
    
    # Cell types (assume hexahedron = 12)
    write(io, "        <DataArray type=\"UInt8\" Name=\"types\" format=\"ascii\">\n")
    for cell in mesh.cells
        cellType = length(cell.nodes) == 8 ? 12 : 10  # hex or tetrahedron
        write(io, "          $cellType\n")
    end
    write(io, "        </DataArray>\n")
    
    write(io, "      </Cells>\n")
end

# Write field data
function writeVTKFields(io::IO, mesh::AbstractMesh{T,N}, fields::Dict{String, F}) where {T,N,F<:AbstractField}
    if !isempty(fields)
        write(io, "      <CellData>\n")
        
        for (name, field) in fields
            writeVTKField(io, name, field, mesh)
        end
        
        write(io, "      </CellData>\n")
    end
end

# Write individual field
function writeVTKField(io::IO, name::String, field::ScalarField{T,N,M}, mesh::AbstractMesh) where {T,N,M}
    write(io, "        <DataArray type=\"Float64\" Name=\"$name\" format=\"ascii\">\n")
    for value in field.data
        write(io, "          $value\n")
    end
    write(io, "        </DataArray>\n")
end

function writeVTKField(io::IO, name::String, field::VectorField{SVector{N,T},N,M}, mesh::AbstractMesh) where {T,N,M}
    write(io, "        <DataArray type=\"Float64\" Name=\"$name\" NumberOfComponents=\"3\" format=\"ascii\">\n")
    for vec in field.data
        if N == 2
            write(io, "          $(vec[1]) $(vec[2]) 0.0\n")
        else
            write(io, "          $(vec[1]) $(vec[2]) $(vec[3])\n")
        end
    end
    write(io, "        </DataArray>\n")
end

# Write VTK footer
function writeVTKFooter(io::IO, time::Float64)
    write(io, "    </Piece>\n")
    write(io, "  </UnstructuredGrid>\n")
    
    # Add time information
    write(io, "  <FieldData>\n")
    write(io, "    <DataArray type=\"Float64\" Name=\"TIME\" NumberOfTuples=\"1\" format=\"ascii\">\n")
    write(io, "      $time\n")
    write(io, "    </DataArray>\n")
    write(io, "  </FieldData>\n")
    
    write(io, "</VTKFile>\n")
end

# Write time step for simulation
function writeTimeStep(simulation, t::Float64; 
                      basename::String="simulation",
                      outputDir::String=".")
    
    # Create time directory name
    timeStr = @sprintf("%.6f", t)
    filename = joinpath(outputDir, "$(basename)_$(timeStr)")
    
    # Collect all fields
    fields = Dict{String, AbstractField}()
    
    # Add velocity and pressure fields
    if isdefined(simulation, :U)
        fields["U"] = simulation.U
    end
    if isdefined(simulation, :p)
        fields["p"] = simulation.p
    end
    
    # Add turbulence fields if available
    if isdefined(simulation, :k) && simulation.k !== nothing
        fields["k"] = simulation.k
    end
    if isdefined(simulation, :epsilon) && simulation.epsilon !== nothing
        fields["epsilon"] = simulation.epsilon
    end
    if isdefined(simulation, :nut) && simulation.nut !== nothing
        fields["nut"] = simulation.nut
    end
    
    # Calculate derived fields if VortexIdentification is available
    try
        # Try to use VortexIdentification module
        if isdefined(simulation, :U) && hasmethod(Main.eval, (Symbol,))
            # Try to calculate Q-criterion
            if isdefined(Main, :VortexIdentification)
                VortexID = Main.VortexIdentification
                
                # Q-criterion
                Q = VortexID.QCriterion(simulation.U)
                fields["Q"] = Q
                
                # Vorticity
                omega = VortexID.vorticity(simulation.U)
                fields["vorticity"] = omega
                
                # Helicity
                H = VortexID.helicity(simulation.U)
                fields["helicity"] = H
            end
        end
    catch e
        # VortexIdentification not available or error occurred
        @debug "VortexIdentification not available: $e"
    end
    
    # Write VTK file
    writeVTK(filename, simulation.mesh, fields, time=t)
    
    println("Wrote time step $t to $filename.vtu")
    
    return basename
end

# Create VTK series
function createVTKSeries(basename::String)
    return VTKSeries(basename)
end

# Add time step to series
function addTimeStep!(series::VTKSeries, time::Float64, filename::String)
    push!(series.timesteps, time)
    push!(series.filenames, filename)
end

# Write ParaView series file for time animation
function writeVTKSeries(series::VTKSeries; outputDir::String=".")
    filename = joinpath(outputDir, "$(series.basename).series")
    
    open(filename, "w") do f
        write(f, "{\n")
        write(f, "  \"file-series-version\" : \"1.0\",\n")
        write(f, "  \"files\" : [\n")
        
        for (i, (t, fname)) in enumerate(zip(series.timesteps, series.filenames))
            write(f, "    { \"name\" : \"$fname.vtu\", \"time\" : $t }")
            if i < length(series.timesteps)
                write(f, ",")
            end
            write(f, "\n")
        end
        
        write(f, "  ]\n")
        write(f, "}\n")
    end
    
    println("Wrote series file: $filename")
    return filename
end

# Convenience function to write simulation results
function writeDroneSimulationResults(simulation, times::Vector{Float64};
                                   basename::String="drone_simulation",
                                   outputDir::String="paraview")
    
    # Create output directory
    if !isdir(outputDir)
        mkdir(outputDir)
    end
    
    # Create series
    series = VTKSeries(basename)
    
    # Write each time step
    for t in times
        filename = writeTimeStep(simulation, t, basename=basename, outputDir=outputDir)
        addTimeStep!(series, t, filename)
    end
    
    # Write series file
    writeVTKSeries(series, outputDir=outputDir)
    
    println("\nParaView files written to $outputDir/")
    println("To visualize:")
    println("1. Open ParaView")
    println("2. File -> Open -> $outputDir/$(basename).series")
    println("3. Apply Q-criterion filter (threshold Q > 1000)")
    println("4. Color by velocity magnitude")
end

end # module VTKOutput