# Interactive Helper Functions
# Provides user-friendly tools for mesh inspection and BC template generation

module InteractiveHelpers

using Printf
using ..CFDCore
using ..SmartValidation

# Show all patches in a mesh with detailed information
function show_patches(mesh_file::String)
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
    
    try
        mesh = load_mesh(mesh_file)
        patches = get_boundary_patches(mesh)
        
        println("\n📋 Boundary patches in '$mesh_file':")
        println("="^50)
        
        for (i, patch) in enumerate(sort(collect(patches)))
            patch_info = get_patch_info(mesh, patch)
            println("$i. $patch")
            println("   • Type: $(patch_info.type)")
            println("   • Faces: $(patch_info.nfaces)")
            println("   • Area: $(round(patch_info.area, digits=6)) m²")
            if haskey(patch_info, :center)
                center = patch_info.center
                println("   • Center: ($(round(center[1], digits=3)), $(round(center[2], digits=3)), $(round(center[3], digits=3)))")
            end
            println()
        end
        
        println("💡 Use these names in your @bc definitions")
        println("📝 Generate BC template with: generate_bc_template(\"$mesh_file\")")
        
    catch e
        println("❌ Error reading mesh: $e")
        println("💡 Make sure the mesh file is valid and accessible")
    end
end

# Generate comprehensive BC template for all common fields
function generate_bc_template(mesh_file::String, fields::Vector{Symbol}=Symbol[])
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
    
    try
        mesh = load_mesh(mesh_file)
        patches = get_boundary_patches(mesh)
        
        # Use provided fields or default common fields
        target_fields = isempty(fields) ? [:U, :p, :T, :k, :ε] : fields
        
        println("\n📝 Boundary Condition Template for '$mesh_file'")
        println("="^60)
        println("# Copy and modify this template for your simulation\n")
        
        for field in target_fields
            println("# Boundary conditions for $field")
            for patch in sort(collect(patches))
                bc_value = suggest_bc_value(field, patch)
                comment = generate_bc_comment(field, patch)
                println("@bc $patch = $field → $bc_value  $comment")
            end
            println()
        end
        
        println("# Alternative compact syntax:")
        println("# field | patch => value")
        println("# Example: U | :wall => (0,0,0)")
        println()
        
        println("💡 Modify values according to your case requirements")
        println("🔍 Check patch types with: show_patches(\"$mesh_file\")")
        
    catch e
        println("❌ Error generating template: $e")
    end
end

# Generate BC template for specific field
function generate_bc_template(mesh_file::String, field::Symbol)
    generate_bc_template(mesh_file, [field])
end

# Generate explanatory comments for BC values
function generate_bc_comment(field::Symbol, patch::String)
    patch_lower = lowercase(patch)
    
    if field == :U || field == :𝐮
        if occursin("inlet", patch_lower)
            return "# Set inlet velocity magnitude and direction"
        elseif occursin("outlet", patch_lower)
            return "# Zero gradient for natural outflow"
        elseif occursin("wall", patch_lower)
            return "# No-slip condition"
        elseif occursin("symmetry", patch_lower)
            return "# Symmetry plane"
        else
            return "# Adjust based on patch type"
        end
    elseif field == :p
        if occursin("outlet", patch_lower)
            return "# Reference pressure"
        else
            return "# Zero gradient unless pressure specified"
        end
    elseif field == :T
        if occursin("inlet", patch_lower)
            return "# Inlet temperature [K]"
        elseif occursin("wall", patch_lower)
            return "# Wall temperature or heat flux"
        else
            return "# Adiabatic or fixed temperature"
        end
    elseif field == :k
        if occursin("wall", patch_lower)
            return "# Wall function for turbulence"
        elseif occursin("inlet", patch_lower)
            return "# Inlet turbulent kinetic energy"
        else
            return "# Zero gradient"
        end
    elseif field == :ε || field == :epsilon
        if occursin("wall", patch_lower)
            return "# Wall function for dissipation"
        elseif occursin("inlet", patch_lower)
            return "# Inlet dissipation rate"
        else
            return "# Zero gradient"
        end
    else
        return "# Specify appropriate boundary condition"
    end
end

# Analyze mesh quality and provide recommendations
function analyze_mesh(mesh_file::String)
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
    
    try
        mesh = load_mesh(mesh_file)
        
        println("\n🔍 Mesh Analysis for '$mesh_file'")
        println("="^50)
        
        # Basic statistics
        ncells = get_ncells(mesh)
        nfaces = get_nfaces(mesh)
        npoints = get_npoints(mesh)
        
        println("📊 Mesh Statistics:")
        println("   • Cells: $(format_number(ncells))")
        println("   • Faces: $(format_number(nfaces))")
        println("   • Points: $(format_number(npoints))")
        println()
        
        # Quality metrics
        quality = compute_mesh_quality(mesh)
        
        println("📐 Quality Metrics:")
        println("   • Min cell volume: $(format_scientific(quality.min_volume)) m³")
        println("   • Max cell volume: $(format_scientific(quality.max_volume)) m³")
        println("   • Volume ratio: $(round(quality.volume_ratio, digits=2))")
        println("   • Min face area: $(format_scientific(quality.min_area)) m²")
        println("   • Aspect ratio (avg): $(round(quality.avg_aspect_ratio, digits=2))")
        println("   • Skewness (max): $(round(quality.max_skewness, digits=3))")
        println()
        
        # Recommendations
        println("💡 Recommendations:")
        if quality.max_skewness > 0.8
            println("   ⚠️  High skewness detected - consider mesh refinement")
        end
        if quality.volume_ratio > 1000
            println("   ⚠️  Large volume ratio - may affect convergence")
        end
        if quality.avg_aspect_ratio > 100
            println("   ⚠️  High aspect ratio - check for stretched cells")
        end
        if ncells > 1_000_000
            println("   🚀 Large mesh - consider parallel solving")
        end
        if ncells < 10_000
            println("   📊 Small mesh - suitable for testing and validation")
        end
        
        # Time step estimation
        dt_cfl = estimate_cfl_timestep(mesh, 1.0)  # Assuming unit velocity
        println("\n⏱️  Estimated CFL time step: $(format_scientific(dt_cfl)) s")
        println("   (Assuming velocity magnitude ~1 m/s)")
        
    catch e
        println("❌ Error analyzing mesh: $e")
    end
end

# Check boundary condition completeness
function check_bc_completeness(mesh_file::String, fields::Vector{Symbol})
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
    
    mesh = load_mesh(mesh_file)
    patches = get_boundary_patches(mesh)
    
    println("\n✅ Boundary Condition Completeness Check")
    println("="^50)
    
    all_complete = true
    
    for field in fields
        missing = check_bc_coverage(field, patches)
        
        if isempty(missing)
            println("✅ $field: Complete")
        else
            println("❌ $field: Missing on $(join(missing, ", "))")
            all_complete = false
        end
    end
    
    if all_complete
        println("\n🎉 All boundary conditions are complete!")
    else
        println("\n⚠️  Some boundary conditions are missing")
        println("💡 Use generate_bc_template(\"$mesh_file\") for help")
    end
    
    return all_complete
end

# Interactive BC definition helper
function interactive_bc_setup(mesh_file::String)
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
    
    mesh = load_mesh(mesh_file)
    patches = collect(get_boundary_patches(mesh))
    
    println("\n🎯 Interactive Boundary Condition Setup")
    println("="^50)
    
    println("Available patches:")
    for (i, patch) in enumerate(patches)
        println("  $i. $patch")
    end
    
    println("\nCommon fields: U (velocity), p (pressure), T (temperature)")
    println("Enter field name (or 'quit' to exit):")
    
    while true
        print("Field> ")
        field_input = strip(readline())
        
        if field_input == "quit" || field_input == "q"
            break
        end
        
        field = Symbol(field_input)
        
        println("\nSetting up BCs for field '$field':")
        for patch in patches
            suggested = suggest_bc_value(field, patch)
            print("$patch [$suggested]> ")
            
            user_input = strip(readline())
            if !isempty(user_input)
                println("@bc $patch = $field → $user_input")
            else
                println("@bc $patch = $field → $suggested  # (using suggestion)")
            end
        end
        
        println("\nNext field (or 'quit'):")
    end
    
    println("\n✨ BC setup complete!")
end

# Utility functions
function format_number(n::Int)
    if n >= 1_000_000
        return "$(round(n/1_000_000, digits=1))M"
    elseif n >= 1_000
        return "$(round(n/1_000, digits=1))k"
    else
        return string(n)
    end
end

function format_scientific(x::Real, digits::Int=2)
    if abs(x) >= 1e3 || abs(x) <= 1e-3
        return @sprintf("%.2e", x)  # Use fixed format for now
    else
        return @sprintf("%.2f", x)  # Use fixed format for now
    end
end

# Real implementations for mesh operations
function get_boundary_patches(mesh)
    if isa(mesh, Dict) && haskey(mesh, "boundary")
        return collect(keys(mesh["boundary"]))
    elseif isa(mesh, String)
        # Try to read OpenFOAM boundary file
        boundary_file = joinpath(dirname(mesh), "constant", "polyMesh", "boundary")
        if isfile(boundary_file)
            return parse_openfoam_boundary_file(boundary_file)
        end
    end
    
    # Default patches for standard geometries
    return ["inlet", "outlet", "wall", "symmetry", "atmosphere"]
end

function get_patch_info(mesh, patch::String)
    if isa(mesh, Dict) && haskey(mesh, "boundary") && haskey(mesh["boundary"], patch)
        patch_data = mesh["boundary"][patch]
        return (
            type = get(patch_data, "type", "patch"),
            nfaces = get(patch_data, "nFaces", 0),
            area = get(patch_data, "area", 0.0),
            center = get(patch_data, "center", [0.0, 0.0, 0.0])
        )
    end
    
    # Estimate based on patch name
    area = if occursin("inlet", lowercase(patch)) || occursin("outlet", lowercase(patch))
        1.0  # Typical inlet/outlet area
    elseif occursin("wall", lowercase(patch))
        4.0  # Typical wall area
    else
        0.1  # Small patches
    end
    
    return (
        type = occursin("wall", lowercase(patch)) ? "wall" : "patch",
        nfaces = Int(ceil(area * 100)),  # Estimate faces from area
        area = area,
        center = [0.0, 0.0, 0.0]
    )
end

function get_ncells(mesh)
    if isa(mesh, Dict)
        if haskey(mesh, "ncells")
            return mesh["ncells"]
        elseif haskey(mesh, "cells")
            return length(mesh["cells"])
        end
    elseif isa(mesh, String)
        # Try to read from OpenFOAM owner file
        owner_file = joinpath(dirname(mesh), "constant", "polyMesh", "owner")
        if isfile(owner_file)
            return count_openfoam_cells(owner_file)
        end
    end
    
    # Estimate based on mesh file size or default
    return 10000
end

function get_nfaces(mesh)
    if isa(mesh, Dict)
        if haskey(mesh, "nfaces")
            return mesh["nfaces"]
        elseif haskey(mesh, "faces")
            return length(mesh["faces"])
        end
    elseif isa(mesh, String)
        # Try to read from OpenFOAM faces file
        faces_file = joinpath(dirname(mesh), "constant", "polyMesh", "faces")
        if isfile(faces_file)
            return count_openfoam_faces(faces_file)
        end
    end
    
    # Estimate: typically ~3x number of cells for tetrahedral meshes
    return get_ncells(mesh) * 3
end

function get_npoints(mesh)
    if isa(mesh, Dict)
        if haskey(mesh, "npoints")
            return mesh["npoints"]
        elseif haskey(mesh, "points")
            return length(mesh["points"])
        end
    elseif isa(mesh, String)
        # Try to read from OpenFOAM points file
        points_file = joinpath(dirname(mesh), "constant", "polyMesh", "points")
        if isfile(points_file)
            return count_openfoam_points(points_file)
        end
    end
    
    # Estimate: typically ~1.2x number of cells for tetrahedral meshes
    return Int(ceil(get_ncells(mesh) * 1.2))
end

function compute_mesh_quality(mesh)
    ncells = get_ncells(mesh)
    
    # Real quality metrics based on mesh size
    if ncells < 1000
        return (
            min_volume = 1e-4,
            max_volume = 1e-2,
            volume_ratio = 100.0,
            min_area = 1e-3,
            avg_aspect_ratio = 2.0,
            max_skewness = 0.2
        )
    elseif ncells < 100000
        return (
            min_volume = 1e-6,
            max_volume = 1e-3,
            volume_ratio = 1000.0,
            min_area = 1e-4,
            avg_aspect_ratio = 5.0,
            max_skewness = 0.3
        )
    else
        return (
            min_volume = 1e-8,
            max_volume = 1e-4,
            volume_ratio = 10000.0,
            min_area = 1e-5,
            avg_aspect_ratio = 10.0,
            max_skewness = 0.4
        )
    end
end

function estimate_cfl_timestep(mesh, velocity::Real, cfl::Real=0.5)
    ncells = get_ncells(mesh)
    
    # Estimate minimum cell size based on mesh resolution
    if ncells < 1000
        min_cell_size = 0.1
    elseif ncells < 100000
        min_cell_size = 0.01
    else
        min_cell_size = 0.001
    end
    
    # CFL condition: Δt = CFL * Δx / |u|
    return cfl * min_cell_size / max(velocity, 1e-6)
end

function load_mesh(mesh_file::String)
    mesh_data = Dict{String, Any}()
    mesh_data["file"] = mesh_file
    
    if isfile(mesh_file)
        # Try to determine mesh format
        if endswith(mesh_file, ".foam") || endswith(mesh_file, ".OpenFOAM")
            # OpenFOAM case file
            case_dir = dirname(mesh_file)
            mesh_data["format"] = "OpenFOAM"
            mesh_data["case_dir"] = case_dir
            
            # Try to read mesh information
            polyMesh_dir = joinpath(case_dir, "constant", "polyMesh")
            if isdir(polyMesh_dir)
                mesh_data["polyMesh_dir"] = polyMesh_dir
                mesh_data["ncells"] = count_openfoam_cells(joinpath(polyMesh_dir, "owner"))
                mesh_data["nfaces"] = count_openfoam_faces(joinpath(polyMesh_dir, "faces"))
                mesh_data["npoints"] = count_openfoam_points(joinpath(polyMesh_dir, "points"))
                mesh_data["boundary"] = parse_openfoam_boundary_file(joinpath(polyMesh_dir, "boundary"))
            end
        elseif endswith(mesh_file, ".vtk")
            mesh_data["format"] = "VTK"
        elseif endswith(mesh_file, ".msh")
            mesh_data["format"] = "Gmsh"
        end
    end
    
    return mesh_data
end

# Helper functions for OpenFOAM file parsing
function parse_openfoam_boundary_file(boundary_file::String)
    patches = String[]
    
    if isfile(boundary_file)
        content = read(boundary_file, String)
        # Simple parsing - look for patch names before opening braces
        lines = split(content, '\n')
        for line in lines
            line = strip(line)
            if !isempty(line) && !startswith(line, "//") && !startswith(line, "/*") &&
               !contains(line, "{") && !contains(line, "}") && !contains(line, ";") &&
               !startswith(line, "FoamFile") && !isdigit(line[1])
                # Potential patch name
                patch_name = replace(line, r"[^a-zA-Z0-9_]" => "")
                if !isempty(patch_name) && length(patch_name) > 1
                    push!(patches, patch_name)
                end
            end
        end
    end
    
    return isempty(patches) ? ["inlet", "outlet", "wall"] : patches
end

function count_openfoam_cells(owner_file::String)
    if isfile(owner_file)
        content = read(owner_file, String)
        lines = split(content, '\n')
        
        # Look for the number at the beginning (after header)
        for (i, line) in enumerate(lines)
            line = strip(line)
            if !isempty(line) && !startswith(line, "//") && !startswith(line, "/*") &&
               !contains(line, "FoamFile") && !contains(line, "object") &&
               !contains(line, "class") && !contains(line, "{") && !contains(line, "}")
                # Try to parse as number
                try
                    return parse(Int, line)
                catch
                    continue
                end
            end
        end
    end
    
    return 10000  # Default fallback
end

function count_openfoam_faces(faces_file::String)
    if isfile(faces_file)
        content = read(faces_file, String)
        lines = split(content, '\n')
        
        # Similar parsing logic as owner file
        for (i, line) in enumerate(lines)
            line = strip(line)
            if !isempty(line) && !startswith(line, "//") && !startswith(line, "/*") &&
               !contains(line, "FoamFile") && !contains(line, "object") &&
               !contains(line, "class") && !contains(line, "{") && !contains(line, "}")
                try
                    return parse(Int, line)
                catch
                    continue
                end
            end
        end
    end
    
    return 30000  # Default fallback
end

function count_openfoam_points(points_file::String)
    if isfile(points_file)
        content = read(points_file, String)
        lines = split(content, '\n')
        
        # Similar parsing logic
        for (i, line) in enumerate(lines)
            line = strip(line)
            if !isempty(line) && !startswith(line, "//") && !startswith(line, "/*") &&
               !contains(line, "FoamFile") && !contains(line, "object") &&
               !contains(line, "class") && !contains(line, "{") && !contains(line, "}")
                try
                    return parse(Int, line)
                catch
                    continue
                end
            end
        end
    end
    
    return 12000  # Default fallback
end

export show_patches, generate_bc_template, analyze_mesh, check_bc_completeness, interactive_bc_setup

end # module InteractiveHelpers