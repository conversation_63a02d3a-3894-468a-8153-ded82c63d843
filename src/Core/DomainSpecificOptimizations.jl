"""
    DomainSpecificOptimizations Module
    
    Intelligent mesh pattern recognition and automatic optimizations for CFD workflows.
"""
module DomainSpecificOptimizations

using LinearAlgebra
using SparseArrays
using Statistics
using Printf
using StaticArrays

# Bring in canonical mesh / boundary types from CFDCore so that tests using
# `DomainSpecificOptimizations.SimpleMesh` are recognised by Numerics kernels
# which expect `CFD.CFDCore.AbstractMesh`.
import ..CFDCore: AbstractMesh as CoreAbstractMesh, AbstractBoundaryCondition as CoreABC, Node

# Re-export under local names to avoid touching the rest of this file
const AbstractMesh = CoreAbstractMesh
const AbstractBoundaryCondition = CoreABC

# Lightweight local placeholders that are *not* provided by CFDCore.  They stay
# here for backwards-compatibility of any external DSO-only usage.
abstract type AbstractField{T,N} end
abstract type AbstractCell{T,N} end
abstract type AbstractFace{T,N} end

# Placeholder for boundary condition types when not available
struct DirichletBC <: AbstractBoundaryCondition
    value::Union{Float64, Vector{Float64}}
end

struct NeumannBC <: AbstractBoundaryCondition
    gradient::Union{Float64, Vector{Float64}}
end

struct RobinBC <: AbstractBoundaryCondition
    alpha::Float64
    beta::Float64
    value::Float64
end

# Placeholder Field type
struct Field{T,N} <: AbstractField{T,N}
    data::Vector{T}
    boundary_conditions::Dict{String, AbstractBoundaryCondition}
end

# Placeholder Physics module and types
module Physics
    abstract type AbstractPhysicsModel end
    struct DummyPhysics <: AbstractPhysicsModel
        transient::Bool
        reynolds_number::Float64
        viscosity::Float64
        source_terms::Any
    end
end

# Basic mesh structures for pattern analysis
struct Cell{T,N} <: AbstractCell{T,N}
    id::Int
    faces::Vector{Int}        # store face indices; node list optional
    center::AbstractVector{T}
    volume::T

    function Cell{T,N}(id::Integer, faces::Vector{Int}, center::AbstractVector{T}, volume::T) where {T,N}
        new(convert(Int,id), faces, center, volume)
    end
end

# Convenience constructor used by numerical tests that pass node list first
function Cell(id::Integer, node_ids::Vector{Int}, face_ids::Vector{Int},
              center::AbstractVector{T}, volume::T) where {T}
    N = length(center)
    return Cell{T,N}(id, face_ids, center, volume)
end

# Allow generic vector representations (Vector or StaticArrays) for geometry fields
struct Face{T,N} <: AbstractFace{T,N}
    id::Int
    vertices::Vector{Int}
    cells::Vector{Int}
    center::AbstractVector{T}
    area::T
    normal::AbstractVector{T}
    boundary_info::Dict{String, Any}

    # Flexible outer constructor to accept any vector-like inputs and convert as needed
    function Face{T,N}(id::Integer, vertices::Vector{Int}, cells::Vector{Int},
                       center::AbstractVector{T}, area::T,
                       normal::AbstractVector{T}, boundary_info::Dict{String,Any}) where {T,N}
        return new(convert(Int,id), vertices, cells, center, area, normal, boundary_info)
    end
end

# Convenience constructor without explicit type parameters (for tests)
function Face(id::Integer, vertices::Vector{Int}, cells::Vector{Int},
              center::AbstractVector{T}, area::T,
              normal::AbstractVector{T}, boundary_info::Dict{String,Any}) where {T}
    N = length(center)
    return Face{T,N}(id, vertices, cells, center, area, normal, boundary_info)
end

struct SimpleMesh{T,N} <: AbstractMesh{T,N}
    vertices::Vector{Vector{T}}
    faces::Vector{Face{T,N}}
    cells::Vector{Cell{T,N}}
    boundary_patches::Dict{String, Vector{Int}}
end

# Flexible constructor for SimpleMesh to accept any vector-like vertex list (including StaticArrays)
function SimpleMesh(vertices::Vector{<:AbstractVector{T}},
                    faces::Vector{Face{T,N}},
                    cells::Vector{Cell{T,N}},
                    boundary_patches::Dict{String,Vector{Int}}) where {T,N}
    verts_vec = Vector{Vector{T}}(undef, length(vertices))
    for i in eachindex(vertices)
        verts_vec[i] = Vector{T}(vertices[i])  # convert SVector → Vector if needed
    end
    return SimpleMesh{T,N}(verts_vec, faces, cells, boundary_patches)
end

export @mesh_optimizer, @matrix_patterns, analyze_mesh_topology, optimize_for_mesh!
export detect_sparsity_structure, benchmark_operators
export StructuredMeshOptimizer, UnstructuredMeshOptimizer, SparsityPatternOptimizer
export optimize_for_mesh!, optimize_matrix_assembly!, optimize_boundary_application!
export TimeSteppingOptimizer, BoundaryConditionOptimizer
export @optimize_cfd, detect_mesh_structure, analyze_sparsity_pattern, detect_mesh_patterns
export SimpleMesh, Face, Cell, Node, DirichletBC, NeumannBC, RobinBC

# ============================================================================
# Advanced Mesh Pattern Detection and Analysis
# ============================================================================

"""
Comprehensive mesh topology and pattern analysis structure
"""
struct MeshAnalysis
    # Basic topology
    n_cells::Int
    n_faces::Int  
    n_vertices::Int
    n_boundary_faces::Int
    
    # Connectivity patterns
    is_structured::Bool
    grid_dimensions::Tuple{Int,Int,Int}
    connectivity_regularity::Float64
    neighbor_count_distribution::Vector{Int}
    
    # Geometric characteristics
    aspect_ratio_distribution::Vector{Float64}
    skewness_distribution::Vector{Float64}
    orthogonality_distribution::Vector{Float64}
    volume_ratio_distribution::Vector{Float64}
    
    # Cell type classification
    cell_type_distribution::Dict{Symbol,Int}
    dominant_cell_type::Symbol
    mesh_quality_score::Float64
    
    # Spatial organization
    has_regular_spacing::Bool
    spacing_uniformity::Float64
    coordinate_alignment::Vector{Float64}
    boundary_layer_detected::Bool
    
    # Performance characteristics
    estimated_bandwidth::Int
    memory_access_pattern::Symbol  # :cache_friendly, :scattered, :mixed
    vectorization_potential::Float64
    parallel_efficiency_estimate::Float64
end

"""
    detect_mesh_patterns(mesh) -> MeshAnalysis

Comprehensive analysis of mesh topology, connectivity patterns, and geometric characteristics.
Analyzes:
- Structured vs unstructured patterns
- Regular spacing and grid alignment
- Cell type distribution and aspect ratios
- Connectivity regularity and bandwidth
- Memory access patterns and optimization potential

Returns detailed MeshAnalysis structure with all detected patterns.
"""
function detect_mesh_patterns(mesh)
    @info "Starting comprehensive mesh pattern analysis..."
    
    # Basic topology extraction
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    n_vertices = length(mesh.vertices)
    
    # Count boundary faces
    n_boundary_faces = count(face -> length(face.cells) == 1, mesh.faces)
    
    @info "Basic topology: $n_cells cells, $n_faces faces, $n_vertices vertices"
    
    # ========================================================================
    # Connectivity Pattern Analysis
    # ========================================================================
    
    # Build connectivity graph and analyze regularity
    connectivity_info = analyze_connectivity_patterns(mesh)
    is_structured = connectivity_info.is_structured
    grid_dimensions = connectivity_info.dimensions
    connectivity_regularity = connectivity_info.regularity_score
    neighbor_distribution = connectivity_info.neighbor_counts
    
    @info "Connectivity analysis: structured=$is_structured, regularity=$connectivity_regularity"
    
    # ========================================================================
    # Geometric Quality Analysis  
    # ========================================================================
    
    # Analyze cell geometry and quality metrics
    geometry_info = analyze_cell_geometry(mesh)
    aspect_ratios = geometry_info.aspect_ratios
    skewness_values = geometry_info.skewness
    orthogonality_values = geometry_info.orthogonality  
    volume_ratios = geometry_info.volume_ratios
    
    @info "Geometry analysis: mean aspect ratio = $(mean(aspect_ratios))"
    
    # ========================================================================
    # Cell Type Classification
    # ========================================================================
    
    # Classify cells by type and compute distribution
    cell_classification = classify_cell_types(mesh)
    cell_type_dist = cell_classification.distribution
    dominant_type = cell_classification.dominant_type
    quality_score = cell_classification.quality_score
    
    @info "Cell types: dominant=$dominant_type, quality_score=$quality_score"
    
    # ========================================================================
    # Spatial Organization Analysis
    # ========================================================================
    
    # Analyze spatial patterns and regularity
    spatial_info = analyze_spatial_organization(mesh)
    has_regular_spacing = spatial_info.regular_spacing
    spacing_uniformity = spatial_info.uniformity_score
    coord_alignment = spatial_info.coordinate_alignment
    boundary_layer = spatial_info.boundary_layer_detected
    
    @info "Spatial organization: regular_spacing=$has_regular_spacing, uniformity=$spacing_uniformity"
    
    # ========================================================================
    # Performance Characteristics
    # ========================================================================
    
    # Estimate performance-related metrics
    perf_info = analyze_performance_characteristics(mesh, connectivity_info)
    bandwidth = perf_info.estimated_bandwidth
    memory_pattern = perf_info.memory_access_pattern
    vectorization_potential = perf_info.vectorization_score
    parallel_efficiency = perf_info.parallel_efficiency
    
    @info "Performance estimates: bandwidth=$bandwidth, memory_pattern=$memory_pattern"
    
    # Construct final analysis
    analysis = MeshAnalysis(
        n_cells, n_faces, n_vertices, n_boundary_faces,
        is_structured, grid_dimensions, connectivity_regularity, neighbor_distribution,
        aspect_ratios, skewness_values, orthogonality_values, volume_ratios,
        cell_type_dist, dominant_type, quality_score,
        has_regular_spacing, spacing_uniformity, coord_alignment, boundary_layer,
        bandwidth, memory_pattern, vectorization_potential, parallel_efficiency
    )
    
    @info "Mesh pattern analysis complete!"
    return analysis
end

"""
Analyze connectivity patterns to determine mesh structure type
"""
function analyze_connectivity_patterns(mesh)
    n_cells = length(mesh.cells)
    
    # Build connectivity matrix
    connectivity = build_detailed_connectivity(mesh)
    
    # Analyze neighbor count distribution
    neighbor_counts = [length(neighbors) for neighbors in connectivity]
    neighbor_stats = (
        mean = mean(neighbor_counts),
        std = std(neighbor_counts),
        min = minimum(neighbor_counts), 
        max = maximum(neighbor_counts)
    )
    
    # Check for structured pattern indicators
    structured_score = assess_structured_patterns(mesh, connectivity, neighbor_counts)
    is_structured = structured_score > 0.8
    
    # Attempt to detect grid dimensions for structured meshes
    dimensions = is_structured ? detect_grid_dimensions(mesh, connectivity) : (1, 1, n_cells)
    
    # Compute connectivity regularity score
    regularity = compute_connectivity_regularity(neighbor_counts, connectivity)
    
    return (
        is_structured = is_structured,
        dimensions = dimensions,
        regularity_score = regularity,
        neighbor_counts = neighbor_counts,
        neighbor_stats = neighbor_stats
    )
end

"""
Analyze geometric properties of mesh cells
"""
function analyze_cell_geometry(mesh)
    n_cells = length(mesh.cells)
    
    aspect_ratios = Float64[]
    skewness_values = Float64[]
    orthogonality_values = Float64[]  
    volume_ratios = Float64[]
    
    for (i, cell) in enumerate(mesh.cells)
        # Compute cell geometric properties
        vertices = get_cell_vertices(mesh, cell)
        
        # Aspect ratio: ratio of largest to smallest dimension
        bbox = compute_bounding_box(vertices)
        dimensions = [bbox.max[j] - bbox.min[j] for j in 1:3]
        aspect_ratio = maximum(dimensions) / (minimum(dimensions) + 1e-12)
        push!(aspect_ratios, min(aspect_ratio, 1000.0))  # Cap extreme values
        
        # Skewness: measure of cell deviation from ideal shape
        skewness = compute_cell_skewness(vertices)
        push!(skewness_values, skewness)
        
        # Orthogonality: measure of face perpendicularity  
        orthogonality = compute_cell_orthogonality(mesh, cell)
        push!(orthogonality_values, orthogonality)
        
        # Volume ratio compared to neighbors
        volume_ratio = compute_volume_ratio(mesh, i)
        push!(volume_ratios, volume_ratio)
    end
    
    return (
        aspect_ratios = aspect_ratios,
        skewness = skewness_values,
        orthogonality = orthogonality_values,
        volume_ratios = volume_ratios
    )
end

"""
Classify mesh cells by type and compute quality metrics
"""
function classify_cell_types(mesh)
    type_counts = Dict{Symbol,Int}()
    total_quality = 0.0
    
    for cell in mesh.cells
        # Determine cell type based on vertex count and connectivity
        cell_type = classify_single_cell(mesh, cell)
        type_counts[cell_type] = get(type_counts, cell_type, 0) + 1
        
        # Compute individual cell quality
        quality = compute_cell_quality(mesh, cell, cell_type)
        total_quality += quality
    end
    
    # Find dominant cell type
    dominant_type = argmax(type_counts)
    
    # Overall quality score
    avg_quality = total_quality / length(mesh.cells)
    
    return (
        distribution = type_counts,
        dominant_type = dominant_type,
        quality_score = avg_quality
    )
end

"""
Analyze spatial organization and regularity patterns
"""
function analyze_spatial_organization(mesh)
    # Analyze coordinate spacing regularity
    spacing_info = analyze_coordinate_spacing(mesh)
    
    # Check for boundary layer patterns
    boundary_layer = detect_boundary_layer_patterns(mesh)
    
    # Compute coordinate alignment scores
    alignment = compute_coordinate_alignment(mesh)
    
    return (
        regular_spacing = spacing_info.is_regular,
        uniformity_score = spacing_info.uniformity,
        coordinate_alignment = alignment,
        boundary_layer_detected = boundary_layer
    )
end

"""
Estimate performance characteristics for optimization
"""
function analyze_performance_characteristics(mesh, connectivity_info)
    # Estimate matrix bandwidth
    bandwidth = estimate_matrix_bandwidth(mesh, connectivity_info)
    
    # Analyze memory access patterns
    memory_pattern = analyze_memory_access_pattern(mesh, connectivity_info)
    
    # Estimate vectorization potential
    vectorization_score = estimate_vectorization_potential(mesh, connectivity_info)
    
    # Estimate parallel efficiency
    parallel_efficiency = estimate_parallel_efficiency(mesh, connectivity_info)
    
    return (
        estimated_bandwidth = bandwidth,
        memory_access_pattern = memory_pattern,
        vectorization_score = vectorization_score,
        parallel_efficiency = parallel_efficiency
    )
end

# ============================================================================
# Mesh-Specific Optimizations
# ============================================================================

abstract type MeshOptimizer end

"""
Optimizer for structured meshes with regular connectivity patterns
"""
struct StructuredMeshOptimizer <: MeshOptimizer
    nx::Int
    ny::Int
    nz::Int
    stencil_size::Int
    cache_line_size::Int
    
    function StructuredMeshOptimizer(mesh)
        # Detect structured mesh dimensions
        nx, ny, nz = detect_structured_dimensions(mesh)
        stencil_size = 7  # Standard 7-point stencil for 3D
        cache_line_size = 64  # Typical cache line size
        new(nx, ny, nz, stencil_size, cache_line_size)
    end
end

"""
Optimizer for unstructured meshes with irregular connectivity
"""
struct UnstructuredMeshOptimizer <: MeshOptimizer
    connectivity::Vector{Vector{Int}}
    cell_ordering::Vector{Int}
    face_ordering::Vector{Int}
    bandwidth::Int
    
    function UnstructuredMeshOptimizer(mesh)
        connectivity = build_connectivity_graph(mesh)
        # Optimize cell and face ordering for cache efficiency
        cell_ordering = optimize_cell_ordering(mesh, connectivity)
        face_ordering = optimize_face_ordering(mesh, connectivity)
        bandwidth = compute_matrix_bandwidth(connectivity)
        new(connectivity, cell_ordering, face_ordering, bandwidth)
    end
end

"""
Automatically detect mesh structure and return appropriate optimizer
"""
function detect_mesh_structure(mesh)
    # Analyze mesh connectivity patterns
    is_structured = check_structured_pattern(mesh)
    
    if is_structured
        return StructuredMeshOptimizer(mesh)
    else
        return UnstructuredMeshOptimizer(mesh)
    end
end

# ============================================================================
# Sparse Matrix Optimizations
# ============================================================================

"""
Optimizer for sparse matrix assembly and operations
"""
struct SparsityPatternOptimizer
    pattern::SparseMatrixCSC{Bool,Int}
    row_ptr::Vector{Int}
    col_idx::Vector{Int}
    block_size::Int
    symmetric::Bool
    
    function SparsityPatternOptimizer(mesh, equation_type::Symbol)
        # Build sparsity pattern based on mesh and equation type
        pattern = build_sparsity_pattern(mesh, equation_type)
        
        # Extract CSR format for efficient assembly
        row_ptr = pattern.colptr
        col_idx = pattern.rowval
        
        # Detect block structure for vectorization
        block_size = detect_block_size(pattern)
        
        # Check symmetry for optimization
        symmetric = is_symmetric_pattern(pattern)
        
        new(pattern, row_ptr, col_idx, block_size, symmetric)
    end
end

"""
Optimize matrix assembly for specific sparsity pattern
"""
function optimize_matrix_assembly!(A::SparseMatrixCSC, spo::SparsityPatternOptimizer, 
                                   mesh, coefficients::Function)
    # Use pattern-specific assembly strategy
    if spo.block_size > 1
        # Block assembly for structured patterns
        assemble_blocked!(A, spo, mesh, coefficients)
    elseif spo.symmetric
        # Symmetric assembly (only upper triangle)
        assemble_symmetric!(A, spo, mesh, coefficients)
    else
        # General assembly with optimized access pattern
        assemble_general!(A, spo, mesh, coefficients)
    end
    
    return A
end

# ============================================================================
# Boundary Condition Optimizations
# ============================================================================

"""
Optimizer for efficient boundary condition application
"""
struct BoundaryConditionOptimizer
    boundary_cells::Dict{String,Vector{Int}}
    boundary_faces::Dict{String,Vector{Int}}
    ghost_cells::Dict{String,Vector{Int}}
    bc_types::Dict{String,Symbol}
    
    function BoundaryConditionOptimizer(mesh, bcs::Dict{String,AbstractBoundaryCondition})
        # Pre-compute boundary information
        boundary_cells = Dict{String,Vector{Int}}()
        boundary_faces = Dict{String,Vector{Int}}()
        ghost_cells = Dict{String,Vector{Int}}()
        bc_types = Dict{String,Symbol}()
        
        for (patch_name, bc) in bcs
            # Extract cells and faces for each boundary patch
            cells, faces = get_boundary_entities(mesh, patch_name)
            boundary_cells[patch_name] = cells
            boundary_faces[patch_name] = faces
            
            # Create ghost cells for certain BC types
            if needs_ghost_cells(bc)
                ghost_cells[patch_name] = create_ghost_cells(mesh, faces)
            end
            
            # Store BC type for optimization
            bc_types[patch_name] = classify_bc_type(bc)
        end
        
        new(boundary_cells, boundary_faces, ghost_cells, bc_types)
    end
end

"""
Apply boundary conditions with optimized strategies
"""
function optimize_boundary_application!(field::Field, bco::BoundaryConditionOptimizer)
    for (patch_name, bc) in field.boundary_conditions
        bc_type = bco.bc_types[patch_name]
        
        if bc_type == :dirichlet
            # Direct value assignment
            apply_dirichlet_optimized!(field, patch_name, bc, bco.boundary_cells[patch_name])
        elseif bc_type == :neumann
            # Gradient-based update using ghost cells
            apply_neumann_optimized!(field, patch_name, bc, bco.ghost_cells[patch_name])
        elseif bc_type == :robin
            # Mixed condition optimization
            apply_robin_optimized!(field, patch_name, bc, bco.boundary_faces[patch_name])
        end
    end
end

# ============================================================================
# Time-Stepping Optimizations
# ============================================================================

"""
Optimizer for time-stepping schemes based on problem characteristics
"""
struct TimeSteppingOptimizer
    scheme::Symbol
    stability_limit::Float64
    adaptive::Bool
    predictor_corrector::Bool
    sub_iterations::Int
    
    function TimeSteppingOptimizer(physics::Physics.AbstractPhysicsModel, mesh, target_cfl::Float64)
        # Analyze problem characteristics
        is_steady = check_steady_state(physics)
        is_stiff = check_stiffness(physics, mesh)
        has_source_terms = check_source_terms(physics)
        
        # Select optimal time-stepping scheme
        if is_steady
            scheme = :steady_state
            stability_limit = Inf
            adaptive = false
            predictor_corrector = false
            sub_iterations = 1
        elseif is_stiff
            scheme = :implicit_euler
            stability_limit = 10.0 * target_cfl  # Implicit allows larger timesteps
            adaptive = true
            predictor_corrector = true
            sub_iterations = 3
        else
            scheme = :runge_kutta_4
            stability_limit = target_cfl
            adaptive = true
            predictor_corrector = false
            sub_iterations = 1
        end
        
        new(scheme, stability_limit, adaptive, predictor_corrector, sub_iterations)
    end
end

# ============================================================================
# Automatic CFD Optimization Macro
# ============================================================================

"""
Macro to automatically optimize CFD operations based on detected patterns
"""
macro optimize_cfd(expr)
    quote
        # Detect mesh structure
        mesh_optimizer = detect_mesh_structure(mesh)
        
        # Build sparsity pattern optimizer
        sparsity_optimizer = SparsityPatternOptimizer(mesh, :navier_stokes)
        
        # Create boundary condition optimizer
        bc_optimizer = BoundaryConditionOptimizer(mesh, boundary_conditions)
        
        # Setup time-stepping optimizer
        time_optimizer = TimeSteppingOptimizer(physics, mesh, target_cfl)
        
        # Apply optimizations to the expression
        optimized_expr = apply_optimizations($expr, mesh_optimizer, sparsity_optimizer, 
                                           bc_optimizer, time_optimizer)
        
        # Execute optimized expression
        optimized_expr
    end
end

# ============================================================================
# Helper Functions
# ============================================================================

function detect_structured_dimensions(mesh)
    # Analyze mesh connectivity to detect structured pattern
    cells = mesh.cells
    n_cells = length(cells)
    
    # Check for regular spacing and connectivity
    # This is simplified - real implementation would be more sophisticated
    nx = ny = nz = 1
    
    # Try to find regular grid dimensions
    for i in 1:100
        for j in 1:100
            for k in 1:100
                if i * j * k == n_cells
                    # Additional checks for regular spacing
                    if check_regular_spacing(mesh, i, j, k)
                        return i, j, k
                    end
                end
            end
        end
    end
    
    return 1, 1, n_cells  # Fallback to 1D
end

function check_structured_pattern(mesh)
    # Check if mesh has structured connectivity
    # Look for regular patterns in face-to-cell connectivity
    
    # Sample a subset of cells
    sample_size = min(100, length(mesh.cells))
    neighbor_counts = Int[]
    
    for i in 1:sample_size
        cell_idx = rand(1:length(mesh.cells))
        n_neighbors = count_cell_neighbors(mesh, cell_idx)
        push!(neighbor_counts, n_neighbors)
    end
    
    # Structured meshes have consistent neighbor counts
    return std(neighbor_counts) < 0.1
end

function build_connectivity_graph(mesh)
    # Build cell-to-cell connectivity graph
    n_cells = length(mesh.cells)
    connectivity = [Int[] for _ in 1:n_cells]
    
    for face in mesh.faces
        if length(face.cells) == 2
            cell1, cell2 = face.cells
            push!(connectivity[cell1], cell2)
            push!(connectivity[cell2], cell1)
        end
    end
    
    return connectivity
end

function optimize_cell_ordering(mesh, connectivity::Vector{Vector{Int}})
    # Use Cuthill-McKee algorithm for bandwidth reduction
    n_cells = length(mesh.cells)
    ordering = collect(1:n_cells)
    
    # Simplified RCM algorithm
    # Real implementation would use proper graph algorithms
    visited = falses(n_cells)
    new_ordering = Int[]
    
    # Start from minimum degree node
    degrees = [length(neighbors) for neighbors in connectivity]
    start_node = argmin(degrees)
    
    # BFS with degree ordering
    queue = [start_node]
    visited[start_node] = true
    
    while !isempty(queue)
        node = popfirst!(queue)
        push!(new_ordering, node)
        
        # Add unvisited neighbors sorted by degree
        neighbors = connectivity[node]
        unvisited_neighbors = [n for n in neighbors if !visited[n]]
        sort!(unvisited_neighbors, by=n->degrees[n])
        
        for neighbor in unvisited_neighbors
            if !visited[neighbor]
                visited[neighbor] = true
                push!(queue, neighbor)
            end
        end
    end
    
    return new_ordering
end

function optimize_face_ordering(mesh, connectivity::Vector{Vector{Int}})
    # Order faces for optimal cache access during flux computation
    # Group faces by type (internal, boundary) and optimize within groups
    
    internal_faces = Int[]
    boundary_faces = Int[]
    
    for (i, face) in enumerate(mesh.faces)
        if length(face.cells) == 2
            push!(internal_faces, i)
        else
            push!(boundary_faces, i)
        end
    end
    
    # Order internal faces for cache efficiency
    # Real implementation would consider cell ordering
    return vcat(internal_faces, boundary_faces)
end

function build_sparsity_pattern(mesh, equation_type::Symbol)
    n_cells = length(mesh.cells)
    
    # Build sparsity pattern based on mesh connectivity
    I = Int[]
    J = Int[]
    
    for (i, cell) in enumerate(mesh.cells)
        # Diagonal entry
        push!(I, i)
        push!(J, i)
        
        # Off-diagonal entries from face neighbors
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            for neighbor_idx in face.cells
                if neighbor_idx != i && neighbor_idx <= n_cells
                    push!(I, i)
                    push!(J, neighbor_idx)
                end
            end
        end
    end
    
    # Create sparse pattern matrix
    V = ones(Bool, length(I))
    pattern = sparse(I, J, V, n_cells, n_cells)
    
    return pattern
end

function detect_block_size(pattern::SparseMatrixCSC)
    # Detect if matrix has block structure
    # Simplified version - checks for regular patterns
    
    n = size(pattern, 1)
    if n < 100
        return 1  # Too small for blocking
    end
    
    # Check for regular block patterns (e.g., from structured mesh)
    # This is simplified - real implementation would be more sophisticated
    for block_size in [2, 3, 4, 5, 7, 9]
        if n % block_size == 0 && check_block_pattern(pattern, block_size)
            return block_size
        end
    end
    
    return 1
end

function check_block_pattern(pattern::SparseMatrixCSC, block_size::Int)
    # Check if pattern has regular block structure
    # Simplified check - real implementation would be more thorough
    n_blocks = div(size(pattern, 1), block_size)
    
    # Sample a few blocks
    for i in 1:min(10, n_blocks)
        block_start = (i-1) * block_size + 1
        block_end = i * block_size
        
        # Check if block has expected structure
        block_nnz = nnz(pattern[block_start:block_end, block_start:block_end])
        expected_nnz = block_size * block_size
        
        if block_nnz < 0.8 * expected_nnz
            return false
        end
    end
    
    return true
end

# ============================================================================
# Assembly Functions
# ============================================================================

function assemble_blocked!(A::SparseMatrixCSC, spo::SparsityPatternOptimizer, 
                          mesh, coefficients::Function)
    # Block-based assembly for structured patterns
    block_size = spo.block_size
    n_blocks = div(size(A, 1), block_size)
    
    # Process blocks for better cache efficiency
    for block_i in 1:n_blocks
        for block_j in 1:n_blocks
            # Check if block exists in pattern
            if has_block(spo.pattern, block_i, block_j, block_size)
                # Assemble entire block at once
                block_vals = compute_block_values(mesh, coefficients, block_i, block_j, block_size)
                set_block!(A, block_i, block_j, block_vals, block_size)
            end
        end
    end
end

function assemble_symmetric!(A::SparseMatrixCSC, spo::SparsityPatternOptimizer,
                           mesh, coefficients::Function)
    # Symmetric assembly - only compute upper triangle
    n = size(A, 1)
    
    for i in 1:n
        for j in i:n  # Only upper triangle
            if spo.pattern[i,j]
                val = coefficients(mesh, i, j)
                A[i,j] = val
                if i != j
                    A[j,i] = val  # Symmetric entry
                end
            end
        end
    end
end

function assemble_general!(A::SparseMatrixCSC, spo::SparsityPatternOptimizer,
                         mesh, coefficients::Function)
    # General assembly with optimized access pattern
    # Use CSR format for row-wise access
    for i in 1:length(spo.row_ptr)-1
        row_start = spo.row_ptr[i]
        row_end = spo.row_ptr[i+1] - 1
        
        for idx in row_start:row_end
            j = spo.col_idx[idx]
            A[i,j] = coefficients(mesh, i, j)
        end
    end
end

# ============================================================================
# Boundary Condition Functions
# ============================================================================

function apply_dirichlet_optimized!(field::Field, patch_name::String, 
                                   bc::AbstractBoundaryCondition, cells::Vector{Int})
    # Vectorized Dirichlet application
    values = get_bc_values(bc, length(cells))
    field.data[cells] .= values
end

function apply_neumann_optimized!(field::Field, patch_name::String,
                                bc::AbstractBoundaryCondition, ghost_cells::Vector{Int})
    # Use ghost cells for gradient conditions
    gradients = get_bc_gradients(bc, length(ghost_cells))
    
    for (i, ghost_idx) in enumerate(ghost_cells)
        # Apply gradient using ghost cell approach
        apply_gradient_condition!(field, ghost_idx, gradients[i])
    end
end

# ============================================================================
# Utility Functions
# ============================================================================

function count_cell_neighbors(mesh, cell_idx::Int)
    cell = mesh.cells[cell_idx]
    neighbors = Set{Int}()
    
    for face_idx in cell.faces
        face = mesh.faces[face_idx]
        for neighbor_idx in face.cells
            if neighbor_idx != cell_idx
                push!(neighbors, neighbor_idx)
            end
        end
    end
    
    return length(neighbors)
end

function check_regular_spacing(mesh, nx::Int, ny::Int, nz::Int)
    # Check if cells are regularly spaced (structured mesh)
    # Sample a few cells and check distances
    
    if length(mesh.cells) != nx * ny * nz
        return false
    end
    
    # Sample cells along each direction
    # Simplified check - real implementation would be more thorough
    return true  # Placeholder
end

function needs_ghost_cells(bc::AbstractBoundaryCondition)
    # Determine if BC type requires ghost cells
    return isa(bc, NeumannBC)  # Remove undefined GradientBC
end

function create_ghost_cells(mesh, face_indices::Vector{Int})
    # Create ghost cells for gradient-based BCs
    ghost_cells = Int[]
    
    for face_idx in face_indices
        # Create ghost cell opposite to the face
        # Simplified - real implementation would properly position ghost cells
        push!(ghost_cells, length(mesh.cells) + length(ghost_cells) + 1)
    end
    
    return ghost_cells
end

function classify_bc_type(bc::AbstractBoundaryCondition)
    if isa(bc, DirichletBC)
        return :dirichlet
    elseif isa(bc, NeumannBC)
        return :neumann
    elseif isa(bc, RobinBC)
        return :robin
    else
        return :general
    end
end

# ============================================================================
# Missing Helper Function Implementations
# ============================================================================

function compute_matrix_bandwidth(connectivity::Vector{Vector{Int}})
    # Calculate matrix bandwidth for bandwidth optimization
    max_bandwidth = 0
    for (i, neighbors) in enumerate(connectivity)
        for neighbor in neighbors
            bandwidth = abs(neighbor - i)
            max_bandwidth = max(max_bandwidth, bandwidth)
        end
    end
    return max_bandwidth
end

function is_symmetric_pattern(pattern::SparseMatrixCSC)
    # Check if sparsity pattern is symmetric
    m, n = size(pattern)
    if m != n
        return false
    end
    
    # Sample check for efficiency
    sample_size = min(100, m)
    for _ in 1:sample_size
        i = rand(1:m)
        j = rand(1:n)
        if pattern[i,j] != pattern[j,i]
            return false
        end
    end
    return true
end

function get_boundary_entities(mesh, patch_name::String)
    # Extract boundary cells and faces for a given patch
    boundary_cells = Int[]
    boundary_faces = Int[]
    
    # Find faces on the specified boundary patch
    for (face_idx, face) in enumerate(mesh.faces)
        if haskey(face.boundary_info, patch_name)
            push!(boundary_faces, face_idx)
            # Add cells adjacent to this face
            for cell_idx in face.cells
                if cell_idx <= length(mesh.cells)  # Valid cell index
                    push!(boundary_cells, cell_idx)
                end
            end
        end
    end
    
    return unique(boundary_cells), boundary_faces
end

function check_steady_state(physics::Physics.AbstractPhysicsModel)
    # Check if physics indicates steady-state problem
    return hasfield(typeof(physics), :transient) ? !physics.transient : false
end

function check_stiffness(physics::Physics.AbstractPhysicsModel, mesh)
    # Simplified stiffness check based on physics type and mesh characteristics
    # Real implementation would analyze eigenvalues
    
    # Check for high Reynolds number flow (potentially stiff)
    if hasfield(typeof(physics), :reynolds_number)
        return physics.reynolds_number > 1000
    end
    
    # Check for small time scales or high gradients
    if hasfield(typeof(physics), :viscosity)
        # High viscosity can indicate stiffness
        return physics.viscosity > 0.1
    end
    
    return false  # Default to non-stiff
end

function check_source_terms(physics::Physics.AbstractPhysicsModel)
    # Check if physics includes source terms
    return hasfield(typeof(physics), :source_terms) && !isnothing(physics.source_terms)
end

function has_block(pattern::SparseMatrixCSC, block_i::Int, block_j::Int, block_size::Int)
    # Check if a block exists in the sparsity pattern
    start_i = (block_i - 1) * block_size + 1
    end_i = block_i * block_size
    start_j = (block_j - 1) * block_size + 1
    end_j = block_j * block_size
    
    # Check if any entry in the block is non-zero
    for i in start_i:end_i
        for j in start_j:end_j
            if i <= size(pattern, 1) && j <= size(pattern, 2) && pattern[i,j]
                return true
            end
        end
    end
    return false
end

function compute_block_values(mesh, coefficients::Function, 
                            block_i::Int, block_j::Int, block_size::Int)
    # Compute values for a block in matrix assembly
    block_vals = zeros(block_size, block_size)
    
    start_i = (block_i - 1) * block_size + 1
    start_j = (block_j - 1) * block_size + 1
    
    for local_i in 1:block_size
        for local_j in 1:block_size
            global_i = start_i + local_i - 1
            global_j = start_j + local_j - 1
            
            if global_i <= length(mesh.cells) && global_j <= length(mesh.cells)
                block_vals[local_i, local_j] = coefficients(mesh, global_i, global_j)
            end
        end
    end
    
    return block_vals
end

function set_block!(A::SparseMatrixCSC, block_i::Int, block_j::Int, 
                   block_vals::Matrix, block_size::Int)
    # Set block values in sparse matrix
    start_i = (block_i - 1) * block_size + 1
    start_j = (block_j - 1) * block_size + 1
    
    for local_i in 1:block_size
        for local_j in 1:block_size
            global_i = start_i + local_i - 1
            global_j = start_j + local_j - 1
            
            if global_i <= size(A, 1) && global_j <= size(A, 2)
                A[global_i, global_j] = block_vals[local_i, local_j]
            end
        end
    end
end

function get_bc_values(bc::AbstractBoundaryCondition, n_cells::Int)
    # Get boundary condition values for Dirichlet BC
    if hasfield(typeof(bc), :value)
        if isa(bc.value, Number)
            return fill(bc.value, n_cells)
        else
            return bc.value  # Assume it's already a vector
        end
    else
        return zeros(n_cells)  # Default to zero
    end
end

function get_bc_gradients(bc::AbstractBoundaryCondition, n_cells::Int)
    # Get boundary condition gradients for Neumann BC
    if hasfield(typeof(bc), :gradient)
        if isa(bc.gradient, Number)
            return fill(bc.gradient, n_cells)
        else
            return bc.gradient  # Assume it's already a vector
        end
    else
        return zeros(n_cells)  # Default to zero gradient
    end
end

function apply_gradient_condition!(field::Field, ghost_idx::Int, gradient::Float64)
    # Apply gradient boundary condition using ghost cell
    # Simplified implementation - real version would use proper geometric factors
    if ghost_idx <= length(field.data)
        # Find corresponding boundary cell
        boundary_cell = max(1, ghost_idx - 1)
        if boundary_cell <= length(field.data)
            field.data[ghost_idx] = field.data[boundary_cell] + gradient * 0.01  # Simplified
        end
    end
end

function apply_optimizations(expr, mesh_optimizer::MeshOptimizer, 
                           sparsity_optimizer::SparsityPatternOptimizer,
                           bc_optimizer::BoundaryConditionOptimizer,
                           time_optimizer::TimeSteppingOptimizer)
    # Apply all optimizations to the given expression
    # This is a placeholder for the macro implementation
    return expr
end

# ============================================================================
# Advanced Mesh Analysis Helper Functions
# ============================================================================

"""
Build detailed connectivity graph with enhanced information
"""
function build_detailed_connectivity(mesh)
    n_cells = length(mesh.cells)
    connectivity = [Int[] for _ in 1:n_cells]
    
    # Build face-based connectivity
    for face in mesh.faces
        if length(face.cells) == 2
            cell1, cell2 = face.cells
            if cell1 <= n_cells && cell2 <= n_cells
                push!(connectivity[cell1], cell2)
                push!(connectivity[cell2], cell1)
            end
        end
    end
    
    # Remove duplicates and sort for consistency
    for i in 1:n_cells
        connectivity[i] = sort(unique(connectivity[i]))
    end
    
    return connectivity
end

"""
Assess how structured a mesh appears based on connectivity patterns
"""
function assess_structured_patterns(mesh, connectivity::Vector{Vector{Int}}, neighbor_counts::Vector{Int})
    n_cells = length(mesh.cells)
    
    # Check neighbor count regularity (structured meshes have consistent neighbors)
    neighbor_variance = var(neighbor_counts)
    neighbor_regularity = exp(-neighbor_variance)  # High variance → low regularity
    
    # Check for grid-like patterns in 2D/3D
    # Structured meshes often have 4/6 neighbors (2D/3D cartesian) or consistent patterns
    common_counts = countmap(neighbor_counts)
    max_count_frequency = maximum(values(common_counts)) / n_cells
    
    # Check coordinate alignment for first few cells
    coordinate_alignment = analyze_coordinate_alignment_sample(mesh)
    
    # Check for regular spacing patterns
    spacing_regularity = analyze_spacing_regularity_sample(mesh)
    
    # Combine metrics for structured score
    structured_score = 0.3 * neighbor_regularity + 
                      0.3 * max_count_frequency + 
                      0.2 * coordinate_alignment +
                      0.2 * spacing_regularity
    
    return min(structured_score, 1.0)
end

"""
Detect grid dimensions for structured meshes
"""
function detect_grid_dimensions(mesh, connectivity::Vector{Vector{Int}})
    n_cells = length(mesh.cells)
    
    # For small meshes, try brute force factorization
    if n_cells <= 10000
        return find_grid_factors_bruteforce(n_cells, mesh)
    end
    
    # For larger meshes, use heuristics based on connectivity and geometry
    return estimate_grid_dimensions_heuristic(mesh, connectivity)
end

"""
Brute force search for grid dimensions
"""
function find_grid_factors_bruteforce(n_cells::Int, mesh)
    best_score = 0.0
    best_dims = (1, 1, n_cells)
    
    # Try different factorizations
    for nx in 1:Int(ceil(n_cells^(1/3))) + 10
        for ny in 1:Int(ceil((n_cells/nx)^0.5)) + 10
            if n_cells % (nx * ny) == 0
                nz = div(n_cells, nx * ny)
                
                # Score this factorization based on geometric regularity
                score = score_grid_factorization(mesh, nx, ny, nz)
                if score > best_score
                    best_score = score
                    best_dims = (nx, ny, nz)
                end
            end
        end
    end
    
    return best_dims
end

"""
Estimate grid dimensions using heuristics
"""
function estimate_grid_dimensions_heuristic(mesh, connectivity::Vector{Vector{Int}})
    n_cells = length(mesh.cells)
    
    # Analyze cell positions to infer grid structure
    positions = [compute_cell_centroid(mesh, i) for i in 1:min(1000, n_cells)]
    
    # Find dominant coordinate directions
    coord_ranges = [[pos[i] for pos in positions] for i in 1:3]
    
    # Estimate dimensions based on coordinate distributions
    nx = estimate_dimension_from_coordinates(coord_ranges[1])
    ny = estimate_dimension_from_coordinates(coord_ranges[2])
    nz = max(1, div(n_cells, nx * ny))
    
    return (nx, ny, nz)
end

"""
Score a proposed grid factorization
"""
function score_grid_factorization(mesh, nx::Int, ny::Int, nz::Int)
    n_cells = length(mesh.cells)
    sample_size = min(100, n_cells)
    
    score = 0.0
    for i in 1:sample_size
        cell_idx = rand(1:n_cells)
        
        # Compute expected position in grid
        grid_pos = linear_to_grid_index(cell_idx, nx, ny, nz)
        
        # Compare with actual geometric position
        actual_pos = compute_cell_centroid(mesh, cell_idx)
        
        # Score based on how well grid position matches geometry
        geometric_score = compute_position_alignment_score(grid_pos, actual_pos)
        score += geometric_score
    end
    
    return score / sample_size
end

"""
Compute connectivity regularity score
"""
function compute_connectivity_regularity(neighbor_counts::Vector{Int}, connectivity::Vector{Vector{Int}})
    # Measure how regular the connectivity pattern is
    
    # Neighbor count regularity
    count_variance = var(neighbor_counts)
    count_regularity = exp(-count_variance / 2.0)
    
    # Connectivity pattern regularity (check for recurring patterns)
    pattern_regularity = analyze_connectivity_patterns_regularity(connectivity)
    
    return 0.6 * count_regularity + 0.4 * pattern_regularity
end

"""
Analyze connectivity patterns for regularity
"""
function analyze_connectivity_patterns_regularity(connectivity::Vector{Vector{Int}})
    n_cells = length(connectivity)
    sample_size = min(100, n_cells)
    
    # Analyze local connectivity patterns
    pattern_scores = Float64[]
    
    for i in 1:sample_size
        cell_idx = rand(1:n_cells)
        neighbors = connectivity[cell_idx]
        
        # Check if neighbors have similar connectivity patterns
        pattern_score = 0.0
        if !isempty(neighbors)
            for neighbor in neighbors
                if neighbor <= n_cells
                    # Compare connectivity patterns
                    neighbor_neighbors = connectivity[neighbor]
                    similarity = compute_connectivity_similarity(neighbors, neighbor_neighbors)
                    pattern_score += similarity
                end
            end
            pattern_score /= length(neighbors)
        end
        
        push!(pattern_scores, pattern_score)
    end
    
    return mean(pattern_scores)
end

"""
Get vertices of a cell
"""
function get_cell_vertices(mesh, cell)
    # Extract vertex indices from cell faces
    vertex_set = Set{Int}()
    
    for face_idx in cell.faces
        if face_idx <= length(mesh.faces)
            face = mesh.faces[face_idx]
            for vertex_idx in face.vertices
                push!(vertex_set, vertex_idx)
            end
        end
    end
    
    # Return vertex positions
    vertices = []
    for vertex_idx in vertex_set
        if vertex_idx <= length(mesh.vertices)
            push!(vertices, mesh.vertices[vertex_idx])
        end
    end
    
    return vertices
end

"""
Compute bounding box of vertices
"""
function compute_bounding_box(vertices)
    if isempty(vertices)
        return (min = [0.0, 0.0, 0.0], max = [0.0, 0.0, 0.0])
    end
    
    min_coords = [Inf, Inf, Inf]
    max_coords = [-Inf, -Inf, -Inf]
    
    for vertex in vertices
        for i in 1:3
            coord = length(vertex) >= i ? vertex[i] : 0.0
            min_coords[i] = min(min_coords[i], coord)
            max_coords[i] = max(max_coords[i], coord)
        end
    end
    
    return (min = min_coords, max = max_coords)
end

"""
Compute cell skewness measure
"""
function compute_cell_skewness(vertices)
    if length(vertices) < 4
        return 0.0
    end
    
    # Simplified skewness: measure deviation from orthogonal directions
    # Real implementation would use proper geometric skewness formulas
    
    # Compute edge vectors
    edge_vectors = []
    for i in 2:min(4, length(vertices))
        edge = vertices[i] - vertices[1]
        if length(edge) >= 3
            push!(edge_vectors, edge[1:3])
        end
    end
    
    if length(edge_vectors) < 2
        return 0.0
    end
    
    # Measure non-orthogonality
    max_skew = 0.0
    for i in 1:length(edge_vectors)
        for j in i+1:length(edge_vectors)
            v1 = edge_vectors[i]
            v2 = edge_vectors[j]
            
            # Angle between vectors
            dot_product = sum(v1 .* v2)
            magnitude1 = sqrt(sum(v1.^2))
            magnitude2 = sqrt(sum(v2.^2))
            
            if magnitude1 > 1e-12 && magnitude2 > 1e-12
                cos_angle = dot_product / (magnitude1 * magnitude2)
                cos_angle = clamp(cos_angle, -1.0, 1.0)
                angle = acos(abs(cos_angle))
                
                # Skewness: deviation from 90 degrees
                skew = abs(angle - π/2) / (π/2)
                max_skew = max(max_skew, skew)
            end
        end
    end
    
    return max_skew
end

"""
Compute cell orthogonality measure
"""
function compute_cell_orthogonality(mesh, cell)
    # Measure how orthogonal the cell faces are
    # Simplified version - real implementation would use proper face normals
    
    orthogonality_sum = 0.0
    face_count = 0
    
    for face_idx in cell.faces
        if face_idx <= length(mesh.faces)
            face = mesh.faces[face_idx]
            
            # Get face normal (simplified)
            normal = compute_face_normal_simplified(mesh, face)
            
            # Check orthogonality with other faces
            for other_face_idx in cell.faces
                if other_face_idx != face_idx && other_face_idx <= length(mesh.faces)
                    other_face = mesh.faces[other_face_idx]
                    other_normal = compute_face_normal_simplified(mesh, other_face)
                    
                    # Compute dot product (should be 0 for orthogonal faces)
                    dot_product = sum(normal .* other_normal)
                    orthogonality = 1.0 - abs(dot_product)  # 1 = orthogonal, 0 = parallel
                    
                    orthogonality_sum += orthogonality
                    face_count += 1
                end
            end
        end
    end
    
    return face_count > 0 ? orthogonality_sum / face_count : 1.0
end

"""
Compute volume ratio with neighbors
"""
function compute_volume_ratio(mesh, cell_idx::Int)
    # Simplified volume ratio computation
    # Real implementation would compute actual cell volumes
    
    cell_size = estimate_cell_size(mesh, cell_idx)
    
    # Get neighbor sizes
    neighbor_sizes = Float64[]
    cell = mesh.cells[cell_idx]
    
    for face_idx in cell.faces
        if face_idx <= length(mesh.faces)
            face = mesh.faces[face_idx]
            for neighbor_idx in face.cells
                if neighbor_idx != cell_idx && neighbor_idx <= length(mesh.cells)
                    neighbor_size = estimate_cell_size(mesh, neighbor_idx)
                    push!(neighbor_sizes, neighbor_size)
                end
            end
        end
    end
    
    if isempty(neighbor_sizes)
        return 1.0
    end
    
    avg_neighbor_size = mean(neighbor_sizes)
    return avg_neighbor_size > 0 ? cell_size / avg_neighbor_size : 1.0
end

"""
Classify a single cell by type
"""
function classify_single_cell(mesh, cell)
    # Count vertices to determine cell type
    vertices = get_cell_vertices(mesh, cell)
    n_vertices = length(vertices)
    
    # Classify based on vertex count
    if n_vertices == 4
        return :tetrahedron
    elseif n_vertices == 5
        return :pyramid  
    elseif n_vertices == 6
        return :prism
    elseif n_vertices == 8
        return :hexahedron
    elseif n_vertices < 4
        return :degenerate
    else
        return :polyhedron
    end
end

"""
Compute cell quality based on type
"""
function compute_cell_quality(mesh, cell, cell_type::Symbol)
    vertices = get_cell_vertices(mesh, cell)
    
    if cell_type == :tetrahedron
        return compute_tet_quality(vertices)
    elseif cell_type == :hexahedron  
        return compute_hex_quality(vertices)
    elseif cell_type == :prism
        return compute_prism_quality(vertices)
    elseif cell_type == :pyramid
        return compute_pyramid_quality(vertices)
    else
        return 0.5  # Default moderate quality
    end
end

"""
Analyze coordinate spacing for regularity
"""
function analyze_coordinate_spacing(mesh)
    n_cells = length(mesh.cells)
    sample_size = min(100, n_cells)
    
    # Sample cell centroids
    centroids = []
    for i in 1:sample_size
        cell_idx = rand(1:n_cells)
        centroid = compute_cell_centroid(mesh, cell_idx)
        push!(centroids, centroid)
    end
    
    # Analyze spacing regularity in each direction
    spacing_regularity = [analyze_direction_spacing(centroids, i) for i in 1:3]
    overall_regularity = mean(spacing_regularity)
    
    # Determine if spacing is regular
    is_regular = overall_regularity > 0.8
    
    return (is_regular = is_regular, uniformity = overall_regularity)
end

"""
Detect boundary layer patterns
"""
function detect_boundary_layer_patterns(mesh)
    # Look for cells with high aspect ratios near boundaries
    boundary_layer_score = 0.0
    boundary_cells = 0
    
    for (i, cell) in enumerate(mesh.cells)
        # Check if cell is near boundary
        near_boundary = check_cell_near_boundary(mesh, cell)
        
        if near_boundary
            boundary_cells += 1
            
            # Check aspect ratio
            vertices = get_cell_vertices(mesh, cell)
            bbox = compute_bounding_box(vertices)
            dimensions = [bbox.max[j] - bbox.min[j] for j in 1:3]
            aspect_ratio = maximum(dimensions) / (minimum(dimensions) + 1e-12)
            
            # High aspect ratio suggests boundary layer
            if aspect_ratio > 5.0
                boundary_layer_score += 1.0
            end
        end
    end
    
    return boundary_cells > 0 && (boundary_layer_score / boundary_cells) > 0.3
end

"""
Compute coordinate alignment
"""
function compute_coordinate_alignment(mesh)
    n_cells = length(mesh.cells)
    sample_size = min(50, n_cells)
    
    alignment_scores = Float64[]
    
    for _ in 1:sample_size
        cell_idx = rand(1:n_cells)
        vertices = get_cell_vertices(mesh, mesh.cells[cell_idx])
        
        if length(vertices) >= 2
            # Compute edge alignment with coordinate axes
            edge = vertices[2] - vertices[1]
            if length(edge) >= 3
                # Check alignment with x, y, z axes
                x_align = abs(edge[1]) / (norm(edge) + 1e-12)
                y_align = abs(edge[2]) / (norm(edge) + 1e-12)  
                z_align = abs(edge[3]) / (norm(edge) + 1e-12)
                
                max_align = max(x_align, y_align, z_align)
                push!(alignment_scores, max_align)
            end
        end
    end
    
    return isempty(alignment_scores) ? [0.0, 0.0, 0.0] : [mean(alignment_scores), std(alignment_scores), maximum(alignment_scores)]
end

"""
Estimate matrix bandwidth
"""
function estimate_matrix_bandwidth(mesh, connectivity_info)
    # Use connectivity graph to estimate matrix bandwidth
    neighbor_counts = connectivity_info.neighbor_counts
    max_neighbors = maximum(neighbor_counts)
    
    # Estimate based on grid dimensions if structured
    if connectivity_info.is_structured
        nx, ny, nz = connectivity_info.dimensions
        # For structured grids, bandwidth is approximately related to grid dimensions
        return max(nx, ny, nz) * max_neighbors
    else
        # For unstructured grids, use heuristic based on connectivity
        n_cells = length(mesh.cells)
        return min(n_cells, Int(ceil(sqrt(n_cells) * max_neighbors)))
    end
end

"""
Analyze memory access patterns
"""
function analyze_memory_access_pattern(mesh, connectivity_info)
    if connectivity_info.is_structured
        return :cache_friendly
    else
        # Analyze regularity of connectivity
        regularity = connectivity_info.regularity_score
        if regularity > 0.7
            return :mixed
        else
            return :scattered
        end
    end
end

"""
Estimate vectorization potential
"""
function estimate_vectorization_potential(mesh, connectivity_info)
    # Structured meshes have high vectorization potential
    if connectivity_info.is_structured
        return 0.9
    end
    
    # Unstructured meshes depend on regularity
    regularity = connectivity_info.regularity_score
    neighbor_variance = var(connectivity_info.neighbor_counts)
    
    # Low variance and high regularity → better vectorization
    vectorization_score = regularity * exp(-neighbor_variance / 4.0)
    
    return min(vectorization_score, 1.0)
end

"""
Estimate parallel efficiency
"""
function estimate_parallel_efficiency(mesh, connectivity_info)
    n_cells = length(mesh.cells)
    
    # Larger meshes generally parallelize better
    size_factor = min(1.0, n_cells / 1000.0)
    
    # Structured meshes parallelize better
    structure_factor = connectivity_info.is_structured ? 0.9 : 0.6
    
    # Regular connectivity helps parallelization
    regularity_factor = connectivity_info.regularity_score
    
    efficiency = 0.4 * size_factor + 0.3 * structure_factor + 0.3 * regularity_factor
    
    return min(efficiency, 1.0)
end

# Additional helper functions for geometric computations

function compute_cell_centroid(mesh, cell_idx::Int)
    vertices = get_cell_vertices(mesh, mesh.cells[cell_idx])
    if isempty(vertices)
        return [0.0, 0.0, 0.0]
    end
    
    centroid = [0.0, 0.0, 0.0]
    for vertex in vertices
        for i in 1:min(3, length(vertex))
            centroid[i] += vertex[i]
        end
    end
    
    return centroid ./ length(vertices)
end

function estimate_cell_size(mesh, cell_idx::Int)
    vertices = get_cell_vertices(mesh, mesh.cells[cell_idx])
    if length(vertices) < 2
        return 1.0
    end
    
    bbox = compute_bounding_box(vertices)
    dimensions = [bbox.max[i] - bbox.min[i] for i in 1:3]
    return prod(dimensions)^(1/3)  # Geometric mean of dimensions
end

function compute_face_normal_simplified(mesh, face)
    # Simplified face normal computation
    if length(face.vertices) < 3
        return [0.0, 0.0, 1.0]  # Default normal
    end
    
    # Use first three vertices to compute normal
    v1 = mesh.vertices[face.vertices[1]]
    v2 = mesh.vertices[face.vertices[2]]  
    v3 = mesh.vertices[face.vertices[3]]
    
    # Ensure we have 3D coordinates
    p1 = length(v1) >= 3 ? v1[1:3] : [v1[1], v1[2], 0.0]
    p2 = length(v2) >= 3 ? v2[1:3] : [v2[1], v2[2], 0.0]
    p3 = length(v3) >= 3 ? v3[1:3] : [v3[1], v3[2], 0.0]
    
    # Cross product for normal
    edge1 = p2 - p1
    edge2 = p3 - p1
    normal = cross(edge1, edge2)
    
    # Normalize
    magnitude = norm(normal)
    return magnitude > 1e-12 ? normal ./ magnitude : [0.0, 0.0, 1.0]
end

# Quality computation functions for different cell types

function compute_tet_quality(vertices)
    if length(vertices) < 4
        return 0.0
    end
    
    # Simplified tetrahedral quality (volume/surface area ratio)
    return 0.7 + 0.3 * rand()  # Placeholder - real implementation would compute actual quality
end

function compute_hex_quality(vertices)
    if length(vertices) < 8
        return 0.0
    end
    
    # Simplified hexahedral quality (aspect ratio and skewness)
    bbox = compute_bounding_box(vertices)
    dimensions = [bbox.max[i] - bbox.min[i] for i in 1:3]
    aspect_ratio = maximum(dimensions) / (minimum(dimensions) + 1e-12)
    
    # Better quality for lower aspect ratios
    return 1.0 / (1.0 + aspect_ratio / 10.0)
end

function compute_prism_quality(vertices)
    # Simplified prism quality
    return 0.6 + 0.3 * rand()  # Placeholder
end

function compute_pyramid_quality(vertices)
    # Simplified pyramid quality  
    return 0.5 + 0.4 * rand()  # Placeholder
end

# Helper functions for coordinate and spacing analysis

function analyze_coordinate_alignment_sample(mesh)
    n_cells = length(mesh.cells)
    sample_size = min(20, n_cells)
    
    alignment_scores = Float64[]
    
    for _ in 1:sample_size
        cell_idx = rand(1:n_cells)
        vertices = get_cell_vertices(mesh, mesh.cells[cell_idx])
        
        if length(vertices) >= 2
            edge = vertices[2] - vertices[1]
            if length(edge) >= 3
                # Check alignment with coordinate axes
                norm_edge = norm(edge)
                if norm_edge > 1e-12
                    edge_normalized = edge ./ norm_edge
                    max_component = maximum(abs.(edge_normalized))
                    push!(alignment_scores, max_component)
                end
            end
        end
    end
    
    return isempty(alignment_scores) ? 0.0 : mean(alignment_scores)
end

function analyze_spacing_regularity_sample(mesh)
    n_cells = length(mesh.cells)
    sample_size = min(20, n_cells)
    
    spacings = Float64[]
    
    for _ in 1:sample_size
        cell_idx = rand(1:n_cells)
        cell_size = estimate_cell_size(mesh, cell_idx)
        push!(spacings, cell_size)
    end
    
    if length(spacings) < 2
        return 0.5
    end
    
    # Regularity based on spacing uniformity
    mean_spacing = mean(spacings)
    std_spacing = std(spacings)
    
    if mean_spacing > 1e-12
        cv = std_spacing / mean_spacing
        return exp(-cv)  # Lower variation → higher regularity
    else
        return 0.5
    end
end

function estimate_dimension_from_coordinates(coords::Vector{Float64})
    if length(coords) < 2
        return 1
    end
    
    # Find unique coordinate values with tolerance
    unique_coords = Float64[]
    tolerance = (maximum(coords) - minimum(coords)) / 100.0
    
    for coord in coords
        is_unique = true
        for existing in unique_coords
            if abs(coord - existing) < tolerance
                is_unique = false
                break
            end
        end
        if is_unique
            push!(unique_coords, coord)
        end
    end
    
    return max(1, length(unique_coords))
end

function linear_to_grid_index(linear_idx::Int, nx::Int, ny::Int, nz::Int)
    # Convert linear index to 3D grid coordinates
    linear_idx -= 1  # Convert to 0-based
    
    k = div(linear_idx, nx * ny)
    remainder = linear_idx % (nx * ny)
    j = div(remainder, nx)
    i = remainder % nx
    
    return (i + 1, j + 1, k + 1)  # Convert back to 1-based
end

function compute_position_alignment_score(grid_pos::Tuple{Int,Int,Int}, actual_pos::Vector{Float64})
    # Score how well grid position aligns with actual geometric position
    # This is a simplified heuristic
    
    if length(actual_pos) < 3
        return 0.5
    end
    
    # Normalize grid position to [0,1] range (approximately)
    normalized_grid = [pos / 100.0 for pos in grid_pos]  # Rough normalization
    
    # Compare with normalized actual position
    coord_range = maximum(actual_pos) - minimum(actual_pos)
    if coord_range > 1e-12
        normalized_actual = (actual_pos .- minimum(actual_pos)) ./ coord_range
        
        # Compute alignment score
        differences = abs.(normalized_grid[1:3] - normalized_actual)
        alignment = 1.0 - mean(differences)
        return max(0.0, alignment)
    else
        return 0.5
    end
end

function analyze_direction_spacing(centroids::Vector, direction::Int)
    if length(centroids) < 2
        return 0.5
    end
    
    # Extract coordinates in specified direction
    coords = [length(c) >= direction ? c[direction] : 0.0 for c in centroids]
    
    # Compute spacing between consecutive sorted coordinates
    sorted_coords = sort(coords)
    spacings = diff(sorted_coords)
    
    if length(spacings) < 2
        return 0.5
    end
    
    # Regularity based on spacing uniformity
    mean_spacing = mean(spacings)
    std_spacing = std(spacings)
    
    if mean_spacing > 1e-12
        cv = std_spacing / mean_spacing
        return exp(-cv * 2.0)  # Penalize high variation
    else
        return 0.5
    end
end

function check_cell_near_boundary(mesh, cell)
    # Check if any face of the cell is a boundary face
    for face_idx in cell.faces
        if face_idx <= length(mesh.faces)
            face = mesh.faces[face_idx]
            if length(face.cells) == 1  # Boundary face has only one cell
                return true
            end
        end
    end
    return false
end

function compute_connectivity_similarity(neighbors1::Vector{Int}, neighbors2::Vector{Int})
    # Compute similarity between two connectivity patterns
    if isempty(neighbors1) && isempty(neighbors2)
        return 1.0
    elseif isempty(neighbors1) || isempty(neighbors2)
        return 0.0
    end
    
    # Jaccard similarity
    intersection = length(intersect(neighbors1, neighbors2))
    union_size = length(Base.union(neighbors1, neighbors2))
    
    return union_size > 0 ? intersection / union_size : 0.0
end

# Import countmap if not available
function countmap(collection)
    counts = Dict()
    for item in collection
        counts[item] = get(counts, item, 0) + 1
    end
    return counts
end

# Provide computed `area_vector` property for compatibility with code that
# expects a pre-computed face area vector (area * normal). This avoids changing
# existing field names while allowing calls like `face.area_vector` to work.
@inline function Base.getproperty(f::Face{T,N}, s::Symbol) where {T,N}
    # Computed / aliased properties for compatibility with Numerics kernels
    if s === :area_vector
        return getfield(f, :normal) * getfield(f, :area)
    elseif s === :owner
        return first(getfield(f, :cells))
    elseif s === :neighbor
        cells = getfield(f, :cells)
        return length(cells) >= 2 ? cells[2] : -1
    elseif s === :boundary
        return length(getfield(f, :cells)) < 2
    else
        return getfield(f, s)
    end
end

# Provide `nodes` → `vertices` and `boundaries` → `boundary_patches` aliases on
# SimpleMesh so that high-level FVM operators (Numerics.fvc) can work with the
# lightweight DSO mesh without modification.
@inline function Base.getproperty(m::SimpleMesh{T,N}, s::Symbol) where {T,N}
    if s === :nodes
        verts = getfield(m, :vertices)
        return [Node{T,N}(i, SVector{N,T}(verts[i]...), false) for i in eachindex(verts)]
    elseif s === :boundaries
        return getfield(m, :boundary_patches)
    else
        return getfield(m, s)
    end
end

end # module DomainSpecificOptimizations