"""
    SmartValidation.jl

Smart error recovery, checkpointing, and validation framework for CFD.jl
Combines intelligent error handling with resilient solving and comprehensive validation.
"""

module SmartValidation

using ..CFDCore
using Dates
using LinearAlgebra
using Printf
using Serialization

# Define types first
struct FieldInfo
    name::Symbol
    type::Symbol
    physics::Symbol
end

# Track all defined BCs and fields during macro expansion
const BC_REGISTRY = Dict{Symbol, Set{String}}()  # field → patches
const MESH_PATCHES = Set{String}()
const FIELD_REGISTRY = Dict{Symbol, FieldInfo}()

# Custom error types with helpful messages
struct SetupError <: Exception
    errors::Vector{String}
    mesh_file::String
end

struct SolverError <: Exception
    message::String
    available_solvers::Vector{Symbol}
end

struct BCError <: Exception
    field::Symbol
    missing_patches::Vector{String}
    suggestions::Vector{String}
end

struct CFDError <: Exception
    message::String
    cause::Any
end

# Enhanced error display
function Base.showerror(io::IO, e::SetupError)
    println(io, "\n", "="^60)
    println(io, "🚨 CFD SETUP ERROR")
    println(io, "="^60)
    
    println(io, "\nMesh file: ", e.mesh_file)
    println(io, "\nFound $(length(e.errors)) issue(s):\n")
    
    for (i, error) in enumerate(e.errors)
        println(io, "$i. ", error)
    end
    
    println(io, "\n💡 Quick fix guide:")
    println(io, "1. Check your mesh file has the expected boundary patches")
    println(io, "2. Run `show_patches(\"$(e.mesh_file)\")` to list all patches")
    println(io, "3. Define BCs for all patches or use @bc_default")
    println(io, "="^60)
end

function Base.showerror(io::IO, e::SolverError)
    println(io, "\n❌ SOLVER ERROR: ", e.message)
    println(io, "\nAvailable solvers:")
    for solver in e.available_solvers
        println(io, "  • ", solver)
    end
    println(io, "\nDefine a solver with: @solver YourSolver begin ... end")
end

function Base.showerror(io::IO, e::BCError)
    println(io, "\n❌ Missing boundary conditions for field '$(e.field)'")
    println(io, "\nMissing patches: $(join(e.missing_patches, ", "))")
    
    if !isempty(e.suggestions)
        println(io, "\nSuggested fixes:")
        for suggestion in e.suggestions
            println(io, "  ", suggestion)
        end
    end
end

# Enhanced solve function with validation
function validated_solve(mesh_file::String, solver_type, args...; kwargs...)
    try
        # Pre-flight checks
        validate_mesh_exists(mesh_file)
        validate_solver_exists(solver_type)
        
        # Load mesh and extract patch names
        mesh = load_mesh(mesh_file)
        patches = get_boundary_patches(mesh)
        global MESH_PATCHES = Set(patches)
        
        # Validate setup
        validation_errors = validate_setup(patches)
        
        if !isempty(validation_errors)
            throw(SetupError(validation_errors, mesh_file))
        end
        
        # Proceed with solving...
        return solve_internal(mesh, solver_type, args...; kwargs...)
        
    catch e
        if e isa SetupError || e isa SolverError || e isa BCError
            rethrow(e)
        else
            # Wrap unknown errors in user-friendly message
            throw(CFDError("Simulation failed: $(e.message)", e))
        end
    end
end

# Intelligent validation
function validate_setup(mesh_patches)
    errors = String[]
    
    # Check BC coverage for all registered fields
    for field in keys(FIELD_REGISTRY)
        missing_patches = check_bc_coverage(field, mesh_patches)
        if !isempty(missing_patches)
            push!(errors, generate_bc_error(field, missing_patches, mesh_patches))
        end
    end
    
    # Check for undefined patches in BCs
    for (field, bc_patches) in BC_REGISTRY
        extra_patches = setdiff(bc_patches, mesh_patches)
        if !isempty(extra_patches)
            push!(errors, generate_patch_error(field, extra_patches, mesh_patches))
        end
    end
    
    # Check physics compatibility
    physics_errors = validate_physics_compatibility()
    append!(errors, physics_errors)
    
    return errors
end

function check_bc_coverage(field::Symbol, mesh_patches)
    if !haskey(BC_REGISTRY, field)
        return collect(mesh_patches)  # All patches missing
    end
    
    defined_patches = BC_REGISTRY[field]
    return collect(setdiff(Set(mesh_patches), defined_patches))
end

# Generate helpful error messages with smart suggestions
function generate_bc_error(field::Symbol, missing_patches, mesh_patches)
    suggestions = generate_bc_suggestions(field, missing_patches)
    
    existing_bcs = get(BC_REGISTRY, field, Set{String}())
    
    msg = """
    
    ❌ Missing boundary conditions for field '$field'
    
    Missing patches: $(join(missing_patches, ", "))
    
    You have defined BCs for: $(join(existing_bcs, ", "))
    
    $(join(suggestions, "\n"))
    
    Example fix:
    ```julia
    @bc $(first(missing_patches)) = $field → $(suggest_bc_value(field, first(missing_patches)))
    ```
    """
    
    return msg
end

function generate_patch_error(field::Symbol, extra_patches, mesh_patches)
    # Find similar patch names using string distance
    suggestions = String[]
    for patch in extra_patches
        similar = find_similar_patch_names(patch, mesh_patches)
        if !isempty(similar)
            push!(suggestions, "Did you mean '$(first(similar))' instead of '$patch'?")
        end
    end
    
    msg = """
    
    ❌ Undefined patches in boundary conditions for field '$field'
    
    Unknown patches: $(join(extra_patches, ", "))
    Available patches: $(join(mesh_patches, ", "))
    
    $(join(suggestions, "\n"))
    """
    
    return msg
end

# Smart BC value suggestions based on patch and field names
function suggest_bc_value(field::Symbol, patch::String)
    patch_lower = lowercase(patch)
    
    if field == :U || field == :𝐮
        if occursin("inlet", patch_lower)
            return "(1, 0, 0)  # Modify inlet velocity"
        elseif occursin("outlet", patch_lower)
            return "zeroGradient"
        elseif occursin("wall", patch_lower)
            return "(0, 0, 0)  # No-slip wall"
        elseif occursin("symmetry", patch_lower)
            return "symmetry"
        else
            return "zeroGradient"
        end
    elseif field == :p
        if occursin("outlet", patch_lower)
            return "0  # Reference pressure"
        else
            return "zeroGradient"
        end
    elseif field == :T
        if occursin("inlet", patch_lower)
            return "300  # Inlet temperature"
        elseif occursin("wall", patch_lower)
            if occursin("hot", patch_lower)
                return "400  # Hot wall"
            elseif occursin("cold", patch_lower)
                return "300  # Cold wall"
            else
                return "adiabaticWall"
            end
        else
            return "zeroGradient"
        end
    elseif field == :k
        if occursin("wall", patch_lower)
            return "wallFunction"
        elseif occursin("inlet", patch_lower)
            return "0.1  # Turbulent kinetic energy"
        else
            return "zeroGradient"
        end
    elseif field == :ε || field == :epsilon
        if occursin("wall", patch_lower)
            return "wallFunction"
        elseif occursin("inlet", patch_lower)
            return "0.01  # Dissipation rate"
        else
            return "zeroGradient"
        end
    else
        return "0  # Default value"
    end
end

function generate_bc_suggestions(field::Symbol, missing_patches)
    suggestions = String[]
    
    for patch in missing_patches
        value = suggest_bc_value(field, patch)
        push!(suggestions, "@bc $patch = $field → $value")
    end
    
    return suggestions
end

# Find similar patch names using Levenshtein distance
function find_similar_patch_names(target::String, available::Vector{String})
    similarities = [(patch, levenshtein_distance(target, patch)) for patch in available]
    sort!(similarities, by=x->x[2])
    
    # Return patches within reasonable distance
    threshold = min(3, length(target) ÷ 2)
    return [patch for (patch, dist) in similarities if dist <= threshold]
end

function levenshtein_distance(s1::String, s2::String)
    m, n = length(s1), length(s2)
    dp = zeros(Int, m+1, n+1)
    
    for i in 1:m+1
        dp[i, 1] = i-1
    end
    for j in 1:n+1
        dp[1, j] = j-1
    end
    
    for i in 2:m+1
        for j in 2:n+1
            if s1[i-1] == s2[j-1]
                dp[i, j] = dp[i-1, j-1]
            else
                dp[i, j] = 1 + min(dp[i-1, j], dp[i, j-1], dp[i-1, j-1])
            end
        end
    end
    
    return dp[m+1, n+1]
end

# Physics compatibility validation
function validate_physics_compatibility()
    errors = String[]
    
    # Check if incompressible solver is used with temperature BC
    if haskey(FIELD_REGISTRY, :T) && any(s -> occursin("incompressible", lowercase(string(s))), keys(SOLVER_REGISTRY))
        if get(FIELD_REGISTRY[:T], :physics, :none) != :heat_transfer
            push!(errors, "⚠️  Temperature field defined but not using heat transfer physics")
        end
    end
    
    # Check for turbulence model consistency
    has_k = haskey(FIELD_REGISTRY, :k)
    has_ε = haskey(FIELD_REGISTRY, :ε)
    
    if has_k && !has_ε
        push!(errors, "❌ k-ε turbulence model requires both k and ε fields")
    elseif !has_k && has_ε
        push!(errors, "❌ k-ε turbulence model requires both k and ε fields")
    end
    
    return errors
end

# Validation helper functions
function validate_mesh_exists(mesh_file::String)
    if !isfile(mesh_file)
        error("Mesh file not found: $mesh_file")
    end
end

function validate_solver_exists(solver_type)
    if !haskey(SOLVER_REGISTRY, Symbol(solver_type))
        available = collect(keys(SOLVER_REGISTRY))
        throw(SolverError("Unknown solver: $solver_type", available))
    end
end

# Register field information
function register_field!(name::Symbol, type::Symbol, physics::Symbol=:general)
    FIELD_REGISTRY[name] = FieldInfo(name, type, physics)
end

# Register boundary condition
function register_bc!(patch::Symbol, field::Symbol, value)
    if !haskey(BC_REGISTRY, field)
        BC_REGISTRY[field] = Set{String}()
    end
    push!(BC_REGISTRY[field], String(patch))
end

# Default BC application
function apply_default_bcs!(field::Symbol, default_value, mesh_patches)
    if !haskey(BC_REGISTRY, field)
        BC_REGISTRY[field] = Set{String}()
    end
    
    for patch in mesh_patches
        if !(patch in BC_REGISTRY[field])
            push!(BC_REGISTRY[field], patch)
            @warn "Applied default BC for $field on patch '$patch': $default_value"
        end
    end
end

# Physical bounds checking
function validate_physical_bounds(field::Symbol, value)
    if field == :T && value < 0
        @warn "⚠️  Negative absolute temperature: $value K"
    elseif field == :k && value < 0
        @warn "⚠️  Negative turbulent kinetic energy: $value"
    elseif field == :ε && value < 0
        @warn "⚠️  Negative dissipation rate: $value"
    elseif field == :ρ && value <= 0
        error("❌ Non-positive density: $value")
    elseif field == :ν && value <= 0
        error("❌ Non-positive viscosity: $value")
    end
end

# ==================== Smart Error Recovery ====================

struct CheckpointData{T}
    iteration::Int
    time::Float64
    fields::Dict{String, Any}
    solver_state::Dict{String, Any}
    residuals::Vector{Float64}
    cfl::Float64
    timestamp::DateTime
end

mutable struct ResilientSolver{T}
    base_solver::Any
    checkpoint_frequency::Int
    max_retries::Int
    cfl_reduction_factor::Float64
    divergence_threshold::Float64
    adaptive_cfl::Bool
    target_cfl::Float64
    
    # State tracking
    current_iteration::Int
    current_time::Float64
    current_cfl::Float64
    residual_history::Vector{Vector{Float64}}
    checkpoint_history::Vector{CheckpointData{T}}
    retry_count::Int
    
    # Paths
    checkpoint_dir::String
    log_file::String
end

function ResilientSolver(base_solver; 
                        checkpoint_frequency::Int = 100,
                        max_retries::Int = 3,
                        cfl_reduction_factor::Float64 = 0.5,
                        divergence_threshold::Float64 = 1e6,
                        adaptive_cfl::Bool = true,
                        target_cfl::Float64 = 0.8,
                        checkpoint_dir::String = "checkpoints")
    
    mkpath(checkpoint_dir)
    log_file = joinpath(checkpoint_dir, "resilient_solve.log")
    
    return ResilientSolver{Float64}(
        base_solver, checkpoint_frequency, max_retries, cfl_reduction_factor,
        divergence_threshold, adaptive_cfl, target_cfl,
        0, 0.0, target_cfl, Vector{Vector{Float64}}(), CheckpointData{Float64}[],
        0, checkpoint_dir, log_file
    )
end

function checkpoint_solver!(solver::ResilientSolver, fields::Dict, solver_state::Dict, residuals::Vector{Float64})
    checkpoint = CheckpointData{Float64}(
        solver.current_iteration,
        solver.current_time,
        deepcopy(fields),
        deepcopy(solver_state),
        copy(residuals),
        solver.current_cfl,
        now()
    )
    
    push!(solver.checkpoint_history, checkpoint)
    
    # Save to disk
    checkpoint_file = joinpath(solver.checkpoint_dir, "checkpoint_$(solver.current_iteration).jls")
    serialize(checkpoint_file, checkpoint)
    
    # Log checkpoint
    open(solver.log_file, "a") do io
        println(io, "$(now()): Checkpoint saved at iteration $(solver.current_iteration), CFL=$(solver.current_cfl)")
    end
    
    # Keep only last 5 checkpoints
    if length(solver.checkpoint_history) > 5
        old_checkpoint = popfirst!(solver.checkpoint_history)
        old_file = joinpath(solver.checkpoint_dir, "checkpoint_$(old_checkpoint.iteration).jls")
        isfile(old_file) && rm(old_file)
    end
end

function restore_checkpoint!(solver::ResilientSolver)
    if isempty(solver.checkpoint_history)
        @warn "No checkpoints available for restoration"
        return nothing
    end
    
    checkpoint = solver.checkpoint_history[end]
    
    # Restore state
    solver.current_iteration = checkpoint.iteration
    solver.current_time = checkpoint.time
    solver.current_cfl = checkpoint.cfl * solver.cfl_reduction_factor  # Reduce CFL on restore
    
    open(solver.log_file, "a") do io
        println(io, "$(now()): Restored checkpoint from iteration $(checkpoint.iteration), reduced CFL to $(solver.current_cfl)")
    end
    
    return checkpoint.fields, checkpoint.solver_state
end

function detect_divergence(residuals::Vector{Float64}, threshold::Float64 = 1e6)::Bool
    return any(r -> isnan(r) || isinf(r) || abs(r) > threshold, residuals)
end

function CFL_adaptive!(solver::ResilientSolver, residuals::Vector{Float64})
    if !solver.adaptive_cfl
        return
    end
    
    push!(solver.residual_history, copy(residuals))
    
    # Keep last 10 iterations for analysis
    if length(solver.residual_history) > 10
        popfirst!(solver.residual_history)
    end
    
    if length(solver.residual_history) >= 3
        # Check convergence trend
        recent_max = maximum(residuals)
        prev_max = maximum(solver.residual_history[end-1])
        
        if recent_max < prev_max * 0.8  # Good convergence
            solver.current_cfl = min(solver.target_cfl, solver.current_cfl * 1.05)
        elseif recent_max > prev_max * 1.5  # Poor convergence
            solver.current_cfl *= 0.9
        end
        
        solver.current_cfl = max(0.01, solver.current_cfl)  # Minimum CFL
    end
end

# ==================== Validation Suite ====================

struct ValidationTest
    name::String
    test_function::Function
    tolerance::Float64
    reference_value::Union{Float64, Nothing}
    description::String
end

struct ValidationSuite
    tests::Vector{ValidationTest}
    results::Dict{String, Any}
    mesh_independence::Bool
    convergence_study::Bool
    benchmark_cases::Vector{String}
end

function ValidationSuite(; mesh_independence::Bool = true, 
                          convergence_study::Bool = true,
                          benchmark_cases::Vector{String} = String[])
    return ValidationSuite(ValidationTest[], Dict{String, Any}(), 
                          mesh_independence, convergence_study, benchmark_cases)
end

function add_test!(suite::ValidationSuite, name::String, test_func::Function; 
                   tolerance::Float64 = 1e-6, reference::Union{Float64, Nothing} = nothing,
                   description::String = "")
    push!(suite.tests, ValidationTest(name, test_func, tolerance, reference, description))
end

function mesh_independence_study(solver_func::Function, mesh_sizes::Vector{Int}; 
                                variable::String = "pressure", location = :center)
    results = Dict{Int, Float64}()
    
    println("🔍 Running mesh independence study...")
    
    for (i, size) in enumerate(mesh_sizes)
        @printf "  Mesh %d/%d: %dx%dx%d cells\n" i length(mesh_sizes) size size size
        
        try
            mesh = auto_mesh("mesh_study_$size", (size, size, size))
            result = solver_func(mesh)
            
            # Extract monitoring value (simplified)
            if haskey(result, variable)
                results[size] = norm(result[variable].data)
            else
                @warn "Variable $variable not found in results"
                results[size] = NaN
            end
        catch e
            @warn "Failed for mesh size $size: $e"
            results[size] = NaN
        end
    end
    
    # Analyze convergence
    sizes = sort(collect(keys(results)))
    values = [results[s] for s in sizes]
    
    # Calculate grid convergence index (GCI)
    if length(values) >= 3
        h = 1.0 ./ sizes  # Grid spacing
        gci = calculate_gci(h, values)
        println("  Grid Convergence Index: $(round(gci, digits=4))")
    end
    
    return results
end

function calculate_gci(h::Vector{Float64}, phi::Vector{Float64})
    # Simplified GCI calculation
    if length(h) < 3
        return NaN
    end
    
    # Use last three points
    r21 = h[end-1] / h[end]
    r32 = h[end-2] / h[end-1]
    
    phi1, phi2, phi3 = phi[end], phi[end-1], phi[end-2]
    
    # Apparent order of accuracy
    p = log((phi3 - phi2) / (phi2 - phi1)) / log(r32 / r21)
    
    # Safety factor
    Fs = 1.25
    
    # GCI
    gci = Fs * abs((phi2 - phi1) / phi1) / (r21^p - 1)
    
    return gci
end

function run_validation(suite::ValidationSuite)
    println("🧪 Running validation suite...")
    suite.results["timestamp"] = now()
    suite.results["tests"] = Dict{String, Any}()
    
    passed = 0
    total = length(suite.tests)
    
    for test in suite.tests
        print("  Testing $(test.name)... ")
        
        try
            result = test.test_function()
            
            if test.reference_value !== nothing
                error_val = abs(result - test.reference_value) / abs(test.reference_value)
                success = error_val < test.tolerance
                
                suite.results["tests"][test.name] = Dict(
                    "result" => result,
                    "reference" => test.reference_value,
                    "error" => error_val,
                    "tolerance" => test.tolerance,
                    "passed" => success,
                    "description" => test.description
                )
                
                if success
                    println("✅ PASSED (error: $(round(error_val*100, digits=3))%)")
                    passed += 1
                else
                    println("❌ FAILED (error: $(round(error_val*100, digits=3))%)")
                end
            else
                println("✅ COMPLETED (result: $result)")
                suite.results["tests"][test.name] = Dict(
                    "result" => result,
                    "passed" => true,
                    "description" => test.description
                )
                passed += 1
            end
            
        catch e
            println("💥 ERROR: $e")
            suite.results["tests"][test.name] = Dict(
                "error" => string(e),
                "passed" => false,
                "description" => test.description
            )
        end
    end
    
    suite.results["summary"] = Dict(
        "passed" => passed,
        "total" => total,
        "success_rate" => passed / total * 100
    )
    
    println("\n📊 Validation Summary: $passed/$total tests passed ($(round(passed/total*100, digits=1))%)")
    
    return suite.results
end

# ==================== Macro Interface ====================

macro resilient_solve(expr)
    quote
        # Parse solver configuration
        solver_config = $(esc(expr))
        
        # Create resilient wrapper
        resilient_solver = ResilientSolver(solver_config.base_solver;
            checkpoint_frequency = get(solver_config, :checkpoint_frequency, 100),
            max_retries = get(solver_config, :max_retries, 3),
            adaptive_cfl = get(solver_config, :adaptive_cfl, true)
        )
        
        resilient_solver
    end
end

macro validate(expr)
    quote
        # Parse validation configuration  
        validation_config = $(esc(expr))
        
        # Create validation suite
        suite = ValidationSuite(
            mesh_independence = get(validation_config, :mesh_independence, true),
            convergence_study = get(validation_config, :convergence_study, true)
        )
        
        suite
    end
end

# Missing type definitions for compatibility
struct SimpleMesh
    mesh_dir::String
    ncells::Int
    nfaces::Int
    patches::Vector{String}
    bounds::Tuple
    
    function SimpleMesh(case_path::String; ncells::Int, bounds::Tuple)
        patches = ["inlet", "outlet", "wall", "front", "back"]
        new(case_path, ncells, ncells*6, patches, bounds)
    end
end

# Mock solver registries for compatibility
const SOLVER_REGISTRY = Dict{Symbol, Any}()

# Helper function to integrate with existing solvers
function auto_mesh(case_name::String, dims::Tuple{Int,Int,Int})
    # Simple mesh generation for validation
    case_path = "validation_cases/$case_name"
    mkpath(case_path)
    
    nx, ny, nz = dims
    return SimpleMesh(case_path, ncells=nx*ny*nz, bounds=((0,1), (0,1), (0,1)))
end

export validated_solve, register_field!, register_bc!, apply_default_bcs!
export validate_physical_bounds, SetupError, SolverError, BCError, CFDError
export ResilientSolver, ValidationSuite, @resilient_solve, @validate
export checkpoint_solver!, restore_checkpoint!, run_validation, mesh_independence_study
export CFL_adaptive!, detect_divergence, add_test!

end # module SmartValidation