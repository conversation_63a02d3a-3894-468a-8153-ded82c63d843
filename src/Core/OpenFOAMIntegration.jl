"""
# OpenFOAMIntegration.jl

OpenFOAM-style ecosystem features for CFD.jl including:
- Field operations library (fvc/fvm style)
- Runtime selection tables for models
- Dictionary-based case setup
- Function objects for runtime processing
- OpenFOAM-compatible input/output

This module provides OpenFOAM-like functionality while maintaining
<PERSON>'s performance and type safety advantages.
"""
module OpenFOAMIntegration

using ..CFDCore
using ..Numerics  
using ..Physics
using LinearAlgebra
using SparseArrays
using StaticArrays

export fvc, fvm, IOobject, dictionary, runTimeSelectionTable
export writeFields, readFields, createMesh, createFields
export functionObjects, dataEntry, dimensionSet
export OpenFOAMCase, setupCase, runCase, postProcess

# ============================================================================
# Field Operations Library (OpenFOAM fvc/fvm style)
# ============================================================================

"""
Finite Volume Calculus (fvc) - Explicit operations
Similar to OpenFOAM's fvc namespace for explicit finite volume operations
"""
module fvc

using ...CFDCore
import ...Numerics: fvc as Nvfc

export grad, div, laplacian, interpolate, domainIntegrate

# Simple aliases → all heavy lifting lives in Numerics.fvc
const grad        = Nvfc.grad
const div         = Nvfc.div
const laplacian   = Nvfc.laplacian
const interpolate = Nvfc.interpolate

# Domain integral helper (not provided by Numerics). Keep lightweight version.
function domainIntegrate(φ::CFDCore.ScalarField)
    mesh = φ.mesh
    return sum(φ.data[i] * mesh.cells[i].volume for i in eachindex(mesh.cells))
end

end # module fvc

"""
Finite Volume Method (fvm) - Implicit operations
Similar to OpenFOAM's fvm namespace for implicit finite volume operations

This module provides OpenFOAM-style aliases to the verified core FVM implementation.
"""
module fvm

using ...CFDCore
import ...Numerics: fvm as Nfvm

export laplacian, div, ddt, grad, Su, Sp
export solve, relax, residual

# Alias to the verified core implementation
const laplacian = Nfvm.laplacian

# Alias to the verified core implementation
const ddt = Nfvm.ddt

# Alias to the verified core implementation
const Su = Nfvm.Su

# Alias to the verified core implementation
const Sp = Nfvm.Sp

# Additional aliases for convenience
const div = Nfvm.div
const solve = Nfvm.solve
const relax! = Nfvm.relax!

end # module fvm

# ============================================================================
# OpenFOAM-style Dictionary System
# ============================================================================

"""
OpenFOAM-style dictionary for case setup
"""
mutable struct Dictionary
    name::String
    entries::Dict{String,Any}
    sub_dicts::Dict{String,Dictionary}
    
    function Dictionary(name::String)
        new(name, Dict{String,Any}(), Dict{String,Dictionary}())
    end
end

"""
Create or access sub-dictionary
"""
function Base.getindex(dict::Dictionary, key::String)
    if haskey(dict.sub_dicts, key)
        return dict.sub_dicts[key]
    elseif haskey(dict.entries, key)
        return dict.entries[key]
    else
        # Create new sub-dictionary
        dict.sub_dicts[key] = Dictionary(key)
        return dict.sub_dicts[key]
    end
end

function Base.setindex!(dict::Dictionary, value, key::String)
    if isa(value, Dictionary)
        dict.sub_dicts[key] = value
    else
        dict.entries[key] = value
    end
end

# ============================================================================
# Runtime Selection Tables
# ============================================================================

"""
Runtime selection table for dynamic model selection
"""
struct RunTimeSelectionTable{T}
    constructors::Dict{String,Function}
    
    function RunTimeSelectionTable{T}() where T
        new{T}(Dict{String,Function}())
    end
end

"""
Add constructor to selection table
"""
function add_to_table!(table::RunTimeSelectionTable{T}, name::String, constructor::Function) where T
    table.constructors[name] = constructor
end

"""
Create object from selection table
"""
function create_from_table(table::RunTimeSelectionTable{T}, name::String, args...) where T
    if haskey(table.constructors, name)
        return table.constructors[name](args...)
    else
        error("Unknown type '$name' in runtime selection table")
    end
end

# ============================================================================
# OpenFOAM Case Structure
# ============================================================================

"""
OpenFOAM-style case management
"""
mutable struct OpenFOAMCase
    name::String
    root_path::String
    time_directories::Vector{String}
    constant_dict::Dictionary
    system_dict::Dictionary
    current_time::Float64
    
    function OpenFOAMCase(name::String, root_path::String)
        constant = Dictionary("constant")
        system = Dictionary("system")
        new(name, root_path, String[], constant, system, 0.0)
    end
end

"""
Setup case directory structure
"""
function setupCase(case::OpenFOAMCase)
    # Create directory structure
    mkpath(joinpath(case.root_path, "0"))
    mkpath(joinpath(case.root_path, "constant"))
    mkpath(joinpath(case.root_path, "system"))
    
    @info "Created OpenFOAM-style case structure at $(case.root_path)"
end

"""
Write OpenFOAM-style field file
"""
function writeFields(case::OpenFOAMCase, fields::Vector{Field}, time::Float64)
    time_dir = joinpath(case.root_path, string(time))
    mkpath(time_dir)
    
    for field in fields
        write_openfoam_field(field, time_dir)
    end
    
    if !(string(time) in case.time_directories)
        push!(case.time_directories, string(time))
    end
end

function write_openfoam_field(field::Field, directory::String)
    filename = joinpath(directory, string(field.name))
    
    open(filename, "w") do io
        # Write OpenFOAM header
        write_openfoam_header(io, field)
        
        # Write field data
        println(io, "internalField   uniform $(field.data[1]);")
        println(io)
        println(io, "boundaryField")
        println(io, "{")
        
        # Write boundary conditions
        for (patch_name, bc) in field.boundary_conditions
            println(io, "    $patch_name")
            println(io, "    {")
            write_boundary_condition(io, bc)
            println(io, "    }")
        end
        
        println(io, "}")
    end
end

function write_openfoam_header(io::IO, field::Field)
    println(io, "/*--------------------------------*- C++ -*----------------------------------*\\")
    println(io, "| =========                 |                                                 |")
    println(io, "| \\\\      /  F ield         | CFD.jl: Julia CFD Framework                    |")
    println(io, "|  \\\\    /   O peration     | Version:  2.1                                  |")
    println(io, "|   \\\\  /    A nd           | Web:      www.github.com/CFD.jl                |")
    println(io, "|    \\\\/     M anipulation  |                                                 |")
    println(io, "\\*---------------------------------------------------------------------------*/")
    println(io, "FoamFile")
    println(io, "{")
    println(io, "    version     2.0;")
    println(io, "    format      ascii;")
    println(io, "    class       $(get_openfoam_class(field));")
    println(io, "    object      $(field.name);")
    println(io, "}")
    println(io, "// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //")
    println(io)
end

function get_openfoam_class(field::Field)
    if isa(field, ScalarField)
        return "volScalarField"
    elseif isa(field, VectorField)
        return "volVectorField"
    else
        return "volScalarField"
    end
end

function write_boundary_condition(io::IO, bc::AbstractBoundaryCondition)
    """
    Write proper OpenFOAM boundary condition based on BC type
    """
    if isa(bc, CFDCore.DirichletBC)
        # Fixed value boundary condition
        println(io, "        type            fixedValue;")
        
        # Handle different value types
        if isa(bc.value, Number)
            println(io, "        value           uniform $(bc.value);")
        elseif isa(bc.value, SVector{3})
            println(io, "        value           uniform ($(bc.value[1]) $(bc.value[2]) $(bc.value[3]));")
        elseif isa(bc.value, AbstractVector) && length(bc.value) == 3
            println(io, "        value           uniform ($(bc.value[1]) $(bc.value[2]) $(bc.value[3]));")
        else
            println(io, "        value           uniform $(bc.value);")
        end
        
    elseif isa(bc, CFDCore.NeumannBC)
        # Zero gradient or fixed gradient boundary condition
        if isa(bc.gradient, Number) && bc.gradient == 0.0
            println(io, "        type            zeroGradient;")
        else
            println(io, "        type            fixedGradient;")
            if isa(bc.gradient, Number)
                println(io, "        gradient        uniform $(bc.gradient);")
            elseif isa(bc.gradient, SVector{3})
                println(io, "        gradient        uniform ($(bc.gradient[1]) $(bc.gradient[2]) $(bc.gradient[3]));")
            else
                println(io, "        gradient        uniform $(bc.gradient);")
            end
        end
        
    elseif isa(bc, CFDCore.RobinBC)
        # Mixed boundary condition (Robin type)
        println(io, "        type            mixed;")
        println(io, "        refValue        uniform $(bc.γ);")
        println(io, "        refGradient     uniform 0;")
        println(io, "        valueFraction   uniform $(bc.α / (bc.α + bc.β));")
        
    else
        # Generic boundary condition - fallback
        println(io, "        type            fixedValue;")
        println(io, "        value           uniform 0;")
        @warn "Unknown boundary condition type: $(typeof(bc)), using fixedValue fallback"
    end
end

# ============================================================================
# Function Objects for Runtime Processing
# ============================================================================

"""
Function object for runtime processing (similar to OpenFOAM functionObjects)
"""
abstract type FunctionObject end

"""
Force calculation function object
"""
mutable struct Forces <: FunctionObject
    name::String
    patches::Vector{String}
    rho_ref::Float64
    center_of_rotation::SVector{3,Float64}
    
    function Forces(name::String, patches::Vector{String}; 
                   rho_ref::Float64=1.0, 
                   center_of_rotation::SVector{3,Float64}=SVector{3,Float64}(0,0,0))
        new(name, patches, rho_ref, center_of_rotation)
    end
end

"""
Execute function object
"""
function execute!(fo::Forces, fields::Vector{Field}, time::Float64)
    # Calculate forces on specified patches
    total_force = SVector{3,Float64}(0,0,0)
    total_moment = SVector{3,Float64}(0,0,0)
    
    # Implementation would calculate forces from pressure and viscous stress
    @info "Forces at time $time: F = $total_force, M = $total_moment"
    
    return (force=total_force, moment=total_moment)
end

"""
Residual monitoring function object
"""
mutable struct Residuals <: FunctionObject
    name::String
    fields::Vector{String}
    tolerance::Float64
    
    function Residuals(name::String, fields::Vector{String}; tolerance::Float64=1e-6)
        new(name, fields, tolerance)
    end
end

function execute!(fo::Residuals, solver_residuals::Dict{String,Float64}, time::Float64)
    @info "Residuals at time $time:"
    for field_name in fo.fields
        if haskey(solver_residuals, field_name)
            residual = solver_residuals[field_name]
            converged = residual < fo.tolerance ? "✓" : "✗"
            @info "  $field_name: $residual $converged"
        end
    end
end

# ============================================================================
# Convenience Functions
# ============================================================================

"""
Create mesh from OpenFOAM case
"""
function createMesh(case::OpenFOAMCase)
    # Read mesh from constant/polyMesh
    mesh_path = joinpath(case.root_path, "constant", "polyMesh")
    return read_openfoam_mesh(mesh_path)
end

"""
Create fields from OpenFOAM case
"""
function createFields(case::OpenFOAMCase, time::Float64=0.0)
    time_dir = joinpath(case.root_path, string(time))
    fields = Field[]
    
    # Read all field files in time directory
    if isdir(time_dir)
        for filename in readdir(time_dir)
            filepath = joinpath(time_dir, filename)
            if isfile(filepath) && !startswith(filename, ".")
                field = read_openfoam_field(filepath)
                push!(fields, field)
            end
        end
    end
    
    return fields
end

# Real OpenFOAM I/O implementations
function read_openfoam_mesh(mesh_path::String)
    """
    Read OpenFOAM mesh using existing Utilities functionality
    """
    try
        # Use the Utilities module mesh reader
        if isdefined(CFD, :Utilities) && isdefined(CFD.Utilities, :read_openfoam_mesh)
            return CFD.Utilities.read_openfoam_mesh(mesh_path)
        else
            # Fallback to manual reading if Utilities not available
            return read_mesh_manual(mesh_path)
        end
    catch e
        @warn "OpenFOAM mesh reading failed: $e"
        return nothing
    end
end

function read_openfoam_field(filepath::String)
    """
    Read OpenFOAM field file using real parser
    """
    try
        if !isfile(filepath)
            @warn "Field file not found: $filepath"
            return nothing
        end
        
        # Parse the OpenFOAM field file
        content = read(filepath, String)
        return parse_openfoam_field_file(content, filepath)
    catch e
        @warn "OpenFOAM field reading failed: $e"
        return nothing
    end
end

function read_mesh_manual(mesh_path::String)
    """
    Manual mesh reading as fallback
    """
    if isdir(mesh_path)
        # Try to read from polyMesh directory
        polymesh_dir = joinpath(mesh_path, "constant", "polyMesh")
        if isdir(polymesh_dir)
            # Use Utilities mesh reader
            return CFD.Utilities.read_openfoam_mesh(mesh_path)
        end
    end
    return nothing
end

end # module OpenFOAMIntegration