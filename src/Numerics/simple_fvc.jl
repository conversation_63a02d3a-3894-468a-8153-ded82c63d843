# Simple primitive FVC implementation for debugging
module simple_fvc

using LinearAlgebra
using StaticArrays

"""
Simple face structure
"""
struct SimpleFace
    owner::Int
    neighbor::Int  # -1 for boundary
    center::SVector{3,Float64}
    area::Float64
    normal::SVector{3,Float64}
    is_boundary::Bool
end

"""
Simple structured mesh for debugging FVC operators
"""
struct SimpleMesh
    nx::Int
    ny::Int  
    nz::Int
    dx::Float64
    dy::Float64
    dz::Float64
    cell_centers::Vector{SVector{3,Float64}}
    cell_volumes::Vector{Float64}
    faces::Vector{SimpleFace}
end

"""
Create simple 3D structured mesh
"""
function create_simple_mesh(nx::Int, ny::Int, nz::Int, Lx=1.0, Ly=1.0, Lz=1.0)
    dx, dy, dz = Lx/nx, Ly/ny, Lz/nz
    
    # Cell centers
    cell_centers = SVector{3,Float64}[]
    for k in 1:nz, j in 1:ny, i in 1:nx
        x = (i - 0.5) * dx
        y = (j - 0.5) * dy
        z = (k - 0.5) * dz
        push!(cell_centers, SVector(x, y, z))
    end
    
    # Cell volumes
    cell_volumes = fill(dx * dy * dz, nx * ny * nz)
    
    # Create faces
    faces = SimpleFace[]
    
    # Function to get cell index
    cell_idx(i, j, k) = (k-1)*nx*ny + (j-1)*nx + i
    
    # X-direction faces
    for k in 1:nz, j in 1:ny, i in 1:nx+1
        x = (i - 1) * dx
        y = (j - 0.5) * dy
        z = (k - 0.5) * dz
        center = SVector(x, y, z)
        area = dy * dz
        normal = SVector(1.0, 0.0, 0.0)
        
        if i == 1  # Left boundary
            owner = cell_idx(1, j, k)
            face = SimpleFace(owner, -1, center, area, -normal, true)
            push!(faces, face)
        elseif i == nx + 1  # Right boundary
            owner = cell_idx(nx, j, k)
            face = SimpleFace(owner, -1, center, area, normal, true)
            push!(faces, face)
        else  # Internal face
            owner = cell_idx(i-1, j, k)
            neighbor = cell_idx(i, j, k)
            face = SimpleFace(owner, neighbor, center, area, normal, false)
            push!(faces, face)
        end
    end
    
    # Y-direction faces
    for k in 1:nz, j in 1:ny+1, i in 1:nx
        x = (i - 0.5) * dx
        y = (j - 1) * dy
        z = (k - 0.5) * dz
        center = SVector(x, y, z)
        area = dx * dz
        normal = SVector(0.0, 1.0, 0.0)
        
        if j == 1  # Bottom boundary
            owner = cell_idx(i, 1, k)
            face = SimpleFace(owner, -1, center, area, -normal, true)
            push!(faces, face)
        elseif j == ny + 1  # Top boundary
            owner = cell_idx(i, ny, k)
            face = SimpleFace(owner, -1, center, area, normal, true)
            push!(faces, face)
        else  # Internal face
            owner = cell_idx(i, j-1, k)
            neighbor = cell_idx(i, j, k)
            face = SimpleFace(owner, neighbor, center, area, normal, false)
            push!(faces, face)
        end
    end
    
    # Z-direction faces
    for k in 1:nz+1, j in 1:ny, i in 1:nx
        x = (i - 0.5) * dx
        y = (j - 0.5) * dy
        z = (k - 1) * dz
        center = SVector(x, y, z)
        area = dx * dy
        normal = SVector(0.0, 0.0, 1.0)
        
        if k == 1  # Bottom boundary
            owner = cell_idx(i, j, 1)
            face = SimpleFace(owner, -1, center, area, -normal, true)
            push!(faces, face)
        elseif k == nz + 1  # Top boundary
            owner = cell_idx(i, j, nz)
            face = SimpleFace(owner, -1, center, area, normal, true)
            push!(faces, face)
        else  # Internal face
            owner = cell_idx(i, j, k-1)
            neighbor = cell_idx(i, j, k)
            face = SimpleFace(owner, neighbor, center, area, normal, false)
            push!(faces, face)
        end
    end
    
    return SimpleMesh(nx, ny, nz, dx, dy, dz, cell_centers, cell_volumes, faces)
end

"""
Simple divergence calculation following pure Gauss theorem
"""
function simple_divergence(mesh::SimpleMesh, vector_field::Vector{SVector{3,Float64}})
    n_cells = length(mesh.cell_centers)
    divergence = zeros(Float64, n_cells)
    
    println("=== Simple Divergence Calculation ===")
    println("Vector field: $(vector_field[1]) (constant)")
    
    # Apply Gauss theorem: ∇⋅U = (1/V) Σ_faces (U_face ⋅ n̂) * A_face
    for (f_idx, face) in enumerate(mesh.faces)
        owner = face.owner
        neighbor = face.neighbor
        
        # Determine face value using simplest interpolation
        U_face = if face.is_boundary
            # Boundary face: use owner cell value
            vector_field[owner]
        else
            # Internal face: simple average
            0.5 * (vector_field[owner] + vector_field[neighbor])
        end
        
        # Compute flux: U_face ⋅ n̂ * Area
        flux = dot(U_face, face.normal) * face.area
        
        if f_idx <= 5  # Debug first few faces
            println("Face $f_idx:")
            println("  Owner: $owner, Neighbor: $neighbor, Boundary: $(face.is_boundary)")
            println("  Center: $(face.center)")
            println("  Normal: $(face.normal), Area: $(face.area)")
            println("  U_face: $U_face")
            println("  Flux: $flux")
        end
        
        # Apply flux to owner cell
        divergence[owner] += flux
        
        # Apply negative flux to neighbor cell (if internal)
        if !face.is_boundary
            divergence[neighbor] -= flux
        end
    end
    
    # Divide by cell volume
    for i in 1:n_cells
        divergence[i] /= mesh.cell_volumes[i]
    end
    
    println("Final divergence: $(divergence[1:min(5, length(divergence))])")
    return divergence
end

"""
Simple gradient calculation following pure Gauss theorem
"""
function simple_gradient(mesh::SimpleMesh, scalar_field::Vector{Float64})
    n_cells = length(mesh.cell_centers)
    gradient = [SVector(0.0, 0.0, 0.0) for _ in 1:n_cells]
    
    println("=== Simple Gradient Calculation ===")
    println("Scalar field: φ = x (linear)")
    
    # Apply Gauss theorem: ∇φ = (1/V) Σ_faces φ_face * S_face
    for (f_idx, face) in enumerate(mesh.faces)
        owner = face.owner
        neighbor = face.neighbor
        
        # Determine face value
        phi_face = if face.is_boundary
            # For φ = x, boundary value should be x-coordinate of face
            face.center[1]  # φ = x
        else
            # Internal face: simple average
            0.5 * (scalar_field[owner] + scalar_field[neighbor])
        end
        
        # Area vector
        area_vector = face.normal * face.area
        
        if f_idx <= 5  # Debug first few faces
            println("Face $f_idx:")
            println("  Center: $(face.center), φ_face: $phi_face")
            println("  Area vector: $area_vector")
        end
        
        # Apply contribution to owner cell
        gradient[owner] += phi_face * area_vector
        
        # Apply negative contribution to neighbor cell (if internal)
        if !face.is_boundary
            gradient[neighbor] -= phi_face * area_vector
        end
    end
    
    # Divide by cell volume
    for i in 1:n_cells
        gradient[i] /= mesh.cell_volumes[i]
    end
    
    println("Final gradient[1]: $(gradient[1])")
    return gradient
end

"""
Simple Laplacian: ∇²φ = ∇⋅(∇φ)
"""
function simple_laplacian(mesh::SimpleMesh, scalar_field::Vector{Float64})
    # Step 1: Compute gradient
    grad_field = simple_gradient(mesh, scalar_field)
    
    # Step 2: Compute divergence of gradient
    laplacian_field = simple_divergence(mesh, grad_field)
    
    return laplacian_field
end

end # module simple_fvc