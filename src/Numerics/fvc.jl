# src/Numerics/fvc.jl
module fvc

import ...CFDCore     # Access to CFD.CFDCore (e.g., ScalarField, VectorField, mesh types)
using ...CFDCore: AbstractMesh, ScalarField, VectorField, Field, AbstractBoundaryCondition
using ..Numerics  # Access to types in the outer Numerics module (e.g., GaussGradient, LinearInterpolation)
using LinearAlgebra
using SparseArrays
using StaticArrays

function grad(field::ScalarField{TF_base, NF_idx, M_type}, scheme::AbstractGradientScheme; current_time::Float64 = 0.0) where {TF_base, NF_idx, M_type<:AbstractMesh}
    mesh = field.mesh
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)
    
    if isempty(mesh.nodes)
        error("<PERSON><PERSON> has no nodes, cannot determine dimensionality for gradient.")
    end
    N_dims = length(mesh.nodes[1].coords) # Infer dimensionality from first node
    
    grad_data = [zero(SVector{N_dims, TF_base}) for _ in 1:num_cells]

    if scheme isa GaussGradient
        # Green–Gauss gradient (second-order on orthogonal meshes)

        for f_idx in 1:num_faces
            face = mesh.faces[f_idx]
            S_f_vec = face.normal * face.area

            if !face.boundary && face.neighbor > 0
                # Internal face – use linear interpolation between owner/neighbor
                owner_idx    = face.owner
                neighbor_idx = face.neighbor

                g_f          = face_distance_weight(face, mesh.cells[owner_idx], mesh.cells[neighbor_idx])
                φ_f          = g_f * field.data[owner_idx] + (1 - g_f) * field.data[neighbor_idx]

                grad_data[owner_idx]    +=  φ_f * S_f_vec
                grad_data[neighbor_idx] -=  φ_f * S_f_vec
            else
                # Boundary face – always contribute to owner cell, even if no BC supplied
                owner_idx = face.owner

                bc_val = if haskey(field.boundary_conditions, get_boundary_patch_name(f_idx, mesh))
                    bc = field.boundary_conditions[get_boundary_patch_name(f_idx, mesh)]
                    if bc isa CFDCore.DirichletBC
                        bc.value(face.center..., current_time)
                    elseif bc isa CFDCore.NeumannBC
                        # For pure Neumann (especially zero-gradient) do not add face term to
                        # Green-Gauss reconstruction because φ is not prescribed at the
                        # boundary. Using the owner value degrades second-order accuracy on
                        # linear functions (see test_gradient_fix).  Therefore treat Neumann
                        # faces as *flux-only* and skip their direct contribution.
                        nothing  # mark as skip
                    else
                        field.data[owner_idx]
                    end
                else
                    # No BC specified – assume zero-gradient ⇒ use owner value
                    field.data[owner_idx]
                end

                if bc_val !== nothing
                    grad_data[owner_idx] += bc_val * S_f_vec
                end
            end
        end

        # Normalise by cell volume
        for cell_idx in 1:num_cells
            vol = mesh.cells[cell_idx].volume
            @assert vol > 0 "Cell $(cell_idx) has non-positive volume ($vol)"
            grad_data[cell_idx] /= vol
        end
    elseif scheme isa LeastSquaresGradient
        # Implement least squares gradient reconstruction for better accuracy on non-orthogonal meshes
        for cell_idx in 1:num_cells
            cell = mesh.cells[cell_idx]
            
            # Build least squares system for this cell
            A = zeros(TF_base, N_dims, N_dims)
            b = zeros(TF_base, N_dims)
            
            # Add contributions from each neighbor
            n_neighbors = 0
            for f_idx in cell.faces
                face = mesh.faces[f_idx]
                
                if face.boundary
                    continue  # Skip boundary faces for now in LS reconstruction
                end
                
                # Get neighbor cell
                neighbor_idx = face.owner == cell_idx ? face.neighbor : face.owner
                if neighbor_idx < 1
                    continue  # Skip invalid neighbors
                end
                
                # Vector from cell center to neighbor center
                d_vec = mesh.cells[neighbor_idx].center - cell.center
                w = 1.0 / norm(d_vec)  # Weight by inverse distance
                
                # Build normal equations components
                A += w^2 * (d_vec * d_vec')
                b += w^2 * d_vec * (field.data[neighbor_idx] - field.data[cell_idx])
                n_neighbors += 1
            end
            
            # Solve system if we have enough neighbors
            if n_neighbors >= N_dims
                grad_data[cell_idx] = SVector{N_dims, TF_base}(A \ b)
            end
        end
    else
        error("Unsupported gradient scheme: $(typeof(scheme)) in fvc.grad")
    end

    return VectorField(Symbol(string(field.name)*"_grad_fvc"), mesh, grad_data, field.boundary_conditions, nothing, nothing)
end

# CORRECTED divergence following user's analysis - fix face orientation at source
function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, scheme::AbstractDivergenceScheme) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    mesh = field.mesh
    n_cells = length(mesh.cells)
    TF = eltype(SVecTF)
    
    # Initialize divergence array
    div_data = zeros(TF, n_cells)
    
    for face in mesh.faces
        # Interpolate to face
        U_face = if face.neighbor > 0
            # Internal face: simple average (can be enhanced with distance weighting)
            0.5 * (field.data[face.owner] + field.data[face.neighbor])
        else
            # Boundary face: zero-gradient BC
            field.data[face.owner]
        end
        
        # Compute flux (face.normal * face.area = Sf, must point from owner to neighbor!)
        flux = dot(U_face, face.normal) * face.area
        
        # Apply with correct signs per Gauss theorem
        div_data[face.owner] += flux      # Positive for owner (outgoing flux)
        if face.neighbor > 0
            div_data[face.neighbor] -= flux # Negative for neighbor (incoming flux)
        end
    end
    
    # Normalize by cell volume
    for i in 1:n_cells
        div_data[i] /= mesh.cells[i].volume
    end
    
    return CFDCore.ScalarField(Symbol(string(field.name)*"_div"), mesh, div_data, field.boundary_conditions, nothing, nothing)
end

# Specific implementation for CentralDifferencing scheme with proper face interpolation
function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}, scheme::CentralDifferencing; 
            rhie_chow_data=nothing) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    mesh = field.mesh
    n_cells = length(mesh.cells)
    TF = eltype(SVecTF)
    divergence_data = zeros(TF, n_cells)

    for face_idx in 1:length(mesh.faces)
        face = mesh.faces[face_idx]
        owner_idx = face.owner  # Already 1-based indexing
        
        if !face.boundary && face.neighbor >= 0
            neighbor_idx = face.neighbor
            
            # Standard interpolation of velocity to face
            g_f = face_distance_weight(face, mesh.cells[owner_idx], mesh.cells[neighbor_idx])
            U_f = g_f * field.data[owner_idx] + (1 - g_f) * field.data[neighbor_idx]
            
            # Apply Rhie-Chow correction if data is provided (for pressure-velocity coupling)
            if !isnothing(rhie_chow_data) && haskey(rhie_chow_data, :p) && haskey(rhie_chow_data, :aP)
                p = rhie_chow_data[:p]
                aP = rhie_chow_data[:aP]
                
                # Face normal vector
                n = normalize(face.normal)
                
                # Distance vector and magnitude
                d_vec = mesh.cells[neighbor_idx].center - mesh.cells[owner_idx].center
                δ = norm(d_vec)
                
                # Average 1/aP at face (harmonic mean)
                aP_f = 2.0 / ((1.0/aP[owner_idx]) + (1.0/aP[neighbor_idx]))
                
                # Pressure difference
                Δp = p[neighbor_idx] - p[owner_idx]
                
                # Rhie-Chow correction vector
                correction = (1.0/aP_f) * (Δp/δ) * n
                
                # Apply correction to interpolated velocity
                U_f -= correction
            end
            
            # Calculate flux through face
            flux = dot(U_f, face.normal) * face.area
            
            # Add to divergence
            divergence_data[owner_idx] += flux
            divergence_data[neighbor_idx] -= flux
        else 
            # Boundary face handling
            bc_patch_name = get_boundary_patch_name(face_idx, mesh)
            
            if !isempty(bc_patch_name) && haskey(field.boundary_conditions, bc_patch_name)
                bc = field.boundary_conditions[bc_patch_name]
                
                # Get boundary velocity based on BC type
                U_b = if bc isa CFDCore.DirichletBC
                    # Fixed value BC
                    SVector{length(field.data[1]), TF}(bc.value(face.center..., 0.0))
                elseif bc isa CFDCore.NeumannBC
                    # Zero gradient or fixed gradient
                    field.data[owner_idx]
                else
                    # Default to field value
                    field.data[owner_idx]
                end
                
                flux = dot(U_b, face.normal) * face.area
                divergence_data[owner_idx] += flux
            else
                # If no specific BC, use cell center value
                flux = dot(field.data[owner_idx], face.normal) * face.area
                divergence_data[owner_idx] += flux
            end
        end
    end

    # Scale by cell volume to get divergence
    for i in 1:n_cells
        divergence_data[i] /= mesh.cells[i].volume
    end

    return CFDCore.ScalarField(Symbol(string(field.name)*"_div"), mesh, divergence_data, field.boundary_conditions, nothing, nothing)
end

# CORRECTED face interpolation following FvCFD.jl linInterp_3D pattern
function interpolate_vectors_to_faces_fvcfd_pattern(vector_field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    mesh = vector_field.mesh
    n_faces = length(mesh.faces)
    
    face_vectors = Vector{SVecTF}(undef, n_faces)
    
    for (f_idx, face) in enumerate(mesh.faces)
        if face.boundary || face.neighbor <= 0
            # Boundary face: use owner cell value (or BC if available)
            face_vectors[f_idx] = vector_field.data[face.owner]
        else
            # Interface face: use distance-weighted interpolation (FvCFD.jl pattern)
            owner_idx = face.owner
            neighbor_idx = face.neighbor
            
            # Distance from face center to cell centers
            owner_center = mesh.cells[owner_idx].center
            neighbor_center = mesh.cells[neighbor_idx].center
            face_center = face.center
            
            # Compute distances (FvCFD.jl uses squared distances)
            dist_owner_sq = sum((owner_center[i] - face_center[i])^2 for i in 1:3)
            dist_neighbor_sq = sum((neighbor_center[i] - face_center[i])^2 for i in 1:3)
            total_dist_sq = dist_owner_sq + dist_neighbor_sq
            
            # Distance-weighted interpolation (FvCFD.jl pattern)
            # faceVal = val1*(dist2/total) + val2*(dist1/total)
            weight_owner = dist_neighbor_sq / total_dist_sq
            weight_neighbor = dist_owner_sq / total_dist_sq
            
            # Interpolated vector at face
            face_vectors[f_idx] = weight_owner * vector_field.data[owner_idx] + weight_neighbor * vector_field.data[neighbor_idx]
        end
    end
    
    return face_vectors
end

# Helper function to calculate face interpolation weight (kept for compatibility)
function face_distance_weight(face, owner_cell, neighbor_cell)
    # Distance from owner to face center
    d_owner = norm(face.center - owner_cell.center)
    
    # Distance from neighbor to face center
    d_neighbor = norm(face.center - neighbor_cell.center)
    
    # Linear interpolation weight
    return d_neighbor / (d_owner + d_neighbor)
end

# Helper function to get boundary patch name for a face
function get_boundary_patch_name(face_idx, mesh)
    for (patch_name, faces_indices) in mesh.boundaries
        if face_idx in faces_indices
            return patch_name
        end
    end
    return ""
end

function laplacian_correct(γ_val::Number, field::ScalarField{TF, NF, M}, scheme::AbstractLaplacianScheme; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    # CORRECTED implementation: ∇²φ = ∇·(∇φ) using corrected FvCFD.jl pattern
    
    # Step 1: Compute gradient at cell centers using corrected gradient operator
    grad_field = grad(field, GaussGradient(), current_time=current_time)
    
    # Step 2: Compute divergence of gradient using corrected divergence operator
    laplacian_field = div(grad_field, GaussDivergence())
    
    # Step 3: Scale by diffusion coefficient
    laplacian_data = γ_val .* laplacian_field.data
    
    return CFDCore.ScalarField(Symbol(string(field.name)*"_laplacian_fvc_correct"), field.mesh, laplacian_data, field.boundary_conditions, nothing, nothing)
end

# Keep original function for compatibility, but fix it using FvCFD.jl insights
function laplacian(γ_val::Number, field::ScalarField{TF, NF, M}, scheme::AbstractLaplacianScheme; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    mesh = field.mesh
    n_cells = length(mesh.cells)
    laplacian_data = zeros(TF, n_cells)

    for face_idx in 1:length(mesh.faces)
        face = mesh.faces[face_idx]
        owner_cell_idx = face.owner  # Already 1-based indexing
        
        if face.boundary
            bc_patch_name = ""
            for (patch_name, faces_indices) in mesh.boundaries
                if face_idx in faces_indices
                    bc_patch_name = patch_name
                    break
                end
            end
            if bc_patch_name == ""
                error("Boundary face $(face.id) not found in any mesh boundary patch.")
            end
            if !haskey(field.boundary_conditions, bc_patch_name)
                 error("Boundary condition for patch '$bc_patch_name' not found in field '$(field.name)' for face $(face.id)")
            end
            bc = field.boundary_conditions[bc_patch_name]

            dist_to_face = norm(face.center - mesh.cells[owner_cell_idx].center)
            dist_to_face = max(dist_to_face, 1e-9)

            flux = 0.0
            if bc isa CFDCore.DirichletBC
                # For FVC explicit operations, compute gradient at face directly
                # For Dirichlet BC with analytical function, we can compute ∇φ·n̂ directly
                
                # Get the analytical function if available
                phi_face = 0.0
                coords = Tuple(face.center)
                if length(coords) == 2
                    phi_face = bc.value(coords[1], coords[2], 0.0, current_time)
                elseif length(coords) == 3
                    phi_face = bc.value(coords[1], coords[2], coords[3], current_time)
                else
                    error("Unsupported face coordinate dimensions: $(length(coords))")
                end
                
                # For now, use improved finite difference approximation
                # TODO: Replace with direct gradient evaluation when BC provides gradient function
                phi_owner = field.data[owner_cell_idx]
                
                # Use distance from cell center to face center (not to boundary)
                cell_to_face_vec = face.center - mesh.cells[owner_cell_idx].center
                normal_dist = abs(dot(cell_to_face_vec, normalize(face.normal)))
                normal_dist = max(normal_dist, 1e-9)
                
                # Gradient in normal direction
                grad_normal = (phi_face - phi_owner) / normal_dist
                flux = γ_val * grad_normal * face.area
                laplacian_data[owner_cell_idx] += flux

            elseif bc isa CFDCore.NeumannBC
                specified_gradient_val = 0.0
                coords = Tuple(face.center)
                if length(coords) == 2
                    specified_gradient_val = bc.gradient(coords[1], coords[2], 0.0, current_time)
                elseif length(coords) == 3
                    specified_gradient_val = bc.gradient(coords[1], coords[2], coords[3], current_time)
                else
                    error("Unsupported face coordinate dimensions: $(length(coords))")
                end
                flux = γ_val * specified_gradient_val * face.area
                laplacian_data[owner_cell_idx] += flux
            else
                error("Unsupported boundary condition type: $(typeof(bc)) for patch $(bc_patch_name)")
            end
        else 
            neighbor_cell_idx = face.neighbor  # Already 1-based indexing
            dist_centers = norm(mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center)
            dist_centers = max(dist_centers, 1e-9)
            
            phi_owner = field.data[owner_cell_idx]
            phi_neighbor = field.data[neighbor_cell_idx]
            flux = γ_val * (phi_neighbor - phi_owner) / dist_centers * face.area
            
            laplacian_data[owner_cell_idx] += flux
            laplacian_data[neighbor_cell_idx] -= flux
        end
    end

    for i in 1:n_cells
        laplacian_data[i] /= mesh.cells[i].volume
    end

    return CFDCore.ScalarField(Symbol(string(field.name)*"_laplacian_fvc"), mesh, laplacian_data, field.boundary_conditions, nothing, nothing)
end

function convection_term(rho::Float64, U_field::VectorField{SVector{N, TF_base}, N, M_type}, scheme::StandardConvectionScheme; current_time::Float64 = 0.0) where {N, TF_base, M_type<:AbstractMesh}
    mesh = U_field.mesh
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)
    
    conv_term_data = [zero(SVector{N, TF_base}) for _ in 1:num_cells]

    interpolation_scheme = scheme.interpolation_scheme
    U_f_face_data = interpolate_vector_to_faces(U_field, interpolation_scheme, current_time=current_time)

    phi_mass_face_data = zeros(TF_base, num_faces)
    for f_idx in 1:num_faces
        face = mesh.faces[f_idx]
        U_f_val_at_face = U_f_face_data[f_idx]
        S_f_vec = face.normal * face.area
        phi_mass_face_data[f_idx] = rho * dot(U_f_val_at_face, S_f_vec)
    end

    for k_comp in 1:N
        U_k_cell_field = CFDCore.extract_scalar_component(U_field, k_comp, component_name_suffix="_conv_comp$(k_comp)")
        U_k_face_data = interpolate(U_k_cell_field, interpolation_scheme, current_time=current_time)
        conv_flux_U_k_face_data = phi_mass_face_data .* U_k_face_data
        
        div_scheme = GaussDivergence() 
        div_conv_U_k_scalar_field = div_face_flux(conv_flux_U_k_face_data, mesh, div_scheme, 
                                                  Symbol("div_conv_flux_U$(k_comp)"), U_k_cell_field.boundary_conditions)
        
        for i_cell in 1:num_cells
            current_vec = conv_term_data[i_cell]
            conv_term_data[i_cell] = setindex(current_vec, div_conv_U_k_scalar_field.data[i_cell], k_comp)
        end
    end

    return VectorField(Symbol(string(U_field.name)*"_convection_fvc"), mesh, conv_term_data, U_field.boundary_conditions, nothing, nothing)
end

function interpolate(field::ScalarField{TF, NF, M}, scheme::AbstractInterpolationScheme; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    mesh = field.mesh
    num_faces = length(mesh.faces)
    face_values = zeros(TF, num_faces)

    for f_idx in 1:num_faces
        face = mesh.faces[f_idx]
        owner_idx = face.owner  # Already 1-based indexing
        owner_val = field.data[owner_idx]

        if face.boundary
            bc_patch_name = ""
            for (patch_name, faces_indices) in mesh.boundaries
                if f_idx in faces_indices
                    bc_patch_name = patch_name
                    break
                end
            end
            
            # Handle case where face isn't found in any patch
            if bc_patch_name == ""
                @warn "Boundary face $(face.id) not found in any mesh boundary patch. Using owner cell value for interpolation."
                face_values[f_idx] = owner_val
                continue
            end
            
            # Handle case where boundary condition for patch isn't defined
            if !haskey(field.boundary_conditions, bc_patch_name)
                @warn "Boundary condition for patch '$bc_patch_name' not found in field '$(field.name)' for face $(face.id). Using owner cell value for interpolation."
                face_values[f_idx] = owner_val
                continue
            end
            
            bc = field.boundary_conditions[bc_patch_name]

            if scheme isa LinearInterpolation || scheme isa CentralDifferencing
                if bc isa CFDCore.DirichletBC
                    coords = Tuple(face.center)
                    if length(coords) == 2
                        face_values[f_idx] = bc.value(coords[1], coords[2], 0.0, current_time)
                    elseif length(coords) == 3
                        face_values[f_idx] = bc.value(coords[1], coords[2], coords[3], current_time)
                    else
                        @warn "Unsupported face coordinate dimensions: $(length(coords)). Using owner cell value."
                        face_values[f_idx] = owner_val
                    end
                elseif bc isa CFDCore.NeumannBC
                    face_values[f_idx] = owner_val
                else
                    @warn "Unhandled BC type $(typeof(bc)) for face $(face.id) with LinearInterpolation. Using owner cell value."
                    face_values[f_idx] = owner_val
                end
            elseif scheme isa UpwindInterpolation
                @warn "UpwindInterpolation BC handling not fully implemented in fvc.interpolate. Using owner value for now."
                face_values[f_idx] = owner_val
            else
                @warn "Unsupported interpolation scheme $(typeof(scheme)) for boundary face $(face.id). Using owner cell value."
            end
        else 
            neighbor_idx = face.neighbor  # Already 1-based indexing
            neighbor_val = field.data[neighbor_idx]
            if scheme isa LinearInterpolation || scheme isa CentralDifferencing
                face_values[f_idx] = 0.5 * (owner_val + neighbor_val)
            elseif scheme isa UpwindInterpolation
                @warn "UpwindInterpolation for internal faces not implemented in fvc.interpolate. Using linear for now."
                face_values[f_idx] = 0.5 * (owner_val + neighbor_val) 
            else
                error("Unsupported interpolation scheme $(typeof(scheme)) for internal face $(face.id).")
            end
        end
    end
    return face_values
end

# Overloaded function that takes a field directly (for test compatibility)
function div_face_flux(surface_field::ScalarField{TF, NF, M}) where {TF, NF, M<:AbstractMesh}
    scheme = GaussDivergence()
    return div_face_flux(surface_field.data, surface_field.mesh, scheme, surface_field.name, surface_field.boundary_conditions)
end

function div_face_flux(face_flux_data::Vector{TF}, mesh::M, scheme::AbstractDivergenceScheme, base_field_name::Symbol, original_bcs::Dict{String, AbstractBoundaryCondition}) where {TF <: Number, M <: AbstractMesh}
    num_cells = length(mesh.cells)
    div_data = zeros(TF, num_cells)

    if !(scheme isa GaussDivergence)
        error("Unsupported divergence scheme: $(typeof(scheme))")
    end

    for f_idx in 1:length(mesh.faces)
        face = mesh.faces[f_idx]
        flux_val = face_flux_data[f_idx]
        div_data[face.owner] += flux_val  # Already 1-based indexing
        if !face.boundary
            div_data[face.neighbor] -= flux_val  # Already 1-based indexing
        end
    end

    for i_cell in 1:num_cells
        if mesh.cells[i_cell].volume <= 0
            error("Cell $(i_cell) has zero or negative volume: $(mesh.cells[i_cell].volume)")
        end
        div_data[i_cell] /= mesh.cells[i_cell].volume
    end

    return CFDCore.ScalarField(Symbol(string(base_field_name)*"_div_face_flux"), mesh, div_data, original_bcs, nothing, nothing)
end

function interpolate_vector_to_faces(vector_field::VectorField{SVector{N, TF}, N, M}, 
                                     scheme::AbstractInterpolationScheme; 
                                     current_time::Float64 = 0.0) where {N, TF, M<:AbstractMesh}
    mesh = vector_field.mesh
    num_faces = length(mesh.faces)
    
    face_vectors = Vector{SVector{N, TF}}(undef, num_faces)

    component_face_values = Vector{Vector{TF}}(undef, N)
    for k_comp in 1:N
        scalar_comp_field = CFDCore.extract_scalar_component(vector_field, k_comp, component_name_suffix="_interp_comp$(k_comp)")
        component_face_values[k_comp] = interpolate(scalar_comp_field, scheme, current_time=current_time)
    end

    for f_idx in 1:num_faces
        face_vectors[f_idx] = SVector{N, TF}(component_face_values[k_comp][f_idx] for k_comp in 1:N)
    end

    return face_vectors
end

# ============================================================================
# Convenience functions with default schemes for easy use
# ============================================================================

"""
Gradient operator with default Gauss gradient scheme
"""
function grad(field::ScalarField{TF, NF, M}; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    return grad(field, GaussGradient(), current_time=current_time)
end

"""
Divergence operator with default central differencing scheme  
"""
function div(field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}; current_time::Float64 = 0.0) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    return div(field, GaussDivergence())
end

"""
Laplacian operator with default scheme
"""
function laplacian(γ_val::Number, field::ScalarField{TF, NF, M}; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    return laplacian_correct(γ_val, field, DefaultFvcLaplacian(), current_time=current_time)
end

"""
Simple laplacian operator (γ = 1.0)
"""
function laplacian(field::ScalarField{TF, NF, M}; current_time::Float64 = 0.0) where {TF, NF, M<:AbstractMesh}
    return laplacian_correct(1.0, field, DefaultFvcLaplacian(), current_time=current_time)
end

end # module fvc
