# src/Numerics/fvm.jl
# Mathematically correct finite volume method (FVM) operators for implicit discretization

module fvm

export laplacian, div, grad, solve, relax!

using ...CFDCore: <PERSON>bs<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ector<PERSON><PERSON>, Field, AbstractBoundaryCondition, DirichletBC, NeumannBC
using LinearAlgebra
using SparseArrays
using StaticArrays

# Matrix type for finite volume discretization
mutable struct FvMatrix{T}
    A::SparseMatrixCSC{T,Int}
    b::Vector{T}
end

# ---------------------------------------------------------------------------
# Compatibility aliases to support legacy field names (e.g., `.source`)
# ---------------------------------------------------------------------------

import Base: getproperty, setproperty!

"""
    getproperty(m::FvMatrix, sym::Symbol)

Provide a compatibility alias so that `fv_matrix.source` returns the RHS
vector `b`, which older test-suites expect. All other properties are
forwarded to the original struct fields through `getfield`.
"""
function getproperty(m::FvMatrix, sym::Symbol)
    if sym === :source
        return getfield(m, :b)
    else
        return getfield(m, sym)
    end
end

"""
    setproperty!(m::FvMatrix, sym::Symbol, val)

Allow writing to `fv_matrix.source` to modify the RHS vector `b` while
maintaining backwards compatibility.
"""
function setproperty!(m::FvMatrix, sym::Symbol, val)
    if sym === :source
        return setfield!(m, :b, val)
    else
        return setfield!(m, sym, val)
    end
end

"""
    laplacian(gamma::Float64, field::ScalarField)

Construct matrix for implicit discretization of Laplacian term: ∇·(γ∇φ)

CRITICAL: CORRECTED sign conventions for proper FVM:
- Matrix represents +∇·(γ∇φ) 
- Use (A*φ - b) to get +∇·(γ∇φ)
- POSITIVE diagonal coefficients (stability)
- NEGATIVE off-diagonal coefficients (diffusion)
- Proper boundary condition handling

The resulting matrix A satisfies: +∇·(γ∇φ) = A*φ - b
"""
function laplacian(gamma::Float64, field::ScalarField; current_time::Float64=0.0,
                   use_non_orthogonal_correction::Bool=false)
    mesh = field.mesh
    num_cells = length(mesh.cells)
    
    # Initialize matrix and RHS vector
    A = spzeros(Float64, num_cells, num_cells)
    b = zeros(Float64, num_cells)
    
    # Process internal faces first
    for face in mesh.faces
        if !face.boundary
            # Get owner and neighbor cell indices
            owner = face.owner
            neighbor = face.neighbor
            
            # Face area vector
            S_f = face.normal * face.area
            
            # Distance vector between cell centers
            d = mesh.cells[neighbor].center - mesh.cells[owner].center
            delta = max(norm(d), 1e-12)
            
            # Diffusion coefficient
            ap = gamma * norm(S_f) / delta
            
            # Add contributions to matrix (symmetric update)
            A[owner, owner] += ap
            A[owner, neighbor] -= ap
            A[neighbor, neighbor] += ap
            A[neighbor, owner] -= ap
        end
    end
    
    # Process boundary faces for Dirichlet and Neumann BCs
    for (face_idx, face) in enumerate(mesh.faces)
        if face.boundary
            owner = face.owner
            bc = get_boundary_condition(field, face_idx)
            
            # Face area vector
            S_f = face.normal * face.area
            
            if bc isa DirichletBC
                # Dirichlet BC: fixed value at boundary
                phi_bc = bc.value(face.center[1], face.center[2], face.center[3], current_time)
                
                # Distance from cell center to face center
                d = face.center - mesh.cells[owner].center
                delta = max(norm(d), 1e-12)
                
                # Diffusion coefficient - for boundary face to boundary value
                ap = gamma * norm(S_f) / delta
                
                # Add to diagonal and source term
                A[owner, owner] += ap
                b[owner] += ap * phi_bc
                
            elseif bc isa NeumannBC
                # Neumann BC: fixed gradient at boundary
                dphi_dn = bc.gradient(face.center[1], face.center[2], face.center[3], current_time)
                
                # Add flux directly to source term (no matrix contribution)
                b[owner] += gamma * dphi_dn * norm(S_f)
            end
        end
    end
    
    # Note: Conservation is naturally satisfied by the symmetric assembly of internal faces.
    # Boundary conditions add extra terms that should NOT be included in conservation.
    # Do NOT enforce row sums to zero after BC application as this makes the matrix singular.
    
    
    # Create and return FvMatrix directly
    return FvMatrix(A, b)
end

"""
    div(flux_field::ScalarField, phi_field::ScalarField; scheme::Symbol=:upwind)

Construct matrix for convection term: ∇·(φU) where U is the velocity field
flux_field contains face fluxes F_f = U_f · S_f

CRITICAL: Implements proper upwind/central differencing with correct signs
"""
function div(flux_field::ScalarField, phi_field::ScalarField; scheme::Symbol=:upwind, current_time::Float64=0.0)
    mesh = phi_field.mesh
    num_cells = length(mesh.cells)
    
    if length(flux_field.data) != length(mesh.faces)
        error("flux_field must have face data (one value per face)")
    end
    
    # Matrix assembly arrays
    I = Int[]
    J_vec = Int[]
    V = Float64[]
    b = zeros(Float64, num_cells)
    
    # Process all faces
    for face_idx in 1:length(mesh.faces)
        face = mesh.faces[face_idx]
        owner_idx = face.owner
        flux = flux_field.data[face_idx]  # F_f = U_f · S_f
        
        if face.boundary
            # Boundary face
            bc = get_boundary_condition(phi_field, face_idx)
            
            if bc isa DirichletBC
                # Fixed value at boundary
                boundary_value = bc.value(face.center..., current_time)
                b[owner_idx] += flux * boundary_value
            end
            
        else
            # Internal face - apply interpolation scheme
            neighbor_idx = face.neighbor
            
            if scheme == :upwind
                # Upwind scheme: use upstream value
                if flux > 0  # Flow from owner to neighbor
                    # Use owner value: φ_f = φ_P
                    push!(I, owner_idx); push!(J_vec, owner_idx); push!(V, flux)
                    push!(I, neighbor_idx); push!(J_vec, owner_idx); push!(V, -flux)
                else  # Flow from neighbor to owner
                    # Use neighbor value: φ_f = φ_N
                    push!(I, owner_idx); push!(J_vec, neighbor_idx); push!(V, flux)
                    push!(I, neighbor_idx); push!(J_vec, neighbor_idx); push!(V, -flux)
                end
                
            elseif scheme == :central
                # Central differencing: φ_f = 0.5*(φ_P + φ_N)
                coeff = 0.5 * flux
                
                # Owner contributions
                push!(I, owner_idx); push!(J_vec, owner_idx); push!(V, coeff)
                push!(I, owner_idx); push!(J_vec, neighbor_idx); push!(V, coeff)
                
                # Neighbor contributions (opposite signs)
                push!(I, neighbor_idx); push!(J_vec, owner_idx); push!(V, -coeff)
                push!(I, neighbor_idx); push!(J_vec, neighbor_idx); push!(V, -coeff)
                
            else
                error("Unknown convection scheme: $scheme")
            end
        end
    end
    
    # Assemble sparse matrix
    A = sparse(I, J_vec, V, num_cells, num_cells)
    
    return FvMatrix(A, b)
end

"""
    ddt(phi_field::ScalarField, phi_old::ScalarField, dt::Float64)

Time derivative term: ∂φ/∂t using Euler implicit scheme
Returns matrix for (φ^{n+1} - φ^n)/Δt
"""
function ddt(phi_field::ScalarField, phi_old::ScalarField, dt::Float64)
    mesh = phi_field.mesh
    num_cells = length(mesh.cells)
    
    if dt <= 0
        error("Time step must be positive")
    end
    
    # Diagonal matrix: (1/Δt) * V * I
    diagonal_coeffs = zeros(Float64, num_cells)
    b = zeros(Float64, num_cells)
    
    for cell_idx in 1:num_cells
        cell = mesh.cells[cell_idx]
        coeff = cell.volume / dt
        
        diagonal_coeffs[cell_idx] = coeff
        b[cell_idx] = coeff * phi_old.data[cell_idx]
    end
    
    A = spdiagm(diagonal_coeffs)
    return FvMatrix(A, b)
end

"""
    Su(source::Union{Float64, Vector{Float64}}, field::ScalarField)

Explicit source term: adds to RHS vector
"""
function Su(source::Union{Float64, Vector{Float64}}, field::ScalarField)
    mesh = field.mesh
    num_cells = length(mesh.cells)
    
    A = spzeros(Float64, num_cells, num_cells)
    
    if source isa Float64
        # Uniform source
        b = fill(source, num_cells)
    else
        # Cell-wise source
        if length(source) != num_cells
            error("Source vector length must match number of cells")
        end
        b = copy(source)
    end
    
    return FvMatrix(A, b)
end

"""
    Sp(source::Union{Float64, Vector{Float64}}, field::ScalarField)

Implicit source term: adds to diagonal matrix (for linearization)
"""
function Sp(source::Union{Float64, Vector{Float64}}, field::ScalarField)
    mesh = field.mesh
    num_cells = length(mesh.cells)
    
    if source isa Float64
        # Uniform source
        diagonal_coeffs = fill(-source, num_cells)  # Negative because goes to LHS
    else
        # Cell-wise source
        if length(source) != num_cells
            error("Source vector length must match number of cells")
        end
        diagonal_coeffs = -source  # Negative because goes to LHS
    end
    
    A = spdiagm(diagonal_coeffs)
    b = zeros(Float64, num_cells)
    
    return FvMatrix(A, b)
end

"""
Helper function to get boundary condition for a face index
"""
function get_boundary_condition(field::ScalarField, face_idx::Int)
    for (patch_name, faces_indices) in field.mesh.boundaries
        if face_idx in faces_indices
            if haskey(field.boundary_conditions, patch_name)
                return field.boundary_conditions[patch_name]
            end
        end
    end
    
    # Default Neumann BC
    return NeumannBC((x,y,z,t) -> 0.0)
end

# Matrix combination operators
import Base: +, -, *

function +(A::FvMatrix{T}, B::FvMatrix{S}) where {T,S}
    R = promote_type(T, S)
    return FvMatrix{R}(A.A + B.A, A.b + B.b)
end

function -(A::FvMatrix{T}, B::FvMatrix{S}) where {T,S}
    R = promote_type(T, S)
    return FvMatrix{R}(A.A - B.A, A.b - B.b)
end

function *(α::Number, A::FvMatrix{T}) where T
    R = promote_type(typeof(α), T)
    return FvMatrix{R}(α * A.A, α * A.b)
end

*(A::FvMatrix{T}, α::Number) where T = α * A

"""
Under-relaxation for iterative solvers
"""
function relax!(fv_matrix::FvMatrix{T}, phi_old::Vector{T}, alpha::Float64) where T
    if !(0.0 < alpha <= 1.0)
        error("Relaxation factor must be between 0 and 1")
    end
    
    if alpha ≈ 1.0
        return  # No relaxation
    end
    
    # Extract diagonal
    A_diag = diag(fv_matrix.A)
    
    # Apply under-relaxation: A_new = A/α, b_new = b + (1-α)/α * A_diag * φ_old
    fv_matrix.A ./= alpha
    fv_matrix.b .+= ((1.0 - alpha) / alpha) .* A_diag .* phi_old
    
    return nothing
end

"""
Solve the linear system
"""
function solve(fv_matrix::FvMatrix; phi_old::Union{Vector, Nothing}=nothing, alpha::Float64=1.0)
    if alpha < 1.0 && phi_old !== nothing
        # Apply under-relaxation
        relaxed_matrix = FvMatrix(copy(fv_matrix.A), copy(fv_matrix.b))
        relax!(relaxed_matrix, phi_old, alpha)
        return relaxed_matrix.A \ relaxed_matrix.b
    else
        return fv_matrix.A \ fv_matrix.b
    end
end

end # module fvm