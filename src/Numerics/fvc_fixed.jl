# Completely rewritten FVC operators following FvCFD.jl pattern
module fvc_fixed

import ...CFDCore
using ...CFDCore: <PERSON><PERSON><PERSON><PERSON><PERSON>, ScalarField, VectorField, Field, AbstractBoundaryCondition
using ..Numerics  
using LinearAlgebra
using SparseArrays
using StaticArrays

"""
    div_correct(vector_field)

Correct divergence implementation following FvCFD.jl pattern exactly.
"""
function div_correct(vector_field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    mesh = vector_field.mesh
    n_cells = length(mesh.cells)
    TF = eltype(SVecTF)
    
    # Initialize divergence array
    divergence_data = zeros(TF, n_cells)
    
    # Step 1: Interpolate vector field to faces (following FvCFD.jl linInterp_3D pattern)
    face_vectors = interpolate_vectors_to_faces_correct(vector_field)
    
    # Step 2: Integrate fluxes (following FvCFD.jl integrateFluxes pattern)
    for (f_idx, face) in enumerate(mesh.faces)
        owner_cell = face.owner
        neighbor_cell = face.neighbor
        
        # Get interpolated vector at face
        U_face = face_vectors[f_idx]
        
        # Compute flux: U_face · n̂ * Area (face normal includes area)
        flux = dot(U_face, face.normal) * face.area
        
        # Apply flux to cells with correct signs
        divergence_data[owner_cell] += flux  # Owner gets positive flux
        
        if neighbor_cell > 0 && !face.boundary
            divergence_data[neighbor_cell] -= flux  # Neighbor gets negative flux
        end
    end
    
    # Step 3: Divide by cell volume
    for c in 1:n_cells
        divergence_data[c] /= mesh.cells[c].volume
    end
    
    return CFDCore.ScalarField(Symbol(string(vector_field.name)*"_div_correct"), mesh, divergence_data, vector_field.boundary_conditions, nothing, nothing)
end

"""
    interpolate_vectors_to_faces_correct(vector_field)

Correct face interpolation following FvCFD.jl linInterp_3D pattern.
"""
function interpolate_vectors_to_faces_correct(vector_field::Union{VectorField{SVecTF, NF, M}, Field{SVecTF, NF, M}}) where {SVecTF<:SVector, NF, M<:AbstractMesh}
    mesh = vector_field.mesh
    n_faces = length(mesh.faces)
    
    face_vectors = Vector{SVecTF}(undef, n_faces)
    
    for (f_idx, face) in enumerate(mesh.faces)
        if face.boundary || face.neighbor <= 0
            # Boundary face: use owner cell value
            face_vectors[f_idx] = vector_field.data[face.owner]
        else
            # Internal face: use distance-weighted interpolation
            owner_idx = face.owner
            neighbor_idx = face.neighbor
            
            # Distance from face center to cell centers (FvCFD.jl pattern)
            owner_center = mesh.cells[owner_idx].center
            neighbor_center = mesh.cells[neighbor_idx].center
            face_center = face.center
            
            # Squared distances (as in FvCFD.jl)
            dist_owner_sq = sum((owner_center[i] - face_center[i])^2 for i in 1:3)
            dist_neighbor_sq = sum((neighbor_center[i] - face_center[i])^2 for i in 1:3)
            total_dist_sq = dist_owner_sq + dist_neighbor_sq
            
            # Weights (FvCFD.jl uses: val1*(dist2/total) + val2*(dist1/total))
            weight_owner = dist_neighbor_sq / total_dist_sq
            weight_neighbor = dist_owner_sq / total_dist_sq
            
            # Interpolated vector
            face_vectors[f_idx] = weight_owner * vector_field.data[owner_idx] + weight_neighbor * vector_field.data[neighbor_idx]
        end
    end
    
    return face_vectors
end

"""
    grad_correct(scalar_field)

Correct gradient implementation following FvCFD.jl greenGaussGrad pattern exactly.
"""
function grad_correct(scalar_field::ScalarField{TF, NF, M}) where {TF, NF, M<:AbstractMesh}
    mesh = scalar_field.mesh
    n_cells = length(mesh.cells)
    
    # Initialize gradient array (3D gradients)
    grad_data = [zero(SVector{3, TF}) for _ in 1:n_cells]
    
    # Step 1: Interpolate scalar values to faces
    face_values = interpolate_scalars_to_faces_correct(scalar_field)
    
    # Step 2: Integrate (following FvCFD.jl greenGaussGrad pattern)
    for (f_idx, face) in enumerate(mesh.faces)
        owner_cell = face.owner
        neighbor_cell = face.neighbor
        
        face_value = face_values[f_idx]
        
        # Area vector (normal * area)
        area_vector = face.normal * face.area
        
        # Apply to owner cell (always)
        grad_data[owner_cell] += face_value * area_vector
        
        # Apply to neighbor cell (if exists)
        if neighbor_cell > 0 && !face.boundary
            grad_data[neighbor_cell] -= face_value * area_vector
        end
    end
    
    # Step 3: Divide by cell volume
    for c in 1:n_cells
        grad_data[c] /= mesh.cells[c].volume
    end
    
    return VectorField(Symbol(string(scalar_field.name)*"_grad_correct"), mesh, grad_data, scalar_field.boundary_conditions, nothing, nothing)
end

"""
    interpolate_scalars_to_faces_correct(scalar_field)

Correct scalar face interpolation following FvCFD.jl pattern.
"""
function interpolate_scalars_to_faces_correct(scalar_field::ScalarField{TF, NF, M}) where {TF, NF, M<:AbstractMesh}
    mesh = scalar_field.mesh
    n_faces = length(mesh.faces)
    
    face_values = zeros(TF, n_faces)
    
    for (f_idx, face) in enumerate(mesh.faces)
        if face.boundary || face.neighbor <= 0
            # Boundary face: use owner cell value or BC
            # For now, simplify by using owner value
            face_values[f_idx] = scalar_field.data[face.owner]
        else
            # Internal face: distance-weighted interpolation (FvCFD.jl pattern)
            owner_idx = face.owner
            neighbor_idx = face.neighbor
            
            owner_center = mesh.cells[owner_idx].center
            neighbor_center = mesh.cells[neighbor_idx].center
            face_center = face.center
            
            # Squared distances
            dist_owner_sq = sum((owner_center[i] - face_center[i])^2 for i in 1:3)
            dist_neighbor_sq = sum((neighbor_center[i] - face_center[i])^2 for i in 1:3)
            total_dist_sq = dist_owner_sq + dist_neighbor_sq
            
            # Weights
            weight_owner = dist_neighbor_sq / total_dist_sq
            weight_neighbor = dist_owner_sq / total_dist_sq
            
            # Interpolated value
            face_values[f_idx] = weight_owner * scalar_field.data[owner_idx] + weight_neighbor * scalar_field.data[neighbor_idx]
        end
    end
    
    return face_values
end

"""
    laplacian_correct(γ, scalar_field)

Correct Laplacian: ∇²φ = ∇·(∇φ) using corrected operators.
"""
function laplacian_correct(γ_val::Number, scalar_field::ScalarField{TF, NF, M}) where {TF, NF, M<:AbstractMesh}
    # Step 1: Compute gradient
    grad_field = grad_correct(scalar_field)
    
    # Step 2: Compute divergence of gradient  
    laplacian_field = div_correct(grad_field)
    
    # Step 3: Scale by diffusion coefficient
    laplacian_data = γ_val .* laplacian_field.data
    
    return CFDCore.ScalarField(Symbol(string(scalar_field.name)*"_laplacian_correct"), scalar_field.mesh, laplacian_data, scalar_field.boundary_conditions, nothing, nothing)
end

end # module fvc_fixed