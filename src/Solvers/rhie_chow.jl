# src/Solvers/rhie_chow.jl
"""
Rhie-Chow interpolation implementation for pressure-velocity coupling.

The Rhie-Chow interpolation prevents pressure oscillations (checkerboard patterns)
in collocated grid finite volume methods by properly interpolating velocities to
face centers with pressure gradient corrections.

Mathematical formulation:
U_f = (U_f)_interpolated - (a_P)_f / (a_P)_center * [(∇p)_f - (∇p)_interpolated]

Where:
- U_f: face-centered velocity
- (U_f)_interpolated: linearly interpolated velocity from cell centers
- (a_P)_f: face-centered momentum equation diagonal coefficient
- (a_P)_center: cell-centered momentum equation diagonal coefficient
- (∇p)_f: face-centered pressure gradient
- (∇p)_interpolated: interpolated pressure gradient from cell centers
"""

module RhieChowInterpolation

using ...CFDCore
using ...CFDCore: AbstractMesh, ScalarField, VectorField, Field, AbstractBoundaryCondition
using ..Numerics.fvc
using ..Numerics: GaussGradient
using LinearAlgebra
using StaticArrays

export rhie_chow_velocity_interpolation!, compute_momentum_diagonal_coefficients!
export validate_rhie_chow_interpolation

"""
    compute_momentum_diagonal_coefficients!(
        A_U_diag::Vector{T},
        mesh::AbstractMesh,
        rho::T,
        dt::T,
        A_momentum_matrices::Vector{<:AbstractMatrix}
    ) where T

Compute diagonal coefficients from momentum equation matrices for Rhie-Chow interpolation.

The diagonal coefficient represents the contribution of the velocity at the current cell
to the momentum equation. For steady-state: a_P = convection + diffusion diagonal terms.
For transient: a_P = convection + diffusion + temporal diagonal terms.

# Arguments
- `A_U_diag`: Output vector of diagonal coefficients (one per cell)
- `mesh`: Computational mesh
- `rho`: Fluid density
- `dt`: Time step (for transient cases)
- `A_momentum_matrices`: Momentum equation matrices for each dimension [A_x, A_y, A_z]
"""
function compute_momentum_diagonal_coefficients!(
    A_U_diag::Vector{T},
    mesh::AbstractMesh,
    rho::T,
    dt::T,
    A_momentum_matrices::Vector{<:AbstractMatrix}
) where T
    
    num_cells = length(mesh.cells)
    @assert length(A_U_diag) == num_cells "A_U_diag size mismatch"
    @assert !isempty(A_momentum_matrices) "No momentum matrices provided"
    @assert size(A_momentum_matrices[1], 1) == num_cells "Matrix size mismatch"
    
    fill!(A_U_diag, zero(T))
    
    # Extract diagonal from first momentum matrix (assume same for all components)
    # In many CFD implementations, momentum matrices have same diagonal structure
    A_momentum = A_momentum_matrices[1]
    
    for i in 1:num_cells
        # Extract diagonal coefficient from momentum matrix
        A_U_diag[i] = A_momentum[i, i]
        
        # Add temporal contribution for transient cases
        if dt > 0
            cell_volume = mesh.cells[i].volume
            A_U_diag[i] += rho * cell_volume / dt
        end
        
        # Ensure positive diagonal for stability
        if A_U_diag[i] <= 0
            @warn "Non-positive diagonal coefficient at cell $i: $(A_U_diag[i])"
            A_U_diag[i] = rho * mesh.cells[i].volume / (dt > 0 ? dt : 1e-6)
        end
    end
end

"""
    rhie_chow_velocity_interpolation!(
        face_velocities::Vector{SVector{N,T}},
        U_field::VectorField,
        p_field::ScalarField,
        A_U_diag::Vector{T},
        mesh::AbstractMesh,
        current_time::T = 0.0
    ) where {N,T}

Perform Rhie-Chow interpolation to compute face-centered velocities that prevent
pressure oscillations in collocated grid methods.

This function implements the classical Rhie-Chow interpolation:
1. Linear interpolation of cell-centered velocities to faces
2. Compute pressure gradients at faces and interpolate from cells
3. Apply Rhie-Chow correction to prevent pressure decoupling

# Arguments
- `face_velocities`: Output face-centered velocities (modified in-place)
- `U_field`: Cell-centered velocity field
- `p_field`: Cell-centered pressure field
- `A_U_diag`: Momentum equation diagonal coefficients
- `mesh`: Computational mesh
- `current_time`: Current simulation time (for time-dependent BCs)

# Mathematical Details
For internal faces:
U_f = g_f * U_N + (1-g_f) * U_P - [g_f * V_N/a_N + (1-g_f) * V_P/a_P] * (∇p_f - g_f * ∇p_N - (1-g_f) * ∇p_P)

Where:
- g_f: geometric interpolation factor
- U_P, U_N: velocity at owner (P) and neighbor (N) cells
- V_P, V_N: cell volumes
- a_P, a_N: momentum diagonal coefficients
- ∇p_f: pressure gradient at face
- ∇p_P, ∇p_N: pressure gradients at cell centers
"""
function rhie_chow_velocity_interpolation!(
    face_velocities::Vector{SVector{N,T}},
    U_field::VectorField,
    p_field::ScalarField,
    A_U_diag::Vector{T},
    mesh::AbstractMesh,
    current_time::T = 0.0
) where {N,T}
    
    num_faces = length(mesh.faces)
    num_cells = length(mesh.cells)
    
    @assert length(face_velocities) == num_faces "face_velocities size mismatch"
    @assert length(U_field.data) == num_cells "U_field size mismatch"
    @assert length(p_field.data) == num_cells "p_field size mismatch"
    @assert length(A_U_diag) == num_cells "A_U_diag size mismatch"
    
    # Compute cell-centered pressure gradients
    grad_p_field = fvc.grad(p_field, GaussGradient(), current_time=current_time)
    
    # Precompute face interpolation weights for efficiency
    face_weights = Vector{T}(undef, num_faces)
    for face_idx in 1:num_faces
        face = mesh.faces[face_idx]
        if !face.boundary && face.neighbor > 0
            owner_idx = face.owner
            neighbor_idx = face.neighbor
            
            # Distance-based interpolation weight
            d_PN = mesh.cells[neighbor_idx].center - mesh.cells[owner_idx].center
            d_Pf = face.center - mesh.cells[owner_idx].center
            
            # Linear interpolation factor: g_f = |d_Pf| / |d_PN|
            face_weights[face_idx] = norm(d_Pf) / norm(d_PN)
        else
            face_weights[face_idx] = 0.0  # Boundary face uses owner cell
        end
    end
    
    # Apply Rhie-Chow interpolation to each face
    for face_idx in 1:num_faces
        face = mesh.faces[face_idx]
        
        if !face.boundary && face.neighbor > 0
            # Internal face
            owner_idx = face.owner
            neighbor_idx = face.neighbor
            g_f = face_weights[face_idx]
            
            # Step 1: Linear interpolation of velocities
            U_interpolated = (1.0 - g_f) * U_field.data[owner_idx] + g_f * U_field.data[neighbor_idx]
            
            # Step 2: Compute face-centered pressure gradient
            # For non-orthogonal meshes, this should include corrections
            d_PN = mesh.cells[neighbor_idx].center - mesh.cells[owner_idx].center
            dp = p_field.data[neighbor_idx] - p_field.data[owner_idx]
            grad_p_face = (dp / norm(d_PN)^2) * d_PN
            
            # Step 3: Interpolate cell-centered pressure gradients
            grad_p_interpolated = (1.0 - g_f) * grad_p_field.data[owner_idx] + g_f * grad_p_field.data[neighbor_idx]
            
            # Step 4: Compute Rhie-Chow correction coefficients
            # Volume-weighted inverse of diagonal coefficients
            V_P = mesh.cells[owner_idx].volume
            V_N = mesh.cells[neighbor_idx].volume
            a_P = A_U_diag[owner_idx]
            a_N = A_U_diag[neighbor_idx]
            
            # Face-centered coefficient (volume-weighted harmonic mean)
            coeff_face = (1.0 - g_f) * V_P / a_P + g_f * V_N / a_N
            
            # Step 5: Apply Rhie-Chow correction
            pressure_correction = coeff_face * (grad_p_face - grad_p_interpolated)
            
            # Final face velocity with Rhie-Chow correction
            face_velocities[face_idx] = U_interpolated - pressure_correction
            
        else
            # Boundary face - use boundary conditions or owner cell value
            owner_idx = face.owner
            
            # Check for velocity boundary conditions
            face_velocity = U_field.data[owner_idx]  # Default to owner cell
            
            # Apply boundary conditions if available
            for (patch_name, patch_faces) in mesh.boundaries
                if face_idx ∈ patch_faces && haskey(U_field.boundary_conditions, patch_name)
                    bc = U_field.boundary_conditions[patch_name]
                    if bc isa CFDCore.DirichletBC
                        # Fixed velocity boundary condition
                        bc_value = bc.value(face.center..., current_time)
                        if bc_value isa Number
                            # Scalar value - assume zero for other components
                            face_velocity = SVector{N,T}(bc_value, zeros(T, N-1)...)
                        else
                            # Vector value
                            face_velocity = SVector{N,T}(bc_value...)
                        end
                    end
                    break
                end
            end
            
            face_velocities[face_idx] = face_velocity
        end
    end
end

"""
    validate_rhie_chow_interpolation(
        face_velocities::Vector{SVector{N,T}},
        U_field::VectorField,
        mesh::AbstractMesh;
        tolerance::T = 1e-10
    ) where {N,T}

Validate Rhie-Chow interpolation results by checking:
1. Mass conservation (continuity equation satisfaction)
2. Velocity magnitude bounds
3. Boundary condition consistency

Returns a validation report with metrics and pass/fail status.
"""
function validate_rhie_chow_interpolation(
    face_velocities::Vector{SVector{N,T}},
    U_field::VectorField,
    mesh::AbstractMesh;
    tolerance::T = 1e-10
) where {N,T}
    
    validation_results = Dict{Symbol, Any}()
    
    # 1. Check mass conservation (continuity equation)
    mass_imbalances = Vector{T}(undef, length(mesh.cells))
    
    for cell_idx in 1:length(mesh.cells)
        cell = mesh.cells[cell_idx]
        mass_flow = zero(T)
        
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            face_area_vector = face.normal * face.area
            
            # Determine face velocity and orientation
            if face.owner == cell_idx
                # Outward flux for owner cell
                mass_flow += dot(face_velocities[face_idx], face_area_vector)
            elseif !face.boundary && face.neighbor == cell_idx
                # Inward flux for neighbor cell (reverse orientation)
                mass_flow -= dot(face_velocities[face_idx], face_area_vector)
            end
        end
        
        mass_imbalances[cell_idx] = abs(mass_flow)
    end
    
    max_mass_imbalance = maximum(mass_imbalances)
    validation_results[:max_mass_imbalance] = max_mass_imbalance
    validation_results[:mass_conservation_ok] = max_mass_imbalance < tolerance
    
    # 2. Check velocity magnitude bounds
    face_vel_magnitudes = [norm(u) for u in face_velocities]
    cell_vel_magnitudes = [norm(u) for u in U_field.data]
    
    max_face_vel = maximum(face_vel_magnitudes)
    max_cell_vel = maximum(cell_vel_magnitudes)
    
    validation_results[:max_face_velocity] = max_face_vel
    validation_results[:max_cell_velocity] = max_cell_vel
    validation_results[:velocity_bounds_ok] = max_face_vel <= 10 * max_cell_vel  # Reasonable bound
    
    # 3. Check for NaN or infinite values
    has_invalid_values = any(v -> any(isnan(v) || any(isinf.(v))), face_velocities)
    validation_results[:no_invalid_values] = !has_invalid_values
    
    # Overall validation status
    validation_results[:overall_valid] = (
        validation_results[:mass_conservation_ok] &&
        validation_results[:velocity_bounds_ok] &&
        validation_results[:no_invalid_values]
    )
    
    return validation_results
end

end # module RhieChowInterpolation