# src/Solvers/hpcExamples.jl - HPC CFD Examples with Parallel PISO/SIMPLE
module HPCExamples

using MPI
using Printf
using LinearAlgebra

using ..CFDCore
using ..Numerics  
using ..Physics
using ..Utilities
using ..ParallelSolvers

export run_parallel_lid_driven_cavity
export run_large_scale_channel_flow
export run_adaptive_mesh_refinement_example
export benchmark_parallel_solvers
export run_weak_scalability_test
export run_strong_scalability_test

# ============================================================================
# Parallel Lid-Driven Cavity Flow
# ============================================================================

"""
    run_parallel_lid_driven_cavity(nx, ny, nprocs; Re=1000)

Large-scale parallel lid-driven cavity flow using PISO algorithm.
Demonstrates mesh partitioning, ghost cell communication, and load balancing.
"""
function run_parallel_lid_driven_cavity(nx::Int=128, ny::Int=128, nprocs::Int=4; Re::Float64=1000.0)
    
    # Initialize MPI
    MPI.Init()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if rank == 0
        println("="^80)
        println("PARALLEL LID-DRIVEN CAVITY FLOW (PISO)")
        println("="^80)
        println("Grid: $(nx)×$(ny), Processors: $(size), Re: $(Re)")
        println("="^80)
    end
    
    try
        # 1. Create and partition mesh
        if rank == 0
            println("Step 1: Creating and partitioning mesh...")
        end
        
        # Create global mesh on rank 0
        global_mesh = nothing
        if rank == 0
            global_mesh = create_cavity_mesh(nx, ny)
        end
        
        # Broadcast mesh or use parallel mesh generation
        partitioner = MetisPartitioner(size, partition_type=:kway, minimize_edgecut=true)
        
        # Decompose mesh using METIS
        distributed_mesh = decompose_mesh_parallel(global_mesh, partitioner, comm)
        
        if rank == 0
            println("  Local cells per rank: ", [length(dm.local_cells) for dm in distributed_mesh])
            println("  Ghost cells per rank: ", [length(dm.ghost_cells) for dm in distributed_mesh])
        end
        
        # 2. Initialize fields and boundary conditions
        if rank == 0
            println("Step 2: Initializing fields and boundary conditions...")
        end
        
        mesh = distributed_mesh[rank+1]  # Get local mesh for this rank
        num_local_cells = length(mesh.local_cells)
        
        # Velocity field (initially zero except near moving wall)
        U_data = [SVector(0.0, 0.0, 0.0) for _ in 1:num_local_cells]
        initialize_cavity_velocity!(U_data, mesh, Re)
        
        U_bcs = Dict{String, AbstractBoundaryCondition}(
            "movingWall" => DirichletBC((x,y,z,t) -> SVector(1.0, 0.0, 0.0)),
            "fixedWalls" => DirichletBC((x,y,z,t) -> SVector(0.0, 0.0, 0.0))
        )
        U = VectorField(:U, mesh, U_data, U_bcs)
        
        # Pressure field (initially zero, Neumann BCs)
        p_data = zeros(Float64, num_local_cells)
        p_bcs = Dict{String, AbstractBoundaryCondition}(
            "movingWall" => NeumannBC((x,y,z,t) -> 0.0),
            "fixedWalls" => NeumannBC((x,y,z,t) -> 0.0)
        )
        p = ScalarField(:p, mesh, p_data, p_bcs)
        
        # 3. Setup physics model
        ρ = 1.0
        μ = ρ / Re
        model = Physics.Incompressible(ρ, μ)
        
        # 4. Create parallel PISO solver
        if rank == 0
            println("Step 3: Setting up parallel PISO solver...")
        end
        
        # Choose solvers based on problem size
        total_cells = nx * ny
        if total_cells > 100000
            pressure_solver = PETScSolver(:cg, preconditioner=:mg, tolerance=1e-8)
            momentum_solver = PETScSolver(:gmres, preconditioner=:ilu, tolerance=1e-6)
        else
            pressure_solver = DistributedIterativeSolver(:cg, preconditioner=:jacobi, tolerance=1e-8)
            momentum_solver = DistributedIterativeSolver(:gmres, preconditioner=:ilu, tolerance=1e-6)
        end
        
        piso_solver = ParallelPISO(mesh,
                                  pressure_solver=pressure_solver,
                                  momentum_solver=momentum_solver,
                                  n_correctors=2,
                                  n_non_orthogonal_correctors=1)
        
        # 5. Time stepping
        if rank == 0
            println("Step 4: Time stepping...")
        end
        
        # CFL-based time step
        max_velocity = 1.0  # Moving wall velocity
        min_cell_size = minimum([cell.volume^(1/2) for cell in mesh.local_cells])
        CFL = 0.5
        Δt = CFL * min_cell_size / max_velocity
        
        # Ensure consistent time step across all ranks
        Δt = MPI.Allreduce(Δt, MPI.MIN, comm)
        
        if rank == 0
            @printf("  Time step: %.6f\n", Δt)
        end
        
        # Time loop parameters
        t = 0.0
        end_time = 10.0
        write_interval = 0.5
        next_write = write_interval
        timestep = 0
        
        # Setup output
        vtk_writer = create_parallel_vtk_writer("cavity_parallel", rank, size)
        convergence_monitor = create_convergence_monitor([:U, :p], Dict(:U => 1e-5, :p => 1e-6))
        
        # 6. Main time loop
        while t < end_time
            if rank == 0
                @printf("\nTime: %8.4f, Step: %6d\n", t, timestep)
            end
            
            solve_start_time = MPI.Wtime()
            
            # Solve PISO
            solve!(piso_solver, U, p, model, Δt)
            
            solve_time = MPI.Wtime() - solve_start_time
            
            # Print performance metrics
            if rank == 0
                @printf("  Solve time: %8.4f s\n", solve_time)
                print_parallel_performance_metrics(piso_solver.monitor)
            end
            
            # Check convergence
            residuals = calculate_parallel_residuals(U, p, mesh, comm)
            if rank == 0 && check_convergence(convergence_monitor, residuals)
                println("  Simulation converged!")
                break
            end
            
            # Write output
            if t >= next_write - 1e-10
                if rank == 0
                    println("  Writing output...")
                end
                write_parallel_solution(vtk_writer, mesh, U, p, timestep, t)
                next_write += write_interval
            end
            
            # Advance time
            t += Δt
            timestep += 1
            
            # Check load balance periodically
            if timestep % 50 == 0 && piso_solver.load_balancer !== nothing
                check_and_rebalance!(piso_solver.load_balancer, piso_solver.monitor)
            end
        end
        
        # 7. Final output and cleanup
        if rank == 0
            println("\nSimulation completed successfully!")
            print_final_performance_summary(piso_solver.monitor)
        end
        
        # Final output
        write_parallel_solution(vtk_writer, mesh, U, p, timestep, t)
        
    catch e
        if rank == 0
            println("Error in parallel cavity simulation: ", e)
            println(stacktrace())
        end
    finally
        MPI.Finalize()
    end
end

# ============================================================================
# Large-Scale Channel Flow
# ============================================================================

"""
    run_large_scale_channel_flow(nx, ny, nz; Re=10000)

Large-scale turbulent channel flow simulation using parallel PISO with LES.
Demonstrates handling of very large meshes and advanced turbulence modeling.
"""
function run_large_scale_channel_flow(nx::Int=256, ny::Int=128, nz::Int=192; Re::Float64=10000.0)
    
    MPI.Init()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if rank == 0
        println("="^80)
        println("LARGE-SCALE TURBULENT CHANNEL FLOW (PARALLEL PISO + LES)")
        println("="^80)
        println("Grid: $(nx)×$(ny)×$(nz) = $(nx*ny*nz) cells")
        println("Processors: $(size), Re: $(Re)")
        println("="^80)
    end
    
    try
        # 1. Mesh generation and partitioning
        if rank == 0
            println("Step 1: Generating and partitioning large mesh...")
        end
        
        # Use parallel mesh generation for very large meshes
        mesh = generate_channel_mesh_parallel(nx, ny, nz, comm)
        
        if rank == 0
            total_cells = sum(length(dm.local_cells) for dm in [mesh])
            @printf("  Total cells: %d\n", total_cells)
            @printf("  Cells per rank: avg=%d, std=%.1f\n", 
                   div(total_cells, size), std([length(dm.local_cells) for dm in [mesh]]))
        end
        
        # 2. Initialize fields
        if rank == 0
            println("Step 2: Initializing fields...")
        end
        
        # Turbulent channel flow initialization
        U, p = initialize_channel_flow(mesh, Re)
        
        # LES model
        turbulence_model = Physics.LES(:Smagorinsky, Cs=0.1)
        model = Physics.IncompressibleLES(1.0, 1.0/Re, turbulence_model)
        
        # 3. High-performance solver setup
        if rank == 0
            println("Step 3: Setting up high-performance solvers...")
        end
        
        # Use PETSc for very large problems
        pressure_solver = PETScSolver(:cg, 
                                    preconditioner=:mg,
                                    tolerance=1e-8,
                                    max_iterations=1000)
        
        momentum_solver = PETScSolver(:gmres,
                                    preconditioner=:asm,  # Additive Schwarz
                                    tolerance=1e-6,
                                    max_iterations=500)
        
        piso_solver = ParallelPISO(mesh,
                                  pressure_solver=pressure_solver,
                                  momentum_solver=momentum_solver,
                                  n_correctors=3,  # Higher for LES
                                  n_non_orthogonal_correctors=2,
                                  enable_load_balancing=true)
        
        # 4. Adaptive time stepping
        if rank == 0
            println("Step 4: Starting adaptive time stepping...")
        end
        
        # Initial time step based on acoustic CFL
        c_acoustic = sqrt(1.4 * 1.0 / 1.0)  # speed of sound
        min_cell_size = calculate_min_cell_size_parallel(mesh, comm)
        Δt_acoustic = 0.1 * min_cell_size / c_acoustic
        
        # Convective CFL limit
        max_U = calculate_max_velocity_parallel(U, comm)
        Δt_convective = 0.5 * min_cell_size / max(max_U, 1e-10)
        
        Δt = min(Δt_acoustic, Δt_convective)
        
        if rank == 0
            @printf("  Initial time step: %.2e\n", Δt)
        end
        
        # Time loop
        t = 0.0
        end_time = 100.0  # Many flow-through times
        output_interval = 1.0
        statistics_interval = 0.1
        
        next_output = output_interval
        next_statistics = statistics_interval
        timestep = 0
        
        # Statistics collection
        U_mean = deepcopy(U.data)
        U_rms = [SVector(0.0, 0.0, 0.0) for _ in U.data]
        statistics_samples = 0
        
        # 5. Main time loop
        while t < end_time
            if rank == 0 && timestep % 10 == 0
                @printf("Time: %8.4f, Step: %6d, Δt: %.2e\n", t, timestep, Δt)
            end
            
            # Solve with LES
            solve_start = MPI.Wtime()
            solve!(piso_solver, U, p, model, Δt)
            solve_time = MPI.Wtime() - solve_start
            
            # Update statistics  
            if t >= next_statistics
                update_turbulence_statistics!(U_mean, U_rms, U, statistics_samples)
                statistics_samples += 1
                next_statistics += statistics_interval
            end
            
            # Adaptive time step
            max_U = calculate_max_velocity_parallel(U, comm)
            CFL = max_U * Δt / min_cell_size
            
            if CFL > 0.7
                Δt *= 0.9
            elseif CFL < 0.3
                Δt *= 1.1
            end
            
            Δt = min(Δt, Δt_acoustic)  # Limit by acoustic CFL
            
            # Output
            if t >= next_output - 1e-10
                if rank == 0
                    @printf("  Writing output... (solve time: %.3f s)\n", solve_time)
                end
                
                write_turbulent_flow_output(mesh, U, p, U_mean, U_rms, timestep, t, statistics_samples)
                next_output += output_interval
            end
            
            t += Δt
            timestep += 1
        end
        
        # Final statistics and output
        if rank == 0
            println("\nLarge-scale simulation completed!")
            print_turbulence_statistics(U_mean, U_rms, statistics_samples)
        end
        
    catch e
        if rank == 0
            println("Error in large-scale channel flow: ", e)
            println(stacktrace())
        end
    finally
        MPI.Finalize()
    end
end

# ============================================================================
# Scalability Benchmarks
# ============================================================================

"""
    run_weak_scalability_test()

Weak scalability test: constant problem size per processor.
Tests how well the parallel efficiency is maintained as more processors are added.
"""
function run_weak_scalability_test()
    
    MPI.Init()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if rank == 0
        println("="^80)
        println("WEAK SCALABILITY TEST - PARALLEL PISO")
        println("="^80)
        println("Processors: $(size)")
        println("="^80)
    end
    
    # Constant work per processor: 64³ cells per rank
    local_nx = local_ny = local_nz = 64
    total_cells_per_rank = local_nx * local_ny * local_nz
    
    if rank == 0
        @printf("Cells per rank: %d\n", total_cells_per_rank)
        @printf("Total cells: %d\n", total_cells_per_rank * size)
    end
    
    # Problem setup
    mesh = create_local_mesh_block(local_nx, local_ny, local_nz, rank, size, comm)
    U, p = initialize_test_fields(mesh)
    model = Physics.Incompressible(1.0, 0.01)
    
    # Solver setup optimized for scalability
    solver = ParallelPISO(mesh,
                         pressure_solver=PETScSolver(:cg, preconditioner=:mg),
                         momentum_solver=PETScSolver(:gmres, preconditioner=:asm),
                         n_correctors=2,
                         n_non_orthogonal_correctors=1)
    
    # Timing runs
    n_timesteps = 100
    Δt = 0.001
    
    MPI.Barrier(comm)
    start_time = MPI.Wtime()
    
    for timestep in 1:n_timesteps
        solve!(solver, U, p, model, Δt)
        
        if rank == 0 && timestep % 20 == 0
            elapsed = MPI.Wtime() - start_time
            @printf("Step %d/%d, Elapsed: %.2f s\n", timestep, n_timesteps, elapsed)
        end
    end
    
    MPI.Barrier(comm)
    total_time = MPI.Wtime() - start_time
    
    # Collect results
    all_times = MPI.Gather([total_time], 0, comm)
    
    if rank == 0
        avg_time = sum(all_times) / length(all_times)
        max_time = maximum(all_times)
        min_time = minimum(all_times)
        
        parallel_efficiency = min_time / max_time
        throughput = (total_cells_per_rank * size * n_timesteps) / max_time
        
        println("\nWEAK SCALABILITY RESULTS:")
        println("-"^50)
        @printf("Average time:         %.3f s\n", avg_time)
        @printf("Max time:             %.3f s\n", max_time)
        @printf("Min time:             %.3f s\n", min_time)
        @printf("Parallel efficiency:  %.1f%%\n", parallel_efficiency * 100)
        @printf("Throughput:           %.2e cell-steps/s\n", throughput)
        println("-"^50)
        
        # Performance breakdown
        print_detailed_performance_breakdown(solver.monitor)
    end
    
    MPI.Finalize()
end

"""
    run_strong_scalability_test(total_cells)

Strong scalability test: fixed total problem size, varying number of processors.
"""
function run_strong_scalability_test(total_cells::Int=1000000)
    
    MPI.Init()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if rank == 0
        println("="^80)
        println("STRONG SCALABILITY TEST - PARALLEL PISO")
        println("="^80)
        @printf("Total cells: %d\n", total_cells)
        @printf("Processors: %d\n", size)
        @printf("Cells per rank: %d\n", div(total_cells, size))
        println("="^80)
    end
    
    # Create mesh with fixed total size
    mesh = create_distributed_test_mesh(total_cells, comm)
    U, p = initialize_test_fields(mesh)
    model = Physics.Incompressible(1.0, 0.01)
    
    # Solver optimized for strong scaling
    solver = ParallelPISO(mesh,
                         pressure_solver=PETScSolver(:cg, preconditioner=:mg),
                         momentum_solver=PETScSolver(:bicgstab, preconditioner=:ilu),
                         n_correctors=2,
                         n_non_orthogonal_correctors=1)
    
    # Timing runs
    n_timesteps = 50
    Δt = 0.001
    
    # Warm-up run
    solve!(solver, U, p, model, Δt)
    
    MPI.Barrier(comm)
    start_time = MPI.Wtime()
    
    for timestep in 1:n_timesteps
        solve!(solver, U, p, model, Δt)
    end
    
    MPI.Barrier(comm)
    total_time = MPI.Wtime() - start_time
    
    if rank == 0
        # Calculate metrics
        serial_time_estimate = total_time * size  # Rough estimate
        parallel_efficiency = serial_time_estimate / (total_time * size)
        speedup = serial_time_estimate / total_time
        
        println("\nSTRONG SCALABILITY RESULTS:")
        println("-"^50)
        @printf("Total time:           %.3f s\n", total_time)
        @printf("Time per timestep:    %.3f s\n", total_time / n_timesteps)
        @printf("Estimated speedup:    %.2fx\n", speedup)
        @printf("Parallel efficiency:  %.1f%%\n", parallel_efficiency * 100)
        @printf("Cell updates/sec:     %.2e\n", (total_cells * n_timesteps) / total_time)
        println("-"^50)
        
        # Detailed timing breakdown
        print_solver_timing_breakdown(solver.monitor)
    end
    
    MPI.Finalize()
end

# ============================================================================
# Adaptive Mesh Refinement Example
# ============================================================================

"""
    run_adaptive_mesh_refinement_example()

Demonstrates dynamic load balancing with adaptive mesh refinement.
"""
function run_adaptive_mesh_refinement_example()
    
    MPI.Init()
    comm = MPI.COMM_WORLD
    rank = MPI.Comm_rank(comm)
    size = MPI.Comm_size(comm)
    
    if rank == 0
        println("="^80)
        println("ADAPTIVE MESH REFINEMENT WITH DYNAMIC LOAD BALANCING")
        println("="^80)
        println("Processors: $(size)")
        println("="^80)
    end
    
    # Initial coarse mesh
    initial_mesh = create_amr_base_mesh(64, 64, 32, comm)
    U, p = initialize_test_fields(initial_mesh)
    model = Physics.Incompressible(1.0, 0.01)
    
    # AMR-enabled solver
    solver = ParallelPISO(initial_mesh,
                         enable_load_balancing=true)
    
    # Refinement criteria
    refinement_indicator = :velocity_gradient
    max_refinement_levels = 3
    
    t = 0.0
    Δt = 0.001
    refinement_interval = 0.05
    next_refinement = refinement_interval
    
    for timestep in 1:1000
        # Solve
        solve!(solver, U, p, model, Δt)
        
        # Check for refinement
        if t >= next_refinement
            if rank == 0
                println("  Checking mesh refinement criteria...")
            end
            
            cells_to_refine = identify_refinement_cells(U, refinement_indicator)
            
            if length(cells_to_refine) > 0
                if rank == 0
                    @printf("  Refining %d cells\n", length(cells_to_refine))
                end
                
                # Refine mesh and rebalance
                refine_mesh!(solver.mesh, cells_to_refine, max_refinement_levels)
                interpolate_fields_to_refined_mesh!(U, p, solver.mesh)
                
                # Trigger load rebalancing
                rebalance_load!(solver.load_balancer, solver.mesh, [U, p])
            end
            
            next_refinement += refinement_interval
        end
        
        if rank == 0 && timestep % 50 == 0
            local_cells = length(solver.mesh.local_cells)
            total_cells = MPI.Allreduce(local_cells, MPI.SUM, comm)
            @printf("Step %d: t=%.3f, Total cells=%d\n", timestep, t, total_cells)
        end
        
        t += Δt
    end
    
    if rank == 0
        println("AMR simulation completed!")
    end
    
    MPI.Finalize()
end

# ============================================================================
# Utility Functions
# ============================================================================

function create_cavity_mesh(nx::Int, ny::Int)
    # Create structured cavity mesh
    # This is a simplified version - real implementation would be more sophisticated
    return Utilities.create_structured_mesh([nx, ny], [0.0, 0.0], [1.0, 1.0])
end

function initialize_cavity_velocity!(U_data::Vector, mesh::DistributedMesh, Re::Float64)
    # Initialize velocity field with reasonable values near moving wall
    for (i, cell) in enumerate(mesh.local_cells)
        y = cell.center[2]
        if y > 0.9  # Near moving wall
            U_data[i] = SVector(0.8 * sin(π * cell.center[1]), 0.0, 0.0)
        end
    end
end

function calculate_parallel_residuals(U::VectorField, p::ScalarField, 
                                    mesh::DistributedMesh, comm::MPI.Comm)
    # Calculate local residuals
    local_U_residual = norm([norm(u) for u in U.data])
    local_p_residual = norm(p.data)
    
    # Global residuals
    global_U_residual = sqrt(MPI.Allreduce(local_U_residual^2, MPI.SUM, comm))
    global_p_residual = sqrt(MPI.Allreduce(local_p_residual^2, MPI.SUM, comm))
    
    return Dict(:U => global_U_residual, :p => global_p_residual)
end

function print_parallel_performance_metrics(monitor::HPCPerformanceMonitor)
    @printf("    Communication time: %.3f s\n", monitor.communication_time)
    @printf("    Load imbalance:     %.1f%%\n", monitor.load_imbalance * 100)
    @printf("    Memory usage:       %.1f MB\n", monitor.memory_usage)
end

function decompose_mesh_parallel(global_mesh, partitioner::MetisPartitioner, comm::MPI.Comm)
    # Parallel mesh decomposition
    rank = MPI.Comm_rank(comm)
    
    if rank == 0 && global_mesh !== nothing
        return decompose_mesh(global_mesh, partitioner)
    else
        # Return placeholder for non-root ranks
        return [DistributedMesh{Float64, 3}(comm) for _ in 1:MPI.Comm_size(comm)]
    end
end

# Additional utility functions would be implemented here...

end # module HPCExamples