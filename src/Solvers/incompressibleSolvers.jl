# src/Solvers/incompressibleSolvers.jl
module IncompressibleSolvers

using ...CFDCore
using ...CFDCore: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alar<PERSON>ield, VectorField, Field, AbstractBoundaryCondition
using ..Numerics: GaussGradient, DefaultFvcLaplacian, CentralDifferencing, StandardConvectionScheme, AbstractInterpolationScheme
using ..Numerics.fvc # For fvc.laplacian fvc and fvm
using ..RhieChowInterpolation  # Import Rhie-Chow interpolation functions
# Import necessary items from the LinearSolvers submodule
# Note: These imports may fail if LinearSolvers module doesn't export them properly
# using ...Solvers: AbstractLinearSolver, solve!, SolverResult 
using LinearAlgebra
using Printf # For @printf macro
using SparseArrays
using StaticArrays

# PISO control parameters
Base.@kwdef struct PISOSettings
    n_correctors::Int = 2       # Number of pressure correction loops
    n_non_orthogonal_correctors::Int = 0 # For non-orthogonal mesh corrections (currently placeholder)
    # Relaxation factors could be added here if desired for PISO
end

# SIMPLE control parameters
Base.@kwdef struct SIMPLESettings
    n_non_orthogonal_correctors::Int = 0 # For non-orthogonal mesh corrections
    max_iterations::Int = 1000          # Maximum iterations
    tolerance::Float64 = 1e-6           # Convergence tolerance
    alpha_p::Float64 = 0.3              # Pressure relaxation factor
    alpha_u::Float64 = 0.7              # Velocity relaxation factor
    residual_output_frequency::Int = 10 # How often to output residuals
    converged::Bool = false             # Flag for convergence
end

"""
    solve_piso_step!(
        U_field::VectorField,         # Velocity field (input/output)
        p_field::ScalarField,         # Pressure field (input/output)
        phi_face_flux::ScalarField,   # Face mass flux ( U.data ⋅ S_f ) (output, though calculated from U)
        rho::Float64,                 # Density
        nu::Float64,                  # Kinematic viscosity
        Δt::Float64,
        mesh::AbstractMesh,
        linear_solver_config,         # Configuration/type for the linear solver for pressure (placeholder)
        piso_settings::PISOSettings
    )

Performs one time step of the PISO algorithm for incompressible flow.

TODO: 
- Flesh out H(U) calculation.
- Flesh out A_U_diag calculation.
- Implement face interpolation for A_U_diag and U_star.
- Integrate actual linear solver calls.
- Handle boundary conditions for U* and p'.
"""
function solve_piso_step!(
    U_field::VectorField,
    p_field::ScalarField,
    phi_face_flux::ScalarField, # This will store face volume fluxes: U_f ⋅ S_f_vec
    rho::Float64,
    nu::Float64,
    Δt::Float64,
    mesh::AbstractMesh,
    pressure_linear_solver,  # Type placeholder for linear solver
    piso_settings::PISOSettings
)
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)

    # Store U at n (U_old) for constructing H(U) and for the time derivative
    # Assuming U_field.old holds U at the previous time step (U^n)
    if isnothing(U_field.old)
        error("U_field.old (U at n) must be populated before calling solve_piso_step!")
    end
    U_n_data = U_field.old # This is U at time n

    # --- 1. Momentum Predictor Step --- 
    # Solve for U*: (A_U_diag) U* = H(U) - ∇p^n
    # where H(U) = A_U_offdiag U^n + Source_U + (Volume/Δt)U^n
    # and A_U = Diagonal + OffDiagonal comes from discretizing -∇⋅(UU) + ν∇²U

    # For simplicity, let's first define A_U_diag (implicit part of momentum equation)
    # A_U_diag_coeffs_scalar = (rho / Δt) .* ones(num_cells) # From rho * U/Δt
    # For nu*∇²U, if treated implicitly, its diagonal would add here.
    # For now, assume diffusion is explicit in H(U) or handled by a full matrix solve.
    # Let A_U_diag be rho * CellVolume / Δt for this simplified PISO variant.
    A_U_diag_scalar_per_cell = [rho * cell.volume / Δt for cell in mesh.cells]

    # Construct H(U) - all terms in momentum eq except pressure gradient and implicit unsteady
    # H(U)_component = - rho * fvc.div(phi_U_component, U_component) + fvc.laplacian(mu, U_component) + Source_terms
    # This H(U) should be cell-centered vector values.
    H_U_data = Vector{eltype(U_field.data)}(undef, num_cells) # Array of SVectors

    # Calculate H(U) - Placeholder for detailed calculation
    # This typically involves: 
    #   - Explicit convection term: -∇⋅(ρU^n U^n)
    #   - Explicit diffusion term: ∇⋅(μ∇U^n)
    #   - Source terms (gravity, etc.)
    #   - The U^n/Δt term from the other side of the implicit unsteady part
    # For now, a conceptual placeholder:
    for i in 1:num_cells
        # H_U_data[i] = compute_explicit_terms_for_cell_i(U_n_data[i], ..., rho, nu, mesh, i)
        # plus (rho * mesh.cells[i].volume / Δt) * U_n_data[i]
        H_U_data[i] = (rho * mesh.cells[i].volume / Δt) * U_n_data[i] # Simplistic: only old term
        # TODO: Add explicit convection, diffusion, sources to H_U_data
    end
    # Call H(U) computation function to add other explicit terms to H_U_data
    compute_H_U!(H_U_data, U_n_field, rho, nu, Δt, mesh, current_time=current_time)

    # Compute ∇p_n using fvc.grad. p_field is the pressure at time n (p_n).
    # TODO: The gradient scheme should ideally be passed as an argument or part of solver settings.
    grad_scheme_p_n = GaussGradient() # Defaulting to GaussGradient
    grad_p_n_field = fvc.grad(p_n_field, grad_scheme_p_n, current_time=current_time) # Use p_n_field for pressure at time n

    U_star_data = Vector{SVector{length(eltype(U_field.data)), eltype(eltype(U_field.data))}}(undef, num_cells)
    for i in 1:num_cells
        # Momentum predictor equation:
        # A_U_diag_scalar_per_cell[i] * U_star_data[i] = H_U_data[i] - grad_p_n_field.data[i] * mesh.cells[i].volume
        # where A_U_diag_scalar_per_cell[i] = rho * mesh.cells[i].volume / Δt
        # and H_U_data[i] contains terms like (rho*Vol/Δt)U_n_i, Vol*(∇⋅(μ∇U_n))_i etc., already multiplied by volume.
        
        vol_grad_p_term = grad_p_n_field.data[i] * mesh.cells[i].volume
        RHS_for_U_star = H_U_data[i] - vol_grad_p_term
        
        # Element-wise division for SVector as A_U_diag_scalar_per_cell is scalar
        U_star_data[i] = RHS_for_U_star / A_U_diag_scalar_per_cell[i]
    end

    # Calculate face fluxes from U_star (phi_star_flux_data)
    # These are volumetric fluxes: U_f* ⋅ S_f_vec
    phi_star_flux_data = zeros(Float64, num_faces)
    # TODO: Interpolate U_star_data to faces and compute dot product with face normal area vector
    temp_U_star_field = VectorField(:Ustar, mesh, U_star_data, U_field.boundary_conditions) # Use U_field's BCs as a guess for U*
    compute_face_volumetric_fluxes!(phi_star_flux_data, temp_U_star_field, mesh, current_time=current_time)

    # --- Pressure Correction Loop (n_correctors times) ---
    p_prime_data = zeros(Float64, num_cells) # Pressure correction field data
    p_current_data = deepcopy(p_field.data) # Pressure at start of correction loops

    for corrector_iter in 1:piso_settings.n_correctors
        # --- 2. Pressure Equation Construction ---
        # Solve: ∇⋅( (Vol / A_U_diag) ∇p' ) = ∇⋅U*  (or ∇⋅phi_star_flux_data for faces)
        
        # Coefficient for Laplacian of p': gamma_p_prime = CellVolume / A_U_diag_scalar_per_cell
        # This needs to be interpolated to faces if A_U_diag is cell-centered.
        # For now, assume A_U_diag_scalar_per_cell can be used to form a scalar gamma for fvm.laplacian
        # This is a simplification. gamma_p_prime should be a face field.
        # gamma_for_p_laplacian_data = [mesh.cells[i].volume / A_U_diag_scalar_per_cell[i] for i in 1:num_cells]
        # gamma_for_p_laplacian_field = ScalarField(:gamma_p, mesh, gamma_for_p_laplacian_data, Dict())
        # A_p_prime_matrix, b_p_prime_sources = fvm.laplacian(gamma_for_p_laplacian_field, p_prime_template_field)
        # For constant density, this is often ∇⋅( (Δt/ρ) ∇p' ) = ∇⋅U*
        # So gamma_p_prime = Δt/ρ (if A_U_diag was Vol*rho/Δt, then Vol/A_U_diag = Δt/rho)
        
        gamma_p_prime_const = Δt / rho # Constant for the whole domain

        # Create a temporary field for p' to pass to fvm.laplacian
        # Boundary conditions for p' are typically zero Neumann ( homogeneousNeumann )
        p_prime_bc = Dict{String, AbstractBoundaryCondition}()
        for patch_name in keys(mesh.boundaries)
            p_prime_bc[patch_name] = NeumannBC((x,y,z,t) -> 0.0)
        end
        p_prime_template_field = ScalarField(:p_prime, mesh, p_prime_data, p_prime_bc)

        A_p_prime_matrix, b_p_prime_explicit_sources = fvm.laplacian(gamma_p_prime_const, p_prime_template_field, current_time=current_time)
        
        # RHS for pressure correction equation: ∇⋅(phi_star_flux_data)
        # Need a ScalarField for phi_star_flux_data to pass to fvm.div_flux
        # This assumes phi_star_flux_data is already total flux per face.
        phi_star_face_field_temp = ScalarField(:phi_star_flux, mesh, phi_star_flux_data, Dict()) # Data is face values
        RHS_p_prime = fvm.div_flux(phi_star_face_field_temp, mesh) # Returns cell-centered divergence

        # Full source for pressure equation: RHS_p_prime - b_p_prime_explicit_sources (from non-homo Neumann BCs in laplacian)
        source_vector_p_prime = RHS_p_prime .- b_p_prime_explicit_sources

        # Solve A_p_prime_matrix * p_prime_data = source_vector_p_prime
        # Initialize p_prime_data if it's the first guess for the solver
        # Some solvers might use it as an initial guess. For now, assume it's zeroed or handled by solver.
        if size(A_p_prime_matrix,1) == num_cells && size(A_p_prime_matrix,2) == num_cells && length(source_vector_p_prime) == num_cells
            # Ensure p_prime_data is correctly sized for the solver if it's used as an initial guess / output vector
            if length(p_prime_data) != num_cells 
                p_prime_data = zeros(Float64, num_cells)
            end
            solver_result = solve!(pressure_linear_solver, A_p_prime_matrix, p_prime_data, source_vector_p_prime)
            # solve! typically updates p_prime_data in-place. If it returns a new vector:
            # p_prime_data = solver_result.x 
            # Assuming p_prime_data is updated in-place by solve! based on typical AbstractLinearSolver behavior
        else
            @warn "Pressure correction matrix or RHS has unexpected dimensions, skipping solve."
            fill!(p_prime_data, 0.0)
        end
        
        # Update pressure: p_new = p_old_at_iter_start + p_prime_accumulated
        # Or p_field.data .+= p_prime_data (if p' is correction to current p)
        # Standard PISO: p_new = p_previous_iter_p + p_prime
        p_field.data = p_current_data .+ p_prime_data # p_current_data is p from before this corrector loop
                                                   # If multiple correctors, p_current_data should be p from previous corrector.
                                                   # Let's adjust: p_field.data is updated iteratively.
        if corrector_iter == 1
            p_field.data = p_current_data .+ p_prime_data # p_current_data is p^n
        else
            # p_field.data was p from previous corrector, add current p_prime
            p_field.data .+= p_prime_data 
        end

        # --- 3. Velocity Corrector --- 
        # U** = U* - (Vol / A_U_diag) ∇p'  = U* - (Δt/ρ) ∇p'
        p_prime_field_for_grad = ScalarField(:p_prime_grad, mesh, p_prime_data, p_prime_bc)
        grad_p_prime = fvc.grad(p_prime_field_for_grad, GaussGradient(), current_time=current_time)
        
        # Store corrected velocity in U_field.data
        for i in 1:num_cells
            # U_field.data[i] = U_star_data[i] - (mesh.cells[i].volume / A_U_diag_scalar_per_cell[i]) .* grad_p_prime.data[i]
            U_field.data[i] = U_star_data[i] - (Δt/rho) .* grad_p_prime.data[i]
        end
        
        # Update face fluxes (phi_double_star) using corrected U_field.data for the next iteration or for output
        # phi_face_flux.data = calculate_face_fluxes(U_field, mesh) # Placeholder
        # TODO: Re-calculate phi_star_flux_data using the new U_field.data if it's not the last corrector
        if corrector_iter < piso_settings.n_correctors
            # U_field.data now holds U** (or U* for the next iteration)
            compute_face_volumetric_fluxes!(phi_star_flux_data, U_field, mesh, current_time=current_time)
        end
    end # End PISO corrector loop

    # Final face flux calculation for output (e.g. phi_face_flux.data)
    compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh, current_time=current_time)

end

# Computes explicit contributions (convection, diffusion, sources) to H(U)
# and adds them to H_U_data.
# H_U_data is pre-initialized with (rho * Vol / Δt) * U_n_data.
function compute_H_U!(
    H_U_data::Vector{<:SVector},  # Output: Modified with explicit terms
    U_n_field::VectorField,       # Velocity at time n (U^n)
    rho::Float64, nu::Float64, Δt::Float64, mesh::AbstractMesh;
    current_time::Float64 = 0.0 # For time-dependent BCs in fvc operators
    # TODO: Pass appropriate fvc schemes for convection and diffusion
)
    num_cells = length(mesh.cells)
    mu = nu * rho
    num_dims = length(eltype(U_n_field.data)) # e.g., 2 for SVector{2,T}, 3 for SVector{3,T}

    # --- Helper function (conceptual) for extracting scalar component from VectorField ---
    # function extract_scalar_component(vf::VectorField, component_idx::Int)
    #     s_data = [v[component_idx] for v in vf.data]
    #     # Boundary conditions for scalar component might need careful handling based on vector BCs
    #     # For now, assume they can be naively copied or are handled by fvc operators.
    #     return ScalarField(Symbol(string(vf.name)*"_comp$(component_idx)"), vf.mesh, s_data, vf.boundary_conditions)
    # end

    # --- Diffusion Term: + Vol * ∇⋅(μ∇U^n) ---
    # This term is added to H_U_data.
    # H_U_data_new[i] = H_U_data_old[i] + mesh.cells[i].volume * (∇⋅(μ∇U^n))_i
    # where (∇⋅(μ∇U^n))_i is a vector, computed component-wise.

    # For each component k = 1 to num_dims:
    #   1. U_k_field = extract_scalar_component(U_n_field, k)
    #   2. laplacian_U_k_field = Numerics.fvc.laplacian(mu, U_k_field, SomeLaplacianScheme())
    #      (This fvc.laplacian must return a ScalarField of cell-centered ∇⋅(μ∇U_k) values)
    #   3. For i in 1:num_cells:
    #        H_U_data[i] = setindex(H_U_data[i], 
    #                               H_U_data[i][k] + laplacian_U_k_field.data[i] * mesh.cells[i].volume, 
    #                               k) # Adds to the k-th component of the SVector
    # Actual implementation for Diffusion Term:
    for k_comp in 1:num_dims
        # 1. Extract scalar component field U_k from U_n_field
        U_k_field = Core.extract_scalar_component(U_n_field, k_comp, component_name_suffix="_n")

        # 2. Compute ∇⋅(μ∇U_k) using fvc.laplacian
        # fvc.laplacian returns cell-centered values of ∇⋅(γ∇φ)
        laplacian_U_k_scalar_field = fvc.laplacian(mu, U_k_field, DefaultFvcLaplacian(), current_time=current_time)
        
        # 3. Add to H_U_data (H_U_data already contains (rho*Vol/Δt)U_n)
        # H_U_data_new[i][k_comp] = H_U_data_old[i][k_comp] + (∇⋅(μ∇U_k))_i * Vol_i
        for i_cell in 1:num_cells
            current_vec_val = H_U_data[i_cell]
            laplacian_term_val = laplacian_U_k_scalar_field.data[i_cell] * mesh.cells[i_cell].volume
            new_component_val = current_vec_val[k_comp] + laplacian_term_val
            H_U_data[i_cell] = setindex(current_vec_val, new_component_val, k_comp)
        end
    end
    # println("compute_H_U!: Diffusion term processed.") # For debugging

    # --- Convection Term: - Vol * ρ ∇⋅(U^n U^n) ---
    # This term is subtracted from H_U_data.
    # H_U_data_new[i] = H_U_data_old[i] - mesh.cells[i].volume * (ρ ∇⋅(U^n U^n))_i
    # where (ρ ∇⋅(U^n U^n))_i is a vector.

    # This typically involves:
    #   1. Numerics.fvc.convection_term(rho, U_n_field, SomeConvectionScheme())
    #      (This fvc.convection_term should return a VectorField of cell-centered ρ∇⋅(UU) values)
    #   2. Let conv_vector_field = result of above.
    #   3. For i in 1:num_cells:
    #        H_U_data[i] = H_U_data[i] - conv_vector_field.data[i] * mesh.cells[i].volume
    # Actual implementation for Convection Term (using placeholder fvc.convection_term):
    # 1. Define the convection scheme to use.
    #    For now, using CentralDifferencing as a placeholder interpolation within StandardConvectionScheme.
    conv_scheme = StandardConvectionScheme(CentralDifferencing())

    # 2. Call fvc.convection_term
    #    This is expected to return a VectorField of cell-centered ρ∇⋅(U^nU^n) values.
    conv_vector_field = fvc.convection_term(rho, U_n_field, conv_scheme, current_time=current_time)

    # 3. Subtract from H_U_data
    #    H_U_data_new[i] = H_U_data_old[i] - (ρ∇⋅(U^nU^n))_i * Vol_i
    for i_cell in 1:num_cells
        H_U_data[i_cell] = H_U_data[i_cell] - conv_vector_field.data[i_cell] * mesh.cells[i_cell].volume
    end
    # println("compute_H_U!: Convection term (placeholder) processed.") # For debugging

    # --- Other Source Terms (e.g., body forces) S_U ---
    # If there are other explicit source terms S_U (vector per cell):
    # H_U_data_new[i] = H_U_data_old[i] + S_U[i] * mesh.cells[i].volume
    # println("Placeholder: compute_H_U! - Add other source terms if any.")

    # Final H_U_data should be: (rho*Vol/Δt)U^n + Vol*(∇⋅(μ∇U^n)) - Vol*(ρ∇⋅(U^nU^n)) + Vol*S_U
    # println("compute_H_U! executed with placeholders for fvc calls.")
end

# Computes volumetric face fluxes (U_f ⋅ S_f)
function compute_face_volumetric_fluxes!(
    face_flux_data::Vector{Float64},  # Output: volumetric flux U_f ⋅ S_f for each face
    U_cell_field::VectorField,        # Input: cell-centered velocity field
    mesh::AbstractMesh;
    current_time::Float64 = 0.0,      # Input: current simulation time for time-dependent BCs
    interpolation_scheme::AbstractInterpolationScheme = CentralDifferencing() # Default scheme
)
    num_faces = length(mesh.faces)

    if !(eltype(U_cell_field.data) <: SVector)
        error("U_cell_field.data must contain SVector elements for compute_face_volumetric_fluxes!")
    end

    # 1. Interpolate cell-centered velocity to face centers
    # fvc.interpolate_vector_to_faces returns a Vector of SVectors, one for each face.
    U_f_vectors = fvc.interpolate_vector_to_faces(U_cell_field, interpolation_scheme, current_time=current_time)

    if length(U_f_vectors) != num_faces
        error("Interpolated face velocities vector length ($(length(U_f_vectors))) does not match number of faces ($(num_faces)).")
    end

    # 2. Compute dot product of U_f with face normal area vector S_f
    for f_idx in 1:num_faces
        face = mesh.faces[f_idx]
        U_at_face = U_f_vectors[f_idx]
        
        surface_vector = face.normal * face.area
        face_flux_data[f_idx] = dot(U_at_face, surface_vector)
    end
    # println("compute_face_volumetric_fluxes! executed using fvc.interpolate_vector_to_faces.") # For debugging
end

"""
    solve_simple!(
        U_field::VectorField,         # Velocity field (input/output)
        p_field::ScalarField,         # Pressure field (input/output)
        phi_face_flux::ScalarField,   # Face mass flux (U.data ⋅ S_f) (output)
        rho::Float64,                 # Density
        nu::Float64,                  # Kinematic viscosity
        mesh::AbstractMesh,
        velocity_linear_solver,       # Linear solver for velocity equations
        pressure_linear_solver,       # Linear solver for pressure equation
        simple_settings::SIMPLESettings = SIMPLESettings()
    )

Performs steady-state solution using the SIMPLE algorithm for incompressible flow.
SIMPLE (Semi-Implicit Method for Pressure-Linked Equations) is used for steady-state
problems and incorporates under-relaxation to improve convergence stability.

The algorithm handles:
- Momentum prediction with relaxation
- Pressure correction with Rhie-Chow interpolation
- Velocity correction
- Flux correction
- Convergence monitoring
"""
function solve_simple!(
    U_field::VectorField,
    p_field::ScalarField,
    phi_face_flux::ScalarField,
    rho::Float64,
    nu::Float64,
    mesh::AbstractMesh,
    velocity_linear_solver,
    pressure_linear_solver,
    simple_settings::SIMPLESettings = SIMPLESettings()
)
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)
    mu = rho * nu
    
    # Initialize fields and storage
    # For U and p we need to store the values from previous iteration
    U_prev_iter = deepcopy(U_field.data)
    p_prev_iter = deepcopy(p_field.data)
    
    # Initialize residuals history
    continuity_residuals = Float64[]
    momentum_x_residuals = Float64[]
    momentum_y_residuals = Float64[]
    momentum_z_residuals = length(eltype(U_field.data)) == 3 ? Float64[] : nothing
    
    # Initialize diagonal coefficient arrays for momentum equations
    # This will be used for Rhie-Chow interpolation in pressure equation
    A_U_diag = Vector{eltype(rho)}(undef, num_cells)
    
    # Setup face interpolation weights for Rhie-Chow
    # These will be used to interpolate cell-centered pressure gradients and diagonal coefficients
    face_weights = Vector{Float64}(undef, num_faces)
    
    # Pre-compute face interpolation weights
    for face_idx in 1:num_faces
        face = mesh.faces[face_idx]
        if !face.boundary
            # Internal face: get owner and neighbor cells
            owner_cell_idx = face.owner
            neighbor_cell_idx = face.neighbor
            
            # Vector from owner to neighbor
            d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
            dist_centers = norm(d_vec)
            
            # Linear interpolation weight based on distance
            # weight_f = distance_owner_to_face / distance_owner_to_neighbor
            face_center = face.center
            dist_owner_to_face = norm(face_center - mesh.cells[owner_cell_idx].center)
            
            # Linear interpolation weight - what portion is from neighbor
            face_weights[face_idx] = dist_owner_to_face / dist_centers
        else
            # Boundary face: weight is 1.0 (100% owner cell)
            face_weights[face_idx] = 0.0
        end
    end
    
    # Initialize residual monitors
    initial_residual = -1.0  # Will be set on first iteration
    current_residual = 1.0   # Start with high residual
    
    # Begin SIMPLE iterations
    for iter in 1:simple_settings.max_iterations
        # 1. Store current fields for reference and relaxation
        U_prev_iter = deepcopy(U_field.data)
        p_prev_iter = deepcopy(p_field.data)
        
        # 2. Momentum Predictor Step
        # Solve discretized momentum equation to get U*
        # The fvm form is: A_U * U = H(U) - grad(p)
        # Where A_U has diagonal and off-diagonal parts: A_U_diag and A_U_offdiag
        
        # Loop through dimensions and solve momentum equation component-wise
        dims = length(eltype(U_field.data)) # 2 or 3
        momentum_residuals = zeros(dims)
        
        for d in 1:dims
            # Extract component of velocity for this dimension
            U_component = ScalarField(
                Symbol("U$d"),
                mesh,
                [U_field.data[i][d] for i in 1:num_cells],
                # Use velocity field BCs for this component
                Dict(patch => U_field.boundary_conditions[patch] for patch in keys(U_field.boundary_conditions))
            )
            
            # Create momentum matrix for this component
            # 1. Add convection term: fvm.div(phi, U_component)
            # We use face fluxes (phi) from previous iteration
            A_conv, b_conv = fvm.div(phi_face_flux, U_component) 
            
            # 2. Add diffusion term: fvm.laplacian(nu, U_component)
            A_diff, b_diff = fvm.laplacian(mu, U_component)
            
            # 3. Combine convection and diffusion
            A_U_component = A_conv + A_diff
            b_U_component = b_conv + b_diff
            
            # 4. Compute pressure gradient source term for this component
            # Use stored pressure from last iteration
            grad_p = fvc.grad(p_field)  # Cell-centered pressure gradient
            
            # Add pressure gradient as explicit source term
            # Negative sign because it moves to RHS: -∇p
            for i in 1:num_cells
                b_U_component[i] -= mesh.cells[i].volume * grad_p.data[i][d]
            end
            
            # 5. Store diagonal for Rhie-Chow correction later
            # Save the diagonal coefficient for this component
            # All components have same diagonal for incompressible flow
            if d == 1
                for i in 1:num_cells
                    A_U_diag[i] = A_U_component[i, i]
                end
            end
            
            # 6. Apply under-relaxation
            # U = α_u * U_new + (1-α_u) * U_old
            # This is implemented as: A_U_diag' = A_U_diag/α_u
            # and RHS' = RHS + (A_U_diag/α_u - A_U_diag)*U_old
            for i in 1:num_cells
                # Store original diagonal
                A_diag_original = A_U_component[i, i]
                
                # Modify diagonal for relaxation
                A_U_component[i, i] = A_diag_original / simple_settings.alpha_u
                
                # Add relaxation term to RHS
                # (1-α)/α * A_diag * U_old
                relaxation_factor = (1.0 - simple_settings.alpha_u) / simple_settings.alpha_u
                b_U_component[i] += relaxation_factor * A_diag_original * U_component.data[i]
            end
            
            # 7. Solve linear system for this velocity component
            # A_U_component * U_component_new = b_U_component
            U_component_new = copy(U_component.data)  # Initial guess for linear solver
            solver_result = solve!(velocity_linear_solver, A_U_component, U_component_new, b_U_component)
            
            # Store residual for this component
            momentum_residuals[d] = solver_result.residual
            
            # 8. Update velocity field with solved component
            for i in 1:num_cells
                # Create new velocity vector with updated component
                new_vec = MVector{dims, Float64}(U_field.data[i])
                new_vec[d] = U_component_new[i]
                U_field.data[i] = SVector{dims, Float64}(new_vec)
            end
        end
        
        # Store momentum residuals
        push!(momentum_x_residuals, momentum_residuals[1])
        push!(momentum_y_residuals, momentum_residuals[2])
        if dims == 3 && !isnothing(momentum_z_residuals)
            push!(momentum_z_residuals, momentum_residuals[3])
        end
        
        # 3. Calculate face fluxes from predicted velocity field (without Rhie-Chow correction yet)
        # Get cell-centered velocity field from momentum predictor
        compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
        
        # 4. Pressure Correction Step
        # Now we solve for pressure correction (p') to enforce continuity
        
        # 4.1 Setup the pressure correction equation
        # The pressure correction equation is: ∇⋅( (1/a_p) ∇p' ) = ∇⋅(U*)
        # where a_p is the diagonal coefficient of momentum equation
        
        # Create temporary field for storing pressure correction
        p_prime_data = zeros(Float64, num_cells)
        
        # Need homogeneous Neumann BC's for pressure correction
        p_prime_bc = Dict{String, AbstractBoundaryCondition}()
        for patch_name in keys(mesh.boundaries)
            p_prime_bc[patch_name] = NeumannBC((x,y,z,t) -> 0.0)
        end
        p_prime_field = ScalarField(:p_prime, mesh, p_prime_data, p_prime_bc)
        
        # Calculate diffusion coefficient for pressure equation: (1/a_p)
        # We need to express this as a face field for the fvm.laplacian operator
        # First approximate as cell field (inverse of momentum diagonal)
        gamma_p_prime_cell = 1.0 ./ A_U_diag
        
        # 4.2. Construct the pressure correction equation with Rhie-Chow interpolation
        # The key part of Rhie-Chow is to calculate face fluxes that prevent checker-boarding
        # Interpolated cell pressure gradients to faces
        grad_p = fvc.grad(p_field)  # Cell-centered pressure gradient
        
        # Create Rhie-Chow corrected flux field
        phi_star_rhiechow = copy(phi_face_flux.data)
        
        # Apply Rhie-Chow correction for internal faces
        for face_idx in 1:num_faces
            face = mesh.faces[face_idx]
            
            if !face.boundary
                # Get owner and neighbor cells
                owner_cell_idx = face.owner
                neighbor_cell_idx = face.neighbor
                
                # Get interpolation weight for this face
                weight = face_weights[face_idx]
                
                # Face normal vector
                S_f = face.normal * face.area  # Area vector (magnitude * direction)
                
                # 1. Interpolate 1/a_p to face
                gamma_f = (1.0 - weight) * gamma_p_prime_cell[owner_cell_idx] + 
                          weight * gamma_p_prime_cell[neighbor_cell_idx]
                
                # 2. Interpolate pressure gradient to face
                grad_p_f = (1.0 - weight) * grad_p.data[owner_cell_idx] + 
                           weight * grad_p.data[neighbor_cell_idx]
                
                # 3. Calculate Rhie-Chow correction to face flux
                # Remove the interpolated pressure gradient component and add explicitly calculated one
                # phi_star_rhiechow = phi_star - gamma_f * (grad_p_interp - grad_p_explicit) ⋅ S_f
                
                # Direct pressure difference along cell-centers line
                d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
                dist_centers = norm(d_vec)
                unit_d = d_vec / dist_centers
                
                # Explicit face normal gradient based on pressure difference
                dp_dn_explicit = (p_field.data[neighbor_cell_idx] - p_field.data[owner_cell_idx]) / dist_centers
                
                # Project the interpolated pressure gradient onto face normal
                dp_dn_interp = dot(grad_p_f, unit_d)
                
                # Apply the Rhie-Chow correction to face flux
                # Add the difference between explicit and interpolated pressure gradient
                rhiechow_correction = gamma_f * (dp_dn_explicit - dp_dn_interp) * face.area
                phi_star_rhiechow[face_idx] += rhiechow_correction
            end
        end
        
        # 4.3 Setup and solve the pressure correction equation
        # This uses the Rhie-Chow corrected fluxes for the right-hand side
        
        # Construct the coefficient matrix for pressure correction
        A_p_prime, b_p_prime = fvm.laplacian(1.0, p_prime_field)
        
        # Modify the matrix diagonal coefficients using the cell 1/a_p values
        # This essentially applies the diffusivity field (1/a_p) to the Laplacian
        for i in 1:num_cells
            for j in 1:num_cells
                if i == j
                    # Scale diagonal by harmonic mean of gamma_p_prime
                    # No need to modify here as we're applying gamma in Rhie-Chow and the final correction
                else
                    # Scale off-diagonal by harmonic mean
                    # No need to modify for the same reason
                end
            end
        end
        
        # Calculate the RHS: divergence of Rhie-Chow corrected flux field
        phi_star_rhiechow_field = ScalarField(:phi_star_rhiechow, mesh, phi_star_rhiechow, Dict())
        div_phi_star = fvc.div(phi_star_rhiechow_field)
        
        # RHS of pressure correction equation is negative divergence
        # Because we want p' such that div(U_new) = 0
        # And div(U*) + div(U') = 0, so div(U') = -div(U*)
        for i in 1:num_cells
            b_p_prime[i] = -div_phi_star.data[i] * mesh.cells[i].volume
        end
        
        # 4.4 Solve pressure correction equation
        solver_result = solve!(pressure_linear_solver, A_p_prime, p_prime_data, b_p_prime)
        
        # Store continuity residual
        continuity_residual = solver_result.residual
        push!(continuity_residuals, continuity_residual)
        
        # 4.5 Apply pressure under-relaxation
        # p = p_old + alpha_p * p'
        for i in 1:num_cells
            p_field.data[i] = p_prev_iter[i] + simple_settings.alpha_p * p_prime_data[i]
        end
        
        # 5. Velocity Correction
        # Now correct velocity field based on pressure correction
        # U_new = U* - (1/a_p) * ∇p'
        
        # Calculate gradient of pressure correction
        grad_p_prime = fvc.grad(p_prime_field)
        
        # Apply velocity correction using momentum equation diagonal and pressure correction gradient
        for i in 1:num_cells
            # Extract diagonal coefficient for this cell
            a_p = A_U_diag[i]
            
            # Calculate velocity correction: U' = -(1/a_p) * ∇p'
            U_correction = -gamma_p_prime_cell[i] * grad_p_prime.data[i]
            
            # Update velocity: U_new = U* + U'
            # Note: No additional under-relaxation needed here since p' is already relaxed
            U_field.data[i] += U_correction
        end
        
        # 6. Update face fluxes
        # Calculate face fluxes from corrected velocity
        compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
        
        # 7. Apply Rhie-Chow correction to face fluxes to ensure mass conservation
        # The corrected flux is: phi = phi_interpolated + phi_rhie_chow
        for face_idx in 1:num_faces
            face = mesh.faces[face_idx]
            
            if !face.boundary
                # Get owner and neighbor cells
                owner_cell_idx = face.owner
                neighbor_cell_idx = face.neighbor
                
                # Interpolation weight for this face
                weight = face_weights[face_idx]
                
                # Interpolate 1/a_p to face
                gamma_f = (1.0 - weight) * gamma_p_prime_cell[owner_cell_idx] + 
                          weight * gamma_p_prime_cell[neighbor_cell_idx]
                
                # Direct pressure correction gradient along cell-centers line
                d_vec = mesh.cells[neighbor_cell_idx].center - mesh.cells[owner_cell_idx].center
                dist_centers = norm(d_vec)
                dp_prime_dn = (p_prime_data[neighbor_cell_idx] - p_prime_data[owner_cell_idx]) / dist_centers
                
                # Rhie-Chow correction to flux based on pressure correction
                rhiechow_correction = -gamma_f * dp_prime_dn * face.area
                
                # Apply Rhie-Chow correction to face flux
                phi_face_flux.data[face_idx] += rhiechow_correction
            end
        end
        
        # 8. Check for convergence
        # Track primary residual (continuity equation residual)
        if iter == 1
            # Initialize initial residual
            initial_residual = continuity_residual
        end
        
        # Calculate normalized residual
        # We use the maximum of initial residual and a small value to avoid division by zero
        normalized_residual = continuity_residual / max(initial_residual, 1e-10)
        current_residual = normalized_residual
        
        # Print residuals at specified frequency
        if iter % simple_settings.residual_output_frequency == 0 || iter == 1
            @printf("SIMPLE Iter %4d: Continuity = %.4e, Ux = %.4e, Uy = %.4e", 
                    iter, normalized_residual, momentum_residuals[1], momentum_residuals[2])
            if dims == 3 && !isnothing(momentum_z_residuals)
                @printf(", Uz = %.4e", momentum_residuals[3])
            end
            println()
        end
        
        # Check convergence
        if normalized_residual < simple_settings.tolerance
            @info "SIMPLE algorithm converged after $iter iterations."
            
            # Update settings to indicate convergence
            simple_settings.converged = true
            
            # Final calculation of face fluxes
            compute_face_volumetric_fluxes!(phi_face_flux.data, U_field, mesh)
            
            # Exit the loop
            break
        end
        
        # Check max iterations
        if iter == simple_settings.max_iterations
            @warn "SIMPLE algorithm did not converge after $(simple_settings.max_iterations) iterations."
            @warn "Final residual: $normalized_residual (target: $(simple_settings.tolerance))"
        end
    end # End of SIMPLE iterations loop
    
    # Return results in a structured format
    result = Dict(
        "iterations" => length(continuity_residuals),
        "converged" => simple_settings.converged,
        "residuals" => Dict(
            "continuity" => continuity_residuals,
            "momentum_x" => momentum_x_residuals,
            "momentum_y" => momentum_y_residuals
        ),
        "final_residual" => current_residual
    )
    
    # Add z-momentum residuals if 3D
    if !isnothing(momentum_z_residuals)
        result["residuals"]["momentum_z"] = momentum_z_residuals
    end
    
    return result
end # End solver function

# Export the main solver functions
export solve_piso_step!, solve_simple!, PISOSettings, SIMPLESettings

end # End module IncompressibleSolvers
