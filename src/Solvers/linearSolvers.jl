# src/Solvers/LinearSolvers.jl
module LinearSolvers

export AbstractLinearSolver, AbstractPreconditioner
export PCG, BiCGSTAB, GMRES, AMG, MatrixFreeAMG, ParallelAMG, MatrixFreeOperator, apply!
export CGS, BiCG, TFQMR  # New high-performance Krylov solvers
export JacobiPreconditioner, ILUPreconditioner, AMGPreconditioner
export CUDASolver, GPUPCG  # GPU solvers
export solve!, solve

using LinearAlgebra
using SparseArrays
using Base.Threads
using Printf

# Optional dependencies
const HAS_MPI = try
    using MPI
    true
catch
    @warn "MPI not available in LinearSolvers, parallel solvers disabled"
    false
end

const HAS_CUDA = try
    using CUDA
    true
catch
    @warn "CUDA not available in LinearSolvers, GPU solvers disabled"
    false
end

const HAS_LOOPVECTORIZATION = try
    using LoopVectorization
    true
catch
    @warn "LoopVectorization not available, using standard loops"
    false
end

# Conditional turbo macro
if HAS_LOOPVECTORIZATION
    macro fast_loop(expr)
        return esc(:(@turbo $expr))
    end
else
    macro fast_loop(expr)
        return esc(expr)
    end
end

# Abstract types
abstract type AbstractLinearSolver end
abstract type AbstractPreconditioner end
abstract type AbstractAMGSolver <: AbstractLinearSolver end

# Solver result type
struct SolverResult{T}
    x::Vector{T}
    converged::Bool
    iterations::Int
    residual::T
    residual_history::Vector{T}
end

# ============================================================================
# Preconditioners
# ============================================================================

struct JacobiPreconditioner{T} <: AbstractPreconditioner
    diag::Vector{T}
end

function JacobiPreconditioner(A::SparseMatrixCSC)
    JacobiPreconditioner( begin
        d = diag(A)
        inv_d = similar(d)
        for i in eachindex(d)
            inv_d[i] = (abs(d[i]) < sqrt(eps(eltype(d))) || !isfinite(d[i])) ? 1.0 : 1.0 / d[i]
        end
        Vector(inv_d)
    end)
end

function apply!(y::Vector, P::JacobiPreconditioner, x::Vector)
    @fast_loop for i in eachindex(y)
        y[i] = P.diag[i] * x[i]
    end
end

function apply!(y::Vector{T}, P::JacobiPreconditioner{T}, x::Vector{T}) where T
    y .= P.diag .* x
end

struct ILUPreconditioner{T} <: AbstractPreconditioner
    L::SparseMatrixCSC{T,Int}
    U::SparseMatrixCSC{T,Int}
end

function ILUPreconditioner(A_orig::SparseMatrixCSC{T}; droptol=0.0) where T # droptol is not used in ILU(0)
    A_factorized = copy(A_orig)
    n = size(A_factorized, 1)

    for k = 1:n # Pivot column/row index
        akk = A_factorized[k,k]
        if abs(akk) < sqrt(eps(real(T))) # Using real(T) for complex numbers, T for real
            error("Zero or too small pivot encountered at ($k, $k) in ILU(0) factorization. Value: $akk")
        end

        # Compute L_ik factors (A_factorized[i,k]) for i > k
        for i = (k+1):n 
            if A_factorized[i,k] != 0 # If element A_ik exists in the original sparsity pattern
                A_factorized[i,k] /= akk # L_ik = A_ik / A_kk, stored in place
            end
        end

        # Update trailing submatrix A_ij = A_ij - L_ik * U_kj
        # L_ik is A_factorized[i,k] (already computed and stored in place)
        # U_kj is A_factorized[k,j] (original value in A_factorized, as k-th row is not modified by L factor computation for j>k)
        for i = (k+1):n # Row index for L_ik
            lik = A_factorized[i,k] # This is L_ik
            if lik != 0 # If L_ik is non-zero (i.e., A_factorized[i,k] was in original pattern and updated)
                for j = (k+1):n # Column index for U_kj and for A_ij to be updated
                    ukj = A_factorized[k,j] # This is U_kj
                    # For ILU(0), update A_factorized[i,j] only if it's in the original sparsity pattern.
                    if ukj != 0 && A_factorized[i,j] != 0
                        A_factorized[i,j] -= lik * ukj
                    end
                end
            end
        end
    end

    # Extract L and U matrices from the modified A_factorized matrix
    # L is unit lower triangular (diagonal is 1s, strict lower part from A_factorized)
    L = tril(A_factorized, -1) + SparseMatrixCSC{T,Int}(I, n, n)
    # U is upper triangular (including the modified diagonal from A_factorized)
    U = triu(A_factorized)
    
    return ILUPreconditioner(L, U)
end

function apply!(y::Vector{T}, P::ILUPreconditioner{T}, x::Vector{T}) where T
    # Solve Lz = x (forward substitution)
    # Solve Uy = z (backward substitution)
    # For sparse L (unit lower) and U (upper):
    # L is unit lower triangular part of LU factor (tril(LU, -1) + I)
    # U is upper triangular part of LU factor (triu(LU))
    # y .= P.U \ (P.L \ x) # This is for dense L, U. For sparse, need to use ldiv!
    
    # Assuming L is unit lower triangular and U is upper triangular
    # Lz = x -> z = L \ x
    ldiv!(y, UnitLowerTriangular(P.L), x) # z stored in y
    # Uy = z -> y = U \ z
    ldiv!(y, UpperTriangular(P.U), y) # final y
end

function apply!(y::Vector, P::ILUPreconditioner, x::Vector)
    # Solve L*U*y = x
    z = similar(x)
    
    # Forward substitution: L*z = x
    ldiv!(z, LowerTriangular(P.L), x)
    
    # Backward substitution: U*y = z
    ldiv!(y, UpperTriangular(P.U), z)
end

# ============================================================================
# Standard Iterative Solvers
# ============================================================================

struct PCG <: AbstractLinearSolver
    tol::Float64
    maxiter::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

PCG(; tol=1e-6, maxiter=1000, preconditioner=nothing, verbose=false) = 
    PCG(tol, maxiter, preconditioner, verbose)

# Two-argument version
function solve(solver::PCG, A::SparseMatrixCSC{T}, b::Vector{T}) where T
    return solve(solver, A, b, nothing)
end

function solve(solver::PCG, A::SparseMatrixCSC{T}, b::Vector{T}, x0::Union{Nothing,Vector{T}}) where T
    n = length(b)
    x = x0 === nothing ? zeros(T, n) : copy(x0)
    
    r = b - A * x
    residual_history = T[]
    
    if solver.preconditioner !== nothing
        z = similar(r)
        apply!(z, solver.preconditioner, r)
        p = copy(z)
        rz_old = dot(r, z)
    else
        p = copy(r)
        rz_old = dot(r, r)
    end
    
    push!(residual_history, sqrt(abs(rz_old)))
    
    for iter = 1:solver.maxiter
        Ap = A * p
        α = rz_old / dot(p, Ap)
        
        @fast_loop for i in eachindex(x)
            x[i] += α * p[i]
            r[i] -= α * Ap[i]
        end
        
        if solver.preconditioner !== nothing
            apply!(z, solver.preconditioner, r)
            rz_new = dot(r, z)
        else
            rz_new = dot(r, r)
        end
        
        res_norm = sqrt(abs(rz_new))
        push!(residual_history, res_norm)
        
        if solver.verbose && iter % 10 == 0
            println("PCG iteration $iter: residual = $res_norm")
        end
        
        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
        
        β = rz_new / rz_old
        
        if solver.preconditioner !== nothing
            @fast_loop for i in eachindex(p)
                p[i] = z[i] + β * p[i]
            end
        else
            @fast_loop for i in eachindex(p)
                p[i] = r[i] + β * p[i]
            end
        end
        
        rz_old = rz_new
    end
    
    return SolverResult(x, false, solver.maxiter, sqrt(abs(rz_old)), residual_history)
end

struct BiCGSTAB <: AbstractLinearSolver
    tol::Float64
    maxiter::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

BiCGSTAB(; tol=1e-6, maxiter=1000, preconditioner=nothing, verbose=false) = 
    BiCGSTAB(tol, maxiter, preconditioner, verbose)

# Two-argument version  
function solve(solver::BiCGSTAB, A::SparseMatrixCSC{T}, b::Vector{T}) where T
    return solve(solver, A, b, nothing)
end

function solve(solver::BiCGSTAB, A::SparseMatrixCSC{T}, b::Vector{T}, x0::Union{Nothing,Vector{T}}) where T
    n = length(b)
    x = x0 === nothing ? zeros(T, n) : copy(x0)
    
    r = b - A * x
    r0 = copy(r)
    
    ρ_old = α = ω = one(T)
    v = zeros(T, n)
    p = zeros(T, n)
    
    residual_history = T[]
    push!(residual_history, norm(r))
    
    for iter = 1:solver.maxiter
        ρ = dot(r0, r)
        
        if abs(ρ) < eps(T)
            return SolverResult(x, false, iter, norm(r), residual_history)
        end
        
        β = (ρ / ρ_old) * (α / ω)
        
        @fast_loop for i in eachindex(p)
            p[i] = r[i] + β * (p[i] - ω * v[i])
        end
        
        # Apply preconditioner if available
        if solver.preconditioner !== nothing
            p_hat = similar(p)
            apply!(p_hat, solver.preconditioner, p)
            v = A * p_hat
        else
            v = A * p
        end
        
        α = ρ / dot(r0, v)
        
        s = similar(r)
        @fast_loop for i in eachindex(s)
            s[i] = r[i] - α * v[i]
        end
        
        # Check for early convergence
        s_norm = norm(s)
        if s_norm < solver.tol
            @fast_loop for i in eachindex(x)
                x[i] += α * p[i]
            end
            return SolverResult(x, true, iter, s_norm, residual_history)
        end
        
        # Apply preconditioner if available
        if solver.preconditioner !== nothing
            s_hat = similar(s)
            apply!(s_hat, solver.preconditioner, s)
            t = A * s_hat
        else
            t = A * s
        end
        
        ω = dot(t, s) / dot(t, t)
        
        @fast_loop for i in eachindex(x)
            x[i] += α * p[i] + ω * s[i]
            r[i] = s[i] - ω * t[i]
        end
        
        res_norm = norm(r)
        push!(residual_history, res_norm)
        
        if solver.verbose && iter % 10 == 0
            println("BiCGSTAB iteration $iter: residual = $res_norm")
        end
        
        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
        
        ρ_old = ρ
    end
    
    return SolverResult(x, false, solver.maxiter, norm(r), residual_history)
end

struct GMRES <: AbstractLinearSolver
    tol::Float64
    maxiter::Int
    restart::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

GMRES(; tol=1e-6, maxiter=1000, restart=30, preconditioner=nothing, verbose=false) = 
    GMRES(tol, maxiter, restart, preconditioner, verbose)

# Two-argument version
function solve(solver::GMRES, A::SparseMatrixCSC{T}, b::Vector{T}) where T
    return solve(solver, A, b, nothing)
end

function solve(solver::GMRES, A::SparseMatrixCSC{T}, b::Vector{T}, x0::Union{Nothing,Vector{T}}) where T
    n = length(b)
    x = x0 === nothing ? zeros(T, n) : copy(x0)
    
    residual_history = T[]
    
    for outer = 1:div(solver.maxiter, solver.restart)
        r = b - A * x
        β = norm(r)
        push!(residual_history, β)
        
        if β < solver.tol
            return SolverResult(x, true, (outer-1)*solver.restart, β, residual_history)
        end
        
        V = zeros(T, n, solver.restart + 1)
        H = zeros(T, solver.restart + 1, solver.restart)
        cs = zeros(T, solver.restart) # Stores cosine of Givens rotations
        sn = zeros(T, solver.restart) # Stores sine of Givens rotations
        
        V[:, 1] = r / β
        s = zeros(T, solver.restart + 1) # Stores the RHS of the least squares system, transformed by Givens
        s[1] = β
        
        for j = 1:solver.restart
            # Apply preconditioner if available
            if solver.preconditioner !== nothing
                w_prec_input = V[:, j]
                w_prec_output = similar(w_prec_input) 
                if solver.verbose && j == 1 && outer == 1 println("GMRES Debug: Applying preconditioner $(typeof(solver.preconditioner))") end
                apply!(w_prec_output, solver.preconditioner, w_prec_input)
                if solver.verbose && outer == 1 && j <= 3 # Print for first few iterations of first restart
                    if solver.verbose && outer == 1 && j <= 3
                        println("  GMRES iter $( (outer-1)*solver.restart + j ): ||V[:,j]|| = $(norm(w_prec_input)), ||M_inv*V[:,j]|| = $(norm(w_prec_output))")
                    end
                end
                w = A * w_prec_output
                if solver.verbose && outer == 1 && j <= 3
                    if solver.verbose && outer == 1 && j <= 3
                        println("  GMRES iter $( (outer-1)*solver.restart + j ): ||A*M_inv*V[:,j]|| = $(norm(w))")
                    end
                end
            else
                w_no_prec = V[:,j]
                w = A * w_no_prec
                if solver.verbose && outer == 1 && j <= 3
                     if solver.verbose && outer == 1 && j <= 3
                         println("  GMRES iter $( (outer-1)*solver.restart + j ): ||V[:,j]|| = $(norm(w_no_prec)), ||A*V[:,j]|| = $(norm(w)) (no preconditioner)")
                     end
                end
            end
            
            # Arnoldi process
            for i = 1:j
                H[i, j] = dot(w, V[:, i])
                @fast_loop for k_idx in eachindex(w) # Renamed k to k_idx to avoid conflict if w is also named k
                    w[k_idx] -= H[i, j] * V[k_idx, i]
                end
            end
            if solver.verbose && outer == 1 && j <= 3
                if solver.verbose && outer == 1 && j <= 3
                    println("  GMRES iter $( (outer-1)*solver.restart + j ): H[1:$j,$j] = $(H[1:j,j]), ||w_ortho|| = $(norm(w))")
                end
            end
            
            H[j+1, j] = norm(w)
            
            if H[j+1, j] > eps(T)
                V[:, j+1] = w / H[j+1, j]
            end
            
            # Apply previous Givens rotations to H[:,j]
            for k = 1:j-1
                temp_Hkj = H[k,j]
                H[k,j]   = cs[k] * temp_Hkj + sn[k] * H[k+1,j]
                H[k+1,j] = -sn[k] * temp_Hkj + cs[k] * H[k+1,j]
            end

            # Calculate and apply new Givens rotation for H[j,j] and H[j+1,j]
            h_jj = H[j,j]
            h_jplus1_j = H[j+1,j]

            if abs(h_jplus1_j) > sqrt(eps(T)) # Check if H[j+1,j] needs to be zeroed
                r_norm = hypot(h_jj, h_jplus1_j)
                c_j = h_jj / r_norm
                s_j = h_jplus1_j / r_norm
                
                cs[j] = c_j
                sn[j] = s_j
                
                H[j,j] = r_norm # c_j * h_jj + s_j * h_jplus1_j
                H[j+1,j] = zero(T) # -s_j * h_jj + c_j * h_jplus1_j (should be 0)
                
                # Apply to s
                s_j_old = s[j]
                s[j]   = c_j * s_j_old + s_j * s[j+1]
                s[j+1] = -s_j * s_j_old + c_j * s[j+1]
            else # H[j+1,j] is already (close to) zero
                cs[j] = one(T)
                sn[j] = zero(T)
                # H[j,j] and s[j] remain as they are for this step's rotation part
                # H[j+1,j] is already zero, s[j+1] carries part of the residual norm
            end
            
            res_norm = abs(s[j+1])
            push!(residual_history, res_norm)
            
            if solver.verbose && ((outer-1)*solver.restart + j) % 1 == 0 # Print more frequently for debugging
                if solver.verbose && ((outer-1)*solver.restart + j) % 10 == 0
                    println("GMRES iteration $((outer-1)*solver.restart + j): res_norm_givens = $res_norm, s[j+1]=$(s[j+1])")
                end
            end
            
            if res_norm < solver.tol
                # Solve H_j y_j = s_j for y_j
                y = H[1:j, 1:j] \ s[1:j]
                # Update solution x = x_0 + M_inv * (V_j y_j)
                correction_krylov = V[:, 1:j] * y
                if solver.preconditioner !== nothing
                    true_correction = similar(correction_krylov)
                    apply!(true_correction, solver.preconditioner, correction_krylov)
                    x .+= true_correction
                else
                    x .+= correction_krylov # No preconditioner, M_inv is identity
                end
                return SolverResult(x, true, (outer-1)*solver.restart + j, res_norm, residual_history)
            end
        end # End of inner j-loop (Arnoldi/Givens)

        # At the end of the restart cycle (j == solver.restart), solve H_m y_m = s_m
        # H is (m+1) x m, but we solve with H[1:m, 1:m] which is upper triangular after Givens.
        m = solver.restart
        y = H[1:m, 1:m] \ s[1:m]
        # Update solution x = x_0 + M_inv * (V_m y_m)
        correction_krylov = V[:, 1:m] * y
        if solver.preconditioner !== nothing
            true_correction = similar(correction_krylov)
            apply!(true_correction, solver.preconditioner, correction_krylov)
            x .+= true_correction
        else
            x .+= correction_krylov # No preconditioner, M_inv is identity
        end
    end
    
    return SolverResult(x, false, solver.maxiter, residual_history[end], residual_history)
end

# ============================================================================
# AMG Components
# ============================================================================

struct AMGLevel{T}
    A::Union{SparseMatrixCSC{T,Int}, Nothing}  # Nothing for matrix-free
    P::Union{SparseMatrixCSC{T,Int}, Nothing}  # Prolongation
    R::Union{SparseMatrixCSC{T,Int}, Nothing}  # Restriction
    smoother::AbstractLinearSolver
    level::Int
end

# Classical AMG coarsening
function classical_coarsening(A::SparseMatrixCSC{T}) where T
    n = size(A, 1)
    C = falses(n)  # Coarse points
    F = falses(n)  # Fine points
    U = trues(n)   # Undecided points
    
    # Compute strength matrix
    S = strength_matrix(A)
    
    # Compute lambda (number of strong connections)
    lambda = vec(sum(S, dims=2))
    
    while any(U)
        # Find undecided point with maximum lambda
        i = argmax(lambda .* U)
        
        # Make i a C-point
        C[i] = true
        U[i] = false
        
        # Make all undecided strongly connected points F-points
        for j in findnz(S[i, :])[1]
            if U[j]
                F[j] = true
                U[j] = false
            end
        end
        
        # Update lambda
        for j in findnz(S[:, i])[1]
            if U[j]
                lambda[j] += 1
            end
        end
    end
    
    return C, F
end

function strength_matrix(A::SparseMatrixCSC{T}, θ=0.25) where T
    n = size(A, 1)
    rows, cols, vals = Int[], Int[], T[]
    
    for i = 1:n
        # Find maximum off-diagonal element
        max_val = zero(T)
        for k in nzrange(A, i)
            j = A.rowval[k]
            if i != j && abs(A.nzval[k]) > max_val
                max_val = abs(A.nzval[k])
            end
        end
        
        # Mark strong connections
        for k in nzrange(A, i)
            j = A.rowval[k]
            if i != j && abs(A.nzval[k]) >= θ * max_val
                push!(rows, i)
                push!(cols, j)
                push!(vals, one(T))
            end
        end
    end
    
    return sparse(rows, cols, vals, n, n)
end

function build_interpolation(A::SparseMatrixCSC{T}, C::BitVector, F::BitVector) where T
    n = size(A, 1)
    nc = sum(C)
    
    # Map from fine to coarse indices
    coarse_idx = zeros(Int, n)
    idx = 1
    for i = 1:n
        if C[i]
            coarse_idx[i] = idx
            idx += 1
        end
    end
    
    # Build interpolation matrix
    rows, cols, vals = Int[], Int[], T[]
    
    for i = 1:n
        if C[i]
            # Coarse points interpolate to themselves
            push!(rows, i)
            push!(cols, coarse_idx[i])
            push!(vals, one(T))
        else
            # Fine points interpolate from coarse neighbors
            sum_weights = zero(T)
            coarse_neighbors = Int[]
            weights = T[]
            
            for k in nzrange(A, i)
                j = A.rowval[k]
                if C[j] && i != j
                    w = -A.nzval[k]
                    push!(coarse_neighbors, j)
                    push!(weights, w)
                    sum_weights += w
                end
            end
            
            # Normalize weights
            if sum_weights > eps(T)
                for (j, w) in zip(coarse_neighbors, weights)
                    push!(rows, i)
                    push!(cols, coarse_idx[j])
                    push!(vals, w / sum_weights)
                end
            end
        end
    end
    
    return sparse(rows, cols, vals, n, nc)
end

# ============================================================================
# Standard AMG Solver
# ============================================================================

struct AMG <: AbstractAMGSolver
    tol::Float64
    maxiter::Int
    n_levels::Int
    smoother_type::Symbol
    n_smooth::Int
    cycle_type::Symbol  # :V or :W
    verbose::Bool
end

AMG(; tol=1e-6, maxiter=100, n_levels=10, smoother_type=:jacobi, 
    n_smooth=3, cycle_type=:V, verbose=false) = 
    AMG(tol, maxiter, n_levels, smoother_type, n_smooth, cycle_type, verbose)

mutable struct AMGHierarchy{T}
    levels::Vector{AMGLevel{T}}
end

function setup_amg(solver::AMG, A::SparseMatrixCSC{T}) where T
    levels = AMGLevel{T}[]
    current_A = A
    
    for level = 1:solver.n_levels
        n = size(current_A, 1)
        
        # Create smoother for this level
        smoother = if solver.smoother_type == :jacobi
            PCG(tol=1e-10, maxiter=solver.n_smooth, 
                preconditioner=JacobiPreconditioner(current_A), verbose=false)
        elseif solver.smoother_type == :ilu
            PCG(tol=1e-10, maxiter=solver.n_smooth,
                preconditioner=ILUPreconditioner(current_A), verbose=false)
        else
            PCG(tol=1e-10, maxiter=solver.n_smooth, verbose=false)
        end
        
        if n <= 50 || level == solver.n_levels
            # Coarsest level - use direct solver
            push!(levels, AMGLevel(current_A, nothing, nothing, smoother, level))
            break
        end
        
        # Coarsen
        C, F = classical_coarsening(current_A)
        P = build_interpolation(current_A, C, F)
        R = SparseMatrixCSC(P')  # Restriction is transpose of prolongation
        
        # Galerkin coarsening: A_coarse = R * A * P
        A_coarse = R * current_A * P
        
        push!(levels, AMGLevel(current_A, P, R, smoother, level))
        current_A = A_coarse
    end
    
    return AMGHierarchy(levels)
end

function amg_cycle!(x::Vector{T}, b::Vector{T}, hierarchy::AMGHierarchy{T}, 
                    level::Int, cycle_type::Symbol) where T
    if level == length(hierarchy.levels)
        # Coarsest level - solve exactly
        x .= hierarchy.levels[level].A \ b
        return
    end
    
    current_level = hierarchy.levels[level]
    
    # Pre-smoothing
    result = solve(current_level.smoother, current_level.A, b, x)
    x .= result.x
    
    # Compute residual
    r = b - current_level.A * x
    
    # Restrict residual
    r_coarse = current_level.R * r
    
    # Solve on coarse grid
    e_coarse = zeros(T, length(r_coarse))
    
    if cycle_type == :V
        amg_cycle!(e_coarse, r_coarse, hierarchy, level + 1, cycle_type)
    else  # W-cycle
        amg_cycle!(e_coarse, r_coarse, hierarchy, level + 1, cycle_type)
        amg_cycle!(e_coarse, r_coarse, hierarchy, level + 1, cycle_type)
    end
    
    # Prolongate correction
    e = current_level.P * e_coarse
    
    # Update solution
    x .+= e
    
    # Post-smoothing
    result = solve(current_level.smoother, current_level.A, b, x)
    x .= result.x
end

# Two-argument version
function solve(solver::AMG, A::SparseMatrixCSC{T}, b::Vector{T}) where T
    return solve(solver, A, b, nothing)
end

function solve(solver::AMG, A::SparseMatrixCSC{T}, b::Vector{T}, x0::Union{Nothing,Vector{T}}) where T
    hierarchy = setup_amg(solver, A)
    
    n = length(b)
    x = x0 === nothing ? zeros(T, n) : copy(x0)
    
    residual_history = T[]
    
    for iter = 1:solver.maxiter
        r = b - A * x
        res_norm = norm(r)
        push!(residual_history, res_norm)
        
        if solver.verbose && iter % 10 == 0
            if solver.verbose && iter % 10 == 0
                println("AMG iteration $iter: residual = $res_norm")
            end
        end
        
        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
        
        amg_cycle!(x, b, hierarchy, 1, solver.cycle_type)
    end
    
    return SolverResult(x, false, solver.maxiter, residual_history[end], residual_history)
end

# ============================================================================
# Matrix-Free AMG with Advanced Coarsening
# ============================================================================

struct MatrixFreeOperator{F}
    apply::F  # Function that applies A*x
    n::Int
end

struct MatrixFreeAMGLevel{T}
    operator::MatrixFreeOperator
    prolongation::Union{MatrixFreeOperator, Nothing}
    restriction::Union{MatrixFreeOperator, Nothing}
    smoother::AbstractLinearSolver
    level::Int
end

struct MatrixFreeAMG <: AbstractAMGSolver
    tol::Float64
    maxiter::Int
    n_levels::Int
    smoother_type::Symbol
    n_smooth::Int
    cycle_type::Symbol
    verbose::Bool
end

MatrixFreeAMG(; tol=1e-6, maxiter=100, n_levels=5, smoother_type=:jacobi,
              n_smooth=3, cycle_type=:V, verbose=false) =
    MatrixFreeAMG(tol, maxiter, n_levels, smoother_type, n_smooth, cycle_type, verbose)

# Matrix-free Jacobi smoother
struct MatrixFreeJacobi{T}
    diag_inv::Vector{T}
    iterations::Int
end

function smooth!(x::Vector{T}, b::Vector{T}, op::MatrixFreeOperator, smoother::MatrixFreeJacobi{T}) where T
    r = similar(b)
    
    for iter = 1:smoother.iterations
        # Compute residual
        op.apply(r, x)
        @fast_loop for i in eachindex(r)
            r[i] = b[i] - r[i]
        end
        
        # Update solution
        @fast_loop for i in eachindex(x)
            x[i] += smoother.diag_inv[i] * r[i]
        end
    end
end

# Setup matrix-free hierarchy
function setup_matrix_free_amg()
    # Placeholder for matrix-free AMG setup
    # TODO: Implement this function
    return nothing
end


function solve(solver::MatrixFreeAMG, A_op::MatrixFreeOperator, b::Vector{T}, x0::Union{Nothing,Vector{T},Function}=nothing, get_diag_func::Union{Nothing,Function}=nothing) where T
    n = length(b)
    x = if isa(x0, AbstractVector)
        copy(x0)
    elseif isa(x0, Function) && length(methods(x0, (typeof(b),))) > 0 && methods(x0, (typeof(b),))[1].nargs == 2
        try
            potential_x = x0(b)
            isa(potential_x, AbstractVector) ? potential_x : zeros(T, n)
        catch e
            if solver.verbose println("MatrixFreeAMG: x0 function call failed ($e), defaulting to zeros.") end
            zeros(T, n)
        end
    else
        zeros(T, n)
    end

    residual_history = T[]
    r_vec = similar(b) # To store A_op*x or residual

    if solver.smoother_type != :jacobi
        error("MatrixFreeAMG currently only supports :jacobi smoother type.")
    end

    if get_diag_func === nothing
        error("MatrixFreeAMG with Jacobi smoother requires get_diag_func to be provided.")
    end

    diag_A = get_diag_func(A_op)
    if length(diag_A) != n
        error("Diagonal returned by get_diag_func has incorrect length.")
    end

    diag_inv = similar(diag_A)
    for i in eachindex(diag_A)
        val = diag_A[i]
        diag_inv[i] = (abs(val) < sqrt(eps(real(T))) || !isfinite(val)) ? one(T) : one(T) / val
    end

    jacobi_smoother = MatrixFreeJacobi(diag_inv, solver.n_smooth)

    for iter = 1:solver.maxiter
        # Apply smoother (e.g., n_smooth iterations of Jacobi)
        smooth!(x, b, A_op, jacobi_smoother)

        # Calculate residual: r = b - A*x
        A_op.apply(r_vec, x) # r_vec = A_op*x
        @fast_loop for i in eachindex(b)
            r_vec[i] = b[i] - r_vec[i]
        end
        res_norm = norm(r_vec)
        push!(residual_history, res_norm)

        if solver.verbose
            if solver.verbose && iter % 10 == 0
                println("MatrixFreeAMG iteration $iter: residual = $res_norm")
            end
        end

        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
    end

    # Calculate final residual if not converged within maxiter
    A_op.apply(r_vec, x)
    @fast_loop for i in eachindex(b)
        r_vec[i] = b[i] - r_vec[i]
    end
    final_res_norm = norm(r_vec)
    if solver.verbose
         if solver.verbose
             println("MatrixFreeAMG: Did not converge in $(solver.maxiter) iterations. Final residual: $final_res_norm")
         end
    end
    return SolverResult(x, false, solver.maxiter, final_res_norm, residual_history)
end

# ============================================================================
# GPU-Accelerated Linear Solvers
# ============================================================================

const HAS_METAL = try
    using Metal
    true
catch
    @warn "Metal not available, GPU acceleration disabled on Apple Silicon"
    false
end

if HAS_CUDA
    """
    CUDASolver
    
    GPU-accelerated linear solver using CUDA.jl for NVIDIA GPUs.
    """
    struct CUDASolver{T} <: AbstractLinearSolver
        host_solver::Symbol  # :pcg, :bicgstab, :gmres
        device_id::Int
        tolerance::T
        maxiter::Int
        verbose::Bool
        
        function CUDASolver{T}(solver_type::Symbol; device_id::Int=0, 
                              tolerance::T=1e-6, maxiter::Int=1000, verbose::Bool=false) where T
            new{T}(solver_type, device_id, tolerance, maxiter, verbose)
        end
    end
    
    # Type alias for compatibility
    const GPUPCG{T} = CUDASolver{T}
    
    # Convenience constructor for GPUPCG
    function GPUPCG(; tol::Real=1e-6, maxiter::Int=1000, preconditioner::Symbol=:none, 
                    device_id::Int=0, verbose::Bool=false)
        return CUDASolver{Float64}(:pcg, device_id=device_id, tolerance=tol, 
                                  maxiter=maxiter, verbose=verbose)
    end
    
    function solve(solver::CUDASolver{T}, A::SparseMatrixCSC{T}, b::Vector{T}, 
                  x0::Union{Nothing,Vector{T}}=nothing) where T
        # Transfer data to GPU
        CUDA.device!(solver.device_id)
        
        A_gpu = CUDA.CUSPARSE.CuSparseMatrixCSC(A)
        b_gpu = CuArray(b)
        x_gpu = x0 === nothing ? CUDA.zeros(T, length(b)) : CuArray(x0)
        
        # Solve on GPU
        if solver.host_solver == :pcg
            result = cuda_pcg(solver, A_gpu, b_gpu, x_gpu)
        elseif solver.host_solver == :bicgstab
            result = cuda_bicgstab(solver, A_gpu, b_gpu, x_gpu)
        else
            error("Unsupported CUDA solver: $(solver.host_solver)")
        end
        
        # Transfer result back to CPU
        x_cpu = Array(result.x)
        
        return SolverResult(x_cpu, result.converged, result.iterations, 
                          result.residual, result.residual_history)
    end
    
    function cuda_pcg(solver::CUDASolver{T}, A_gpu, b_gpu, x_gpu) where T
        n = length(b_gpu)
        r = b_gpu - A_gpu * x_gpu
        p = copy(r)
        rsold = CUDA.dot(r, r)
        
        residual_history = T[]
        push!(residual_history, sqrt(rsold))
        
        for iter = 1:solver.maxiter
            Ap = A_gpu * p
            α = rsold / CUDA.dot(p, Ap)
            
            # Use CUDA kernels for element-wise operations
            x_gpu .+= α .* p
            r .-= α .* Ap
            
            rsnew = CUDA.dot(r, r)
            res_norm = sqrt(rsnew)
            push!(residual_history, res_norm)
            
            if solver.verbose && iter % 10 == 0
                @printf("CUDA PCG iteration %d: residual = %.2e\n", iter, res_norm)
            end
            
            if res_norm < solver.tolerance
                return (x=x_gpu, converged=true, iterations=iter, 
                       residual=res_norm, residual_history=residual_history)
            end
            
            β = rsnew / rsold
            p .= r .+ β .* p
            rsold = rsnew
        end
        
        return (x=x_gpu, converged=false, iterations=solver.maxiter, 
               residual=sqrt(rsold), residual_history=residual_history)
    end
end

if HAS_METAL
    """
    MetalSolver
    
    GPU-accelerated linear solver using Metal.jl for Apple Silicon.
    """
    struct MetalSolver{T} <: AbstractLinearSolver
        host_solver::Symbol
        tolerance::T
        maxiter::Int
        verbose::Bool
        
        function MetalSolver{T}(solver_type::Symbol; tolerance::T=1e-6, 
                               maxiter::Int=1000, verbose::Bool=false) where T
            new{T}(solver_type, tolerance, maxiter, verbose)
        end
    end
end

# ============================================================================
# Adaptive Precision Solvers
# ============================================================================

"""
    AdaptivePrecisionSolver
    
    Solver that adaptively adjusts numerical precision for optimal performance.
"""
struct AdaptivePrecisionSolver{T} <: AbstractLinearSolver
    initial_precision::Type
    target_precision::Type
    refinement_tolerance::T
    base_solver::AbstractLinearSolver
    
    function AdaptivePrecisionSolver{T}(initial_prec::Type, target_prec::Type, 
                                       base_solver::AbstractLinearSolver;
                                       refinement_tol::T=1e-3) where T
        new{T}(initial_prec, target_prec, refinement_tol, base_solver)
    end
end

function solve(solver::AdaptivePrecisionSolver{T}, A::SparseMatrixCSC{T}, 
              b::Vector{T}, x0::Union{Nothing,Vector{T}}=nothing) where T
    
    # Phase 1: Solve with lower precision for initial guess
    if solver.initial_precision != T
        A_low = convert(SparseMatrixCSC{solver.initial_precision}, A)
        b_low = convert(Vector{solver.initial_precision}, b)
        x0_low = x0 === nothing ? nothing : convert(Vector{solver.initial_precision}, x0)
        
        result_low = solve(solver.base_solver, A_low, b_low, x0_low)
        x_initial = convert(Vector{T}, result_low.x)
    else
        x_initial = x0
    end
    
    # Phase 2: Refine with target precision
    result = solve(solver.base_solver, A, b, x_initial)
    
    return result
end

# ============================================================================
# High-Performance Krylov Subspace Solvers
# ============================================================================

"""
CGS (Conjugate Gradient Squared) solver
High-performance solver for symmetric and mildly non-symmetric systems.
"""
struct CGS{T} <: AbstractLinearSolver
    tol::T
    maxiter::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

CGS(; tol=1e-6, maxiter=1000, preconditioner=nothing, verbose=false) = 
    CGS(tol, maxiter, preconditioner, verbose)

function solve!(solver::CGS, A, b, x=nothing)
    n = length(b)
    x = x === nothing ? zeros(eltype(b), n) : copy(x)
    
    residual_history = Float64[]
    
    # Initial residual
    r = b - A * x
    r0_tilde = copy(r)
    p = copy(r)
    u = copy(r)
    
    rho_old = dot(r0_tilde, r)
    
    if abs(rho_old) < eps()
        return SolverResult(x, true, 0, norm(r), residual_history)
    end
    
    for iter in 1:solver.maxiter
        res_norm = norm(r)
        push!(residual_history, res_norm)
        
        if solver.verbose && iter % 10 == 0
            println("CGS iteration $iter: residual = $res_norm")
        end
        
        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
        
        # Apply preconditioner if provided
        if solver.preconditioner !== nothing
            u_prec = similar(u)
            p_prec = similar(p)
            apply!(u_prec, solver.preconditioner, u)
            apply!(p_prec, solver.preconditioner, p)
            u = u_prec
            p = p_prec
        end
        
        # CGS core iteration
        v = A * p
        alpha = rho_old / dot(r0_tilde, v)
        
        q = u - alpha * v
        
        # Apply preconditioner to q if provided
        if solver.preconditioner !== nothing
            q_prec = similar(q)
            apply!(q_prec, solver.preconditioner, q)
            q = q_prec
        end
        
        u_hat = u + q
        x += alpha * u_hat
        
        q_hat = A * u_hat
        r -= alpha * q_hat
        
        rho_new = dot(r0_tilde, r)
        
        if abs(rho_new) < eps()
            break
        end
        
        beta = rho_new / rho_old
        u = r + beta * q
        p = u + beta * (q + beta * p)
        
        rho_old = rho_new
    end
    
    final_res_norm = norm(r)
    return SolverResult(x, false, solver.maxiter, final_res_norm, residual_history)
end

"""
BiCG (Bi-Conjugate Gradient) solver
Robust solver for non-symmetric systems.
"""
struct BiCG{T} <: AbstractLinearSolver
    tol::T
    maxiter::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

BiCG(; tol=1e-6, maxiter=1000, preconditioner=nothing, verbose=false) = 
    BiCG(tol, maxiter, preconditioner, verbose)

function solve!(solver::BiCG, A, b, x=nothing)
    n = length(b)
    x = x === nothing ? zeros(eltype(b), n) : copy(x)
    
    residual_history = Float64[]
    
    # Initial residual
    r = b - A * x
    r_tilde = copy(r)
    p = copy(r)
    p_tilde = copy(r)
    
    rho_old = dot(r_tilde, r)
    
    if abs(rho_old) < eps()
        return SolverResult(x, true, 0, norm(r), residual_history)
    end
    
    for iter in 1:solver.maxiter
        res_norm = norm(r)
        push!(residual_history, res_norm)
        
        if solver.verbose && iter % 10 == 0
            println("BiCG iteration $iter: residual = $res_norm")
        end
        
        if res_norm < solver.tol
            return SolverResult(x, true, iter, res_norm, residual_history)
        end
        
        # Apply preconditioner if provided
        if solver.preconditioner !== nothing
            z = similar(r)
            z_tilde = similar(r)
            apply!(z, solver.preconditioner, r)
            apply!(z_tilde, solver.preconditioner, r_tilde)
        else
            z = r
            z_tilde = r_tilde
        end
        
        # BiCG core iteration
        v = A * p
        v_tilde = A' * p_tilde  # Transpose multiplication
        
        alpha = rho_old / dot(p_tilde, v)
        
        x += alpha * p
        r -= alpha * v
        r_tilde -= alpha * v_tilde
        
        rho_new = dot(r_tilde, r)
        
        if abs(rho_new) < eps()
            break
        end
        
        beta = rho_new / rho_old
        p = r + beta * p
        p_tilde = r_tilde + beta * p_tilde
        
        rho_old = rho_new
    end
    
    final_res_norm = norm(r)
    return SolverResult(x, false, solver.maxiter, final_res_norm, residual_history)
end

"""
TFQMR (Transpose-Free Quasi-Minimal Residual) solver
Most robust and stable Krylov solver for general non-symmetric systems.
"""
struct TFQMR{T} <: AbstractLinearSolver
    tol::T
    maxiter::Int
    preconditioner::Union{Nothing, AbstractPreconditioner}
    verbose::Bool
end

TFQMR(; tol=1e-6, maxiter=1000, preconditioner=nothing, verbose=false) = 
    TFQMR(tol, maxiter, preconditioner, verbose)

function solve!(solver::TFQMR, A, b, x=nothing)
    n = length(b)
    x = x === nothing ? zeros(eltype(b), n) : copy(x)
    
    residual_history = Float64[]
    
    # Initial residual and setup
    r = b - A * x
    r0_tilde = copy(r)
    
    # Initialize vectors
    p = copy(r)
    u = copy(r)
    v = A * u
    
    rho = dot(r0_tilde, r)
    alpha = 0.0
    beta = 0.0
    
    # For quasi-minimal residual
    tau = norm(r)
    push!(residual_history, tau)
    
    if tau < solver.tol
        return SolverResult(x, true, 0, tau, residual_history)
    end
    
    theta = 0.0
    eta = 0.0
    d = zeros(eltype(b), n)
    
    for iter in 1:solver.maxiter
        if solver.verbose && iter % 10 == 0
            println("TFQMR iteration $iter: residual = $tau")
        end
        
        if tau < solver.tol
            return SolverResult(x, true, iter, tau, residual_history)
        end
        
        rho_new = dot(r0_tilde, r)
        
        if abs(rho_new) < eps()
            break
        end
        
        if iter == 1
            beta = 0.0
        else
            beta = rho_new / rho
        end
        
        u = r + beta * u
        p = u + beta * (u + beta * p)
        
        # Apply preconditioner if provided
        if solver.preconditioner !== nothing
            p_hat = similar(p)
            apply!(p_hat, solver.preconditioner, p)
        else
            p_hat = p
        end
        
        v = A * p_hat
        alpha = rho_new / dot(r0_tilde, v)
        
        u = u - alpha * v
        
        # Update solution using quasi-minimal residual approach
        # First sub-iteration
        if iter == 1
            d = copy(p_hat)
            theta = 0.0
            eta = alpha
        else
            d = p_hat + (theta^2 / alpha) * eta * d
            theta = norm(u) / tau
            c = 1.0 / sqrt(1.0 + theta^2)
            eta = c^2 * alpha
            tau = tau * theta * c
        end
        
        x += eta * d
        
        # Second sub-iteration  
        if solver.preconditioner !== nothing
            u_hat = similar(u)
            apply!(u_hat, solver.preconditioner, u)
        else
            u_hat = u
        end
        
        v_new = A * u_hat
        u = u - alpha * v_new
        
        # Update for second sub-iteration
        d = u_hat + (theta^2 / alpha) * eta * d
        theta = norm(u) / tau
        c = 1.0 / sqrt(1.0 + theta^2)
        tau = tau * theta * c
        eta = c^2 * alpha
        
        x += eta * d
        
        push!(residual_history, tau)
        
        if tau < solver.tol
            return SolverResult(x, true, iter, tau, residual_history)
        end
        
        r = u
        rho = rho_new
    end
    
    return SolverResult(x, false, solver.maxiter, tau, residual_history)
end

# ============================================================================
# Mixed Precision and Auto-tuning
# ============================================================================

"""
    AutoTuningSolver
    
    Automatically selects optimal solver and parameters based on matrix properties.
"""
struct AutoTuningSolver{T} <: AbstractLinearSolver
    candidate_solvers::Vector{AbstractLinearSolver}
    selection_strategy::Symbol  # :performance, :accuracy, :memory
    benchmark_iterations::Int
    cache::Dict{UInt64, AbstractLinearSolver}
    
    function AutoTuningSolver{T}(candidates::Vector{AbstractLinearSolver};
                                strategy::Symbol=:performance,
                                benchmark_iters::Int=3) where T
        new{T}(candidates, strategy, benchmark_iters, Dict{UInt64, AbstractLinearSolver}())
    end
end

function solve(solver::AutoTuningSolver{T}, A::SparseMatrixCSC{T}, 
              b::Vector{T}, x0::Union{Nothing,Vector{T}}=nothing) where T
    
    # Create matrix fingerprint for caching
    matrix_hash = hash((size(A), nnz(A), hash(A.nzval[1:min(100, length(A.nzval))])))
    
    # Check cache first
    if haskey(solver.cache, matrix_hash)
        best_solver = solver.cache[matrix_hash]
        return solve(best_solver, A, b, x0)
    end
    
    # Benchmark candidate solvers
    best_solver = nothing
    best_score = Inf
    
    for candidate in solver.candidate_solvers
        total_time = 0.0
        total_iterations = 0
        converged_count = 0
        
        for iter in 1:solver.benchmark_iterations
            start_time = time()
            result = solve(candidate, A, b, x0)
            elapsed = time() - start_time
            
            total_time += elapsed
            total_iterations += result.iterations
            if result.converged
                converged_count += 1
            end
        end
        
        # Calculate score based on strategy
        if solver.selection_strategy == :performance
            score = total_time / solver.benchmark_iterations
        elseif solver.selection_strategy == :accuracy
            score = (solver.benchmark_iterations - converged_count) * 1000 + total_time
        else  # :memory
            # Estimate memory usage (simplified)
            score = total_iterations * size(A, 1) * 8 / 1024^2  # MB estimate
        end
        
        if score < best_score
            best_score = score
            best_solver = candidate
        end
    end
    
    # Cache the best solver
    solver.cache[matrix_hash] = best_solver
    
    # Solve with the best solver
    return solve(best_solver, A, b, x0)
end

# ============================================================================
# Fault-Tolerant Solvers
# ============================================================================

"""
    FaultTolerantSolver
    
    Solver with automatic recovery from numerical issues and convergence failures.
"""
struct FaultTolerantSolver{T} <: AbstractLinearSolver
    primary_solver::AbstractLinearSolver
    fallback_solvers::Vector{AbstractLinearSolver}
    max_retries::Int
    condition_number_threshold::T
    
    function FaultTolerantSolver{T}(primary::AbstractLinearSolver,
                                   fallbacks::Vector{AbstractLinearSolver};
                                   max_retries::Int=3,
                                   cond_threshold::T=1e12) where T
        new{T}(primary, fallbacks, max_retries, cond_threshold)
    end
end

function solve(solver::FaultTolerantSolver{T}, A::SparseMatrixCSC{T}, 
              b::Vector{T}, x0::Union{Nothing,Vector{T}}=nothing) where T
    
    # Check matrix condition
    if estimate_condition_number(A) > solver.condition_number_threshold
        @warn "High condition number detected, using robust solver"
        return solve(solver.fallback_solvers[end], A, b, x0)
    end
    
    # Try primary solver first
    try
        result = solve(solver.primary_solver, A, b, x0)
        if result.converged && isfinite(norm(result.x))
            return result
        end
    catch e
        @warn "Primary solver failed: $e"
    end
    
    # Try fallback solvers
    for (i, fallback) in enumerate(solver.fallback_solvers)
        try
            @info "Trying fallback solver $i/$(length(solver.fallback_solvers))"
            result = solve(fallback, A, b, x0)
            if result.converged && isfinite(norm(result.x))
                return result
            end
        catch e
            @warn "Fallback solver $i failed: $e"
        end
    end
    
    error("All solvers failed to converge")
end

function estimate_condition_number(A::SparseMatrixCSC{T}) where T
    # Quick estimate using power method for largest eigenvalue
    # and inverse power method for smallest eigenvalue
    n = size(A, 1)
    
    # Largest eigenvalue estimate
    v = randn(n)
    for _ in 1:10
        v = A * v
        v ./= norm(v)
    end
    λ_max = dot(v, A * v)
    
    # Simple estimate (full condition number is expensive)
    # Use diagonal dominance as proxy
    min_diag = minimum(abs.(diag(A)))
    max_diag = maximum(abs.(diag(A)))
    
    return max_diag / (min_diag + eps(T))
end

end # module LinearSolvers