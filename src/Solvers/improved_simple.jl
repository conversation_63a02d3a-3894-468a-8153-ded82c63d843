# src/Solvers/improved_simple.jl
"""
Improved SIMPLE solver with proper Rhie-Chow interpolation.

This implementation provides a mathematically correct SIMPLE algorithm with:
1. Proper Rhie-Chow interpolation to prevent pressure oscillations
2. Momentum equation assembly with under-relaxation
3. Pressure correction equation with proper boundary conditions
4. Velocity and pressure correction steps
5. Convergence monitoring and control
"""

using ..CFDCore
using ..CFDCore: <PERSON><PERSON><PERSON><PERSON><PERSON>, ScalarField, VectorField, Field, AbstractBoundaryCondition, DirichletBC, NeumannBC
using ..Numerics.fvc, ..Numerics.fvm
using ..Numerics: GaussGradient
using .RhieChowInterpolation
using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf

"""
    solve_simple_improved!(
        U_field::VectorField,
        p_field::<PERSON>alarField,
        mesh::AbstractMesh,
        rho::Float64,
        nu::Float64,
        simple_settings::SIMPLESettings = SIMPLESettings()
    )

Solve incompressible Navier-Stokes equations using improved SIMPLE algorithm.

This implementation includes proper Rhie-Chow interpolation and follows the 
standard SIMPLE procedure:

1. **Momentum Predictor**: Solve momentum equations using pressure from previous iteration
2. **Rhie-Chow Interpolation**: Compute face velocities with pressure gradient correction  
3. **Pressure Correction**: Solve pressure Poisson equation for pressure correction
4. **Velocity Correction**: Update velocity field using pressure correction gradients
5. **Update**: Apply under-relaxation and check convergence

# Arguments
- `U_field`: Velocity field (modified in-place)
- `p_field`: Pressure field (modified in-place)  
- `mesh`: Computational mesh
- `rho`: Fluid density [kg/m³]
- `nu`: Kinematic viscosity [m²/s]
- `simple_settings`: SIMPLE algorithm settings

# Returns
- `convergence_info`: Dictionary with convergence history and final residuals
"""
function solve_simple_improved!(
    U_field::VectorField,
    p_field::ScalarField,
    mesh::AbstractMesh,
    rho::Float64,
    nu::Float64,
    simple_settings::SIMPLESettings = SIMPLESettings()
)
    
    num_cells = length(mesh.cells)
    num_faces = length(mesh.faces)
    mu = rho * nu  # Dynamic viscosity
    
    # Initialize convergence monitoring
    convergence_info = Dict{Symbol, Any}(
        :continuity_residuals => Float64[],
        :momentum_residuals => Float64[],
        :iterations => 0,
        :converged => false,
        :final_residual => 1.0
    )
    
    # Storage for momentum equation diagonal coefficients (needed for Rhie-Chow)
    A_U_diag = Vector{Float64}(undef, num_cells)
    
    # Storage for face velocities with Rhie-Chow correction
    face_velocities = Vector{SVector{3,Float64}}(undef, num_faces)
    
    # Storage for face mass fluxes
    face_mass_fluxes = Vector{Float64}(undef, num_faces)
    
    println("Starting SIMPLE iterations...")
    println("Settings: max_iter=$(simple_settings.max_iterations), tol=$(simple_settings.tolerance)")
    println("Relaxation: α_u=$(simple_settings.alpha_u), α_p=$(simple_settings.alpha_p)")
    
    # SIMPLE iteration loop
    for iter in 1:simple_settings.max_iterations
        
        # Store previous iteration values for relaxation
        U_prev = deepcopy(U_field.data)
        p_prev = deepcopy(p_field.data)
        
        # ================================================================
        # STEP 1: MOMENTUM PREDICTOR
        # ================================================================
        
        # Solve momentum equations component-wise
        momentum_residuals = Float64[]
        
        for d in 1:3  # x, y, z components
            # Extract velocity component
            U_component_data = [U_field.data[i][d] for i in 1:num_cells]
            
            # Create boundary conditions for this velocity component
            U_component_bcs = Dict{String, AbstractBoundaryCondition}()
            for (patch_name, vector_bc) in U_field.boundary_conditions
                if isa(vector_bc, DirichletBC)
                    # Extract the d-th component from vector boundary condition
                    U_component_bcs[patch_name] = DirichletBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[d])
                elseif isa(vector_bc, NeumannBC)
                    # Extract the d-th component from vector boundary condition
                    U_component_bcs[patch_name] = NeumannBC((x, y, z, t) -> vector_bc.value(x, y, z, t)[d])
                else
                    # Default to zero Dirichlet for unknown BC types
                    U_component_bcs[patch_name] = DirichletBC((x, y, z, t) -> 0.0)
                end
            end
            
            U_component = ScalarField(Symbol("U$d"), mesh, U_component_data, U_component_bcs)
            
            # Assemble momentum equation matrix
            # Convection term: ∇⋅(ρUU)
            mass_flux_field = ScalarField(:mass_flux, mesh, face_mass_fluxes, Dict{String, AbstractBoundaryCondition}())
            A_conv = fvm.div(mass_flux_field, U_component)
            
            # Diffusion term: μ∇²U  
            A_diff = fvm.laplacian(mu, U_component)
            
            # Combine terms
            A_momentum = A_conv.A + A_diff.A
            b_momentum = A_conv.b + A_diff.b
            
            # Add pressure gradient as source term: -∇p
            grad_p = fvc.grad(p_field, GaussGradient())
            for i in 1:num_cells
                cell_volume = mesh.cells[i].volume
                b_momentum[i] -= cell_volume * grad_p.data[i][d]
            end
            
            # Store diagonal coefficients for Rhie-Chow (first component only)
            if d == 1
                for i in 1:num_cells
                    A_U_diag[i] = A_momentum[i, i]
                end
            end
            
            # Apply under-relaxation to momentum equation
            # Modified equation: A_U/α_u * U = H(U) + (1-α_u)/α_u * A_U * U_old
            alpha_u = simple_settings.alpha_u
            for i in 1:num_cells
                A_diag_orig = A_momentum[i, i]
                A_momentum[i, i] = A_diag_orig / alpha_u
                b_momentum[i] += (1.0 - alpha_u) / alpha_u * A_diag_orig * U_component.data[i]
            end
            
            # Solve momentum equation
            U_new_component = A_momentum \ b_momentum
            
            # Calculate residual
            residual = norm(A_momentum * U_component.data - b_momentum) / max(norm(b_momentum), 1e-12)
            push!(momentum_residuals, residual)
            
            # Update velocity field component
            for i in 1:num_cells
                U_new = collect(U_field.data[i])
                U_new[d] = U_new_component[i]
                U_field.data[i] = SVector{3,Float64}(U_new...)
            end
        end
        
        # ================================================================
        # STEP 2: RHIE-CHOW INTERPOLATION  
        # ================================================================
        
        # Compute face velocities with Rhie-Chow correction
        rhie_chow_velocity_interpolation!(face_velocities, U_field, p_field, A_U_diag, mesh)
        
        # Compute face mass fluxes: ṁ_f = ρ * U_f ⋅ S_f
        for face_idx in 1:num_faces
            face = mesh.faces[face_idx]
            face_area_vector = face.normal * face.area
            face_mass_fluxes[face_idx] = rho * dot(face_velocities[face_idx], face_area_vector)
        end
        
        # ================================================================
        # STEP 3: PRESSURE CORRECTION EQUATION
        # ================================================================
        
        # Assemble pressure correction equation: ∇⋅(ρ/A_p ∇p') = ∇⋅(ρU*)
        # Create coefficient field for pressure equation
        p_coeff_data = Vector{Float64}(undef, num_cells)
        for i in 1:num_cells
            cell_volume = mesh.cells[i].volume
            p_coeff_data[i] = rho * cell_volume / A_U_diag[i]
        end
        
        p_coeff_field = ScalarField(:p_coeff, mesh, p_coeff_data, Dict{String, AbstractBoundaryCondition}())
        
        # Pressure correction boundary conditions (typically zero Neumann)
        p_prime_bcs = Dict{String, AbstractBoundaryCondition}()
        for patch_name in keys(mesh.boundaries)
            p_prime_bcs[patch_name] = CFDCore.NeumannBC((x, y, z, t) -> 0.0)
        end
        
        p_prime_field = ScalarField(:p_prime, mesh, zeros(num_cells), p_prime_bcs)
        
        # Assemble pressure correction matrix
        A_pressure = fvm.laplacian(1.0, p_coeff_field)  # ∇⋅(coeff ∇p')
        
        # Right-hand side: ∇⋅(ρU*) = mass flux divergence
        b_pressure = zeros(num_cells)
        for cell_idx in 1:num_cells
            cell = mesh.cells[cell_idx]
            mass_flux_div = 0.0
            
            for face_idx in cell.faces
                face = mesh.faces[face_idx]
                
                if face.owner == cell_idx
                    # Outgoing flux (positive contribution)
                    mass_flux_div += face_mass_fluxes[face_idx]
                elseif !face.boundary && face.neighbor == cell_idx
                    # Incoming flux (negative contribution)  
                    mass_flux_div -= face_mass_fluxes[face_idx]
                end
            end
            
            b_pressure[cell_idx] = -mass_flux_div  # Negative for pressure correction RHS
        end
        
        # Solve pressure correction equation
        p_prime_solution = A_pressure.A \ (b_pressure - A_pressure.b)
        
        # Calculate continuity residual
        continuity_residual = norm(b_pressure) / (rho * sum(mesh.cells[i].volume for i in 1:num_cells))
        push!(convergence_info[:continuity_residuals], continuity_residual)
        
        # ================================================================
        # STEP 4: VELOCITY CORRECTION
        # ================================================================
        
        # Update pressure with under-relaxation: p_new = p_old + α_p * p'
        alpha_p = simple_settings.alpha_p
        for i in 1:num_cells
            p_field.data[i] = p_prev[i] + alpha_p * p_prime_solution[i]
        end
        
        # Correct velocity: U_new = U* - V/A_p * ∇p'
        p_prime_field_for_grad = ScalarField(:p_prime_grad, mesh, p_prime_solution, p_prime_bcs)
        grad_p_prime = fvc.grad(p_prime_field_for_grad, GaussGradient())
        
        for i in 1:num_cells
            cell_volume = mesh.cells[i].volume
            velocity_correction = -(cell_volume / A_U_diag[i]) * grad_p_prime.data[i]
            U_field.data[i] = U_field.data[i] + velocity_correction
        end
        
        # ================================================================
        # STEP 5: CONVERGENCE CHECK
        # ================================================================
        
        # Calculate overall residual
        max_momentum_residual = maximum(momentum_residuals)
        overall_residual = max(max_momentum_residual, continuity_residual)
        
        push!(convergence_info[:momentum_residuals], max_momentum_residual)
        convergence_info[:iterations] = iter
        convergence_info[:final_residual] = overall_residual
        
        # Print progress
        if iter % simple_settings.residual_output_frequency == 0 || iter == 1
            @printf("Iter %4d: Continuity = %.2e, Momentum = %.2e, Overall = %.2e\\n", 
                    iter, continuity_residual, max_momentum_residual, overall_residual)
        end
        
        # Check convergence
        if overall_residual < simple_settings.tolerance
            convergence_info[:converged] = true
            println("\\n✅ SIMPLE converged in $iter iterations!")
            println("Final residual: $(overall_residual)")
            break
        end
        
        # Check for divergence
        if overall_residual > 1e6 || isnan(overall_residual)
            println("\\n❌ SIMPLE diverged at iteration $iter")
            println("Residual: $(overall_residual)")
            break
        end
    end
    
    if !convergence_info[:converged]
        println("\\n⚠️  SIMPLE reached maximum iterations without convergence")
        println("Final residual: $(convergence_info[:final_residual])")
    end
    
    return convergence_info
end

export solve_simple_improved!