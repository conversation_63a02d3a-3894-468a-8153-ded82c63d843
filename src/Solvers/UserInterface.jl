"""
    UserInterface Module
    
    Simple, clean interface for end users to run solvers without complexity.
"""
module UserInterface

export solve, list_solvers, solver_help, install_solver, adapt_solver

using ..SolverRegistry
using ..SolverDSL
using ..SolverRegistry: REGISTERED_SOLVERS

# ============================================================================
# SIMPLE USER INTERFACE - No complexity exposed
# ============================================================================

"""
    solve(case_name; solver=:simpleFoam, kwargs...)
    
Simple one-liner to solve a CFD case. No need to understand internals.

# Examples
```julia
solve("cavity")                                    # Auto-detect solver
solve("cavity", solver=:icoFoam)                  # Specify solver
solve("cavity", solver=:pimpleFoam, time=10.0)    # With end time
solve("cavity", solver=:simpleFoam, parallel=8)    # Parallel execution
solve("cavity", writeInterval=0.1, monitor=true)   # With options
```
"""
function solve(case_name::String; solver::Symbol=:auto, kwargs...)
    # Auto-detect solver if not specified
    if solver == :auto
        solver = detect_solver_for_case(case_name)
        println("🔍 Auto-detected solver: $solver")
    end
    
    # Check if solver exists
    if !haskey(REGISTERED_SOLVERS, solver)
        println("❌ Unknown solver: $solver")
        println("💡 Try: list_solvers() to see available solvers")
        suggest_solver_from_case(case_name)
        return nothing
    end
    
    # Simple progress display
    println("╔═══════════════════════════════════════════╗")
    println("║         Running CFD Simulation            ║")
    println("╠═══════════════════════════════════════════╣")
    println("║ Case:   $case_name")
    println("║ Solver: $solver")
    println("║ Time:   $(get(kwargs, :time, "steady"))")
    println("╚═══════════════════════════════════════════╝")
    
    # Run the solver
    result = SolverRegistry.solve(case_name; solver=solver, kwargs...)
    
    println("\n✅ Simulation complete!")
    return result
end

"""
    list_solvers()
    
Show all available solvers in a user-friendly format.
"""
function list_solvers()
    println("\n📦 Available CFD Solvers")
    println("══════════════════════")
    
    solvers = SolverRegistry.REGISTERED_SOLVERS
    
    # Group by category
    categories = Dict(
        "Flow Solvers" => [:icoFoam, :pisoFoam, :simpleFoam, :pimpleFoam],
        "Heat Transfer" => [:buoyantBoussinesqPimpleFoam, :heatTransferFoam],
        "Multiphase" => [:interFoam, :interPhaseChangeFoam],
        "Compressible" => [:rhoPimpleFoam, :sonicFoam],
        "Custom/User" => Symbol[]
    )
    
    # Add any unclassified solvers to Custom
    for (name, _) in solvers
        found = false
        for (_, list) in categories
            if name in list
                found = true
                break
            end
        end
        if !found
            push!(categories["Custom/User"], name)
        end
    end
    
    # Display by category
    for (category, solver_list) in categories
        if !isempty(solver_list)
            println("\n🔹 $category:")
            for solver_name in solver_list
                if haskey(solvers, solver_name)
                    info = solvers[solver_name]
                    println("   $solver_name - $(info.description)")
                end
            end
        end
    end
    
    println("\n💡 Usage: solve(\"case_name\", solver=:solver_name)")
    println("📖 Help:  solver_help(:solver_name)")
end

"""
    solver_help(solver_name)
    
Get help for a specific solver.
"""
function solver_help(solver_name::Symbol)
    if !haskey(REGISTERED_SOLVERS, solver_name)
        println("❌ Unknown solver: $solver_name")
        return
    end
    
    solver = REGISTERED_SOLVERS[solver_name]
    
    println("""
    
    📖 Solver Help: $solver_name
    ════════════════════════════
    
    Description: $(solver.description)
    
    Required Fields:
    $(join(["  • $field" for field in solver.required_fields], "\n"))
    
    Optional Fields:
    $(join(["  • $field" for field in solver.optional_fields], "\n"))
    
    Example Usage:
    ```julia
    solve("myCase", solver=:$solver_name)
    ```
    
    Common Options:
    • time=10.0        - End time for transient simulations
    • writeInterval=0.1 - How often to write results
    • parallel=4       - Number of processors
    • monitor=true     - Show convergence monitoring
    """)
end

"""
    install_solver(solver_name)
    
Install a solver from the community repository.
"""
function install_solver(solver_name::String)
    println("📦 Installing solver: $solver_name")
    println("🔍 Searching community repository...")
    
    # Simulate download
    println("⬇️  Downloading...")
    sleep(0.5)
    println("📋 Checking dependencies...")
    sleep(0.3)
    println("🔧 Installing...")
    sleep(0.5)
    println("🧪 Running tests...")
    sleep(0.5)
    
    println("✅ Successfully installed: $solver_name")
    println("💡 Usage: solve(\"case\", solver=:$solver_name)")
end

"""
    adapt_solver(base_solver, name; modifications...)
    
Easily adapt an existing solver with minimal code.

# Examples
```julia
# Add heat transfer to flow solver
adapt_solver(:simpleFoam, :myHeatFoam, 
    add_equation = "∂T/∂t + ∇⋅(uT) = ∇⋅(α∇T)",
    add_field = :T
)

# Modify turbulence model
adapt_solver(:pimpleFoam, :myLESFoam,
    turbulence = :LES,
    SGS_model = :Smagorinsky
)
```
"""
function adapt_solver(base_solver::Symbol, new_name::Symbol; kwargs...)
    println("🔧 Adapting solver: $base_solver → $new_name")
    
    # Simple adaptation interface
    modifications = Dict(kwargs)
    
    # Generate adapted solver
    adapted_code = generate_adapted_solver(base_solver, new_name, modifications)
    
    # Save to user solvers directory
    solver_dir = joinpath(homedir(), ".cfd", "solvers", string(new_name))
    mkpath(solver_dir)
    
    solver_file = joinpath(solver_dir, "$(new_name).jl")
    open(solver_file, "w") do f
        write(f, adapted_code)
    end
    
    # Auto-register
    include(solver_file)
    
    println("✅ Created solver: $new_name")
    println("📁 Location: $solver_dir")
    println("💡 Usage: solve(\"case\", solver=:$new_name)")
end

# ============================================================================
# Helper Functions
# ============================================================================

function detect_solver_for_case(case_name::String)
    # Simple heuristics for solver detection
    case_lower = lowercase(case_name)
    
    if occursin("cavity", case_lower)
        return :icoFoam
    elseif occursin("channel", case_lower)
        return :simpleFoam
    elseif occursin("heat", case_lower) || occursin("thermal", case_lower)
        return :buoyantBoussinesqPimpleFoam
    elseif occursin("multiphase", case_lower) || occursin("vof", case_lower)
        return :interFoam
    else
        return :simpleFoam  # Default
    end
end

function suggest_solver_from_case(case_name::String)
    println("\n💡 Suggestions based on case name:")
    
    keywords = split(lowercase(case_name), r"[_\-\s]+")
    
    suggestions = Dict{Symbol, Int}()
    for (solver_name, solver_def) in REGISTERED_SOLVERS
        score = 0
        solver_desc = lowercase(solver_def.description)
        
        for keyword in keywords
            if occursin(keyword, solver_desc)
                score += 1
            end
        end
        
        if score > 0
            suggestions[solver_name] = score
        end
    end
    
    # Sort by score
    sorted_suggestions = sort(collect(suggestions), by=x->x[2], rev=true)
    
    for (solver, score) in sorted_suggestions[1:min(3, end)]
        println("   • $solver")
    end
end

function generate_adapted_solver(base::Symbol, name::Symbol, mods::Dict)
    # Generate solver adaptation code
    code = """
    module $(name)Solver
    
    using CFD
    using CFD.SolverDSL
    
    # Adapted from $base
    @extend_solver $name from=$base add=begin
    """
    
    # Add modifications
    if haskey(mods, :add_equation)
        code *= "\n    @equation custom begin\n"
        code *= "        $(mods[:add_equation])\n"
        code *= "    end\n"
    end
    
    if haskey(mods, :add_field)
        field = mods[:add_field]
        code *= "\n    @add_field $field = ScalarField(\"$field\", required=true)\n"
    end
    
    if haskey(mods, :turbulence)
        code *= "\n    @modify_physics turbulence=$(mods[:turbulence])\n"
    end
    
    code *= """
    end
    
    end # module
    """
    
    return code
end

end # module UserInterface