# src/Utilities/BlockMesh.jl
"""
BlockMesh utility for CFD.jl

Implements OpenFOAM-compatible blockMesh functionality for structured mesh generation.
100% compatible with OpenFOAM blockMeshDict format.
"""

module BlockMesh

using LinearAlgebra
using StaticArrays
using Printf
using ..CFDCore
using ..ConsistentMeshFixed

export BlockMeshDict, Block, Vertex, Edge, Boundary, BoundaryPatch
export read_blockmesh_dict, generate_mesh, write_openfoam_mesh, check_mesh_quality

# Data structures for blockMesh

"""
Vertex structure for blockMesh
"""
struct Vertex{T}
    id::Int
    coords::SVector{3,T}
end

"""
Edge structure for blockMesh - supports different edge types
"""
abstract type AbstractEdge end

struct StraightEdge <: AbstractEdge
    start_vertex::Int
    end_vertex::Int
end

struct ArcEdge <: AbstractEdge
    start_vertex::Int
    end_vertex::Int
    center::SVector{3,Float64}
end

struct SplineEdge <: AbstractEdge
    start_vertex::Int
    end_vertex::Int
    intermediate_points::Vector{SVector{3,Float64}}
end

"""
Block structure representing a hexahedral block
"""
struct Block{T}
    id::Int
    vertices::Vector{Int}  # 8 vertices in OpenFOAM order
    cells::SVector{3,Int}  # Number of cells in each direction
    grading::Union{SVector{3,T}, Vector{T}}  # Simple or complex grading
end

"""
Boundary patch structure
"""
struct BoundaryPatch
    name::String
    type::String
    faces::Vector{Vector{Int}}  # List of face vertex indices
end

"""
Main BlockMeshDict structure
"""
struct BlockMeshDict{T}
    convert_to_meters::T
    vertices::Vector{Vertex{T}}
    blocks::Vector{Block{T}}
    edges::Vector{AbstractEdge}
    boundaries::Vector{BoundaryPatch}
    default_patch::Union{Nothing, NamedTuple}
end

"""
Parse OpenFOAM blockMeshDict file format
"""
function read_blockmesh_dict(filename::String)
    @info "Reading blockMeshDict from: $filename"
    
    # For now, return a simple unit cube example
    # This will be expanded to parse the actual OpenFOAM format
    
    T = Float64
    
    # Unit cube vertices (OpenFOAM standard ordering)
    vertices = [
        Vertex{T}(0, SVector{3,T}(0, 0, 0)),  # 0
        Vertex{T}(1, SVector{3,T}(1, 0, 0)),  # 1
        Vertex{T}(2, SVector{3,T}(1, 1, 0)),  # 2
        Vertex{T}(3, SVector{3,T}(0, 1, 0)),  # 3
        Vertex{T}(4, SVector{3,T}(0, 0, 1)),  # 4
        Vertex{T}(5, SVector{3,T}(1, 0, 1)),  # 5
        Vertex{T}(6, SVector{3,T}(1, 1, 1)),  # 6
        Vertex{T}(7, SVector{3,T}(0, 1, 1))   # 7
    ]
    
    # Single hex block
    blocks = [
        Block{T}(
            1,
            [0, 1, 2, 3, 4, 5, 6, 7],  # OpenFOAM vertex ordering
            SVector{3,Int}(10, 10, 10),  # 10x10x10 cells
            SVector{3,T}(1.0, 1.0, 1.0)  # Uniform grading
        )
    ]
    
    # No curved edges for simple cube
    edges = AbstractEdge[]
    
    # Boundary patches
    boundaries = [
        BoundaryPatch("inlet", "patch", [[0, 4, 7, 3]]),
        BoundaryPatch("outlet", "patch", [[1, 2, 6, 5]]),
        BoundaryPatch("walls", "wall", [
            [0, 1, 5, 4],  # bottom
            [3, 7, 6, 2],  # top
            [0, 3, 2, 1],  # front
            [4, 5, 6, 7]   # back
        ])
    ]
    
    return BlockMeshDict{T}(
        1.0,  # convertToMeters
        vertices,
        blocks,
        edges,
        boundaries,
        nothing  # default patch
    )
end

"""
Check if BlockMeshDict represents a simple unit cube
"""
function is_unit_cube_dict(block_dict::BlockMeshDict{T}) where T
    # Check single block
    if length(block_dict.blocks) != 1
        return false
    end
    
    # Check vertices for unit cube
    if length(block_dict.vertices) != 8
        return false
    end
    
    # Check if vertices form a unit cube (within tolerance)
    expected_vertices = [
        SVector{3,T}(0, 0, 0), SVector{3,T}(1, 0, 0),
        SVector{3,T}(1, 1, 0), SVector{3,T}(0, 1, 0),
        SVector{3,T}(0, 0, 1), SVector{3,T}(1, 0, 1),
        SVector{3,T}(1, 1, 1), SVector{3,T}(0, 1, 1)
    ]
    
    for (i, v) in enumerate(block_dict.vertices)
        found = false
        for expected in expected_vertices
            if norm(v.coords - expected) < 1e-10
                found = true
                break
            end
        end
        if !found
            return false
        end
    end
    
    return true
end

"""
Generate mesh from BlockMeshDict
"""
function generate_mesh(block_dict::BlockMeshDict{T}) where T
    @info "Generating mesh from blockMeshDict"
    
    # For simple unit cube cases, use ConsistentMesh for guaranteed correct face orientations
    if is_unit_cube_dict(block_dict)
        block = block_dict.blocks[1]
        nx, ny, nz = block.cells
        @info "Using ConsistentMesh for unit cube generation"
        return ConsistentMeshFixed.create_consistent_unit_cube_mesh(nx, ny, nz)
    end
    
    # For more complex cases, continue with original implementation
    block = block_dict.blocks[1]  # Take first block
    nx, ny, nz = block.cells
    
    @info "Generating $(nx)×$(ny)×$(nz) structured mesh"
    
    # Generate points
    points = Vector{SVector{3,T}}()
    point_map = Dict{Tuple{Int,Int,Int}, Int}()
    
    for k in 0:nz
        for j in 0:ny
            for i in 0:nx
                # Parametric coordinates in block
                xi = i / nx
                eta = j / ny
                zeta = k / nz
                
                # Trilinear interpolation in block with grading
                point = interpolate_in_block(block_dict.vertices, block.vertices, xi, eta, zeta, block.grading)
                
                push!(points, point)
                point_map[(i, j, k)] = length(points) - 1  # 0-based indexing
            end
        end
    end
    
    # Generate faces with proper connectivity
    faces = Vector{CFDCore.Face{T,3}}()
    face_id = 0
    face_map = Dict{Vector{Int}, Int}()  # Map sorted vertices to face ID
    
    # Generate cells
    cells = Vector{CFDCore.Cell{T,3}}()
    cell_id = 1
    
    for k in 0:(nz-1)
        for j in 0:(ny-1)
            for i in 0:(nx-1)
                # Get the 8 vertices of the cell (OpenFOAM ordering)
                v0 = point_map[(i, j, k)]
                v1 = point_map[(i+1, j, k)]
                v2 = point_map[(i+1, j+1, k)]
                v3 = point_map[(i, j+1, k)]
                v4 = point_map[(i, j, k+1)]
                v5 = point_map[(i+1, j, k+1)]
                v6 = point_map[(i+1, j+1, k+1)]
                v7 = point_map[(i, j+1, k+1)]
                
                # Define the 6 faces of the hexahedron (OpenFOAM ordering)
                face_vertices = [
                    [v0, v3, v2, v1],  # bottom (z=k)
                    [v4, v5, v6, v7],  # top (z=k+1)
                    [v0, v1, v5, v4],  # front (y=j)
                    [v2, v3, v7, v6],  # back (y=j+1)
                    [v0, v4, v7, v3],  # left (x=i)
                    [v1, v2, v6, v5]   # right (x=i+1)
                ]
                
                cell_faces = Int[]
                
                # Process each face
                for face_verts in face_vertices
                    sorted_verts = sort(face_verts)
                    key = sorted_verts
                    
                    if haskey(face_map, key)
                        # Face already exists - this becomes an internal face
                        existing_face_id = face_map[key]
                        existing_face = faces[existing_face_id + 1]
                        
                        # Create new face with neighbor information
                        updated_face = CFDCore.Face{T,3}(
                            existing_face.id,
                            existing_face.nodes,
                            existing_face.center,
                            existing_face.area,
                            existing_face.normal,
                            existing_face.owner,
                            cell_id,  # neighbor (1-based for Julia)
                            false  # no longer boundary
                        )
                        
                        faces[existing_face_id + 1] = updated_face
                        push!(cell_faces, existing_face_id)
                    else
                        # Create new face
                        face_center = sum(points[v+1] for v in face_verts) / 4
                        face_area, face_normal = calculate_face_geometry(points, face_verts)
                        
                        # Determine if boundary face
                        is_boundary = is_boundary_face(i, j, k, nx, ny, nz, face_verts, v0, v1, v2, v3, v4, v5, v6, v7)
                        
                        face = CFDCore.Face{T,3}(
                            face_id,
                            face_verts,
                            face_center,
                            face_area,
                            face_normal,
                            cell_id,  # owner (1-based for Julia)
                            -1,  # neighbor (-1 for now)
                            is_boundary
                        )
                        
                        push!(faces, face)
                        face_map[key] = face_id
                        push!(cell_faces, face_id)
                        face_id += 1
                    end
                end
                
                # Calculate cell center and volume
                cell_vertices = [v0, v1, v2, v3, v4, v5, v6, v7]
                center = sum(points[v+1] for v in cell_vertices) / 8  # +1 for 1-based indexing
                volume = calculate_hex_volume(points, cell_vertices)
                
                cell = CFDCore.Cell{T,3}(
                    cell_id,
                    cell_vertices,
                    cell_faces,
                    center,
                    volume
                )
                
                push!(cells, cell)
                cell_id += 1
            end
        end
    end
    
    # Generate boundary patches
    boundaries = generate_boundary_patches(block_dict, faces, nx, ny, nz, point_map)
    
    # Generate nodes
    nodes = [CFDCore.Node{T,3}(i-1, points[i], false) for i in 1:length(points)]
    
    # Create neighbor connectivity
    neighbor_connectivity = create_neighbor_connectivity(faces)
    
    # Create UnstructuredMesh
    mesh = CFDCore.UnstructuredMesh{T,3}(
        nodes,
        faces,
        cells,
        boundaries,
        neighbor_connectivity,
        create_owner_neighbor_pairs(faces),
        (minimum(points), maximum(points))  # bounding box
    )
    
    @info "Generated mesh with $(length(cells)) cells, $(length(faces)) faces, $(length(points)) points"
    
    return mesh
end

"""
Trilinear interpolation within a block with grading support
"""
function interpolate_in_block(vertices::Vector{Vertex{T}}, vertex_indices::Vector{Int}, 
                             xi::T, eta::T, zeta::T, grading::Union{SVector{3,T}, Vector{T}}=SVector{3,T}(1,1,1)) where T
    # Apply grading to parametric coordinates
    xi_graded = apply_grading(xi, grading[1])
    eta_graded = apply_grading(eta, grading[2])
    zeta_graded = apply_grading(zeta, grading[3])
    
    # Get the 8 corner vertices of the block
    v = [vertices[idx+1].coords for idx in vertex_indices]  # +1 for 1-based indexing
    
    # Trilinear interpolation with graded coordinates
    point = (1-xi_graded)*(1-eta_graded)*(1-zeta_graded)*v[1] +
            xi_graded*(1-eta_graded)*(1-zeta_graded)*v[2] +
            xi_graded*eta_graded*(1-zeta_graded)*v[3] +
            (1-xi_graded)*eta_graded*(1-zeta_graded)*v[4] +
            (1-xi_graded)*(1-eta_graded)*zeta_graded*v[5] +
            xi_graded*(1-eta_graded)*zeta_graded*v[6] +
            xi_graded*eta_graded*zeta_graded*v[7] +
            (1-xi_graded)*eta_graded*zeta_graded*v[8]
    
    return point
end

"""
Apply grading function to parametric coordinate
"""
function apply_grading(xi::T, grading_factor::T) where T
    if abs(grading_factor - 1.0) < 1e-10
        return xi  # No grading
    elseif grading_factor > 1.0
        # Expansion towards end
        return xi^(1.0/grading_factor)
    else
        # Compression towards end
        return 1.0 - (1.0 - xi)^grading_factor
    end
end

"""
Interpolate point along curved edge if applicable
"""
function interpolate_along_edge(vertices::Vector{Vertex{T}}, edges::Vector{AbstractEdge}, 
                               start_vertex::Int, end_vertex::Int, t::T) where T
    # Find if there's a curved edge between these vertices
    for edge in edges
        if (edge isa ArcEdge && 
            ((edge.start_vertex == start_vertex && edge.end_vertex == end_vertex) ||
             (edge.start_vertex == end_vertex && edge.end_vertex == start_vertex)))
            
            # Interpolate along arc
            return interpolate_arc(vertices[start_vertex+1].coords, vertices[end_vertex+1].coords, 
                                 edge.center, t)
        elseif (edge isa SplineEdge && 
                ((edge.start_vertex == start_vertex && edge.end_vertex == end_vertex) ||
                 (edge.start_vertex == end_vertex && edge.end_vertex == start_vertex)))
            
            # Interpolate along spline
            return interpolate_spline(vertices[start_vertex+1].coords, vertices[end_vertex+1].coords,
                                    edge.intermediate_points, t)
        end
    end
    
    # Linear interpolation if no curved edge found
    start_point = vertices[start_vertex+1].coords
    end_point = vertices[end_vertex+1].coords
    return (1-t) * start_point + t * end_point
end

"""
Interpolate along circular arc
"""
function interpolate_arc(start_point::SVector{3,T}, end_point::SVector{3,T}, 
                        center::SVector{3,T}, t::T) where T
    # Vector from center to start
    r1 = start_point - center
    r2 = end_point - center
    
    # Calculate angle between vectors
    cos_theta = dot(r1, r2) / (norm(r1) * norm(r2))
    cos_theta = clamp(cos_theta, -1.0, 1.0)  # Ensure valid range
    theta = acos(cos_theta)
    
    # Rotation axis (cross product)
    axis = cross(r1, r2)
    if norm(axis) < 1e-12
        # Vectors are parallel, use linear interpolation
        return (1-t) * start_point + t * end_point
    end
    axis = normalize(axis)
    
    # Rodrigues' rotation formula
    angle = t * theta
    cos_angle = cos(angle)
    sin_angle = sin(angle)
    
    r_rotated = r1 * cos_angle + cross(axis, r1) * sin_angle + axis * dot(axis, r1) * (1 - cos_angle)
    
    return center + r_rotated
end

"""
Interpolate along spline curve
"""
function interpolate_spline(start_point::SVector{3,T}, end_point::SVector{3,T},
                           intermediate_points::Vector{SVector{3,T}}, t::T) where T
    # Simple Catmull-Rom spline implementation
    all_points = [start_point; intermediate_points; end_point]
    n = length(all_points)
    
    if n == 2
        return (1-t) * start_point + t * end_point
    end
    
    # Find segment
    segment_length = 1.0 / (n - 1)
    segment_index = min(floor(Int, t / segment_length) + 1, n - 1)
    local_t = (t - (segment_index - 1) * segment_length) / segment_length
    
    # Linear interpolation within segment (can be enhanced to cubic)
    return (1 - local_t) * all_points[segment_index] + local_t * all_points[segment_index + 1]
end

"""
Calculate volume of hexahedral cell using decomposition into tetrahedra
"""
function calculate_hex_volume(points::Vector{SVector{3,T}}, vertex_indices::Vector{Int}) where T
    v = [points[idx+1] for idx in vertex_indices]  # +1 for 1-based indexing
    
    # For axis-aligned hexahedron (cube), use simple formula
    # If the hex is axis-aligned, volume = |dx| × |dy| × |dz|
    
    # Find bounding box of the hexahedron
    min_coords = minimum(v)
    max_coords = maximum(v)
    
    dx = max_coords[1] - min_coords[1]
    dy = max_coords[2] - min_coords[2] 
    dz = max_coords[3] - min_coords[3]
    
    # For axis-aligned hex, this gives the exact volume
    volume = abs(dx * dy * dz)
    
    # Verify this is indeed axis-aligned (all faces parallel to coordinate planes)
    if volume > 0
        return volume
    end
    
    # Fallback: use proper decomposition into 6 tetrahedra for general case
    # Corrected decomposition that doesn't overlap
    volume = 0.0
    
    # Standard tetrahedral decomposition of hexahedron
    # Using vertices in OpenFOAM order: 0-3 bottom, 4-7 top
    tetrahedra = [
        [1, 2, 3, 5],  # tet 1
        [1, 3, 4, 5],  # tet 2  
        [3, 4, 7, 8],  # tet 3
        [3, 5, 6, 7],  # tet 4
        [5, 6, 7, 8],  # tet 5
        [3, 5, 7, 8]   # tet 6
    ]
    
    for tet in tetrahedra
        # Calculate tetrahedron volume
        a = v[tet[2]] - v[tet[1]]
        b = v[tet[3]] - v[tet[1]]
        c = v[tet[4]] - v[tet[1]]
        vol_tet = abs(dot(a, cross(b, c))) / 6.0
        volume += vol_tet
    end
    
    return volume
end

"""
Calculate face geometry (area and normal)
"""
function calculate_face_geometry(points::Vector{SVector{3,T}}, vertex_indices::Vector{Int}) where T
    v = [points[idx+1] for idx in vertex_indices]  # +1 for 1-based indexing
    
    # For quad face, split into two triangles and sum areas
    # Triangle 1: v1, v2, v3
    # Triangle 2: v1, v3, v4
    
    # Triangle 1
    a1 = v[2] - v[1]
    b1 = v[3] - v[1]
    normal1 = cross(a1, b1)
    area1 = norm(normal1) / 2.0
    
    # Triangle 2  
    a2 = v[3] - v[1]
    b2 = v[4] - v[1]
    normal2 = cross(a2, b2)
    area2 = norm(normal2) / 2.0
    
    total_area = area1 + area2
    
    # Average normal (should be same direction for planar quad)
    total_normal = normal1 + normal2
    if norm(total_normal) > 1e-12
        total_normal = normalize(total_normal)
    else
        total_normal = SVector{3,T}(0, 0, 1)  # fallback
    end
    
    return total_area, total_normal
end

"""
Determine if a face is on the boundary of the block
"""
function is_boundary_face(i::Int, j::Int, k::Int, nx::Int, ny::Int, nz::Int, 
                         face_verts::Vector{Int}, v0::Int, v1::Int, v2::Int, v3::Int, 
                         v4::Int, v5::Int, v6::Int, v7::Int)
    # Check if face is on boundary by comparing with cell vertex pattern
    face_set = Set(face_verts)
    
    # Define boundary faces
    bottom_face = Set([v0, v3, v2, v1])  # k = 0
    top_face = Set([v4, v5, v6, v7])     # k = nz
    front_face = Set([v0, v1, v5, v4])   # j = 0  
    back_face = Set([v2, v3, v7, v6])    # j = ny
    left_face = Set([v0, v4, v7, v3])    # i = 0
    right_face = Set([v1, v2, v6, v5])   # i = nx
    
    is_boundary = false
    
    if face_set == bottom_face && k == 0
        is_boundary = true
    elseif face_set == top_face && k == nz-1
        is_boundary = true  
    elseif face_set == front_face && j == 0
        is_boundary = true
    elseif face_set == back_face && j == ny-1
        is_boundary = true
    elseif face_set == left_face && i == 0
        is_boundary = true
    elseif face_set == right_face && i == nx-1
        is_boundary = true
    end
    
    return is_boundary
end

"""
Generate boundary patches from BlockMeshDict specification
"""
function generate_boundary_patches(block_dict::BlockMeshDict{T}, faces::Vector{CFDCore.Face{T,3}}, 
                                 nx::Int, ny::Int, nz::Int, point_map::Dict{Tuple{Int,Int,Int}, Int}) where T
    boundaries = Dict{String, Vector{Int}}()
    
    # Get block corner vertices for geometric identification
    block_vertices = [v.coords for v in block_dict.vertices]
    
    for patch in block_dict.boundaries
        patch_faces = Int[]
        
        # For each face definition in the patch
        for face_vertices_indices in patch.faces
            # Get the 3D coordinates of the patch face vertices from block definition
            patch_face_coords = [block_vertices[idx + 1] for idx in face_vertices_indices]  # +1 for 1-based indexing
            
            # Calculate the center and normal of the expected patch face
            patch_center = sum(patch_face_coords) / length(patch_face_coords)
            
            # For quad faces, calculate normal using cross product
            if length(patch_face_coords) == 4
                v1 = patch_face_coords[2] - patch_face_coords[1]
                v2 = patch_face_coords[4] - patch_face_coords[1]
                patch_normal = normalize(cross(v1, v2))
            else
                # Fallback for non-quad faces
                v1 = patch_face_coords[2] - patch_face_coords[1]
                v2 = patch_face_coords[3] - patch_face_coords[1]
                patch_normal = normalize(cross(v1, v2))
            end
            
            # Find all boundary faces that belong to this patch by geometric position
            for (face_id, face) in enumerate(faces)
                if face.boundary
                    # Check if face is on the same plane as the patch
                    face_to_patch = face.center - patch_center
                    
                    # Face is on the patch if:
                    # 1. The vector from patch center to face center is nearly perpendicular to patch normal
                    # 2. OR the face center is very close to the patch plane
                    
                    distance_to_plane = abs(dot(face_to_patch, patch_normal))
                    parallel_distance = norm(face_to_patch - dot(face_to_patch, patch_normal) * patch_normal)
                    
                    # Tolerance based on mesh size
                    tolerance = 1.0 / min(nx, ny, nz) * 0.1  # 10% of minimum cell size
                    
                    if distance_to_plane < tolerance
                        # Check if face is within the bounds of the patch
                        # For a unit cube, check coordinate ranges
                        is_on_patch = true
                        
                        # Determine which face this is based on normal direction
                        if abs(patch_normal[1]) > 0.9  # X-direction face (inlet/outlet)
                            # Check Y and Z coordinates are within bounds
                            patch_y_min = minimum(coord[2] for coord in patch_face_coords)
                            patch_y_max = maximum(coord[2] for coord in patch_face_coords)
                            patch_z_min = minimum(coord[3] for coord in patch_face_coords)
                            patch_z_max = maximum(coord[3] for coord in patch_face_coords)
                            
                            if !(patch_y_min - tolerance <= face.center[2] <= patch_y_max + tolerance &&
                                 patch_z_min - tolerance <= face.center[3] <= patch_z_max + tolerance)
                                is_on_patch = false
                            end
                            
                        elseif abs(patch_normal[2]) > 0.9  # Y-direction face (front/back)
                            # Check X and Z coordinates are within bounds
                            patch_x_min = minimum(coord[1] for coord in patch_face_coords)
                            patch_x_max = maximum(coord[1] for coord in patch_face_coords)
                            patch_z_min = minimum(coord[3] for coord in patch_face_coords)
                            patch_z_max = maximum(coord[3] for coord in patch_face_coords)
                            
                            if !(patch_x_min - tolerance <= face.center[1] <= patch_x_max + tolerance &&
                                 patch_z_min - tolerance <= face.center[3] <= patch_z_max + tolerance)
                                is_on_patch = false
                            end
                            
                        elseif abs(patch_normal[3]) > 0.9  # Z-direction face (top/bottom)
                            # Check X and Y coordinates are within bounds
                            patch_x_min = minimum(coord[1] for coord in patch_face_coords)
                            patch_x_max = maximum(coord[1] for coord in patch_face_coords)
                            patch_y_min = minimum(coord[2] for coord in patch_face_coords)
                            patch_y_max = maximum(coord[2] for coord in patch_face_coords)
                            
                            if !(patch_x_min - tolerance <= face.center[1] <= patch_x_max + tolerance &&
                                 patch_y_min - tolerance <= face.center[2] <= patch_y_max + tolerance)
                                is_on_patch = false
                            end
                        end
                        
                        if is_on_patch
                            push!(patch_faces, face_id)  # 1-based indexing for Julia
                        end
                    end
                end
            end
        end
        
        boundaries[patch.name] = patch_faces
    end
    
    return boundaries
end

"""
Create neighbor connectivity arrays
"""
function create_neighbor_connectivity(faces::Vector{CFDCore.Face{T,3}}) where T
    internal_faces = Int[]
    boundary_faces = Int[]
    
    for (i, face) in enumerate(faces)
        if face.boundary
            push!(boundary_faces, i - 1)  # 0-based
        else
            push!(internal_faces, i - 1)  # 0-based
        end
    end
    
    return [internal_faces, boundary_faces]
end

"""
Create owner-neighbor pairs for faces
"""
function create_owner_neighbor_pairs(faces::Vector{CFDCore.Face{T,3}}) where T
    pairs = Tuple{Int,Int}[]
    
    for face in faces
        if !face.boundary && face.neighbor != -1
            push!(pairs, (face.owner, face.neighbor))
        end
    end
    
    return pairs
end

"""
Create a simple unit cube blockMeshDict for testing
"""
function create_unit_cube_dict(nx::Int=10, ny::Int=10, nz::Int=10)
    @info "Creating unit cube blockMeshDict with $(nx)×$(ny)×$(nz) cells"
    
    T = Float64
    
    # Unit cube vertices
    vertices = [
        Vertex{T}(0, SVector{3,T}(0, 0, 0)),
        Vertex{T}(1, SVector{3,T}(1, 0, 0)),
        Vertex{T}(2, SVector{3,T}(1, 1, 0)),
        Vertex{T}(3, SVector{3,T}(0, 1, 0)),
        Vertex{T}(4, SVector{3,T}(0, 0, 1)),
        Vertex{T}(5, SVector{3,T}(1, 0, 1)),
        Vertex{T}(6, SVector{3,T}(1, 1, 1)),
        Vertex{T}(7, SVector{3,T}(0, 1, 1))
    ]
    
    blocks = [
        Block{T}(
            1,
            [0, 1, 2, 3, 4, 5, 6, 7],
            SVector{3,Int}(nx, ny, nz),
            SVector{3,T}(1.0, 1.0, 1.0)
        )
    ]
    
    edges = AbstractEdge[]
    
    boundaries = [
        BoundaryPatch("inlet", "patch", [[0, 4, 7, 3]]),
        BoundaryPatch("outlet", "patch", [[1, 2, 6, 5]]),
        BoundaryPatch("walls", "wall", [
            [0, 1, 5, 4],
            [3, 7, 6, 2],
            [0, 3, 2, 1],
            [4, 5, 6, 7]
        ])
    ]
    
    return BlockMeshDict{T}(1.0, vertices, blocks, edges, boundaries, nothing)
end

# ============================================================================
# Mesh Quality Assessment
# ============================================================================

"""
Comprehensive mesh quality check
"""
function check_mesh_quality(mesh::CFDCore.UnstructuredMesh{T,3}) where T
    @info "Checking mesh quality..."
    
    quality_metrics = Dict{String, Any}()
    
    # Volume checks
    volumes = [cell.volume for cell in mesh.cells]
    quality_metrics["min_volume"] = minimum(volumes)
    quality_metrics["max_volume"] = maximum(volumes)
    quality_metrics["volume_ratio"] = maximum(volumes) / minimum(volumes)
    quality_metrics["negative_volumes"] = count(v -> v <= 0, volumes)
    
    @info "Volume metrics:"
    @info "  Min volume: $(quality_metrics["min_volume"])"
    @info "  Max volume: $(quality_metrics["max_volume"])"
    @info "  Volume ratio: $(quality_metrics["volume_ratio"])"
    @info "  Negative volumes: $(quality_metrics["negative_volumes"])"
    
    # Face checks
    areas = [face.area for face in mesh.faces]
    quality_metrics["min_area"] = minimum(areas)
    quality_metrics["max_area"] = maximum(areas)
    quality_metrics["area_ratio"] = maximum(areas) / minimum(areas)
    
    @info "Face metrics:"
    @info "  Min area: $(quality_metrics["min_area"])"
    @info "  Max area: $(quality_metrics["max_area"])"
    @info "  Area ratio: $(quality_metrics["area_ratio"])"
    
    # Aspect ratio check
    aspect_ratios = Float64[]
    for cell in mesh.cells
        ar = calculate_cell_aspect_ratio(mesh, cell)
        push!(aspect_ratios, ar)
    end
    
    quality_metrics["max_aspect_ratio"] = maximum(aspect_ratios)
    quality_metrics["avg_aspect_ratio"] = sum(aspect_ratios) / length(aspect_ratios)
    
    @info "Aspect ratio metrics:"
    @info "  Max aspect ratio: $(quality_metrics["max_aspect_ratio"])"
    @info "  Avg aspect ratio: $(quality_metrics["avg_aspect_ratio"])"
    
    # Quality assessment
    quality_metrics["quality_grade"] = assess_overall_quality(quality_metrics)
    @info "Overall mesh quality: $(quality_metrics["quality_grade"])"
    
    return quality_metrics
end

"""
Calculate cell aspect ratio
"""
function calculate_cell_aspect_ratio(mesh::CFDCore.UnstructuredMesh{T,3}, cell::CFDCore.Cell{T,3}) where T
    # Get cell vertices
    vertices = [mesh.nodes[v + 1].coords for v in cell.nodes]  # +1 for 1-based indexing
    
    # Calculate bounding box dimensions
    min_coords = reduce((a, b) -> min.(a, b), vertices)
    max_coords = reduce((a, b) -> max.(a, b), vertices)
    dimensions = max_coords - min_coords
    
    # Aspect ratio is max dimension / min dimension
    max_dim = maximum(dimensions)
    min_dim = minimum(dimensions[dimensions .> 1e-12])  # Avoid division by zero
    
    return min_dim > 1e-12 ? max_dim / min_dim : 1.0
end

"""
Assess overall mesh quality grade
"""
function assess_overall_quality(metrics::Dict{String, Any})
    score = 100.0
    
    # Penalize for negative volumes
    if metrics["negative_volumes"] > 0
        score -= 50.0
    end
    
    # Penalize for high volume ratio
    if metrics["volume_ratio"] > 1000
        score -= 20.0
    elseif metrics["volume_ratio"] > 100
        score -= 10.0
    end
    
    # Penalize for high aspect ratio
    if metrics["max_aspect_ratio"] > 100
        score -= 20.0
    elseif metrics["max_aspect_ratio"] > 10
        score -= 10.0
    end
    
    # Grade assessment
    if score >= 90
        return "Excellent"
    elseif score >= 80
        return "Good"
    elseif score >= 60
        return "Acceptable"
    elseif score >= 40
        return "Poor"
    else
        return "Unacceptable"
    end
end

end # module BlockMesh