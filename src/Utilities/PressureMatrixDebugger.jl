# src/Utilities/PressureMatrixDebugger.jl
"""
Pressure Matrix Debugging Toolkit

Specialized tools for debugging pressure correction matrix assembly issues
in SIMPLE/PISO algorithms. Focuses on:

1. Matrix structure validation
2. Boundary condition consistency  
3. Nullspace analysis for pure Neumann problems
4. Discrete Laplacian verification
5. Small grid manual inspection
"""

module PressureMatrixDebugger

using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf

# Optional dependencies
const HAS_ARPACK = try
    using Arpack
    true
catch
    @warn "Arpack.jl not available, eigenvalue analysis disabled"
    false
end

export debug_pressure_matrix, assemble_reference_poisson
export validate_pressure_bc, check_matrix_properties
export create_small_test_matrix, fix_pressure_reference

"""
    assemble_reference_poisson(nx, ny; dx=1.0, dy=1.0)

Assemble reference 2D Poisson matrix for comparison with CFD implementation.
Uses standard 5-point stencil with Dirichlet boundary conditions.
"""
function assemble_reference_poisson(nx, ny; dx=1.0, dy=1.0)
    println("Assembling reference $(nx)×$(ny) Poisson matrix...")
    
    N = nx * ny
    rows = Int[]
    cols = Int[]
    vals = Float64[]
    
    # Helper function to convert (i,j) to linear index
    idx(i, j) = (i-1) * ny + j
    
    # Assemble 5-point stencil
    for i in 1:nx
        for j in 1:ny
            p = idx(i, j)
            
            # Diagonal coefficient
            diag_coeff = 2.0/dx^2 + 2.0/dy^2
            push!(rows, p)
            push!(cols, p) 
            push!(vals, diag_coeff)
            
            # Off-diagonal coefficients
            # Left neighbor
            if i > 1
                push!(rows, p)
                push!(cols, idx(i-1, j))
                push!(vals, -1.0/dx^2)
            end
            
            # Right neighbor  
            if i < nx
                push!(rows, p)
                push!(cols, idx(i+1, j))
                push!(vals, -1.0/dx^2)
            end
            
            # Bottom neighbor
            if j > 1
                push!(rows, p)
                push!(cols, idx(i, j-1))
                push!(vals, -1.0/dy^2)
            end
            
            # Top neighbor
            if j < ny
                push!(rows, p)
                push!(cols, idx(i, j+1))
                push!(vals, -1.0/dy^2)
            end
        end
    end
    
    A = sparse(rows, cols, vals, N, N)
    
    println("Reference matrix: $(N)×$(N), $(nnz(A)) non-zeros")
    return A, (nx, ny)
end

"""
    check_matrix_properties(A::SparseMatrixCSC, name="Matrix")

Comprehensive matrix property analysis.
"""
function check_matrix_properties(A::SparseMatrixCSC, name="Matrix")
    println("\n=== $(name) PROPERTIES ===")
    
    n = size(A, 1)
    
    # Basic properties
    println("Size: $(n)×$(n)")
    println("Non-zeros: $(nnz(A))")
    println("Sparsity: $(round(100*(1 - nnz(A)/n^2), digits=2))%")
    
    # Symmetry check
    symmetry_error = norm(A - A', Inf)
    is_symmetric = symmetry_error < 1e-12
    println("Symmetric: $(is_symmetric) (error: $(symmetry_error))")
    
    # Diagonal properties
    diag_vals = diag(A)
    min_diag = minimum(diag_vals)
    max_diag = maximum(diag_vals)
    zero_diag = count(abs.(diag_vals) .< 1e-14)
    
    println("Diagonal range: [$(min_diag), $(max_diag)]")
    if zero_diag > 0
        @warn "$(zero_diag) zero diagonal entries detected!"
    end
    
    # Row sum analysis (important for Neumann BC)
    row_sums = [sum(A[i, :]) for i in 1:n]
    max_row_sum = maximum(abs.(row_sums))
    
    println("Max |row sum|: $(max_row_sum)")
    if max_row_sum < 1e-12
        println("  Matrix is consistent with pure Neumann BC (zero row sums)")
    end
    
    # Diagonal dominance
    off_diag_sums = [sum(abs, A[i, :]) - abs(A[i, i]) for i in 1:n]
    diag_dominance = diag_vals - off_diag_sums
    min_dominance = minimum(diag_dominance)
    
    println("Diagonal dominance: min = $(min_dominance)")
    if min_dominance < 0
        @warn "Matrix is not diagonally dominant!"
    end
    
    # Check for positive definiteness indicators
    if is_symmetric
        try
            # Try Cholesky decomposition
            chol(Matrix(A))
            println("Positive definite: YES (Cholesky successful)")
        catch
            println("Positive definite: NO (Cholesky failed)")
        end
    end
    
    return Dict(
        :symmetric => is_symmetric,
        :min_diagonal => min_diag,
        :max_diagonal => max_diag,
        :zero_diagonals => zero_diag,
        :max_row_sum => max_row_sum,
        :min_diag_dominance => min_dominance
    )
end

"""
    create_small_test_matrix(mesh, gamma_field=nothing)

Create and inspect pressure matrix for small mesh to manually verify assembly.
"""
function create_small_test_matrix(mesh, gamma_field=nothing)
    println("\n=== SMALL MATRIX INSPECTION ===")
    
    num_cells = length(mesh.cells)
    
    if num_cells > 100
        @warn "Mesh too large for detailed inspection ($(num_cells) cells)"
        return nothing
    end
    
    println("Creating $(num_cells)×$(num_cells) pressure matrix...")
    
    # This would call the actual FVM matrix assembly
    # For now, create a placeholder structure
    A = spzeros(num_cells, num_cells)
    
    # Fill with simple Laplacian pattern
    for i in 1:num_cells
        A[i, i] = 4.0  # Diagonal
        
        # Add off-diagonals based on mesh connectivity
        # This is a simplified placeholder
        if i > 1
            A[i, i-1] = -1.0
        end
        if i < num_cells
            A[i, i+1] = -1.0
        end
    end
    
    # Convert to dense for inspection
    A_dense = Matrix(A)
    
    println("Matrix structure:")
    if num_cells <= 10
        # Print full matrix for very small cases
        for i in 1:num_cells
            row_str = "Row $i: "
            for j in 1:num_cells
                row_str *= @sprintf("%8.2f ", A_dense[i, j])
            end
            println(row_str)
        end
    else
        # Print diagonal and first/last rows
        println("Diagonal: $(diag(A_dense))")
        println("First row: $(A_dense[1, :])")
        println("Last row: $(A_dense[end, :])")
    end
    
    # Check properties
    props = check_matrix_properties(A, "Small Test Matrix")
    
    return A, props
end

"""
    validate_pressure_bc(mesh, boundary_conditions)

Validate pressure boundary condition consistency.
"""
function validate_pressure_bc(mesh, boundary_conditions)
    println("\n=== PRESSURE BOUNDARY CONDITION VALIDATION ===")
    
    # Count boundary condition types
    dirichlet_patches = String[]
    neumann_patches = String[]
    
    for (patch_name, bc) in boundary_conditions
        if isa(bc, DirichletBC)
            push!(dirichlet_patches, patch_name)
        elseif isa(bc, NeumannBC)
            push!(neumann_patches, patch_name)
        else
            @warn "Unknown BC type for patch $(patch_name): $(typeof(bc))"
        end
    end
    
    println("Dirichlet patches: $(dirichlet_patches)")
    println("Neumann patches: $(neumann_patches)")
    
    has_dirichlet = !isempty(dirichlet_patches)
    has_neumann = !isempty(neumann_patches)
    
    if !has_dirichlet && !has_neumann
        @error "No boundary conditions specified!"
        return false
    elseif !has_dirichlet && has_neumann
        @warn "Pure Neumann problem - pressure reference needed"
        println("Consider fixing one point or using relative pressure")
        return "pure_neumann"
    elseif has_dirichlet && !has_neumann
        println("Pure Dirichlet problem - should be well-posed")
        return true
    else
        println("Mixed BC problem - should be well-posed")
        return true
    end
end

"""
    fix_pressure_reference!(A, b, ref_cell=1, ref_value=0.0)

Fix pressure reference for pure Neumann problems by setting one cell value.
"""
function fix_pressure_reference!(A, b, ref_cell=1, ref_value=0.0)
    println("Fixing pressure reference at cell $(ref_cell) = $(ref_value)")
    
    n = size(A, 1)
    
    if ref_cell < 1 || ref_cell > n
        error("Reference cell $(ref_cell) out of range [1, $(n)]")
    end
    
    # Clear the reference row and set to identity
    A[ref_cell, :] .= 0.0
    A[ref_cell, ref_cell] = 1.0
    b[ref_cell] = ref_value
    
    println("Modified row $(ref_cell) for pressure reference")
    return A, b
end

"""
    debug_pressure_matrix(A, b, mesh; 
                         check_eigenvalues=true,
                         fix_reference=true,
                         test_solve=true)

Main pressure matrix debugging function.
"""
function debug_pressure_matrix(A, b, mesh; 
                              check_eigenvalues=true,
                              fix_reference=true, 
                              test_solve=true)
    println("🔍 PRESSURE MATRIX DEBUGGING")
    println("="^40)
    
    debug_info = Dict{Symbol, Any}()
    
    # 1. Basic matrix properties
    props = check_matrix_properties(A, "Pressure Matrix")
    debug_info[:matrix_properties] = props
    
    # 2. Check if matrix needs pressure reference
    needs_reference = props[:max_row_sum] < 1e-12
    debug_info[:needs_pressure_reference] = needs_reference
    
    if needs_reference
        println("\n⚠️  Pure Neumann detected - matrix is singular")
        
        if fix_reference
            A_fixed, b_fixed = fix_pressure_reference!(copy(A), copy(b))
            props_fixed = check_matrix_properties(A_fixed, "Fixed Matrix")
            debug_info[:fixed_matrix_properties] = props_fixed
            A_test, b_test = A_fixed, b_fixed
        else
            A_test, b_test = A, b
        end
    else
        A_test, b_test = A, b
    end
    
    # 3. Eigenvalue analysis (if requested and available)
    if check_eigenvalues && HAS_ARPACK
        try
            n = size(A_test, 1)
            if n > 10
                eigs_result = eigs(A_test; nev=min(6, n-2), which=:SM)
                eigenvals = real(eigs_result[1])
                sort!(eigenvals)
                
                println("\nSmallest eigenvalues: $(eigenvals)")
                debug_info[:eigenvalues] = eigenvals
                
                min_eigenval = minimum(eigenvals)
                if min_eigenval < -1e-12
                    @error "Negative eigenvalues! Matrix is indefinite: λ_min = $(min_eigenval)"
                    debug_info[:matrix_definite] = false
                else
                    debug_info[:matrix_definite] = true
                    if min_eigenval < 1e-12
                        println("Near-zero eigenvalue (expected for fixed Neumann): λ_min = $(min_eigenval)")
                    end
                end
            end
        catch e
            @warn "Eigenvalue analysis failed: $e"
        end
    end
    
    # 4. Test linear solve
    if test_solve
        try
            x = A_test \ b_test
            residual = norm(A_test * x - b_test)
            
            println("\nDirect solve test:")
            println("  Solution norm: $(norm(x))")
            println("  Residual: $(residual)")
            
            debug_info[:solve_successful] = true
            debug_info[:solve_residual] = residual
            debug_info[:solution_norm] = norm(x)
            
            if residual > 1e-10
                @warn "High residual in direct solve: $(residual)"
            end
            
        catch e
            @error "Direct solve failed: $e"
            debug_info[:solve_successful] = false
            debug_info[:solve_error] = string(e)
        end
    end
    
    # 5. Recommendations
    println("\n=== RECOMMENDATIONS ===")
    
    if !props[:symmetric]
        @warn "Matrix is not symmetric - check discretization"
    end
    
    if props[:zero_diagonals] > 0
        @error "Zero diagonal entries detected - check matrix assembly"
    end
    
    if props[:min_diag_dominance] < 0
        @warn "Matrix not diagonally dominant - may affect iterative solvers"
    end
    
    if needs_reference && !fix_reference
        println("→ Use fix_pressure_reference! to handle pure Neumann problem")
    end
    
    if haskey(debug_info, :matrix_definite) && !debug_info[:matrix_definite]
        println("→ Check sign conventions in discretization")
        println("→ Verify boundary condition implementation")
    end
    
    println("\n✅ Pressure matrix analysis complete")
    return debug_info
end

end # module PressureMatrixDebugger