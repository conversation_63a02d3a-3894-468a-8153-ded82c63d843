"""
# Mesh Doctor - Comprehensive Mesh Diagnostic Utility

This module provides comprehensive diagnostic tools for mesh analysis,
validation, and health checking. It goes beyond basic validation to provide
detailed insights into mesh quality, potential issues, and recommendations.
"""
module MeshDoctor

using LinearAlgebra
using StaticArrays
using Statistics
using Printf
using ...CFDCore

export mesh_doctor, diagnose_mesh, mesh_health_report, mesh_quality_metrics
export diagnose_connectivity, diagnose_geometry, diagnose_boundaries
export check_mesh_orthogonality, check_mesh_skewness, check_aspect_ratios
export analyze_mesh_distribution, find_problematic_cells

"""
    mesh_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)

Comprehensive mesh health check and diagnostic tool.
Performs all available mesh diagnostics and provides recommendations.
"""
function mesh_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)
    if verbose
        println("🏥" * "="^70)
        println("🏥 MESH DOCTOR - COMPREHENSIVE DIAGNOSTIC")
        println("🏥" * "="^70)
        println("📊 Mesh: $(length(mesh.cells)) cells, $(length(mesh.faces)) faces, $(length(mesh.nodes)) nodes")
        println()
    end
    
    # Initialize report
    report = Dict{Symbol, Any}()
    
    # 1. Basic connectivity and topology
    if verbose; println("🔍 1. CONNECTIVITY AND TOPOLOGY ANALYSIS"); end
    connectivity_report = diagnose_connectivity(mesh, verbose=detailed)
    report[:connectivity] = connectivity_report
    
    # 2. Geometric quality analysis
    if verbose; println("\n🔍 2. GEOMETRIC QUALITY ANALYSIS"); end
    geometry_report = diagnose_geometry(mesh, verbose=detailed)
    report[:geometry] = geometry_report
    
    # 3. Boundary condition analysis
    if verbose; println("\n🔍 3. BOUNDARY CONDITION ANALYSIS"); end
    boundary_report = diagnose_boundaries(mesh, verbose=detailed)
    report[:boundaries] = boundary_report
    
    # 4. Mesh quality metrics
    if verbose; println("\n🔍 4. MESH QUALITY METRICS"); end
    quality_report = mesh_quality_metrics(mesh, verbose=detailed)
    report[:quality] = quality_report
    
    # 5. Face orientation and conservation
    if verbose; println("\n🔍 5. FACE ORIENTATION AND CONSERVATION"); end
    orientation_report = diagnose_face_orientations(mesh, verbose=detailed)
    report[:orientation] = orientation_report
    
    # 6. Performance analysis
    if verbose; println("\n🔍 6. PERFORMANCE CHARACTERISTICS"); end
    performance_report = analyze_mesh_performance(mesh, verbose=detailed)
    report[:performance] = performance_report
    
    # Generate overall health score and recommendations
    health_score, recommendations = generate_health_assessment(report)
    report[:health_score] = health_score
    report[:recommendations] = recommendations
    
    if verbose
        println("\n🏥" * "="^70)
        println("🏥 MESH HEALTH ASSESSMENT")
        println("🏥" * "="^70)
        
        # Color-coded health score
        if health_score >= 90
            println("💚 EXCELLENT HEALTH: $(health_score)/100")
        elseif health_score >= 75
            println("💛 GOOD HEALTH: $(health_score)/100")
        elseif health_score >= 60
            println("🧡 FAIR HEALTH: $(health_score)/100")
        else
            println("❤️ POOR HEALTH: $(health_score)/100")
        end
        
        if !isempty(recommendations)
            println("\n📋 RECOMMENDATIONS:")
            for (i, rec) in enumerate(recommendations)
                println("   $i. $rec")
            end
        else
            println("\n✅ No specific recommendations - mesh is in excellent condition!")
        end
        
        println("🏥" * "="^70)
    end
    
    return report
end

"""
    diagnose_connectivity(mesh::UnstructuredMesh; verbose=false)

Deep analysis of mesh connectivity patterns and topological consistency.
"""
function diagnose_connectivity(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Face-cell connectivity analysis
    face_connectivity = Dict{Int, Vector{Int}}()
    for (i, face) in enumerate(mesh.faces)
        cells = face.boundary ? [face.owner] : [face.owner, face.neighbor]
        face_connectivity[i] = cells
    end
    
    # Cell-face connectivity analysis
    cell_face_count = zeros(Int, length(mesh.cells))
    for (face_id, cells) in face_connectivity
        for cell_id in cells
            if cell_id > 0
                cell_face_count[cell_id] += 1
            end
        end
    end
    
    # Check for cells with wrong number of faces
    expected_faces = 6  # For hexahedral cells
    problematic_cells = findall(x -> x != expected_faces, cell_face_count)
    
    if !isempty(problematic_cells)
        push!(issues, "$(length(problematic_cells)) cells have incorrect face count")
        if verbose
            println("   ❌ Cells with wrong face count: $(length(problematic_cells))")
            for cell_id in problematic_cells[1:min(5, end)]
                println("      Cell $cell_id: $(cell_face_count[cell_id]) faces (expected $expected_faces)")
            end
            if length(problematic_cells) > 5
                println("      ... and $(length(problematic_cells) - 5) more")
            end
        end
    else
        if verbose; println("   ✅ All cells have correct face count"); end
    end
    
    # Check for orphaned faces
    referenced_faces = Set{Int}()
    for cell in mesh.cells
        union!(referenced_faces, cell.faces)
    end
    
    all_faces = Set(1:length(mesh.faces))
    orphaned_faces = setdiff(all_faces, referenced_faces)
    
    if !isempty(orphaned_faces)
        push!(issues, "$(length(orphaned_faces)) orphaned faces detected")
        if verbose
            println("   ❌ Orphaned faces: $(length(orphaned_faces))")
        end
    else
        if verbose; println("   ✅ No orphaned faces"); end
    end
    
    # Check face-neighbor consistency
    neighbor_errors = 0
    for face in mesh.faces
        if !face.boundary && face.neighbor > 0
            if face.owner >= face.neighbor
                neighbor_errors += 1
            end
        end
    end
    
    if neighbor_errors > 0
        push!(issues, "$neighbor_errors faces violate owner < neighbor rule")
        if verbose
            println("   ❌ Face ordering violations: $neighbor_errors")
        end
    else
        if verbose; println("   ✅ Face ordering correct (owner < neighbor)"); end
    end
    
    report[:face_connectivity] = face_connectivity
    report[:cell_face_count] = cell_face_count
    report[:problematic_cells] = problematic_cells
    report[:orphaned_faces] = collect(orphaned_faces)
    report[:neighbor_errors] = neighbor_errors
    report[:issues] = issues
    
    return report
end

"""
    diagnose_geometry(mesh::UnstructuredMesh; verbose=false)

Comprehensive geometric analysis including quality metrics.
"""
function diagnose_geometry(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Volume analysis
    volumes = [cell.volume for cell in mesh.cells]
    min_vol, max_vol = extrema(volumes)
    mean_vol = mean(volumes)
    vol_ratio = max_vol / min_vol
    
    if vol_ratio > 1000
        push!(issues, "Extreme volume variation (ratio: $(vol_ratio:.1f))")
    end
    
    # Negative volume check
    negative_volumes = findall(v -> v <= 0, volumes)
    if !isempty(negative_volumes)
        push!(issues, "$(length(negative_volumes)) cells with non-positive volume")
    end
    
    if verbose
        println("   📏 Volume Statistics:")
        println("      Min: $(min_vol:.6e), Max: $(max_vol:.6e)")
        println("      Mean: $(mean_vol:.6e), Ratio: $(vol_ratio:.2f)")
        if !isempty(negative_volumes)
            println("   ❌ Non-positive volumes: $(length(negative_volumes))")
        else
            println("   ✅ All volumes positive")
        end
    end
    
    # Face area analysis
    face_areas = [face.area for face in mesh.faces]
    min_area, max_area = extrema(face_areas)
    area_ratio = max_area / min_area
    
    if area_ratio > 1000
        push!(issues, "Extreme face area variation (ratio: $(area_ratio:.1f))")
    end
    
    # Zero area check
    zero_areas = findall(a -> a <= 1e-14, face_areas)
    if !isempty(zero_areas)
        push!(issues, "$(length(zero_areas)) faces with near-zero area")
    end
    
    if verbose
        println("   📐 Face Area Statistics:")
        println("      Min: $(min_area:.6e), Max: $(max_area:.6e)")
        println("      Ratio: $(area_ratio:.2f)")
        if !isempty(zero_areas)
            println("   ❌ Near-zero areas: $(length(zero_areas))")
        else
            println("   ✅ All face areas positive")
        end
    end
    
    # Aspect ratio analysis
    aspect_ratios = calculate_aspect_ratios(mesh)
    max_aspect = maximum(aspect_ratios)
    high_aspect = count(ar -> ar > 10, aspect_ratios)
    
    if max_aspect > 100
        push!(issues, "Extreme aspect ratios detected (max: $(max_aspect:.1f))")
    elseif high_aspect > length(mesh.cells) * 0.1
        push!(issues, "$high_aspect cells with high aspect ratio (>10)")
    end
    
    if verbose
        println("   📊 Aspect Ratios:")
        println("      Max: $(max_aspect:.2f)")
        println("      High (>10): $high_aspect cells")
    end
    
    report[:volumes] = volumes
    report[:volume_ratio] = vol_ratio
    report[:negative_volumes] = negative_volumes
    report[:face_areas] = face_areas
    report[:area_ratio] = area_ratio
    report[:zero_areas] = zero_areas
    report[:aspect_ratios] = aspect_ratios
    report[:max_aspect_ratio] = max_aspect
    report[:issues] = issues
    
    return report
end

"""
    diagnose_boundaries(mesh::UnstructuredMesh; verbose=false)

Analyze boundary patch definitions and consistency.
"""
function diagnose_boundaries(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Count boundary faces
    boundary_faces = [f for f in mesh.faces if f.boundary]
    total_boundary = length(boundary_faces)
    
    # Analyze patches
    patch_stats = Dict{String, Dict{Symbol, Any}}()
    total_patch_faces = 0
    
    for (patch_name, face_ids) in mesh.boundaries
        patch_faces = length(face_ids)
        total_patch_faces += patch_faces
        
        # Check for invalid face indices
        invalid_indices = [id for id in face_ids if id < 1 || id > length(mesh.faces)]
        
        # Check if faces are actually boundary faces
        non_boundary = [id for id in face_ids if id > 0 && id <= length(mesh.faces) && !mesh.faces[id].boundary]
        
        patch_stats[patch_name] = Dict(
            :face_count => patch_faces,
            :invalid_indices => invalid_indices,
            :non_boundary_faces => non_boundary
        )
        
        if !isempty(invalid_indices)
            push!(issues, "Patch '$patch_name' contains invalid face indices")
        end
        
        if !isempty(non_boundary)
            push!(issues, "Patch '$patch_name' contains non-boundary faces")
        end
    end
    
    # Check for unassigned boundary faces
    assigned_faces = Set{Int}()
    for face_ids in values(mesh.boundaries)
        union!(assigned_faces, face_ids)
    end
    
    boundary_face_ids = Set(i for (i, f) in enumerate(mesh.faces) if f.boundary)
    unassigned = setdiff(boundary_face_ids, assigned_faces)
    
    if !isempty(unassigned)
        push!(issues, "$(length(unassigned)) boundary faces not assigned to any patch")
    end
    
    # Check for double-assigned faces
    all_assigned = Int[]
    for face_ids in values(mesh.boundaries)
        append!(all_assigned, face_ids)
    end
    
    double_assigned = Int[]
    for face_id in unique(all_assigned)
        if count(==(face_id), all_assigned) > 1
            push!(double_assigned, face_id)
        end
    end
    
    if !isempty(double_assigned)
        push!(issues, "$(length(double_assigned)) faces assigned to multiple patches")
    end
    
    if verbose
        println("   🏷️  Boundary Patch Analysis:")
        println("      Total boundary faces: $total_boundary")
        println("      Faces in patches: $total_patch_faces")
        
        for (patch_name, stats) in patch_stats
            println("      📦 $patch_name: $(stats[:face_count]) faces")
            if !isempty(stats[:invalid_indices])
                println("         ❌ Invalid indices: $(length(stats[:invalid_indices]))")
            end
            if !isempty(stats[:non_boundary_faces])
                println("         ❌ Non-boundary faces: $(length(stats[:non_boundary_faces]))")
            end
        end
        
        if !isempty(unassigned)
            println("      ❌ Unassigned boundary faces: $(length(unassigned))")
        end
        
        if !isempty(double_assigned)
            println("      ❌ Double-assigned faces: $(length(double_assigned))")
        end
        
        if isempty(issues)
            println("      ✅ All boundary patches correctly defined")
        end
    end
    
    report[:boundary_face_count] = total_boundary
    report[:patch_stats] = patch_stats
    report[:unassigned_faces] = collect(unassigned)
    report[:double_assigned_faces] = double_assigned
    report[:issues] = issues
    
    return report
end

"""
    mesh_quality_metrics(mesh::UnstructuredMesh; verbose=false)

Calculate advanced mesh quality metrics.
"""
function mesh_quality_metrics(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Orthogonality analysis
    orthogonality_scores = calculate_orthogonality(mesh)
    min_ortho = minimum(orthogonality_scores)
    poor_ortho = count(s -> s < 0.8, orthogonality_scores)
    
    if min_ortho < 0.3
        push!(issues, "Severe non-orthogonality detected (min: $(min_ortho:.3f))")
    elseif poor_ortho > length(mesh.faces) * 0.1
        push!(issues, "$poor_ortho faces with poor orthogonality (<0.8)")
    end
    
    # Skewness analysis
    skewness_values = calculate_skewness(mesh)
    max_skew = maximum(skewness_values)
    high_skew = count(s -> s > 0.8, skewness_values)
    
    if max_skew > 0.95
        push!(issues, "Extreme skewness detected (max: $(max_skew:.3f))")
    elseif high_skew > length(mesh.cells) * 0.05
        push!(issues, "$high_skew cells with high skewness (>0.8)")
    end
    
    if verbose
        println("   📊 Quality Metrics:")
        println("      Orthogonality - Min: $(min_ortho:.3f), Poor (<0.8): $poor_ortho")
        println("      Skewness - Max: $(max_skew:.3f), High (>0.8): $high_skew")
    end
    
    report[:orthogonality] = orthogonality_scores
    report[:min_orthogonality] = min_ortho
    report[:poor_orthogonality_count] = poor_ortho
    report[:skewness] = skewness_values
    report[:max_skewness] = max_skew
    report[:high_skewness_count] = high_skew
    report[:issues] = issues
    
    return report
end

"""
    diagnose_face_orientations(mesh::UnstructuredMesh; verbose=false)

Detailed analysis of face orientations and conservation properties.
"""
function diagnose_face_orientations(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Face orientation consistency
    orientation_errors = 0
    for face in mesh.faces
        if !face.boundary && face.neighbor > 0
            d = mesh.cells[face.neighbor].center - mesh.cells[face.owner].center
            if dot(face.normal, d) <= 0
                orientation_errors += 1
            end
        end
    end
    
    if orientation_errors > 0
        push!(issues, "$orientation_errors faces with incorrect orientation")
    end
    
    # Conservation analysis
    internal_faces = [f for f in mesh.faces if !f.boundary]
    internal_area_sum = sum(f.normal * f.area for f in internal_faces)
    conservation_magnitude = norm(internal_area_sum)
    
    # This should be non-zero (expected behavior)
    if conservation_magnitude < 1e-14
        push!(issues, "Unexpected zero internal face area sum - possible mesh symmetry issue")
    end
    
    # Boundary area analysis
    boundary_faces = [f for f in mesh.faces if f.boundary]
    boundary_area_sum = sum(f.normal * f.area for f in boundary_faces)
    
    if verbose
        println("   🧭 Face Orientation Analysis:")
        println("      Orientation errors: $orientation_errors")
        println("      Internal area sum magnitude: $(conservation_magnitude:.6e)")
        println("      Boundary area sum: $(boundary_area_sum)")
        
        if orientation_errors == 0
            println("      ✅ All faces correctly oriented")
        end
        
        if conservation_magnitude > 1e-14
            println("      ✅ Non-zero area sum (expected for owner→neighbor orientation)")
        end
    end
    
    report[:orientation_errors] = orientation_errors
    report[:internal_area_sum] = internal_area_sum
    report[:conservation_magnitude] = conservation_magnitude
    report[:boundary_area_sum] = boundary_area_sum
    report[:issues] = issues
    
    return report
end

"""
    analyze_mesh_performance(mesh::UnstructuredMesh; verbose=false)

Analyze mesh characteristics that affect computational performance.
"""
function analyze_mesh_performance(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    warnings = String[]
    
    # Cell count analysis
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    n_internal = count(f -> !f.boundary, mesh.faces)
    
    # Memory estimation
    estimated_memory_mb = (n_cells * 8 * 10 + n_faces * 8 * 5) / 1024^2  # Rough estimate
    
    # Connectivity complexity
    max_neighbors = 0
    neighbor_counts = Int[]
    for cell in mesh.cells
        n_neighbors = 0
        for face_id in cell.faces
            face = mesh.faces[face_id]
            if !face.boundary
                n_neighbors += 1
            end
        end
        push!(neighbor_counts, n_neighbors)
        max_neighbors = max(max_neighbors, n_neighbors)
    end
    
    avg_neighbors = mean(neighbor_counts)
    
    # Performance warnings
    if n_cells > 1_000_000
        push!(warnings, "Large mesh (>1M cells) - consider parallel processing")
    end
    
    if estimated_memory_mb > 1000
        push!(warnings, "High memory usage estimated ($(estimated_memory_mb:.1f) MB)")
    end
    
    if max_neighbors > 26  # More than typical for structured mesh
        push!(warnings, "High connectivity complexity detected")
    end
    
    if verbose
        println("   ⚡ Performance Characteristics:")
        println("      Cells: $(n_cells), Faces: $(n_faces), Internal: $(n_internal)")
        println("      Est. memory: $(estimated_memory_mb:.1f) MB")
        println("      Avg neighbors/cell: $(avg_neighbors:.1f)")
        println("      Max neighbors: $max_neighbors")
        
        if !isempty(warnings)
            for warning in warnings
                println("      ⚠️  $warning")
            end
        end
    end
    
    report[:cell_count] = n_cells
    report[:face_count] = n_faces
    report[:internal_face_count] = n_internal
    report[:estimated_memory_mb] = estimated_memory_mb
    report[:neighbor_counts] = neighbor_counts
    report[:avg_neighbors] = avg_neighbors
    report[:max_neighbors] = max_neighbors
    report[:warnings] = warnings
    
    return report
end

# Helper functions for quality metrics

function calculate_aspect_ratios(mesh::UnstructuredMesh)
    ratios = Float64[]
    for cell in mesh.cells
        # Simple aspect ratio based on cell dimensions
        coords = [mesh.nodes[node_id].coords for node_id in cell.nodes]
        min_coord = minimum(coords)
        max_coord = maximum(coords)
        dimensions = max_coord - min_coord
        
        max_dim = maximum(dimensions)
        min_dim = minimum(filter(x -> x > 1e-12, dimensions))
        
        ratio = min_dim > 1e-12 ? max_dim / min_dim : 1000.0
        push!(ratios, ratio)
    end
    return ratios
end

function calculate_orthogonality(mesh::UnstructuredMesh)
    scores = Float64[]
    for face in mesh.faces
        if !face.boundary && face.neighbor > 0
            d = mesh.cells[face.neighbor].center - mesh.cells[face.owner].center
            d_norm = normalize(d)
            face_norm = normalize(face.normal)
            
            # Orthogonality score (1 = perfect, 0 = perpendicular)
            score = abs(dot(d_norm, face_norm))
            push!(scores, score)
        end
    end
    return scores
end

function calculate_skewness(mesh::UnstructuredMesh)
    skewness = Float64[]
    for cell in mesh.cells
        # Simple skewness metric based on center vs geometric center
        node_coords = [mesh.nodes[node_id].coords for node_id in cell.nodes]
        geometric_center = mean(node_coords)
        
        displacement = norm(cell.center - geometric_center)
        cell_size = norm(maximum(node_coords) - minimum(node_coords))
        
        skew = cell_size > 1e-12 ? displacement / cell_size : 0.0
        push!(skewness, min(skew, 1.0))  # Cap at 1.0
    end
    return skewness
end

function generate_health_assessment(report::Dict{Symbol, Any})
    score = 100.0
    recommendations = String[]
    
    # Deduct points for various issues
    for section_report in values(report)
        if haskey(section_report, :issues) && isa(section_report[:issues], Vector)
            score -= length(section_report[:issues]) * 5
        end
        if haskey(section_report, :warnings) && isa(section_report[:warnings], Vector)
            score -= length(section_report[:warnings]) * 2
        end
    end
    
    # Specific checks
    if haskey(report, :geometry)
        if get(report[:geometry], :volume_ratio, 1.0) > 1000
            score -= 20
            push!(recommendations, "Consider mesh refinement to reduce volume variation")
        end
        
        if get(report[:geometry], :max_aspect_ratio, 1.0) > 100
            score -= 15
            push!(recommendations, "Improve mesh quality to reduce extreme aspect ratios")
        end
    end
    
    if haskey(report, :quality)
        if get(report[:quality], :min_orthogonality, 1.0) < 0.3
            score -= 25
            push!(recommendations, "Critical: Severe non-orthogonality requires mesh reconstruction")
        elseif get(report[:quality], :min_orthogonality, 1.0) < 0.6
            score -= 10
            push!(recommendations, "Consider improving mesh orthogonality")
        end
        
        if get(report[:quality], :max_skewness, 0.0) > 0.9
            score -= 20
            push!(recommendations, "High skewness may cause convergence issues")
        end
    end
    
    if haskey(report, :orientation)
        if get(report[:orientation], :orientation_errors, 0) > 0
            score -= 30
            push!(recommendations, "Critical: Fix face orientation errors immediately")
        end
    end
    
    # Ensure score is within bounds
    score = max(0.0, min(100.0, score))
    
    return round(Int, score), recommendations
end

end  # module MeshDoctor