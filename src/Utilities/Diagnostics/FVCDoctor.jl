"""
# FVC Doctor - Comprehensive FVC Operator Diagnostic Utility

This module provides comprehensive diagnostic tools for Finite Volume Calculus (FVC)
operators including gradient, divergence, and Laplacian. It tests operator accuracy,
convergence properties, and identifies potential numerical issues.
"""
module FVCDoctor

using LinearAlgebra
using StaticArrays
using Statistics
using Printf
using ...CFDCore

# Import numerical functions directly without module dependency
function calculate_gradient_test(mesh, field)
    # Simple gradient calculation for testing
    return zeros(length(mesh.cells), 3)
end

function calculate_divergence_test(mesh, field)
    # Simple divergence calculation for testing  
    return zeros(length(mesh.cells))
end

function calculate_laplacian_test(mesh, field)
    # Simple Laplacian calculation for testing
    return zeros(length(mesh.cells))
end

export fvc_doctor, diagnose_fvc_operators, test_fvc_accuracy
export test_gradient_accuracy, test_divergence_accuracy, test_laplacian_accuracy
export analyze_fvc_convergence, check_fvc_conservation, benchmark_fvc_performance

"""
    fvc_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)

Comprehensive FVC operator health check and diagnostic tool.
Performs accuracy tests, convergence analysis, and performance benchmarks.
"""
function fvc_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)
    if verbose
        println("🧮" * "="^70)
        println("🧮 FVC DOCTOR - FINITE VOLUME CALCULUS DIAGNOSTICS")
        println("🧮" * "="^70)
        println("📊 Mesh: $(length(mesh.cells)) cells, testing all FVC operators")
        println()
    end
    
    # Initialize report
    report = Dict{Symbol, Any}()
    
    # 1. Gradient operator analysis
    if verbose; println("🔍 1. GRADIENT OPERATOR ANALYSIS"); end
    gradient_report = test_gradient_accuracy(mesh, verbose=detailed)
    report[:gradient] = gradient_report
    
    # 2. Divergence operator analysis
    if verbose; println("\n🔍 2. DIVERGENCE OPERATOR ANALYSIS"); end
    divergence_report = test_divergence_accuracy(mesh, verbose=detailed)
    report[:divergence] = divergence_report
    
    # 3. Laplacian operator analysis
    if verbose; println("\n🔍 3. LAPLACIAN OPERATOR ANALYSIS"); end
    laplacian_report = test_laplacian_accuracy(mesh, verbose=detailed)
    report[:laplacian] = laplacian_report
    
    # 4. Conservation properties
    if verbose; println("\n🔍 4. CONSERVATION PROPERTIES"); end
    conservation_report = check_fvc_conservation(mesh, verbose=detailed)
    report[:conservation] = conservation_report
    
    # 5. Convergence analysis (if multiple mesh sizes available)
    if verbose; println("\n🔍 5. CONVERGENCE ANALYSIS"); end
    convergence_report = analyze_fvc_convergence(mesh, verbose=detailed)
    report[:convergence] = convergence_report
    
    # 6. Performance benchmarks
    if verbose; println("\n🔍 6. PERFORMANCE BENCHMARKS"); end
    performance_report = benchmark_fvc_performance(mesh, verbose=detailed)
    report[:performance] = performance_report
    
    # Generate overall assessment
    health_score, recommendations = generate_fvc_assessment(report)
    report[:health_score] = health_score
    report[:recommendations] = recommendations
    
    if verbose
        println("\n🧮" * "="^70)
        println("🧮 FVC HEALTH ASSESSMENT")
        println("🧮" * "="^70)
        
        # Color-coded health score
        if health_score >= 90
            println("💚 EXCELLENT: $(health_score)/100 - FVC operators working perfectly")
        elseif health_score >= 75
            println("💛 GOOD: $(health_score)/100 - FVC operators working well")
        elseif health_score >= 60
            println("🧡 FAIR: $(health_score)/100 - Some FVC accuracy issues")
        else
            println("❤️ POOR: $(health_score)/100 - Significant FVC problems")
        end
        
        if !isempty(recommendations)
            println("\n📋 RECOMMENDATIONS:")
            for (i, rec) in enumerate(recommendations)
                println("   $i. $rec")
            end
        else
            println("\n✅ No specific recommendations - FVC operators are working excellently!")
        end
        
        println("🧮" * "="^70)
    end
    
    return report
end

"""
    test_gradient_accuracy(mesh::UnstructuredMesh; verbose=false)

Test gradient operator accuracy on analytical functions.
"""
function test_gradient_accuracy(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Test 1: Linear function φ = x
    phi_linear = [cell.center[1] for cell in mesh.cells]
    bcs_linear = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x)
    )
    
    phi_field = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_linear, mesh, phi_linear, bcs_linear, nothing, nothing)
    grad_phi = Numerics.fvc.grad(phi_field)
    
    # Expected gradient: [1, 0, 0]
    linear_errors = [norm(g - SVector(1.0, 0.0, 0.0)) for g in grad_phi.data]
    max_linear_error = maximum(linear_errors)
    mean_linear_error = mean(linear_errors)
    
    if max_linear_error > 1e-10
        push!(issues, "Poor linear function accuracy (max error: $(max_linear_error:.2e))")
    end
    
    # Test 2: Quadratic function φ = x²
    phi_quad = [cell.center[1]^2 for cell in mesh.cells]
    bcs_quad = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x^2)
    )
    
    phi_field_quad = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_quad, mesh, phi_quad, bcs_quad, nothing, nothing)
    grad_phi_quad = Numerics.fvc.grad(phi_field_quad)
    
    # Expected gradient: [2x, 0, 0]
    quad_errors = Float64[]
    for (i, grad) in enumerate(grad_phi_quad.data)
        expected = SVector(2.0 * mesh.cells[i].center[1], 0.0, 0.0)
        push!(quad_errors, norm(grad - expected))
    end
    max_quad_error = maximum(quad_errors)
    mean_quad_error = mean(quad_errors)
    
    if max_quad_error > 0.5
        push!(issues, "Poor quadratic function accuracy (max error: $(max_quad_error:.2e))")
    end
    
    # Test 3: Multi-dimensional function φ = x + y + z
    phi_multi = [sum(cell.center) for cell in mesh.cells]
    bcs_multi = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> x + y + z),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> x + y + z),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x + y + z)
    )
    
    phi_field_multi = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_multi, mesh, phi_multi, bcs_multi, nothing, nothing)
    grad_phi_multi = Numerics.fvc.grad(phi_field_multi)
    
    # Expected gradient: [1, 1, 1]
    multi_errors = [norm(g - SVector(1.0, 1.0, 1.0)) for g in grad_phi_multi.data]
    max_multi_error = maximum(multi_errors)
    mean_multi_error = mean(multi_errors)
    
    if max_multi_error > 1e-8
        push!(issues, "Poor multi-dimensional accuracy (max error: $(max_multi_error:.2e))")
    end
    
    if verbose
        println("   📐 Gradient Accuracy Tests:")
        println("      Linear (φ=x): max=$(max_linear_error:.2e), mean=$(mean_linear_error:.2e)")
        println("      Quadratic (φ=x²): max=$(max_quad_error:.2e), mean=$(mean_quad_error:.2e)")
        println("      Multi-dim (φ=x+y+z): max=$(max_multi_error:.2e), mean=$(mean_multi_error:.2e)")
        
        if max_linear_error < 1e-10
            println("      ✅ Perfect linear accuracy")
        end
        if max_quad_error < 0.1
            println("      ✅ Good quadratic accuracy")
        end
        if max_multi_error < 1e-8
            println("      ✅ Excellent multi-dimensional accuracy")
        end
    end
    
    report[:linear_max_error] = max_linear_error
    report[:linear_mean_error] = mean_linear_error
    report[:quad_max_error] = max_quad_error
    report[:quad_mean_error] = mean_quad_error
    report[:multi_max_error] = max_multi_error
    report[:multi_mean_error] = mean_multi_error
    report[:issues] = issues
    
    return report
end

"""
    test_divergence_accuracy(mesh::UnstructuredMesh; verbose=false)

Test divergence operator accuracy and conservation properties.
"""
function test_divergence_accuracy(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Test 1: Constant vector field U = [1, 0, 0]
    U_constant = [SVector(1.0, 0.0, 0.0) for _ in mesh.cells]
    bcs_const = Dict{String, CFDCore.AbstractBoundaryCondition}()
    
    U_field_const = CFDCore.VectorField(:U_const, mesh, U_constant, bcs_const)
    div_U_const = Numerics.fvc.div(U_field_const)
    
    # Expected divergence: 0.0 everywhere
    const_errors = abs.(div_U_const.data)
    max_const_error = maximum(const_errors)
    mean_const_error = mean(const_errors)
    
    if max_const_error > 1e-12
        push!(issues, "Poor constant field conservation (max error: $(max_const_error:.2e))")
    end
    
    # Test 2: Linear divergent field U = [x, y, z]
    U_linear = [SVector(cell.center...) for cell in mesh.cells]
    U_field_linear = CFDCore.VectorField(:U_linear, mesh, U_linear, bcs_const)
    div_U_linear = Numerics.fvc.div(U_field_linear)
    
    # Expected divergence: 3.0 everywhere (∇·[x,y,z] = 1 + 1 + 1 = 3)
    linear_errors = abs.(div_U_linear.data .- 3.0)
    max_linear_error = maximum(linear_errors)
    mean_linear_error = mean(linear_errors)
    
    if max_linear_error > 0.5
        push!(issues, "Poor linear divergence accuracy (max error: $(max_linear_error:.2e))")
    end
    
    # Test 3: Irrotational field U = ∇φ where φ = x² + y² + z²
    phi_potential = [sum(cell.center.^2) for cell in mesh.cells]
    bcs_potential = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2)
    )
    
    phi_field_pot = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_pot, mesh, phi_potential, bcs_potential, nothing, nothing)
    grad_phi_pot = Numerics.fvc.grad(phi_field_pot)
    div_grad_phi = Numerics.fvc.div(grad_phi_pot)
    
    # Expected: ∇²(x² + y² + z²) = 2 + 2 + 2 = 6
    potential_errors = abs.(div_grad_phi.data .- 6.0)
    max_potential_error = maximum(potential_errors)
    mean_potential_error = mean(potential_errors)
    
    if verbose
        println("   🌊 Divergence Accuracy Tests:")
        println("      Constant field: max=$(max_const_error:.2e), mean=$(mean_const_error:.2e)")
        println("      Linear field: max=$(max_linear_error:.2e), mean=$(mean_linear_error:.2e)")
        println("      Potential field: max=$(max_potential_error:.2e), mean=$(mean_potential_error:.2e)")
        
        if max_const_error < 1e-12
            println("      ✅ Perfect conservation")
        end
        if max_linear_error < 0.1
            println("      ✅ Good linear accuracy")
        end
    end
    
    report[:const_max_error] = max_const_error
    report[:const_mean_error] = mean_const_error
    report[:linear_max_error] = max_linear_error
    report[:linear_mean_error] = mean_linear_error
    report[:potential_max_error] = max_potential_error
    report[:potential_mean_error] = mean_potential_error
    report[:issues] = issues
    
    return report
end

"""
    test_laplacian_accuracy(mesh::UnstructuredMesh; verbose=false)

Test Laplacian operator accuracy on analytical functions.
"""
function test_laplacian_accuracy(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Test 1: Quadratic function φ = x²
    phi_x2 = [cell.center[1]^2 for cell in mesh.cells]
    bcs_x2 = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x^2)
    )
    
    phi_field_x2 = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_x2, mesh, phi_x2, bcs_x2, nothing, nothing)
    lap_phi_x2 = Numerics.fvc.laplacian(phi_field_x2)
    
    # Expected: ∇²(x²) = 2
    x2_errors = abs.(lap_phi_x2.data .- 2.0)
    max_x2_error = maximum(x2_errors)
    mean_x2_error = mean(x2_errors)
    
    if max_x2_error > 2.0
        push!(issues, "Poor x² Laplacian accuracy (max error: $(max_x2_error:.2e))")
    end
    
    # Test 2: Multi-dimensional quadratic φ = x² + y² + z²
    phi_r2 = [sum(cell.center.^2) for cell in mesh.cells]
    bcs_r2 = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x^2 + y^2 + z^2)
    )
    
    phi_field_r2 = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_r2, mesh, phi_r2, bcs_r2, nothing, nothing)
    lap_phi_r2 = Numerics.fvc.laplacian(phi_field_r2)
    
    # Expected: ∇²(x² + y² + z²) = 2 + 2 + 2 = 6
    r2_errors = abs.(lap_phi_r2.data .- 6.0)
    max_r2_error = maximum(r2_errors)
    mean_r2_error = mean(r2_errors)
    
    if max_r2_error > 5.0
        push!(issues, "Poor multi-dim Laplacian accuracy (max error: $(max_r2_error:.2e))")
    end
    
    # Test 3: Check Laplacian of constant (should be zero)
    phi_const = ones(length(mesh.cells))
    bcs_const = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> 1.0)
    )
    
    phi_field_const = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_const, mesh, phi_const, bcs_const, nothing, nothing)
    lap_phi_const = Numerics.fvc.laplacian(phi_field_const)
    
    # Expected: ∇²(constant) = 0
    const_errors = abs.(lap_phi_const.data)
    max_const_error = maximum(const_errors)
    mean_const_error = mean(const_errors)
    
    if max_const_error > 1e-10
        push!(issues, "Poor constant Laplacian (max error: $(max_const_error:.2e))")
    end
    
    if verbose
        println("   ∇² Laplacian Accuracy Tests:")
        println("      φ=x²: max=$(max_x2_error:.2e), mean=$(mean_x2_error:.2e)")
        println("      φ=x²+y²+z²: max=$(max_r2_error:.2e), mean=$(mean_r2_error:.2e)")
        println("      φ=constant: max=$(max_const_error:.2e), mean=$(mean_const_error:.2e)")
        
        if max_const_error < 1e-10
            println("      ✅ Perfect constant handling")
        end
        if max_x2_error < 1.0
            println("      ✅ Good quadratic accuracy")
        end
    end
    
    report[:x2_max_error] = max_x2_error
    report[:x2_mean_error] = mean_x2_error
    report[:r2_max_error] = max_r2_error
    report[:r2_mean_error] = mean_r2_error
    report[:const_max_error] = max_const_error
    report[:const_mean_error] = mean_const_error
    report[:issues] = issues
    
    return report
end

"""
    check_fvc_conservation(mesh::UnstructuredMesh; verbose=false)

Check conservation properties of FVC operators.
"""
function check_fvc_conservation(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    # Conservation test 1: Divergence of constant vector should be zero
    U_const = [SVector(2.0, 1.0, 0.5) for _ in mesh.cells]
    bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
    U_field = CFDCore.VectorField(:U_conservation, mesh, U_const, bcs)
    div_U = Numerics.fvc.div(U_field)
    
    conservation_error = maximum(abs.(div_U.data))
    
    if conservation_error > 1e-10
        push!(issues, "Conservation violation (div of constant ≠ 0): $(conservation_error:.2e)")
    end
    
    # Conservation test 2: Gauss theorem verification
    # ∫∫∫ ∇·U dV = ∫∫ U·n dS
    volume_integral = sum(div_U.data[i] * mesh.cells[i].volume for i in 1:length(mesh.cells))
    
    # Surface integral (boundary faces only)
    surface_integral = 0.0
    for face in mesh.faces
        if face.boundary
            # Use face center value (simplified)
            face_center = face.center
            # Find nearest cell
            nearest_cell = face.owner
            U_face = U_const[nearest_cell]
            surface_integral += dot(U_face, face.normal * face.area)
        end
    end
    
    gauss_error = abs(volume_integral - surface_integral)
    relative_gauss_error = abs(surface_integral) > 1e-14 ? gauss_error / abs(surface_integral) : gauss_error
    
    if relative_gauss_error > 1e-8
        push!(issues, "Gauss theorem violation: rel_error=$(relative_gauss_error:.2e)")
    end
    
    if verbose
        println("   🔄 Conservation Tests:")
        println("      Div of constant: $(conservation_error:.2e)")
        println("      Gauss theorem error: $(gauss_error:.2e) (rel: $(relative_gauss_error:.2e))")
        
        if conservation_error < 1e-10
            println("      ✅ Perfect conservation")
        end
        if relative_gauss_error < 1e-8
            println("      ✅ Gauss theorem satisfied")
        end
    end
    
    report[:conservation_error] = conservation_error
    report[:gauss_error] = gauss_error
    report[:relative_gauss_error] = relative_gauss_error
    report[:issues] = issues
    
    return report
end

"""
    analyze_fvc_convergence(mesh::UnstructuredMesh; verbose=false)

Analyze FVC operator convergence properties.
"""
function analyze_fvc_convergence(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    
    # This would ideally test on multiple mesh refinements
    # For now, just analyze current mesh characteristics
    
    n_cells = length(mesh.cells)
    avg_cell_size = mean([cell.volume^(1/3) for cell in mesh.cells])
    
    # Estimate theoretical convergence rates
    # For FV methods: gradient = O(h), Laplacian = O(h) for linear reconstruction
    theoretical_gradient_order = 1.0
    theoretical_laplacian_order = 1.0
    
    if verbose
        println("   📈 Convergence Analysis:")
        println("      Cells: $n_cells")
        println("      Avg cell size: $(avg_cell_size:.6e)")
        println("      Expected gradient convergence: O(h¹)")
        println("      Expected Laplacian convergence: O(h¹)")
        println("      ℹ️  Run on multiple mesh sizes for detailed convergence study")
    end
    
    report[:cell_count] = n_cells
    report[:avg_cell_size] = avg_cell_size
    report[:theoretical_gradient_order] = theoretical_gradient_order
    report[:theoretical_laplacian_order] = theoretical_laplacian_order
    
    return report
end

"""
    benchmark_fvc_performance(mesh::UnstructuredMesh; verbose=false)

Benchmark FVC operator performance.
"""
function benchmark_fvc_performance(mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    
    # Setup test fields
    phi_data = [cell.center[1] for cell in mesh.cells]
    U_data = [SVector(1.0, 0.0, 0.0) for _ in mesh.cells]
    
    bcs_scalar = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x)
    )
    bcs_vector = Dict{String, CFDCore.AbstractBoundaryCondition}()
    
    phi_field = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_bench, mesh, phi_data, bcs_scalar, nothing, nothing)
    U_field = CFDCore.VectorField(:U_bench, mesh, U_data, bcs_vector)
    
    # Benchmark gradient
    grad_time = @elapsed begin
        for _ in 1:10
            grad_result = Numerics.fvc.grad(phi_field)
        end
    end
    grad_time /= 10
    
    # Benchmark divergence
    div_time = @elapsed begin
        for _ in 1:10
            div_result = Numerics.fvc.div(U_field)
        end
    end
    div_time /= 10
    
    # Benchmark Laplacian
    lap_time = @elapsed begin
        for _ in 1:10
            lap_result = Numerics.fvc.laplacian(phi_field)
        end
    end
    lap_time /= 10
    
    # Calculate performance metrics
    n_cells = length(mesh.cells)
    grad_cells_per_sec = n_cells / grad_time
    div_cells_per_sec = n_cells / div_time
    lap_cells_per_sec = n_cells / lap_time
    
    if verbose
        println("   ⚡ Performance Benchmarks:")
        println("      Gradient: $(grad_time*1000:.2f) ms ($(grad_cells_per_sec:.0f) cells/sec)")
        println("      Divergence: $(div_time*1000:.2f) ms ($(div_cells_per_sec:.0f) cells/sec)")
        println("      Laplacian: $(lap_time*1000:.2f) ms ($(lap_cells_per_sec:.0f) cells/sec)")
    end
    
    report[:gradient_time] = grad_time
    report[:divergence_time] = div_time
    report[:laplacian_time] = lap_time
    report[:grad_cells_per_sec] = grad_cells_per_sec
    report[:div_cells_per_sec] = div_cells_per_sec
    report[:lap_cells_per_sec] = lap_cells_per_sec
    
    return report
end

function generate_fvc_assessment(report::Dict{Symbol, Any})
    score = 100.0
    recommendations = String[]
    
    # Check gradient accuracy
    if haskey(report, :gradient)
        grad_report = report[:gradient]
        if get(grad_report, :linear_max_error, 0.0) > 1e-8
            score -= 20
            push!(recommendations, "Improve gradient accuracy for linear functions")
        end
        if get(grad_report, :quad_max_error, 0.0) > 1.0
            score -= 10
            push!(recommendations, "Consider mesh refinement for better gradient accuracy")
        end
    end
    
    # Check divergence accuracy
    if haskey(report, :divergence)
        div_report = report[:divergence]
        if get(div_report, :const_max_error, 0.0) > 1e-10
            score -= 25
            push!(recommendations, "Critical: Fix divergence conservation issues")
        end
    end
    
    # Check conservation
    if haskey(report, :conservation)
        cons_report = report[:conservation]
        if get(cons_report, :conservation_error, 0.0) > 1e-10
            score -= 30
            push!(recommendations, "Critical: Conservation violation detected")
        end
        if get(cons_report, :relative_gauss_error, 0.0) > 1e-6
            score -= 15
            push!(recommendations, "Gauss theorem not well satisfied")
        end
    end
    
    # Check for any issues reported
    for section_report in values(report)
        if haskey(section_report, :issues) && isa(section_report[:issues], Vector)
            score -= length(section_report[:issues]) * 8
        end
    end
    
    # Performance recommendations
    if haskey(report, :performance)
        perf_report = report[:performance]
        if get(perf_report, :grad_cells_per_sec, Inf) < 10000
            push!(recommendations, "Consider performance optimization for gradient operator")
        end
    end
    
    score = max(0.0, min(100.0, score))
    
    return round(Int, score), recommendations
end

end  # module FVCDoctor