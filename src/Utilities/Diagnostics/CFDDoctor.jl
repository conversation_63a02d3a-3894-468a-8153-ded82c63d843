"""
# CFD Doctor - Unified Comprehensive CFD Diagnostic Suite

This module provides a unified interface to all CFD diagnostic tools including
mesh analysis, FVC operator validation, FVM matrix analysis, and overall
system health checks. It's the main entry point for comprehensive CFD debugging.
"""
module CFDDoctor

using Printf
using Statistics
using LinearAlgebra
using ...CFDCore
include("MeshDoctor.jl")
include("FVCDoctor.jl") 
include("FVMDoctor.jl")

using .MeshDoctor
using .FVCDoctor
using .FVMDoctor

export cfd_doctor, full_cfd_diagnosis, quick_health_check
export doctor_report, save_doctor_report, compare_meshes
export benchmark_cfd_system, validate_cfd_setup

"""
    cfd_doctor(mesh::UnstructuredMesh; mode=:full, verbose=true, save_report=false)

Comprehensive CFD system diagnosis covering mesh, operators, and matrices.

# Arguments
- `mesh::UnstructuredMesh`: The mesh to analyze
- `mode::Symbol`: Diagnostic mode
  - `:full` - Complete analysis (mesh + FVC + FVM)
  - `:quick` - Fast health check with key metrics
  - `:mesh` - Mesh analysis only
  - `:operators` - FVC/FVM analysis only
- `verbose::Bool`: Print detailed output
- `save_report::Bool`: Save report to file

# Returns
- `Dict`: Comprehensive diagnostic report
"""
function cfd_doctor(mesh::UnstructuredMesh; mode=:full, verbose=true, save_report=false)
    if verbose
        println("🏥" * "="^80)
        println("🏥 CFD DOCTOR - COMPREHENSIVE SYSTEM DIAGNOSTICS")
        println("🏥" * "="^80)
        println("🏥 System: $(length(mesh.cells)) cells | Mode: $mode")
        println("🏥 Time: $(now())")
        println("🏥" * "="^80)
        println()
    end
    
    # Initialize comprehensive report
    report = Dict{Symbol, Any}()
    report[:timestamp] = now()
    report[:mode] = mode
    report[:mesh_info] = Dict(
        :cells => length(mesh.cells),
        :faces => length(mesh.faces),
        :nodes => length(mesh.nodes),
        :boundaries => collect(keys(mesh.boundaries))
    )
    
    # Track timing
    total_start = time()
    
    # 1. Mesh Analysis (always included)
    if verbose; println("🔍 PHASE 1: MESH HEALTH ANALYSIS"); end
    mesh_start = time()
    
    detailed_mesh = (mode == :full) || (mode == :mesh)
    mesh_report = mesh_doctor(mesh, verbose=verbose, detailed=detailed_mesh)
    report[:mesh] = mesh_report
    
    mesh_time = time() - mesh_start
    
    # 2. FVC Operator Analysis
    if mode in [:full, :operators]
        if verbose; println("\n🔍 PHASE 2: FVC OPERATOR ANALYSIS"); end
        fvc_start = time()
        
        detailed_fvc = (mode == :full)
        fvc_report = fvc_doctor(mesh, verbose=verbose, detailed=detailed_fvc)
        report[:fvc] = fvc_report
        
        fvc_time = time() - fvc_start
        report[:timing_fvc] = fvc_time
    end
    
    # 3. FVM Matrix Analysis
    if mode in [:full, :operators]
        if verbose; println("\n🔍 PHASE 3: FVM MATRIX ANALYSIS"); end
        fvm_start = time()
        
        detailed_fvm = (mode == :full)
        fvm_report = fvm_doctor(mesh, verbose=verbose, detailed=detailed_fvm)
        report[:fvm] = fvm_report
        
        fvm_time = time() - fvm_start
        report[:timing_fvm] = fvm_time
    end
    
    total_time = time() - total_start
    report[:timing_mesh] = mesh_time
    report[:timing_total] = total_time
    
    # 4. Overall System Assessment
    if verbose; println("\n🔍 PHASE 4: OVERALL SYSTEM ASSESSMENT"); end
    system_assessment = generate_system_assessment(report)
    report[:system] = system_assessment
    
    # 5. Generate Summary
    if verbose
        print_comprehensive_summary(report)
    end
    
    # 6. Save report if requested
    if save_report
        filename = save_doctor_report(report, mesh)
        if verbose
            println("\n💾 Report saved to: $filename")
        end
        report[:saved_filename] = filename
    end
    
    return report
end

"""
    quick_health_check(mesh::UnstructuredMesh; verbose=true)

Fast health check focusing on critical issues only.
"""
function quick_health_check(mesh::UnstructuredMesh; verbose=true)
    if verbose
        println("⚡ CFD QUICK HEALTH CHECK")
        println("="^50)
    end
    
    issues = String[]
    warnings = String[]
    
    # Quick mesh checks
    n_cells = length(mesh.cells)
    n_faces = length(mesh.faces)
    n_nodes = length(mesh.nodes)
    
    # Basic sanity checks
    if n_cells == 0
        push!(issues, "No cells in mesh")
    end
    
    if n_faces == 0
        push!(issues, "No faces in mesh")
    end
    
    # Face orientation quick check
    orientation_errors = 0
    for face in mesh.faces
        if !face.boundary && face.neighbor > 0
            d = mesh.cells[face.neighbor].center - mesh.cells[face.owner].center
            if dot(face.normal, d) <= 0
                orientation_errors += 1
            end
        end
    end
    
    if orientation_errors > 0
        push!(issues, "$orientation_errors face orientation errors")
    end
    
    # Volume check
    negative_volumes = count(cell -> cell.volume <= 0, mesh.cells)
    if negative_volumes > 0
        push!(issues, "$negative_volumes cells with non-positive volume")
    end
    
    # Quick FVC test - gradient of linear function
    try
        phi_data = [cell.center[1] for cell in mesh.cells]
        bcs = Dict{String, CFDCore.AbstractBoundaryCondition}(
            "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
            "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
            "walls" => CFDCore.DirichletBC((x, y, z, t) -> x)
        )
        
        phi_field = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_quick, mesh, phi_data, bcs, nothing, nothing)
        grad_phi = Numerics.fvc.grad(phi_field)
        
        linear_errors = [norm(g - SVector(1.0, 0.0, 0.0)) for g in grad_phi.data]
        max_grad_error = maximum(linear_errors)
        
        if max_grad_error > 1e-8
            push!(warnings, "Poor gradient accuracy ($(max_grad_error:.2e))")
        end
    catch e
        push!(issues, "FVC gradient test failed: $(string(e))")
    end
    
    # Generate quick score
    health_score = 100 - length(issues) * 20 - length(warnings) * 5
    health_score = max(0, min(100, health_score))
    
    if verbose
        println("📊 Mesh: $n_cells cells, $n_faces faces, $n_nodes nodes")
        
        if health_score >= 90
            println("💚 HEALTH SCORE: $health_score/100 - EXCELLENT")
        elseif health_score >= 70
            println("💛 HEALTH SCORE: $health_score/100 - GOOD")
        elseif health_score >= 50
            println("🧡 HEALTH SCORE: $health_score/100 - FAIR")
        else
            println("❤️ HEALTH SCORE: $health_score/100 - POOR")
        end
        
        if !isempty(issues)
            println("\n❌ CRITICAL ISSUES:")
            for issue in issues
                println("   • $issue")
            end
        end
        
        if !isempty(warnings)
            println("\n⚠️  WARNINGS:")
            for warning in warnings
                println("   • $warning")
            end
        end
        
        if isempty(issues) && isempty(warnings)
            println("✅ No issues detected in quick check")
        end
        
        println("="^50)
    end
    
    return Dict(
        :health_score => health_score,
        :issues => issues,
        :warnings => warnings,
        :mesh_info => Dict(:cells => n_cells, :faces => n_faces, :nodes => n_nodes)
    )
end

"""
    full_cfd_diagnosis(mesh::UnstructuredMesh; save_report=true)

Complete CFD system diagnosis with detailed analysis and recommendations.
"""
function full_cfd_diagnosis(mesh::UnstructuredMesh; save_report=true)
    return cfd_doctor(mesh, mode=:full, verbose=true, save_report=save_report)
end

"""
    benchmark_cfd_system(mesh::UnstructuredMesh; iterations=5)

Benchmark CFD system performance across different operations.
"""
function benchmark_cfd_system(mesh::UnstructuredMesh; iterations=5)
    println("⚡ CFD SYSTEM PERFORMANCE BENCHMARK")
    println("="^60)
    
    n_cells = length(mesh.cells)
    results = Dict{Symbol, Any}()
    
    # Setup test fields
    phi_data = [cell.center[1] for cell in mesh.cells]
    U_data = [SVector(1.0, 0.0, 0.0) for _ in mesh.cells]
    
    bcs_scalar = Dict{String, CFDCore.AbstractBoundaryCondition}(
        "inlet" => CFDCore.DirichletBC((x, y, z, t) -> 0.0),
        "outlet" => CFDCore.DirichletBC((x, y, z, t) -> 1.0),
        "walls" => CFDCore.DirichletBC((x, y, z, t) -> x)
    )
    bcs_vector = Dict{String, CFDCore.AbstractBoundaryCondition}()
    
    phi_field = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_bench, mesh, phi_data, bcs_scalar, nothing, nothing)
    U_field = CFDCore.VectorField(:U_bench, mesh, U_data, bcs_vector)
    
    # Benchmark FVC operations
    println("🧮 FVC Operations:")
    
    # Gradient benchmark
    grad_times = Float64[]
    for i in 1:iterations
        t = @elapsed Numerics.fvc.grad(phi_field)
        push!(grad_times, t)
    end
    avg_grad_time = mean(grad_times)
    grad_rate = n_cells / avg_grad_time
    
    println("   Gradient: $(avg_grad_time*1000:.2f) ± $(std(grad_times)*1000:.2f) ms ($(grad_rate:.0f) cells/sec)")
    
    # Divergence benchmark
    div_times = Float64[]
    for i in 1:iterations
        t = @elapsed Numerics.fvc.div(U_field)
        push!(div_times, t)
    end
    avg_div_time = mean(div_times)
    div_rate = n_cells / avg_div_time
    
    println("   Divergence: $(avg_div_time*1000:.2f) ± $(std(div_times)*1000:.2f) ms ($(div_rate:.0f) cells/sec)")
    
    # Laplacian benchmark
    lap_times = Float64[]
    for i in 1:iterations
        t = @elapsed Numerics.fvc.laplacian(phi_field)
        push!(lap_times, t)
    end
    avg_lap_time = mean(lap_times)
    lap_rate = n_cells / avg_lap_time
    
    println("   Laplacian: $(avg_lap_time*1000:.2f) ± $(std(lap_times)*1000:.2f) ms ($(lap_rate:.0f) cells/sec)")
    
    # FVM matrix assembly benchmark
    println("\n🔧 FVM Operations:")
    fvm_times = Float64[]
    for i in 1:iterations
        t = @elapsed Numerics.fvm.laplacian(1.0, phi_field)
        push!(fvm_times, t)
    end
    avg_fvm_time = mean(fvm_times)
    fvm_rate = n_cells / avg_fvm_time
    
    println("   Matrix Assembly: $(avg_fvm_time*1000:.2f) ± $(std(fvm_times)*1000:.2f) ms ($(fvm_rate:.0f) cells/sec)")
    
    # Overall performance assessment
    total_fvc_time = avg_grad_time + avg_div_time + avg_lap_time
    
    println("\n📊 Performance Summary:")
    println("   Total FVC time: $(total_fvc_time*1000:.2f) ms")
    println("   FVM assembly: $(avg_fvm_time*1000:.2f) ms")
    println("   Memory efficiency: $(sizeof(mesh)/1024^2:.1f) MB mesh data")
    
    # Performance rating
    combined_rate = n_cells / (total_fvc_time + avg_fvm_time)
    if combined_rate > 50000
        rating = "🚀 EXCELLENT"
    elseif combined_rate > 20000
        rating = "⚡ GOOD"
    elseif combined_rate > 5000
        rating = "✅ ACCEPTABLE"
    else
        rating = "🐌 SLOW"
    end
    
    println("   Overall rating: $rating ($(combined_rate:.0f) cells/sec combined)")
    println("="^60)
    
    results[:gradient] = Dict(:time => avg_grad_time, :rate => grad_rate)
    results[:divergence] = Dict(:time => avg_div_time, :rate => div_rate)
    results[:laplacian] = Dict(:time => avg_lap_time, :rate => lap_rate)
    results[:fvm_assembly] = Dict(:time => avg_fvm_time, :rate => fvm_rate)
    results[:combined_rate] = combined_rate
    results[:rating] = rating
    
    return results
end

"""
    compare_meshes(mesh1::UnstructuredMesh, mesh2::UnstructuredMesh; names=["Mesh 1", "Mesh 2"])

Compare two meshes and highlight differences in quality and performance.
"""
function compare_meshes(mesh1::UnstructuredMesh, mesh2::UnstructuredMesh; names=["Mesh 1", "Mesh 2"])
    println("🔍 MESH COMPARISON ANALYSIS")
    println("="^60)
    
    # Quick health checks for both
    report1 = quick_health_check(mesh1, verbose=false)
    report2 = quick_health_check(mesh2, verbose=false)
    
    # Size comparison
    println("📊 Size Comparison:")
    println("   $(names[1]): $(report1[:mesh_info][:cells]) cells")
    println("   $(names[2]): $(report2[:mesh_info][:cells]) cells")
    
    size_ratio = report2[:mesh_info][:cells] / report1[:mesh_info][:cells]
    println("   Size ratio: $(size_ratio:.2f)")
    
    # Health comparison
    println("\n🏥 Health Comparison:")
    println("   $(names[1]): $(report1[:health_score])/100")
    println("   $(names[2]): $(report2[:health_score])/100")
    
    if report2[:health_score] > report1[:health_score]
        println("   Winner: $(names[2]) (+$(report2[:health_score] - report1[:health_score]) points)")
    elseif report1[:health_score] > report2[:health_score]
        println("   Winner: $(names[1]) (+$(report1[:health_score] - report2[:health_score]) points)")
    else
        println("   Tie!")
    end
    
    # Performance comparison (if feasible)
    if report1[:mesh_info][:cells] < 50000 && report2[:mesh_info][:cells] < 50000
        println("\n⚡ Performance Comparison:")
        
        perf1 = benchmark_cfd_system(mesh1, iterations=3)
        perf2 = benchmark_cfd_system(mesh2, iterations=3)
        
        println("   $(names[1]) combined rate: $(perf1[:combined_rate]:.0f) cells/sec")
        println("   $(names[2]) combined rate: $(perf2[:combined_rate]:.0f) cells/sec")
        
        if perf2[:combined_rate] > perf1[:combined_rate]
            speedup = perf2[:combined_rate] / perf1[:combined_rate]
            println("   $(names[2]) is $(speedup:.1f)x faster")
        else
            speedup = perf1[:combined_rate] / perf2[:combined_rate]
            println("   $(names[1]) is $(speedup:.1f)x faster")
        end
    else
        println("\n⚠️  Meshes too large for performance comparison")
    end
    
    println("="^60)
    
    return Dict(
        :mesh1 => report1,
        :mesh2 => report2,
        :size_ratio => size_ratio,
        :health_winner => report2[:health_score] > report1[:health_score] ? names[2] : names[1]
    )
end

function generate_system_assessment(report::Dict{Symbol, Any})
    assessment = Dict{Symbol, Any}()
    
    # Collect health scores from all components
    health_scores = Float64[]
    all_issues = String[]
    all_recommendations = String[]
    
    # Mesh assessment
    if haskey(report, :mesh)
        mesh_score = get(report[:mesh], :health_score, 0)
        push!(health_scores, mesh_score)
        
        if haskey(report[:mesh], :recommendations)
            append!(all_recommendations, ["[MESH] $rec" for rec in report[:mesh][:recommendations]])
        end
    end
    
    # FVC assessment
    if haskey(report, :fvc)
        fvc_score = get(report[:fvc], :health_score, 0)
        push!(health_scores, fvc_score)
        
        if haskey(report[:fvc], :recommendations)
            append!(all_recommendations, ["[FVC] $rec" for rec in report[:fvc][:recommendations]])
        end
    end
    
    # FVM assessment
    if haskey(report, :fvm)
        fvm_score = get(report[:fvm], :health_score, 0)
        push!(health_scores, fvm_score)
        
        if haskey(report[:fvm], :recommendations)
            append!(all_recommendations, ["[FVM] $rec" for rec in report[:fvm][:recommendations]])
        end
    end
    
    # Overall system health
    if !isempty(health_scores)
        overall_score = mean(health_scores)
        min_score = minimum(health_scores)
        
        # Overall score is limited by the worst component
        weighted_score = 0.6 * overall_score + 0.4 * min_score
    else
        weighted_score = 0
    end
    
    # System status
    if weighted_score >= 90
        status = "EXCELLENT"
        status_emoji = "💚"
    elseif weighted_score >= 75
        status = "GOOD"
        status_emoji = "💛"
    elseif weighted_score >= 60
        status = "FAIR"
        status_emoji = "🧡"
    else
        status = "POOR"
        status_emoji = "❤️"
    end
    
    assessment[:overall_score] = round(Int, weighted_score)
    assessment[:component_scores] = health_scores
    assessment[:min_component_score] = isempty(health_scores) ? 0 : minimum(health_scores)
    assessment[:status] = status
    assessment[:status_emoji] = status_emoji
    assessment[:all_recommendations] = all_recommendations
    
    return assessment
end

function print_comprehensive_summary(report::Dict{Symbol, Any})
    println("\n🏥" * "="^80)
    println("🏥 COMPREHENSIVE CFD SYSTEM HEALTH REPORT")
    println("🏥" * "="^80)
    
    # System overview
    mesh_info = report[:mesh_info]
    println("📊 SYSTEM OVERVIEW:")
    println("   Cells: $(mesh_info[:cells]), Faces: $(mesh_info[:faces]), Nodes: $(mesh_info[:nodes])")
    println("   Boundaries: $(join(mesh_info[:boundaries], ", "))")
    println("   Analysis time: $(report[:timing_total]:.2f) seconds")
    
    # Component scores
    if haskey(report, :system)
        system = report[:system]
        println("\n🎯 COMPONENT HEALTH SCORES:")
        
        component_names = []
        scores = system[:component_scores]
        idx = 1
        
        if haskey(report, :mesh)
            println("   Mesh Quality: $(round(Int, scores[idx]))/100")
            idx += 1
        end
        
        if haskey(report, :fvc)
            println("   FVC Operators: $(round(Int, scores[idx]))/100")
            idx += 1
        end
        
        if haskey(report, :fvm)
            println("   FVM Matrices: $(round(Int, scores[idx]))/100")
        end
        
        # Overall assessment
        println("\n$(system[:status_emoji]) OVERALL SYSTEM HEALTH: $(system[:overall_score])/100 - $(system[:status])")
        
        # Recommendations
        if !isempty(system[:all_recommendations])
            println("\n📋 PRIORITY RECOMMENDATIONS:")
            for (i, rec) in enumerate(system[:all_recommendations][1:min(10, end)])
                println("   $i. $rec")
            end
            
            if length(system[:all_recommendations]) > 10
                println("   ... and $(length(system[:all_recommendations]) - 10) more recommendations")
            end
        else
            println("\n✅ SYSTEM IS OPERATING OPTIMALLY - NO RECOMMENDATIONS")
        end
    end
    
    # Quick action items
    println("\n🚀 QUICK ACTION ITEMS:")
    urgent_found = false
    
    # Check for critical issues
    for section in [:mesh, :fvc, :fvm]
        if haskey(report, section) && haskey(report[section], :health_score)
            score = report[section][:health_score]
            if score < 60
                println("   ❗ Address critical issues in $(uppercase(string(section))) (score: $score)")
                urgent_found = true
            end
        end
    end
    
    if !urgent_found
        println("   ✅ No urgent action required - system is stable")
    end
    
    println("🏥" * "="^80)
end

function save_doctor_report(report::Dict{Symbol, Any}, mesh::UnstructuredMesh)
    timestamp = replace(string(now()), ":" => "-")
    filename = "cfd_doctor_report_$(timestamp).txt"
    
    open(filename, "w") do io
        println(io, "CFD DOCTOR DIAGNOSTIC REPORT")
        println(io, "="^50)
        println(io, "Generated: $(report[:timestamp])")
        println(io, "Mode: $(report[:mode])")
        println(io)
        
        # System info
        mesh_info = report[:mesh_info]
        println(io, "SYSTEM INFORMATION:")
        println(io, "  Cells: $(mesh_info[:cells])")
        println(io, "  Faces: $(mesh_info[:faces])")
        println(io, "  Nodes: $(mesh_info[:nodes])")
        println(io, "  Boundaries: $(join(mesh_info[:boundaries], ", "))")
        println(io)
        
        # Component scores
        if haskey(report, :system)
            system = report[:system]
            println(io, "HEALTH ASSESSMENT:")
            println(io, "  Overall Score: $(system[:overall_score])/100")
            println(io, "  Status: $(system[:status])")
            println(io, "  Component Scores: $(system[:component_scores])")
            println(io)
            
            if !isempty(system[:all_recommendations])
                println(io, "RECOMMENDATIONS:")
                for (i, rec) in enumerate(system[:all_recommendations])
                    println(io, "  $i. $rec")
                end
                println(io)
            end
        end
        
        # Detailed section reports
        for section in [:mesh, :fvc, :fvm]
            if haskey(report, section)
                section_report = report[section]
                println(io, "$(uppercase(string(section))) DETAILED REPORT:")
                
                for (key, value) in section_report
                    if key == :issues && isa(value, Vector) && !isempty(value)
                        println(io, "  Issues:")
                        for issue in value
                            println(io, "    - $issue")
                        end
                    elseif key == :recommendations && isa(value, Vector) && !isempty(value)
                        println(io, "  Recommendations:")
                        for rec in value
                            println(io, "    - $rec")
                        end
                    elseif isa(value, Number)
                        println(io, "  $(key): $value")
                    end
                end
                println(io)
            end
        end
    end
    
    return filename
end

end  # module CFDDoctor