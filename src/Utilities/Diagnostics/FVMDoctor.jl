"""
# FVM Doctor - Comprehensive FVM Matrix Diagnostic Utility

This module provides comprehensive diagnostic tools for Finite Volume Method (FVM)
matrices including structure analysis, conditioning, convergence properties,
and solver performance analysis.
"""
module FVMDoctor

using LinearAlgebra
using SparseArrays
using Statistics
using Printf
using ...CFDCore

# Simple matrix creation for testing
function create_test_matrix(mesh)
    n = length(mesh.cells)
    return sparse(I, n, n) + 0.1 * sprand(n, n, 0.1)
end

export fvm_doctor, diagnose_fvm_matrix, analyze_matrix_structure
export check_matrix_properties, analyze_conditioning, test_solver_performance
export diagnose_boundary_coupling, analyze_matrix_pattern, check_conservation_matrix

"""
    fvm_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)

Comprehensive FVM matrix health check and diagnostic tool.
Analyzes matrix properties, conditioning, solver performance, and numerical stability.
"""
function fvm_doctor(mesh::UnstructuredMesh; verbose=true, detailed=false)
    if verbose
        println("🔧" * "="^70)
        println("🔧 FVM DOCTOR - FINITE VOLUME METHOD MATRIX DIAGNOSTICS")
        println("🔧" * "="^70)
        println("📊 Mesh: $(length(mesh.cells)) cells, analyzing FVM matrices")
        println()
    end
    
    # Initialize report
    report = Dict{Symbol, Any}()
    
    # Create test matrices for different operators
    n_cells = length(mesh.cells)
    phi_data = zeros(n_cells)
    bcs = Dict{String, CFDCore.AbstractBoundaryCondition}()
    phi_field = CFDCore.ScalarField{Float64, 3, typeof(mesh)}(:phi_test, mesh, phi_data, bcs, nothing, nothing)
    
    # 1. Laplacian matrix analysis
    if verbose; println("🔍 1. LAPLACIAN MATRIX ANALYSIS"); end
    laplacian_matrix = Numerics.fvm.laplacian(1.0, phi_field)
    laplacian_report = analyze_matrix_structure(laplacian_matrix.A, "Laplacian", verbose=detailed)
    report[:laplacian] = laplacian_report
    
    # 2. Matrix properties analysis
    if verbose; println("\n🔍 2. MATRIX PROPERTIES ANALYSIS"); end
    properties_report = check_matrix_properties(laplacian_matrix.A, mesh, verbose=detailed)
    report[:properties] = properties_report
    
    # 3. Conditioning analysis
    if verbose; println("\n🔍 3. CONDITIONING AND STABILITY ANALYSIS"); end
    conditioning_report = analyze_conditioning(laplacian_matrix.A, verbose=detailed)
    report[:conditioning] = conditioning_report
    
    # 4. Boundary coupling analysis
    if verbose; println("\n🔍 4. BOUNDARY COUPLING ANALYSIS"); end
    boundary_report = diagnose_boundary_coupling(laplacian_matrix.A, mesh, verbose=detailed)
    report[:boundary] = boundary_report
    
    # 5. Solver performance analysis
    if verbose; println("\n🔍 5. SOLVER PERFORMANCE ANALYSIS"); end
    solver_report = test_solver_performance(laplacian_matrix.A, verbose=detailed)
    report[:solver] = solver_report
    
    # 6. Conservation matrix properties
    if verbose; println("\n🔍 6. CONSERVATION PROPERTIES"); end
    conservation_report = check_conservation_matrix(laplacian_matrix.A, mesh, verbose=detailed)
    report[:conservation] = conservation_report
    
    # Generate overall assessment
    health_score, recommendations = generate_fvm_assessment(report)
    report[:health_score] = health_score
    report[:recommendations] = recommendations
    
    if verbose
        println("\n🔧" * "="^70)
        println("🔧 FVM MATRIX HEALTH ASSESSMENT")
        println("🔧" * "="^70)
        
        # Color-coded health score
        if health_score >= 90
            println("💚 EXCELLENT: $(health_score)/100 - FVM matrices optimal")
        elseif health_score >= 75
            println("💛 GOOD: $(health_score)/100 - FVM matrices working well")
        elseif health_score >= 60
            println("🧡 FAIR: $(health_score)/100 - Some matrix issues detected")
        else
            println("❤️ POOR: $(health_score)/100 - Significant matrix problems")
        end
        
        if !isempty(recommendations)
            println("\n📋 RECOMMENDATIONS:")
            for (i, rec) in enumerate(recommendations)
                println("   $i. $rec")
            end
        else
            println("\n✅ No specific recommendations - FVM matrices are excellent!")
        end
        
        println("🔧" * "="^70)
    end
    
    return report
end

"""
    analyze_matrix_structure(A::AbstractMatrix, matrix_name::String; verbose=false)

Analyze the structure and sparsity pattern of an FVM matrix.
"""
function analyze_matrix_structure(A::AbstractMatrix, matrix_name::String; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    n, m = size(A)
    
    # Basic structure checks
    if n != m
        push!(issues, "Matrix is not square ($n × $m)")
    end
    
    # Sparsity analysis
    if isa(A, SparseMatrixCSC)
        nnz_count = nnz(A)
        total_elements = n * m
        sparsity = 1.0 - (nnz_count / total_elements)
        
        # Bandwidth analysis
        I, J, V = findnz(A)
        bandwidth = maximum(abs.(I - J))
        avg_row_nnz = nnz_count / n
        
        if sparsity < 0.5
            push!(issues, "Matrix is quite dense (sparsity: $(sparsity:.2f))")
        end
        
        if bandwidth > sqrt(n) * 2
            push!(issues, "Large bandwidth may affect solver performance")
        end
    else
        nnz_count = count(!iszero, A)
        total_elements = n * m
        sparsity = 1.0 - (nnz_count / total_elements)
        bandwidth = n  # Worst case for dense matrix
        avg_row_nnz = nnz_count / n
    end
    
    # Diagonal analysis
    diagonal = diag(A)
    zero_diag = count(iszero, diagonal)
    min_diag = minimum(abs.(diagonal))
    max_diag = maximum(abs.(diagonal))
    diag_ratio = max_diag / min_diag
    
    if zero_diag > 0
        push!(issues, "$zero_diag zero diagonal elements")
    end
    
    if diag_ratio > 1e6
        push!(issues, "Extreme diagonal variation (ratio: $(diag_ratio:.1e))")
    end
    
    if verbose
        println("   📋 $matrix_name Matrix Structure:")
        println("      Size: $n × $m")
        println("      Non-zeros: $nnz_count ($(sparsity:.1f) sparse)")
        println("      Avg NNZ/row: $(avg_row_nnz:.1f)")
        println("      Bandwidth: $bandwidth")
        println("      Diagonal range: $(min_diag:.2e) - $(max_diag:.2e)")
        
        if zero_diag == 0
            println("      ✅ No zero diagonal elements")
        else
            println("      ❌ $zero_diag zero diagonal elements")
        end
    end
    
    report[:size] = (n, m)
    report[:nnz] = nnz_count
    report[:sparsity] = sparsity
    report[:bandwidth] = bandwidth
    report[:avg_row_nnz] = avg_row_nnz
    report[:zero_diagonal] = zero_diag
    report[:min_diagonal] = min_diag
    report[:max_diagonal] = max_diag
    report[:diagonal_ratio] = diag_ratio
    report[:issues] = issues
    
    return report
end

"""
    check_matrix_properties(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)

Check mathematical properties of FVM matrices (symmetry, definiteness, etc.).
"""
function check_matrix_properties(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    n = size(A, 1)
    
    # Symmetry check
    if isa(A, SparseMatrixCSC)
        symmetry_error = norm(A - A', Inf)
    else
        symmetry_error = norm(A - A')
    end
    
    is_symmetric = symmetry_error < 1e-12
    
    if !is_symmetric && symmetry_error > 1e-8
        push!(issues, "Matrix is significantly non-symmetric (error: $(symmetry_error:.2e))")
    end
    
    # Diagonal dominance check
    diagonal = diag(A)
    diag_dominant_count = 0
    weak_rows = Int[]
    
    for i in 1:n
        row_sum = sum(abs.(A[i, :]))
        off_diag_sum = row_sum - abs(diagonal[i])
        
        if abs(diagonal[i]) >= off_diag_sum - 1e-12  # Allow weak diagonal dominance
            diag_dominant_count += 1
        else
            push!(weak_rows, i)
        end
    end
    
    diag_dominance_ratio = diag_dominant_count / n
    
    if diag_dominance_ratio < 0.8
        push!(issues, "Poor diagonal dominance ($(diag_dominance_ratio:.1f) of rows)")
    end
    
    # Sign pattern check (for diffusion matrices)
    positive_off_diag = 0
    negative_diag = 0
    
    for i in 1:n
        if diagonal[i] <= 0
            negative_diag += 1
        end
        
        for j in 1:n
            if i != j && A[i, j] > 1e-12
                positive_off_diag += 1
            end
        end
    end
    
    if positive_off_diag > 0
        push!(issues, "$positive_off_diag positive off-diagonal elements (should be ≤ 0)")
    end
    
    if negative_diag > 0
        push!(issues, "$negative_diag non-positive diagonal elements")
    end
    
    # M-matrix properties
    is_m_matrix = (negative_diag == 0) && (positive_off_diag == 0) && is_symmetric
    
    if verbose
        println("   🔍 Matrix Properties:")
        println("      Symmetric: $(is_symmetric ? "✅" : "❌") (error: $(symmetry_error:.2e))")
        println("      Diag dominant: $(diag_dominance_ratio:.1f) of rows")
        println("      M-matrix: $(is_m_matrix ? "✅" : "❌")")
        println("      Positive off-diag: $positive_off_diag")
        println("      Non-positive diag: $negative_diag")
        
        if !isempty(weak_rows) && length(weak_rows) <= 5
            println("      Weak rows: $(weak_rows)")
        elseif length(weak_rows) > 5
            println("      Weak rows: $(length(weak_rows)) total")
        end
    end
    
    report[:is_symmetric] = is_symmetric
    report[:symmetry_error] = symmetry_error
    report[:diagonal_dominance_ratio] = diag_dominance_ratio
    report[:weak_rows] = weak_rows
    report[:positive_off_diagonal] = positive_off_diag
    report[:negative_diagonal] = negative_diag
    report[:is_m_matrix] = is_m_matrix
    report[:issues] = issues
    
    return report
end

"""
    analyze_conditioning(A::AbstractMatrix; verbose=false)

Analyze matrix conditioning and numerical stability.
"""
function analyze_conditioning(A::AbstractMatrix; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    warnings = String[]
    
    n = size(A, 1)
    
    # Condition number estimation (for reasonable sizes)
    if n <= 1000
        try
            cond_number = cond(Matrix(A))
            
            if cond_number > 1e12
                push!(issues, "Very ill-conditioned matrix (κ = $(cond_number:.1e))")
            elseif cond_number > 1e8
                push!(warnings, "Poorly conditioned matrix (κ = $(cond_number:.1e))")
            end
            
            report[:condition_number] = cond_number
            
            if verbose
                println("   📊 Conditioning Analysis:")
                println("      Condition number: $(cond_number:.2e)")
                
                if cond_number < 1e6
                    println("      ✅ Well-conditioned")
                elseif cond_number < 1e10
                    println("      ⚠️  Moderately conditioned")
                else
                    println("      ❌ Ill-conditioned")
                end
            end
        catch
            push!(warnings, "Could not compute condition number")
            if verbose
                println("   📊 Conditioning Analysis:")
                println("      ⚠️  Could not compute condition number")
            end
        end
    else
        push!(warnings, "Matrix too large for condition number computation")
        if verbose
            println("   📊 Conditioning Analysis:")
            println("      ⚠️  Matrix too large for direct conditioning analysis")
        end
    end
    
    # Eigenvalue analysis (for small matrices)
    if n <= 500
        try
            eigenvals = eigvals(Matrix(A))
            min_eigval = minimum(real(eigenvals))
            max_eigval = maximum(real(eigenvals))
            
            negative_eigenvals = count(real(eig) < -1e-12 for eig in eigenvals)
            zero_eigenvals = count(abs(real(eig)) < 1e-12 for eig in eigenvals)
            
            if negative_eigenvals > 0
                push!(issues, "$negative_eigenvals negative eigenvalues")
            end
            
            if zero_eigenvals > 1
                push!(warnings, "Multiple zero eigenvalues ($(zero_eigenvals))")
            end
            
            report[:min_eigenvalue] = min_eigval
            report[:max_eigenvalue] = max_eigval
            report[:negative_eigenvals] = negative_eigenvals
            report[:zero_eigenvals] = zero_eigenvals
            
            if verbose
                println("      Eigenvalue range: $(min_eigval:.2e) - $(max_eigval:.2e)")
                println("      Negative eigenvals: $negative_eigenvals")
                println("      Zero eigenvals: $zero_eigenvals")
            end
        catch
            push!(warnings, "Could not compute eigenvalues")
            if verbose
                println("      ⚠️  Could not compute eigenvalues")
            end
        end
    end
    
    # Matrix norm analysis
    frobenius_norm = norm(A, 2)
    infinity_norm = norm(A, Inf)
    
    report[:frobenius_norm] = frobenius_norm
    report[:infinity_norm] = infinity_norm
    report[:issues] = issues
    report[:warnings] = warnings
    
    return report
end

"""
    diagnose_boundary_coupling(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)

Analyze how boundary conditions are coupled into the matrix.
"""
function diagnose_boundary_coupling(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    n = size(A, 1)
    n_cells = length(mesh.cells)
    
    if n != n_cells
        push!(issues, "Matrix size ($n) doesn't match cell count ($n_cells)")
        return Dict(:issues => issues)
    end
    
    # Identify boundary cells
    boundary_cells = Set{Int}()
    for face in mesh.faces
        if face.boundary
            push!(boundary_cells, face.owner)
        end
    end
    
    n_boundary = length(boundary_cells)
    boundary_ratio = n_boundary / n_cells
    
    # Analyze boundary rows
    boundary_diagonal_sum = 0.0
    interior_diagonal_sum = 0.0
    boundary_off_diag_count = 0
    interior_off_diag_count = 0
    
    for i in 1:n
        diagonal_val = A[i, i]
        off_diag_count = count(j -> j != i && abs(A[i, j]) > 1e-14, 1:n)
        
        if i in boundary_cells
            boundary_diagonal_sum += diagonal_val
            boundary_off_diag_count += off_diag_count
        else
            interior_diagonal_sum += diagonal_val
            interior_off_diag_count += off_diag_count
        end
    end
    
    avg_boundary_diag = n_boundary > 0 ? boundary_diagonal_sum / n_boundary : 0.0
    avg_interior_diag = (n_cells - n_boundary) > 0 ? interior_diagonal_sum / (n_cells - n_boundary) : 0.0
    
    avg_boundary_connections = n_boundary > 0 ? boundary_off_diag_count / n_boundary : 0.0
    avg_interior_connections = (n_cells - n_boundary) > 0 ? interior_off_diag_count / (n_cells - n_boundary) : 0.0
    
    # Check for reasonable boundary treatment
    if abs(avg_boundary_diag) < 1e-14
        push!(issues, "Boundary cells have very small diagonal values")
    end
    
    diag_ratio = avg_interior_diag != 0 ? avg_boundary_diag / avg_interior_diag : 0.0
    if abs(diag_ratio) > 10 || abs(diag_ratio) < 0.1
        push!(issues, "Large diagonal ratio between boundary/interior ($(diag_ratio:.2f))")
    end
    
    if verbose
        println("   🏷️  Boundary Coupling Analysis:")
        println("      Boundary cells: $n_boundary ($(boundary_ratio:.1f))")
        println("      Avg boundary diagonal: $(avg_boundary_diag:.2e)")
        println("      Avg interior diagonal: $(avg_interior_diag:.2e)")
        println("      Avg boundary connections: $(avg_boundary_connections:.1f)")
        println("      Avg interior connections: $(avg_interior_connections:.1f)")
        
        if abs(diag_ratio - 1.0) < 0.5
            println("      ✅ Reasonable boundary/interior balance")
        end
    end
    
    report[:boundary_cell_count] = n_boundary
    report[:boundary_ratio] = boundary_ratio
    report[:avg_boundary_diagonal] = avg_boundary_diag
    report[:avg_interior_diagonal] = avg_interior_diag
    report[:avg_boundary_connections] = avg_boundary_connections
    report[:avg_interior_connections] = avg_interior_connections
    report[:diagonal_ratio] = diag_ratio
    report[:issues] = issues
    
    return report
end

"""
    test_solver_performance(A::AbstractMatrix; verbose=false)

Test iterative solver performance on the matrix.
"""
function test_solver_performance(A::AbstractMatrix; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    n = size(A, 1)
    
    # Create test right-hand side
    rhs = ones(n)
    
    # Test different solvers if available
    solver_results = Dict{Symbol, Any}()
    
    # Direct solver test (for small matrices)
    if n <= 1000
        try
            direct_time = @elapsed x_direct = A \ rhs
            direct_residual = norm(A * x_direct - rhs)
            
            solver_results[:direct] = Dict(
                :time => direct_time,
                :residual => direct_residual,
                :converged => true
            )
            
            if verbose
                println("   ⚡ Solver Performance:")
                println("      Direct solve: $(direct_time*1000:.2f) ms, residual: $(direct_residual:.2e)")
            end
        catch e
            solver_results[:direct] = Dict(:error => string(e))
            push!(issues, "Direct solver failed: $(string(e))")
        end
    end
    
    # Iterative solver test (CG for symmetric positive definite)
    try
        # Simple iterative method (Jacobi-like)
        x = zeros(n)
        max_iter = min(100, n)
        tol = 1e-10
        
        iter_time = @elapsed begin
            for iter in 1:max_iter
                x_new = similar(x)
                for i in 1:n
                    if abs(A[i, i]) > 1e-14
                        sum_off_diag = sum(A[i, j] * x[j] for j in 1:n if j != i)
                        x_new[i] = (rhs[i] - sum_off_diag) / A[i, i]
                    else
                        x_new[i] = x[i]
                    end
                end
                
                residual = norm(A * x_new - rhs)
                if residual < tol
                    x = x_new
                    solver_results[:iterative] = Dict(
                        :time => iter_time,
                        :iterations => iter,
                        :residual => residual,
                        :converged => true
                    )
                    break
                end
                x = x_new
                
                if iter == max_iter
                    solver_results[:iterative] = Dict(
                        :time => iter_time,
                        :iterations => iter,
                        :residual => norm(A * x - rhs),
                        :converged => false
                    )
                end
            end
        end
        
        if verbose && haskey(solver_results, :iterative)
            result = solver_results[:iterative]
            println("      Iterative: $(result[:time]*1000:.2f) ms, $(result[:iterations]) iter, residual: $(result[:residual]:.2e)")
            if !result[:converged]
                println("      ⚠️  Iterative solver did not converge")
            end
        end
        
    catch e
        solver_results[:iterative] = Dict(:error => string(e))
        push!(issues, "Iterative solver failed: $(string(e))")
    end
    
    # Performance metrics
    if haskey(solver_results, :direct) && haskey(solver_results[:direct], :time)
        cells_per_sec_direct = n / solver_results[:direct][:time]
        report[:direct_cells_per_sec] = cells_per_sec_direct
    end
    
    if haskey(solver_results, :iterative) && haskey(solver_results[:iterative], :time)
        cells_per_sec_iter = n / solver_results[:iterative][:time]
        report[:iterative_cells_per_sec] = cells_per_sec_iter
    end
    
    report[:solver_results] = solver_results
    report[:issues] = issues
    
    return report
end

"""
    check_conservation_matrix(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)

Check if the matrix satisfies conservation properties.
"""
function check_conservation_matrix(A::AbstractMatrix, mesh::UnstructuredMesh; verbose=false)
    report = Dict{Symbol, Any}()
    issues = String[]
    
    n = size(A, 1)
    
    # Row sum check (for pure Neumann problems, row sums should be zero)
    row_sums = [sum(A[i, :]) for i in 1:n]
    max_row_sum = maximum(abs.(row_sums))
    mean_row_sum = mean(abs.(row_sums))
    
    # For Dirichlet BC problems, some rows will have non-zero sums
    # Check what fraction of rows have near-zero sums
    zero_sum_rows = count(abs(rs) < 1e-10 for rs in row_sums)
    zero_sum_ratio = zero_sum_rows / n
    
    # Conservation check depends on boundary conditions
    # For pure diffusion with Neumann BC everywhere, row sums should be zero
    # For mixed BC, some rows will be modified
    
    if zero_sum_ratio < 0.1 && max_row_sum > 1e-6
        push!(issues, "Very few rows conserve (only $(zero_sum_ratio:.1f))")
    end
    
    # Check matrix nullspace (for pure Neumann problems)
    constant_vector = ones(n)
    A_times_ones = A * constant_vector
    nullspace_residual = norm(A_times_ones)
    
    has_constant_nullspace = nullspace_residual < 1e-10
    
    if verbose
        println("   🔄 Conservation Analysis:")
        println("      Max row sum: $(max_row_sum:.2e)")
        println("      Mean row sum: $(mean_row_sum:.2e)")
        println("      Zero-sum rows: $zero_sum_rows ($(zero_sum_ratio:.1f))")
        println("      Constant nullspace: $(has_constant_nullspace ? "✅" : "❌") (residual: $(nullspace_residual:.2e))")
        
        if zero_sum_ratio > 0.5
            println("      ✅ Good conservation properties")
        end
    end
    
    report[:max_row_sum] = max_row_sum
    report[:mean_row_sum] = mean_row_sum
    report[:zero_sum_rows] = zero_sum_rows
    report[:zero_sum_ratio] = zero_sum_ratio
    report[:nullspace_residual] = nullspace_residual
    report[:has_constant_nullspace] = has_constant_nullspace
    report[:issues] = issues
    
    return report
end

function generate_fvm_assessment(report::Dict{Symbol, Any})
    score = 100.0
    recommendations = String[]
    
    # Check structure issues
    for section_report in values(report)
        if haskey(section_report, :issues) && isa(section_report[:issues], Vector)
            score -= length(section_report[:issues]) * 10
        end
        if haskey(section_report, :warnings) && isa(section_report[:warnings], Vector)
            score -= length(section_report[:warnings]) * 3
        end
    end
    
    # Specific checks
    if haskey(report, :properties)
        prop_report = report[:properties]
        if !get(prop_report, :is_symmetric, false)
            score -= 15
            push!(recommendations, "Matrix should be symmetric for diffusion operators")
        end
        
        if get(prop_report, :diagonal_dominance_ratio, 0.0) < 0.8
            score -= 20
            push!(recommendations, "Improve diagonal dominance for better convergence")
        end
        
        if get(prop_report, :positive_off_diagonal, 0) > 0
            score -= 25
            push!(recommendations, "Critical: Fix positive off-diagonal elements")
        end
    end
    
    # Conditioning checks
    if haskey(report, :conditioning)
        cond_report = report[:conditioning]
        if haskey(cond_report, :condition_number)
            cond_num = cond_report[:condition_number]
            if cond_num > 1e10
                score -= 20
                push!(recommendations, "Very ill-conditioned - consider preconditioning")
            elseif cond_num > 1e8
                score -= 10
                push!(recommendations, "Consider improving mesh quality for better conditioning")
            end
        end
        
        if get(cond_report, :negative_eigenvals, 0) > 0
            score -= 15
            push!(recommendations, "Negative eigenvalues indicate matrix issues")
        end
    end
    
    # Solver performance
    if haskey(report, :solver)
        solver_report = report[:solver]
        if haskey(solver_report, :solver_results)
            results = solver_report[:solver_results]
            if haskey(results, :iterative) && haskey(results[:iterative], :converged)
                if !results[:iterative][:converged]
                    score -= 15
                    push!(recommendations, "Poor iterative solver convergence")
                end
            end
        end
    end
    
    score = max(0.0, min(100.0, score))
    
    return round(Int, score), recommendations
end

end  # module FVMDoctor