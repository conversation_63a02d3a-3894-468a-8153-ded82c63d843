# src/Utilities/SIMPLEDebugger.jl
"""
SIMPLE Solver Debugging and Diagnostics Toolkit

This module provides comprehensive debugging tools for diagnosing and fixing
SIMPLE solver numerical instability issues. It includes:

1. Pressure Poisson matrix eigenvalue analysis
2. Iterative solver convergence testing
3. Velocity divergence field analysis
4. Momentum residual tracking
5. Discrete operator unit tests
6. Under-relaxation parameter tuning
7. High-Reynolds number stabilization
8. Comprehensive logging and visualization

Usage:
```julia
using CFD.Utilities.SIMPLEDebugger
debug_info = debug_simple_solver(U_field, p_field, mesh, solver_settings)
```
"""
module SIMPLEDebugger

using LinearAlgebra
using SparseArrays
using StaticArrays
using Printf

# Optional dependencies
const HAS_PLOTS = try
    using Plots
    true
catch
    @warn "Plots.jl not available, plotting disabled"
    false
end

const HAS_CSV = try
    using CSV
    using DataFrames
    true
catch
    @warn "CSV/DataFrames not available, CSV logging disabled"
    false
end

# Check if optional packages are available
const HAS_ARPACK = try
    using Arpack
    true
catch
    @warn "Arpack.jl not available, eigenvalue analysis disabled"
    false
end

const HAS_ITERATIVESOLVERS = try
    using IterativeSolvers
    true
catch
    @warn "IterativeSolvers.jl not available, using fallback CG implementation"
    false
end

export debug_simple_solver, analyze_pressure_matrix, test_momentum_operators
export compute_velocity_divergence, track_solver_residuals, create_debug_plots
export test_under_relaxation, validate_discrete_operators

"""
    analyze_pressure_matrix(A::SparseMatrixCSC; check_eigenvalues=true)

Analyze the pressure Poisson matrix for numerical properties:
- Matrix conditioning
- Eigenvalue spectrum (if Arpack available)
- Diagonal dominance
- Symmetry properties
"""
function analyze_pressure_matrix(A::SparseMatrixCSC; check_eigenvalues=true)
    println("=== PRESSURE MATRIX ANALYSIS ===")
    
    n = size(A, 1)
    println("Matrix size: $(n)×$(n)")
    println("Non-zeros: $(nnz(A)) ($(round(100*nnz(A)/n^2, digits=2))% fill)")
    
    # Check symmetry
    symmetry_error = norm(A - A', Inf)
    println("Symmetry error: $(symmetry_error)")
    is_symmetric = symmetry_error < 1e-12
    println("Is symmetric: $(is_symmetric)")
    
    # Check diagonal dominance
    diag_vals = diag(A)
    off_diag_sums = [sum(abs, A[i, :]) - abs(A[i, i]) for i in 1:n]
    min_diag_dominance = minimum(diag_vals - off_diag_sums)
    println("Minimum diagonal dominance: $(min_diag_dominance)")
    
    # Matrix conditioning
    if n <= 1000  # Only for small matrices
        try
            cond_num = cond(Matrix(A))
            println("Condition number: $(cond_num)")
        catch e
            println("Could not compute condition number: $e")
        end
    end
    
    # Eigenvalue analysis
    if check_eigenvalues && HAS_ARPACK && n >= 10
        try
            # Smallest eigenvalues
            eigs_small = eigs(A; nev=min(6, n-2), which=:SM)
            eigenvals = real(eigs_small[1])
            sort!(eigenvals)
            
            println("Smallest eigenvalues: $(eigenvals)")
            
            min_eigenval = minimum(eigenvals)
            if min_eigenval < -1e-12
                @warn "Negative eigenvalues detected! Matrix may be indefinite: min λ = $(min_eigenval)"
                return false
            elseif min_eigenval < 1e-12
                println("Near-zero eigenvalue detected (nullspace): λ_min = $(min_eigenval)")
                println("This is expected for pure Neumann boundary conditions")
            else
                println("All eigenvalues positive: λ_min = $(min_eigenval)")
            end
            
        catch e
            @warn "Eigenvalue analysis failed: $e"
        end
    end
    
    return true
end

"""
    test_iterative_solver(A::SparseMatrixCSC, b::Vector; 
                         method=:cg, tol=1e-6, maxiter=500)

Test iterative solver convergence on the pressure system.
"""
function test_iterative_solver(A::SparseMatrixCSC, b::Vector; 
                              method=:cg, tol=1e-6, maxiter=500)
    println("\n=== ITERATIVE SOLVER TEST ===")
    println("Method: $(method), tolerance: $(tol), max iterations: $(maxiter)")
    
    n = size(A, 1)
    x0 = zeros(n)
    
    if HAS_ITERATIVESOLVERS
        try
            if method == :cg
                x, hist = cg(A, b; tol=tol, maxiter=maxiter, log=true)
            elseif method == :gmres
                x, hist = gmres(A, b; tol=tol, maxiter=maxiter, log=true)
            elseif method == :bicgstabl
                x, hist = bicgstabl(A, b; tol=tol, maxiter=maxiter, log=true)
            else
                error("Unknown method: $(method)")
            end
            
            final_residual = hist.resnorm[end]
            converged = final_residual < tol
            iterations = length(hist.resnorm) - 1
            
            println("Converged: $(converged)")
            println("Iterations: $(iterations)")
            println("Final residual: $(final_residual)")
            
            if !converged
                @warn "Solver did not converge! This indicates matrix issues."
            end
            
            return Dict(
                :converged => converged,
                :iterations => iterations,
                :final_residual => final_residual,
                :residual_history => hist.resnorm,
                :solution => x
            )
            
        catch e
            @error "Iterative solver failed: $e"
            return Dict(:converged => false, :error => string(e))
        end
    else
        # Fallback simple CG implementation
        println("Using fallback CG implementation")
        x, residuals = simple_cg(A, b, x0; tol=tol, maxiter=maxiter)
        
        final_residual = residuals[end]
        converged = final_residual < tol
        iterations = length(residuals) - 1
        
        println("Converged: $(converged)")
        println("Iterations: $(iterations)")
        println("Final residual: $(final_residual)")
        
        return Dict(
            :converged => converged,
            :iterations => iterations,
            :final_residual => final_residual,
            :residual_history => residuals,
            :solution => x
        )
    end
end

"""
Simple conjugate gradient implementation as fallback
"""
function simple_cg(A, b, x0; tol=1e-6, maxiter=500)
    x = copy(x0)
    r = b - A * x
    p = copy(r)
    residuals = [norm(r)]
    
    for i in 1:maxiter
        Ap = A * p
        alpha = dot(r, r) / dot(p, Ap)
        x += alpha * p
        r_new = r - alpha * Ap
        
        residual = norm(r_new)
        push!(residuals, residual)
        
        if residual < tol
            break
        end
        
        beta = dot(r_new, r_new) / dot(r, r)
        p = r_new + beta * p
        r = r_new
    end
    
    return x, residuals
end

"""
    compute_velocity_divergence(U_field, mesh)

Compute velocity divergence field and identify problematic regions.
"""
function compute_velocity_divergence(U_field, mesh)
    println("\n=== VELOCITY DIVERGENCE ANALYSIS ===")
    
    num_cells = length(mesh.cells)
    divergence = zeros(num_cells)
    
    # Compute divergence using finite volume method
    for (cell_idx, cell) in enumerate(mesh.cells)
        div_sum = 0.0
        
        for face_idx in cell.faces
            face = mesh.faces[face_idx]
            
            if face.boundary
                # Boundary face - use cell velocity
                face_velocity = U_field.data[cell_idx]
            else
                # Internal face - interpolate
                if face.owner == cell_idx
                    neighbor_idx = face.neighbor
                    face_velocity = 0.5 * (U_field.data[cell_idx] + U_field.data[neighbor_idx])
                    # Outward flux
                    div_sum += dot(face_velocity, face.normal * face.area)
                elseif face.neighbor == cell_idx
                    owner_idx = face.owner
                    face_velocity = 0.5 * (U_field.data[owner_idx] + U_field.data[cell_idx])
                    # Inward flux (negative)
                    div_sum -= dot(face_velocity, face.normal * face.area)
                end
            end
        end
        
        divergence[cell_idx] = div_sum / cell.volume
    end
    
    # Statistics
    max_div = maximum(abs.(divergence))
    mean_div = sum(abs.(divergence)) / num_cells
    rms_div = sqrt(sum(divergence.^2) / num_cells)
    
    println("Divergence statistics:")
    println("  Maximum |∇⋅U|: $(max_div)")
    println("  Mean |∇⋅U|: $(mean_div)")
    println("  RMS ∇⋅U: $(rms_div)")
    
    # Find problematic cells
    threshold = 10 * mean_div
    problematic_cells = findall(abs.(divergence) .> threshold)
    
    if !isempty(problematic_cells)
        println("  Problematic cells (|∇⋅U| > $(threshold)): $(length(problematic_cells))")
        println("  Cell indices: $(problematic_cells[1:min(10, end)])")
    end
    
    return Dict(
        :divergence => divergence,
        :max_divergence => max_div,
        :mean_divergence => mean_div,
        :rms_divergence => rms_div,
        :problematic_cells => problematic_cells
    )
end

"""
    validate_discrete_operators(mesh; test_linear=true, test_quadratic=true)

Unit test discrete operators on analytical fields.
"""
function validate_discrete_operators(mesh; test_linear=true, test_quadratic=true)
    println("\n=== DISCRETE OPERATOR VALIDATION ===")
    
    num_cells = length(mesh.cells)
    results = Dict()
    
    if test_linear
        println("Testing linear field u = ax + by + cz...")
        
        # Test coefficients
        a, b, c = 1.2, -0.8, 0.5
        
        # Create linear velocity field
        linear_u_data = [SVector{3,Float64}(
            a * cell.center[1] + b * cell.center[2] + c * cell.center[3],
            b * cell.center[1] - a * cell.center[2] + 0.5 * cell.center[3],
            c * cell.center[1] + 0.3 * cell.center[2] - a * cell.center[3]
        ) for cell in mesh.cells]
        
        # Compute divergence (should be constant: a - a - a = -a)
        div_result = compute_velocity_divergence_simple(linear_u_data, mesh)
        theoretical_div = a - a - a  # ∂u/∂x + ∂v/∂y + ∂w/∂z for this field
        
        max_error = maximum(abs.(div_result .- theoretical_div))
        println("  Linear field divergence error: $(max_error)")
        
        results[:linear_divergence_error] = max_error
    end
    
    if test_quadratic
        println("Testing quadratic field for Laplacian...")
        
        # Create quadratic scalar field: φ = x² + y² + z²
        quad_field_data = [sum(cell.center.^2) for cell in mesh.cells]
        
        # Theoretical Laplacian: ∇²φ = 2 + 2 + 2 = 6
        # This would require implementing discrete Laplacian test
        println("  Quadratic Laplacian test: TODO - implement discrete Laplacian")
        
        results[:quadratic_laplacian_test] = "TODO"
    end
    
    return results
end

"""
Simple divergence computation for validation
"""
function compute_velocity_divergence_simple(u_data, mesh)
    num_cells = length(mesh.cells)
    divergence = zeros(num_cells)
    
    for (i, cell) in enumerate(mesh.cells)
        # Simple finite difference approximation
        # This is a placeholder - actual implementation would use FVM
        center = cell.center
        
        # Find nearby cells for finite difference
        # For now, return zero as placeholder
        divergence[i] = 0.0
    end
    
    return divergence
end

"""
    create_debug_plots(debug_data; save_path="./debug_plots/")

Create comprehensive diagnostic plots.
"""
function create_debug_plots(debug_data; save_path="./debug_plots/")
    println("\n=== CREATING DEBUG PLOTS ===")
    
    # Create directory if it doesn't exist
    if !isdir(save_path)
        mkpath(save_path)
        println("Created directory: $(save_path)")
    end
    
    plots_created = String[]
    
    # Residual convergence plot
    if HAS_PLOTS && haskey(debug_data, :residual_history)
        residuals = debug_data[:residual_history]
        if !isempty(residuals)
            try
                p1 = plot(1:length(residuals), residuals,
                         yscale=:log10,
                         xlabel="Iteration",
                         ylabel="Residual",
                         title="Solver Convergence",
                         linewidth=2,
                         legend=false)
                
                plot_path = joinpath(save_path, "residual_convergence.png")
                savefig(p1, plot_path)
                push!(plots_created, plot_path)
                println("  Saved: residual_convergence.png")
            catch e
                @warn "Could not create residual plot: $e"
            end
        end
    end
    
    # Divergence field plot (if 2D structured)
    if HAS_PLOTS && haskey(debug_data, :divergence_field)
        try
            div_field = debug_data[:divergence_field]
            
            p2 = heatmap(div_field,
                        title="Velocity Divergence Field",
                        xlabel="i",
                        ylabel="j",
                        aspect_ratio=:equal,
                        color=:RdBu)
            
            plot_path = joinpath(save_path, "divergence_field.png")
            savefig(p2, plot_path)
            push!(plots_created, plot_path)
            println("  Saved: divergence_field.png")
        catch e
            @warn "Could not create divergence plot: $e"
        end
    end
    
    # Eigenvalue spectrum plot
    if HAS_PLOTS && haskey(debug_data, :eigenvalues)
        try
            eigenvals = real(debug_data[:eigenvalues])
            
            p3 = scatter(1:length(eigenvals), eigenvals,
                        xlabel="Index",
                        ylabel="Eigenvalue",
                        title="Pressure Matrix Eigenvalues",
                        markersize=3,
                        legend=false)
            
            # Add zero line
            hline!([0], color=:red, linestyle=:dash, alpha=0.7)
            
            plot_path = joinpath(save_path, "eigenvalue_spectrum.png")
            savefig(p3, plot_path)
            push!(plots_created, plot_path)
            println("  Saved: eigenvalue_spectrum.png")
        catch e
            @warn "Could not create eigenvalue plot: $e"
        end
    end
    
    println("Created $(length(plots_created)) debug plots in $(save_path)")
    return plots_created
end

"""
    debug_simple_solver(U_field, p_field, mesh, settings; 
                       full_analysis=true, save_plots=true)

Comprehensive SIMPLE solver debugging function.
"""
function debug_simple_solver(U_field, p_field, mesh, settings; 
                           full_analysis=true, save_plots=true)
    println("🔍 SIMPLE SOLVER DEBUGGING SUITE")
    println("="^50)
    
    debug_results = Dict{Symbol, Any}()
    
    # 1. Mesh statistics
    println("Mesh: $(length(mesh.cells)) cells, $(length(mesh.faces)) faces")
    debug_results[:mesh_cells] = length(mesh.cells)
    debug_results[:mesh_faces] = length(mesh.faces)
    
    # 2. Field statistics
    max_velocity = maximum(norm.(U_field.data))
    avg_velocity = sum(norm.(U_field.data)) / length(U_field.data)
    pressure_range = maximum(p_field.data) - minimum(p_field.data)
    
    println("Max velocity: $(max_velocity)")
    println("Avg velocity: $(avg_velocity)")
    println("Pressure range: $(pressure_range)")
    
    debug_results[:max_velocity] = max_velocity
    debug_results[:avg_velocity] = avg_velocity
    debug_results[:pressure_range] = pressure_range
    
    # 3. Velocity divergence analysis
    if full_analysis
        div_analysis = compute_velocity_divergence(U_field, mesh)
        debug_results[:divergence_analysis] = div_analysis
    end
    
    # 4. Discrete operator validation
    if full_analysis
        operator_validation = validate_discrete_operators(mesh)
        debug_results[:operator_validation] = operator_validation
    end
    
    # 5. Create diagnostic plots
    if save_plots
        plots = create_debug_plots(debug_results)
        debug_results[:plots_created] = plots
    end
    
    # 6. Recommendations
    println("\n=== DEBUGGING RECOMMENDATIONS ===")
    
    if debug_results[:max_velocity] > 1e10
        @warn "Extremely high velocities detected - solution likely diverged"
        println("→ Check matrix assembly and boundary conditions")
        println("→ Reduce under-relaxation factors")
        println("→ Use lower Reynolds number for initial testing")
    end
    
    if haskey(debug_results, :divergence_analysis)
        max_div = debug_results[:divergence_analysis][:max_divergence]
        if max_div > 1e3
            @warn "High velocity divergence detected: $(max_div)"
            println("→ Check mass conservation in discretization")
            println("→ Verify Rhie-Chow interpolation implementation")
        end
    end
    
    println("\n✅ Debugging analysis complete")
    return debug_results
end

end # module SIMPLEDebugger