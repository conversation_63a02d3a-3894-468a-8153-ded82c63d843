# Conservative mesh generation that ensures proper face pairing and conservation
module <PERSON><PERSON><PERSON>

using StaticArrays
using LinearAlgebra
using ..CFDCore

export create_conservative_unit_cube_mesh

"""
Create a unit cube mesh with guaranteed conservation properties:
1. All internal faces properly paired (owner ↔ neighbor)
2. Face normals consistently point from owner → neighbor
3. ∑(face.normal * face.area) = [0,0,0] exactly
4. Owner index < Neighbor index for all internal faces
"""
function create_conservative_unit_cube_mesh(nx::Int, ny::Int, nz::Int)
    println("Creating conservative $(nx)×$(ny)×$(nz) mesh...")
    
    dx = 1.0 / nx
    dy = 1.0 / ny  
    dz = 1.0 / nz
    
    # ========== CREATE NODES ==========
    nodes = Node[]
    for k in 0:nz, j in 0:ny, i in 0:nx
        x = i * dx
        y = j * dy
        z = k * dz
        push!(nodes, Node{Float64,3}(length(nodes) + 1, SVector(x, y, z), false))
    end
    
    # Helper to get node index
    node_idx(i, j, k) = k * (nx+1) * (ny+1) + j * (nx+1) + i + 1
    
    # ========== CREATE CELLS ==========
    cells = Cell[]
    for k in 1:nz, j in 1:ny, i in 1:nx
        # Get 8 vertices of hexahedron
        v1 = node_idx(i-1, j-1, k-1)
        v2 = node_idx(i, j-1, k-1)
        v3 = node_idx(i, j, k-1)
        v4 = node_idx(i-1, j, k-1)
        v5 = node_idx(i-1, j-1, k)
        v6 = node_idx(i, j-1, k)
        v7 = node_idx(i, j, k)
        v8 = node_idx(i-1, j, k)
        
        vertex_indices = [v1, v2, v3, v4, v5, v6, v7, v8]
        center = SVector((i-0.5)*dx, (j-0.5)*dy, (k-0.5)*dz)
        volume = dx * dy * dz
        
        push!(cells, Cell{Float64,3}(length(cells) + 1, vertex_indices, Int[], center, volume))
    end
    
    # Helper to get cell index  
    cell_idx(i, j, k) = (k-1) * nx * ny + (j-1) * nx + i
    
    # ========== CREATE FACES WITH CONSERVATION ==========
    faces = Face[]
    face_map = Dict{Tuple{Int,Int,Int,Int}, Int}()  # Maps (v1,v2,v3,v4) → face_id
    
    # Function to create face with consistent orientation
    function create_face!(vertices::Vector{Int}, owner::Int, neighbor::Int, 
                         is_boundary::Bool)
        
        # Calculate face properties
        p1 = nodes[vertices[1]].coords
        p2 = nodes[vertices[2]].coords
        p3 = nodes[vertices[3]].coords
        p4 = nodes[vertices[4]].coords
        
        # Face center
        center = 0.25 * (p1 + p2 + p3 + p4)
        
        # Face normal using cross product of diagonals
        d1 = p3 - p1
        d2 = p4 - p2
        normal_vec = 0.5 * cross(d1, d2)
        area = norm(normal_vec)
        normal = normalize(normal_vec)
        
        # For internal faces, ensure normal points from owner to neighbor
        if !is_boundary && neighbor > 0
            owner_center = cells[owner].center
            neighbor_center = cells[neighbor].center
            owner_to_neighbor = neighbor_center - owner_center
            
            # If normal points opposite to owner→neighbor, flip it
            if dot(normal, owner_to_neighbor) < 0
                normal = -normal
            end
        end
        
        # For boundary faces, ensure normal points outward from domain
        if is_boundary
            owner_center = cells[owner].center
            face_to_center = center - owner_center
            
            # Normal should point away from cell center
            if dot(normal, face_to_center) < 0
                normal = -normal
            end
        end
        
        face_id = length(faces) + 1
        face = Face{Float64,3}(face_id, vertices, center, area, normal, owner, neighbor, is_boundary)
        push!(faces, face)
        
        return face_id
    end
    
    # ========== PROCESS ALL CELL FACES ==========
    
    for k in 1:nz, j in 1:ny, i in 1:nx
        cell_id = cell_idx(i, j, k)
        
        # Define 6 faces of hexahedron with consistent vertex ordering
        face_definitions = [
            # (vertices, direction, neighbor_offset)
            ([node_idx(i-1,j-1,k-1), node_idx(i-1,j,k-1), node_idx(i-1,j,k), node_idx(i-1,j-1,k)], "west", (-1,0,0)),   # -X face
            ([node_idx(i,j-1,k-1), node_idx(i,j-1,k), node_idx(i,j,k), node_idx(i,j,k-1)], "east", (1,0,0)),      # +X face
            ([node_idx(i-1,j-1,k-1), node_idx(i,j-1,k-1), node_idx(i,j-1,k), node_idx(i-1,j-1,k)], "south", (0,-1,0)),  # -Y face
            ([node_idx(i-1,j,k-1), node_idx(i-1,j,k), node_idx(i,j,k), node_idx(i,j,k-1)], "north", (0,1,0)),     # +Y face
            ([node_idx(i-1,j-1,k-1), node_idx(i,j-1,k-1), node_idx(i,j,k-1), node_idx(i-1,j,k-1)], "bottom", (0,0,-1)), # -Z face
            ([node_idx(i-1,j-1,k), node_idx(i-1,j,k), node_idx(i,j,k), node_idx(i,j-1,k)], "top", (0,0,1))        # +Z face
        ]
        
        for (vertices, direction, offset) in face_definitions
            # Create unique face key (sorted vertices)
            face_key = Tuple(sort(vertices))
            
            # Calculate neighbor cell
            ni, nj, nk = i + offset[1], j + offset[2], k + offset[3]
            
            if haskey(face_map, face_key)
                # Face already exists - this should be an internal face
                existing_face_id = face_map[face_key]
                existing_face = faces[existing_face_id]
                
                # Update the existing face to become internal
                if existing_face.neighbor == -1  # It was a boundary face
                    # Make it internal by setting neighbor
                    neighbor_id = cell_id
                    owner_id = existing_face.owner
                    
                    # Ensure owner < neighbor ordering
                    if owner_id > neighbor_id
                        owner_id, neighbor_id = neighbor_id, owner_id
                    end
                    
                    # Create corrected internal face
                    updated_face = Face{Float64,3}(
                        existing_face.id,
                        existing_face.nodes,
                        existing_face.center,
                        existing_face.area,
                        existing_face.normal,
                        owner_id,
                        neighbor_id,
                        false  # No longer boundary
                    )
                    faces[existing_face_id] = updated_face
                end
            else
                # New face - determine if boundary or will become internal
                is_boundary = (ni < 1 || ni > nx || nj < 1 || nj > ny || nk < 1 || nk > nz)
                
                if is_boundary
                    # Boundary face
                    face_id = create_face!(vertices, cell_id, -1, true)
                else
                    # Will be internal (neighbor will be set when neighbor cell is processed)
                    face_id = create_face!(vertices, cell_id, -1, true)  # Temporary boundary
                end
                
                face_map[face_key] = face_id
            end
        end
    end
    
    # ========== CREATE BOUNDARY GROUPS ==========
    boundaries = Dict{String, Vector{Int}}()
    
    # We need to reconstruct boundary patches since Face doesn't store patch names
    for (f_idx, face) in enumerate(faces)
        if face.boundary
            # Determine patch based on face location
            face_center = face.center
            patch_name = if abs(face_center[1]) < 1e-10
                "inlet"
            elseif abs(face_center[1] - 1.0) < 1e-10
                "outlet"
            else
                "walls"
            end
            
            if !haskey(boundaries, patch_name)
                boundaries[patch_name] = Int[]
            end
            push!(boundaries[patch_name], f_idx)
        end
    end
    
    # ========== VALIDATION ==========
    mesh = UnstructuredMesh{Float64,3}(
        nodes,
        faces,
        cells,
        boundaries,
        [Int[] for _ in 1:length(cells)],  # cell_to_cell (empty for now)
        [(f.owner, f.neighbor) for f in faces],  # face_to_cell
        (SVector(0.0, 0.0, 0.0), SVector(1.0, 1.0, 1.0))  # bbox
    )
    validate_conservation(mesh)
    
    return mesh
end

"""
Determine boundary patch name based on face location
"""
function determine_boundary_patch(i, j, k, offset, nx, ny, nz)
    if offset == (-1, 0, 0) && i == 1
        return "inlet"     # Left boundary (x = 0)
    elseif offset == (1, 0, 0) && i == nx
        return "outlet"    # Right boundary (x = 1)
    else
        return "walls"     # All other boundaries (y=0, y=1, z=0, z=1)
    end
end

"""
Validate mesh conservation properties
"""
function validate_conservation(mesh::UnstructuredMesh)
    println("\n=== MESH CONSERVATION VALIDATION ===")
    
    # Check global conservation
    total_area_vector = sum(f.normal * f.area for f in mesh.faces)
    conservation_error = norm(total_area_vector)
    
    println("Total area vector: $total_area_vector")
    println("Conservation error: $conservation_error")
    
    if conservation_error < 1e-12
        println("✅ PERFECT: Mesh is conservative!")
    elseif conservation_error < 1e-6
        println("✅ GOOD: Mesh is nearly conservative")
    else
        println("❌ ERROR: Mesh conservation violation!")
    end
    
    # Check face count consistency
    internal_faces = count(f -> !f.boundary, mesh.faces)
    boundary_faces = count(f -> f.boundary, mesh.faces)
    
    println("\nFace statistics:")
    println("  Internal faces: $internal_faces")
    println("  Boundary faces: $boundary_faces")
    println("  Total faces: $(length(mesh.faces))")
    
    # Check owner < neighbor ordering
    ordering_violations = 0
    for face in mesh.faces
        if !face.boundary && face.neighbor > 0 && face.owner >= face.neighbor
            ordering_violations += 1
        end
    end
    
    if ordering_violations == 0
        println("✅ All internal faces have owner < neighbor")
    else
        println("❌ $ordering_violations faces violate owner < neighbor ordering")
    end
    
    return conservation_error < 1e-12
end

end # module ConservativeMesh