# Heat Transfer Solver Manifest
[solver]
name = "heatTransferFoam"
version = "1.0.0"
description = "Transient heat transfer solver with optional buoyancy-driven flow"
author = "CFD.jl Framework"
license = "MIT"

# Main solver file
main = "HeatTransferSolver.jl"

# Physics capabilities
[physics]
models = ["heat_transfer", "natural_convection", "forced_convection"]
equations = ["energy", "momentum", "continuity"]
coupling = ["temperature-flow", "buoyancy"]

# Required fields
[fields.required]
T = "Temperature field"

# Optional fields  
[fields.optional]
U = "Velocity field (for convection)"
p = "Pressure field"
rho = "Density"
k = "Thermal conductivity"
Cp = "Specific heat capacity"

# Default configuration
[defaults]
endTime = 1.0
deltaT = 0.001
writeInterval = 0.1
coupled_flow = false

[defaults.tolerances]
T = 1e-6
U = 1e-5
p = 1e-7

[defaults.relaxation]
T = 0.9
U = 0.7
p = 0.3

# Example cases
[[examples]]
name = "heated_cavity"
description = "Natural convection in a square cavity"
path = "examples/heated_cavity"

[[examples]]
name = "heat_exchanger"
description = "Forced convection heat exchanger"
path = "examples/heat_exchanger"