# src/Physics/Physics.jl
module Physics

export AbstractPhysicsModel, AbstractFlowModel, AbstractTurbulenceModel, AbstractMultiphaseModel, AbstractCombustionModel, AbstractThermalModel
export Incompressible, Compressible, kEpsilon, kOmegaSST, LES, DNS
export VOF, Eulerian # Lagrangian was mentioned in plane.md but not defined, so omitting for now
export ThermalModel
export PISO # PISO struct was defined in this section in plane.md
export strain_rate_magnitude, production_term, source_terms, wall_distance, turbulent_viscosity
export energy_equation, buoyancy_source, turbulent_thermal_diffusivity
export momentum_equation, momentum_equation_turbulent
export k_equation, epsilon_equation, solve_rans_step!

# Physics Unicode symbols for enhanced CFD notation
export ρ_field, μ_field, ν_field, k_field, ε_field, ω_field  # Field constructors
export Re, Pr, Gr, Ma  # Dimensionless numbers
export Cμ, Cε1, Cε2, σk, σε  # Turbulence model constants

# Export physics type symbols for DSL
export IncompressibleFlow, CompressibleFlow, TurbulentFlow, HeatTransfer, MultiphaseFlow
export LaminarFlow, TransientFlow, SteadyFlow

using ..CFDCore
using ..CFDCore: AbstractMesh, ScalarField, VectorField, Field, AbstractBoundaryCondition
using ..Numerics # For fvc, fvm, schemes like UpwindInterpolation, GaussGradient, CentralDifferencing
using ..Numerics: GaussGradient, CentralDifferencing, UpwindInterpolation
using LinearAlgebra
using StaticArrays # Potentially for SVector/SMatrix if needed in physics calculations

# Abstract physics model types
abstract type AbstractPhysicsModel end
abstract type AbstractFlowModel <: AbstractPhysicsModel end
abstract type AbstractTurbulenceModel <: AbstractPhysicsModel end
abstract type AbstractMultiphaseModel <: AbstractPhysicsModel end
abstract type AbstractCombustionModel <: AbstractPhysicsModel end

# Flow models
struct Incompressible <: AbstractFlowModel
    ρ::Float64  # Density
    ν::Float64  # Kinematic viscosity
end

struct Compressible <: AbstractFlowModel
    γ::Float64  # Specific heat ratio
    R::Float64  # Gas constant
    Pr::Float64 # Prandtl number
end

# Turbulence models
struct kEpsilon <: AbstractTurbulenceModel
    Cμ::Float64
    C1ε::Float64
    C2ε::Float64
    σk::Float64
    σε::Float64
    
    # Default constructor with standard constants
    kEpsilon() = new(0.09, 1.44, 1.92, 1.0, 1.3)
end

struct kOmegaSST <: AbstractTurbulenceModel
    α1::Float64
    β1::Float64
    σω1::Float64 # σ_ω1 in some notations
    σk1::Float64 # σ_k1
    α2::Float64
    β2::Float64
    σω2::Float64 # σ_ω2
    σk2::Float64 # σ_k2
    
    kOmegaSST() = new(0.31, 0.075, 0.85, 0.5, 0.44, 0.0828, 0.856, 1.0)
end

struct LES <: AbstractTurbulenceModel
    model::Symbol  # :Smagorinsky, :WALE, :Dynamic
    Cs::Float64    # Smagorinsky constant
    
    LES(model=:Smagorinsky, Cs=0.1) = new(model, Cs)
end

struct DNS <: AbstractTurbulenceModel end

# Multiphase models
struct VOF <: AbstractMultiphaseModel
    phases::Vector{Symbol}
    surface_tension::Matrix{Float64} # Assuming interaction between phases
    contact_angles::Dict{Symbol, Float64} # Phase to contact angle
end

struct Eulerian <: AbstractMultiphaseModel
    phases::Vector{Symbol}
    drag_model::Symbol  # :SchillerNaumann, :WenYu, etc.
end

# Heat transfer models
abstract type AbstractThermalModel <: AbstractPhysicsModel end

struct ThermalModel <: AbstractThermalModel
    Pr::Float64      # Prandtl number
    Prt::Float64     # Turbulent Prandtl number
    β::Float64       # Thermal expansion coefficient (for buoyancy)
    T_ref::Float64   # Reference temperature
    
    ThermalModel(; Pr=0.71, Prt=0.9, β=0.0, T_ref=293.15) = new(Pr, Prt, β, T_ref)
end

# Model-specific functions
# Note: These functions take Fields as input. Their data is accessed via .data
function turbulent_viscosity(model::kEpsilon, k::ScalarField, ε::ScalarField)
    # ν_t = Cμ * k^2 / ε
    return model.Cμ .* k.data.^2 ./ (ε.data .+ 1e-12) # Add small epsilon to avoid division by zero
end

function turbulent_viscosity(model::kOmegaSST, k::ScalarField, ω::ScalarField)
    # ν_t = k / ω (simplified, actual SST has blending functions and limiters)
    return k.data ./ (ω.data .+ 1e-12) # Add small epsilon
end

# Strain rate tensor calculation
function strain_rate_magnitude(U::VectorField)
    """
    Calculate |S| = sqrt(2 * Sij * Sij) where Sij = 0.5 * (∂Ui/∂xj + ∂Uj/∂xi)
    """
    mesh = U.mesh
    num_cells = length(mesh.cells)
    strain_magnitude = zeros(Float64, num_cells)
    
    # Calculate velocity gradients at cell centers using Green-Gauss theorem
    for cell_idx in 1:num_cells
        cell = mesh.cells[cell_idx]
        grad_u = zeros(3, 3)  # Velocity gradient tensor
        
        # Sum contributions from all faces of this cell
        face_count = 0
        for face_idx in 1:length(mesh.faces)
            face = mesh.faces[face_idx]
            
            if face.owner == cell_idx
                face_count += 1
                if face.boundary
                    # Boundary face - assume zero gradient (can be improved)
                    face_velocity = U.data[cell_idx]
                else
                    # Internal face - use neighbor value
                    neighbor_velocity = U.data[face.neighbor]
                    face_velocity = 0.5 * (U.data[cell_idx] + neighbor_velocity)
                end
                
                # Green-Gauss gradient: ∇u = (1/V) * Σ(face_value * face_normal * face_area)
                for i in 1:3, j in 1:3
                    grad_u[i, j] += face_velocity[i] * face.normal[j] * face.area / cell.volume
                end
            end
        end
        
        # Calculate strain rate tensor: S_ij = 0.5 * (∂ui/∂xj + ∂uj/∂xi)
        strain_tensor = 0.5 * (grad_u + grad_u')
        
        # Calculate magnitude: |S| = sqrt(2 * Sij * Sij)
        strain_magnitude[cell_idx] = sqrt(2.0 * sum(strain_tensor.^2))
    end
    
    return strain_magnitude
end

# Turbulence production terms for k-ε model
function production_term(model::kEpsilon, U::VectorField, k::ScalarField, ε::ScalarField, νt::AbstractArray{Float64})
    """
    Calculate turbulence production Pk = νt * |S|^2
    where |S| is the strain rate magnitude
    """
    strain_mag = strain_rate_magnitude(U)
    Pk = νt .* strain_mag.^2
    return Pk
end

# Source terms for k-ε model with proper physics
function source_terms(model::kEpsilon, U::VectorField, k::ScalarField, ε::ScalarField, νt::AbstractArray{Float64})
    # Calculate turbulence production
    Pk = production_term(model, U, k, ε, νt)
    
    # k-equation source terms: Sk = Pk - ε
    Sk_data = Pk .- ε.data
    
    # ε-equation source terms: Sε = (C1ε * Pk - C2ε * ε) * ε / k
    Sε_data = (model.C1ε .* Pk .- model.C2ε .* ε.data) .* ε.data ./ (k.data .+ 1e-12)
    
    return Sk_data, Sε_data
end

# Enhanced k-ω SST turbulent viscosity with limiters
function turbulent_viscosity(model::kOmegaSST, k::ScalarField, ω::ScalarField, strain_mag::AbstractArray{Float64})
    # SST limiter: νt = a1*k / max(a1*ω, S*F2)
    # where S is strain rate magnitude, F2 is blending function
    # Simplified version without full blending functions
    a1 = 0.31  # SST constant
    
    νt = similar(k.data)
    for i in 1:length(k.data)
        denominator = max(a1 * ω.data[i], strain_mag[i])
        νt[i] = a1 * k.data[i] / (denominator + 1e-12)
    end
    
    return νt
end

# Simple wall distance calculation (placeholder)
function wall_distance(mesh::AbstractMesh)
    """
    Calculate approximate wall distance for each cell.
    This is a simplified version - production code would use proper algorithms.
    """
    num_cells = length(mesh.cells)
    y = ones(Float64, num_cells) * 1e-3  # Default small value
    
    # Find boundary faces and estimate distance
    for (patch_name, face_indices) in mesh.boundaries
        if occursin("wall", lowercase(patch_name)) || occursin("wall", lowercase(string(patch_name)))
            for face_idx in face_indices
                face = mesh.faces[face_idx]
                cell_idx = face.owner
                
                # Distance from cell center to face center (approximation)
                dist = norm(mesh.cells[cell_idx].center - face.center)
                y[cell_idx] = min(y[cell_idx], dist)
            end
        end
    end
    
    return y
end

# Energy equation for heat transfer
function energy_equation(thermal::ThermalModel, T::ScalarField, U::VectorField, 
                        α::ScalarField, αt::ScalarField, Δt::Float64)
    """
    Energy equation: ∂T/∂t + ∇·(UT) = ∇·((α + αt)∇T) + ST
    where α is molecular thermal diffusivity, αt is turbulent thermal diffusivity
    """
    # Time derivative
    ddt_T = Numerics.fvm.ddt(T, T, Δt)  # Current and old temperature fields
    
    # Convection term
    conv_T = Numerics.fvm.convection(U, T)
    
    # Effective thermal diffusivity field
    α_eff_data = α.data .+ αt.data
    α_eff = ScalarField(:alpha_eff, T.mesh, α_eff_data, Dict{String, AbstractBoundaryCondition}())
    
    # Diffusion term
    diff_T = Numerics.fvm.laplacian(α_eff, T)
    
    # For now, no explicit source terms (ST = 0)
    # In practice, ST could include heat sources, buoyancy, radiation, etc.
    
    # Energy equation: ∂T/∂t + ∇·(UT) - ∇·(α_eff∇T) = 0
    # Note: This returns FvMatrix structure for the linear system
    return ddt_T.A + conv_T.A - diff_T.A, ddt_T.b + conv_T.b - diff_T.b
end

# Buoyancy source term for momentum equation
function buoyancy_source(thermal::ThermalModel, T::ScalarField, g::SVector{3,Float64})
    """
    Calculate buoyancy source term: -ρβg(T - T_ref)
    This should be added to the momentum equation RHS
    """
    num_cells = length(T.data)
    buoyancy_data = Vector{SVector{3,Float64}}(undef, num_cells)
    
    for i in 1:num_cells
        dT = T.data[i] - thermal.T_ref
        buoyancy_data[i] = -thermal.β * dT * g
    end
    
    return VectorField(:buoyancy, T.mesh, buoyancy_data, Dict{String, AbstractBoundaryCondition}())
end

# Turbulent thermal diffusivity
function turbulent_thermal_diffusivity(thermal::ThermalModel, νt::AbstractArray{Float64})
    """
    Calculate turbulent thermal diffusivity: αt = νt / Prt
    """
    αt = νt ./ thermal.Prt
    return αt
end


# Equation systems for different models
function momentum_equation(model::Incompressible, U::VectorField, p::ScalarField, Δt::Float64; 
                          νt::Union{Nothing, AbstractArray{Float64}}=nothing)
    """
    Assemble momentum equation: ∂U/∂t + ∇·(UU) = -∇p + ∇·((ν + νt)∇U)
    Returns matrices and source terms for component-wise solution
    """
    mesh = U.mesh
    num_cells = length(mesh.cells)
    
    # Effective viscosity (molecular + turbulent)
    ν_eff = model.ν
    if νt !== nothing
        ν_eff_data = fill(model.ν, num_cells) .+ νt
        ν_eff_field = ScalarField(:nu_eff, mesh, ν_eff_data, Dict{String, AbstractBoundaryCondition}())
    else
        ν_eff_field = ScalarField(:nu_eff, mesh, fill(model.ν, num_cells), Dict{String, AbstractBoundaryCondition}())
    end
    
    # Solve momentum equation component-wise
    U_matrices = Vector{Numerics.fvm.FvMatrix{Float64}}(undef, 3)
    
    for comp in 1:3
        # Extract component field
        U_comp_data = [U.data[i][comp] for i in 1:num_cells]
        
        # Create scalar boundary conditions for this component
        U_comp_bcs = Dict{String, AbstractBoundaryCondition}()
        for (patch_name, bc) in U.boundary_conditions
            if bc isa DirichletBC
                # Extract component from vector BC
                U_comp_bcs[patch_name] = DirichletBC((x, y, z, t) -> bc.value(x, y, z, t)[comp])
            elseif bc isa NeumannBC
                # Extract component from vector BC gradient
                U_comp_bcs[patch_name] = NeumannBC((x, y, z, t) -> bc.gradient(x, y, z, t)[comp])
            end
        end
        
        U_comp = ScalarField(Symbol("U", comp), mesh, U_comp_data, U_comp_bcs)
        
        # Time derivative term
        U_old_comp = ScalarField(Symbol("U", comp, "_old"), mesh, U_comp_data, U_comp_bcs)
        ddt_term = Numerics.fvm.ddt(U_comp, U_old_comp, Δt)
        
        # Convection term: ∇·(UU_comp)
        conv_term = Numerics.fvm.convection(U, U_comp)
        
        # Diffusion term: ∇·(ν_eff∇U_comp)
        diff_term = Numerics.fvm.laplacian(ν_eff_field, U_comp)
        
        # Assemble: ∂U/∂t + ∇·(UU) - ∇·(ν_eff∇U) = -∇p_comp (pressure gradient added later)
        A_momentum = ddt_term.A + conv_term.A - diff_term.A
        b_momentum = ddt_term.b + conv_term.b - diff_term.b
        
        U_matrices[comp] = Numerics.fvm.FvMatrix(A_momentum, b_momentum)
    end
    
    return U_matrices
end

# Enhanced momentum equation with turbulence
function momentum_equation_turbulent(model::Incompressible, U::VectorField, p::ScalarField, 
                                   k::ScalarField, ε::ScalarField, turbulence_model, Δt::Float64)
    """
    Momentum equation with turbulent viscosity from RANS models
    """
    # Calculate turbulent viscosity
    νt = turbulent_viscosity(turbulence_model, k, ε)
    
    # Use enhanced momentum equation
    return momentum_equation(model, U, p, Δt; νt=νt)
end

function continuity_equation(model::Incompressible, U::VectorField)
    # Continuity equation: ∇·U = 0
    # This is typically not an equation solved in the same way as momentum for incompressible flow.
    # It's used to derive the pressure equation.
    # fvc.div returns a ScalarField representing ∇·U.
    return Numerics.fvc.div(U, CentralDifferencing()) # Returns a ScalarField
end

# PISO algorithm components (as defined in plane.md under Physics)
struct PISO
    nCorrectors::Int
    nNonOrthogonalCorrectors::Int
    
    PISO(nCorr=2, nNonOrth=1) = new(nCorr, nNonOrth)
end

# pressure_correction_equation was also in plane.md under Physics
# model::Incompressible, U::VectorField, p::ScalarField, rAU::ScalarField, Δt::Float64
# rAU is typically 1/Ap from the momentum matrix diagonal after assembling H(U) terms.
# plane.md: ∇·(rAU∇p') = ∇·U* (where U* is from discretized momentum)
# The fvc.div(U,...) on RHS implies it's the divergence of the current/intermediate velocity field.
function pressure_correction_equation(model::Incompressible, U_star_field::VectorField, 
                                      p_prime::ScalarField, rAU_field::ScalarField, Δt::Float64)
    # Equation for pressure correction p': ∇·( (1/Ap) ∇p' ) = ∇·U*
    # where rAU_field represents (1/Ap) or similar coefficients for the laplacian term.
    # The LHS is an implicit Laplacian for p_prime.
    # The RHS is the divergence of the U* field (explicitly calculated).
    
    lhs_ppe = Numerics.fvm.laplacian(rAU_field, p_prime, CentralDifferencing()) # rAU acts as 'gamma'
    rhs_ppe = Numerics.fvc.div(U_star_field, CentralDifferencing()) # This returns a ScalarField

    return lhs_ppe == rhs_ppe
end

# Turbulence equation solvers
function k_equation(model::kEpsilon, U::VectorField, k::ScalarField, ε::ScalarField, 
                   νt::AbstractArray{Float64}, Δt::Float64)
    """
    k-equation: ∂k/∂t + ∇·(Uk) = ∇·((ν + νt/σk)∇k) + Pk - ε
    """
    mesh = k.mesh
    num_cells = length(mesh.cells)
    
    # Effective diffusivity: ν + νt/σk
    σk = 1.0  # k-ε model constant
    ν_eff_k_data = fill(0.01, num_cells) .+ νt ./ σk  # Assuming molecular ν = 0.01
    ν_eff_k = ScalarField(:nu_eff_k, mesh, ν_eff_k_data, Dict{String, AbstractBoundaryCondition}())
    
    # Calculate source terms
    Sk_data, _ = source_terms(model, U, k, ε, νt)
    
    # Assemble k-equation
    k_old = ScalarField(:k_old, mesh, k.data, k.boundary_conditions)
    ddt_k = Numerics.fvm.ddt(k, k_old, Δt)
    conv_k = Numerics.fvm.convection(U, k)
    diff_k = Numerics.fvm.laplacian(ν_eff_k, k)
    
    # k-equation matrix
    A_k = ddt_k.A + conv_k.A - diff_k.A
    b_k = ddt_k.b + conv_k.b - diff_k.b .+ Sk_data
    
    return Numerics.fvm.FvMatrix(A_k, b_k)
end

function epsilon_equation(model::kEpsilon, U::VectorField, k::ScalarField, ε::ScalarField, 
                         νt::AbstractArray{Float64}, Δt::Float64)
    """
    ε-equation: ∂ε/∂t + ∇·(Uε) = ∇·((ν + νt/σε)∇ε) + Sε
    """
    mesh = ε.mesh
    num_cells = length(mesh.cells)
    
    # Effective diffusivity: ν + νt/σε
    σε = 1.3  # k-ε model constant
    ν_eff_ε_data = fill(0.01, num_cells) .+ νt ./ σε
    ν_eff_ε = ScalarField(:nu_eff_eps, mesh, ν_eff_ε_data, Dict{String, AbstractBoundaryCondition}())
    
    # Calculate source terms
    _, Sε_data = source_terms(model, U, k, ε, νt)
    
    # Assemble ε-equation
    ε_old = ScalarField(:eps_old, mesh, ε.data, ε.boundary_conditions)
    ddt_ε = Numerics.fvm.ddt(ε, ε_old, Δt)
    conv_ε = Numerics.fvm.convection(U, ε)
    diff_ε = Numerics.fvm.laplacian(ν_eff_ε, ε)
    
    # ε-equation matrix
    A_ε = ddt_ε.A + conv_ε.A - diff_ε.A
    b_ε = ddt_ε.b + conv_ε.b - diff_ε.b .+ Sε_data
    
    return Numerics.fvm.FvMatrix(A_ε, b_ε)
end

# Complete RANS solver combining momentum and turbulence
function solve_rans_step!(U::VectorField, p::ScalarField, k::ScalarField, ε::ScalarField,
                         model::Incompressible, turbulence_model::kEpsilon, Δt::Float64)
    """
    Complete RANS step: solve momentum, k, ε equations with turbulent viscosity coupling
    """
    num_cells = length(U.mesh.cells)
    
    # Iterate between momentum and turbulence equations
    for iter in 1:3  # Inner iterations for coupling
        
        # 1. Calculate turbulent viscosity from current k, ε
        νt = turbulent_viscosity(turbulence_model, k, ε)
        
        # 2. Solve momentum equations with updated νt
        U_matrices = momentum_equation(model, U, p, Δt; νt=νt)
        
        # 3. Solve k-equation
        k_matrix = k_equation(turbulence_model, U, k, ε, νt, Δt)
        k_solution = k_matrix.A \ k_matrix.b
        k.data .= max.(k_solution, 1e-12)  # Ensure positive k
        
        # 4. Solve ε-equation  
        ε_matrix = epsilon_equation(turbulence_model, U, k, ε, νt, Δt)
        ε_solution = ε_matrix.A \ ε_matrix.b
        ε.data .= max.(ε_solution, 1e-12)  # Ensure positive ε
        
        println("RANS iteration $iter completed")
    end
    
    return νt
end

# ============================================================================
# Physics Unicode Symbols and Dimensionless Numbers
# ============================================================================

"""
Physics Unicode symbols for enhanced CFD notation.
These provide intuitive mathematical notation for physical quantities.
"""

# Field constructor functions using Unicode symbols
"""
    ρ_field(name, mesh, data, bcs)

Create a density field using Unicode notation.
"""
ρ_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

"""
    μ_field(name, mesh, data, bcs)

Create a dynamic viscosity field using Unicode notation.
"""
μ_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

"""
    ν_field(name, mesh, data, bcs)

Create a kinematic viscosity field using Unicode notation.
"""
ν_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

"""
    k_field(name, mesh, data, bcs)

Create a turbulent kinetic energy field.
"""
k_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

"""
    ε_field(name, mesh, data, bcs)

Create a turbulent dissipation rate field using Unicode notation.
"""
ε_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

"""
    ω_field(name, mesh, data, bcs)

Create a specific dissipation rate field using Unicode notation.
"""
ω_field(name, mesh, data, bcs) = ScalarField(name, mesh, data, bcs)

# Dimensionless numbers using Unicode notation
"""
    Re(ρ, U, L, μ)

Reynolds number: Re = ρUL/μ
"""
Re(ρ, U, L, μ) = ρ * U * L / μ

"""
    Pr(ν, α)

Prandtl number: Pr = ν/α (momentum diffusivity / thermal diffusivity)
"""
Pr(ν, α) = ν / α

"""
    Gr(g, β, ΔT, L, ν)

Grashof number: Gr = gβΔTL³/ν²
"""
Gr(g, β, ΔT, L, ν) = g * β * ΔT * L^3 / ν^2

"""
    Ma(U, c)

Mach number: Ma = U/c (velocity / speed of sound)
"""
Ma(U, c) = U / c

# Turbulence model constants using Unicode notation
"""
Standard k-ε model constants
"""
const Cμ = 0.09      # k-ε model constant
const Cε1 = 1.44     # k-ε model constant  
const Cε2 = 1.92     # k-ε model constant
const σk = 1.0       # Turbulent Prandtl number for k
const σε = 1.3       # Turbulent Prandtl number for ε

# ============================================================================
# Physics Type Symbols for DSL
# ============================================================================

"""
Physics type symbols used by the @physics macro in solver DSL.
These define different physics models available for CFD simulations.
"""

# Flow models
const IncompressibleFlow = :IncompressibleFlow
const CompressibleFlow = :CompressibleFlow
const LaminarFlow = :LaminarFlow
const TurbulentFlow = :TurbulentFlow

# Heat transfer models
const HeatTransfer = :HeatTransfer

# Multiphase models  
const MultiphaseFlow = :MultiphaseFlow

# Time dependency
const TransientFlow = :TransientFlow
const SteadyFlow = :SteadyFlow

# Combined physics models (commonly used combinations)
const IncompressibleTurbulentFlow = :IncompressibleTurbulentFlow
const CompressibleTurbulentFlow = :CompressibleTurbulentFlow
const IncompressibleHeatTransfer = :IncompressibleHeatTransfer
const TurbulentHeatTransfer = :TurbulentHeatTransfer

# Export combined models too
export IncompressibleTurbulentFlow, CompressibleTurbulentFlow
export IncompressibleHeatTransfer, TurbulentHeatTransfer

# ============================================================================
# Algorithm Types for DSL
# ============================================================================

"""
Algorithm type symbols used by the @algorithm macro in solver DSL.
These define different solution algorithms available for CFD simulations.
"""

# Algorithm type symbols (use Algorithm suffix to avoid conflicts with struct definitions)
const SIMPLEAlgorithm = :SIMPLE
const PISOAlgorithm = :PISO  
const PIMPLEAlgorithm = :PIMPLE

# Enhanced algorithms
const SIMPLECAlgorithm = :SIMPLEC
const PIMPLECAlgorithm = :PIMPLEC

# Time stepping algorithms
const ForwardEulerAlgorithm = :ForwardEuler
const BackwardEulerAlgorithm = :BackwardEuler
const CrankNicolsonAlgorithm = :CrankNicolson

# Parallel algorithms
const ParallelSIMPLEAlgorithm = :ParallelSIMPLE
const ParallelPISOAlgorithm = :ParallelPISO
const ParallelPIMPLEAlgorithm = :ParallelPIMPLE

# Export algorithm type symbols (avoiding conflicts with struct exports)
export SIMPLEAlgorithm, PISOAlgorithm, PIMPLEAlgorithm, SIMPLECAlgorithm, PIMPLECAlgorithm
export ForwardEulerAlgorithm, BackwardEulerAlgorithm, CrankNicolsonAlgorithm
export ParallelSIMPLEAlgorithm, ParallelPISOAlgorithm, ParallelPIMPLEAlgorithm

end # module Physics
