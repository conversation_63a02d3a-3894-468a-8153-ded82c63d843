"""
    BoundaryConditions Module
    
    Comprehensive boundary condition ecosystem for CFD applications.
    Provides OpenFOAM-compatible BC types with modern Julia features.
"""
module BoundaryConditions

export AbstractBoundaryCondition, BoundaryConditionRegistry
export DirichletBC, NeumannBC, RobinBC, MixedBC

# Core BC types
export FixedValueBC, ZeroGradientBC, CalculatedBC
export InletOutletBC, PressureInletVelocityOutletBC, TotalPressureBC, FixedFluxPressureBC

# Wall BCs
export NoSlipWallBC, SlipWallBC, PartialSlipWallBC, MovingWallBC, WallFunctionBC

# Symmetry and periodicity
export SymmetryBC, SymmetryPlaneBC, CyclicBC, CyclicAMIBC, PeriodicBC

# Advanced BCs
export LinearRampBC, TableBC, CodedBC, InterfaceBC, CoupledBC

# Turbulence BCs
export KqRWallFunctionBC, EpsilonWallFunctionBC, OmegaWallFunctionBC
export TurbulentInletBC, TurbulentIntensityInletBC

# Heat transfer BCs
export FixedTemperatureBC, ConvectiveHeatFluxBC, RadiationBC, ConjugateHeatTransferBC

# Multiphase BCs
export AlphaInletBC, SurfaceTensionBC, ContactAngleBC

# BC utilities
export apply_boundary_condition!, update_boundary_values!, validate_boundary_condition
export interpolate_boundary_value, get_boundary_gradient, evaluate_bc_value, evaluate_bc_gradient
export @bc_register, @bc_validate

using LinearAlgebra
using Printf
using Dates

# ============================================================================
# ABSTRACT BASE TYPE AND CORE INFRASTRUCTURE
# ============================================================================

"""
    AbstractBoundaryCondition
    
Base type for all boundary conditions in the CFD framework.
"""
abstract type AbstractBoundaryCondition end

"""
    BoundaryConditionInfo
    
Metadata about boundary condition implementation.
"""
struct BoundaryConditionInfo
    name::String
    description::String
    field_types::Vector{Symbol}    # [:scalar, :vector, :tensor]
    physics_types::Vector{Symbol}  # [:incompressible, :compressible, :turbulent, :thermal]
    openfoam_equivalent::String
    validation_rules::Vector{Function}
    examples::Vector{String}
end

"""
    BoundaryConditionRegistry
    
Global registry for boundary condition types and their properties.
"""
struct BoundaryConditionRegistry
    conditions::Dict{Symbol, Type{<:AbstractBoundaryCondition}}
    info::Dict{Symbol, BoundaryConditionInfo}
    aliases::Dict{Symbol, Symbol}
    
    function BoundaryConditionRegistry()
        new(
            Dict{Symbol, Type{<:AbstractBoundaryCondition}}(),
            Dict{Symbol, BoundaryConditionInfo}(),
            Dict{Symbol, Symbol}()
        )
    end
end

# Global registry instance
const BC_REGISTRY = BoundaryConditionRegistry()

# ============================================================================
# CORE BC TYPES (FUNDAMENTAL MATHEMATICAL CONDITIONS)
# ============================================================================

"""
    DirichletBC{T} <: AbstractBoundaryCondition
    
Fixed value boundary condition: φ = specified_value
"""
struct DirichletBC{T} <: AbstractBoundaryCondition
    value::T
    time_dependence::Union{Function, Nothing}
    space_dependence::Union{Function, Nothing}
    
    function DirichletBC(value::T; time_dep=nothing, space_dep=nothing) where T
        new{T}(value, time_dep, space_dep)
    end
end

"""
    NeumannBC{T} <: AbstractBoundaryCondition
    
Fixed gradient boundary condition: ∂φ/∂n = specified_gradient
"""
struct NeumannBC{T} <: AbstractBoundaryCondition
    gradient::T
    time_dependence::Union{Function, Nothing}
    space_dependence::Union{Function, Nothing}
    
    function NeumannBC(gradient::T; time_dep=nothing, space_dep=nothing) where T
        new{T}(gradient, time_dep, space_dep)
    end
end

"""
    RobinBC{T_alpha, T_beta, T_gamma} <: AbstractBoundaryCondition
    
Mixed boundary condition: α·φ + β·(∂φ/∂n) = γ
"""
struct RobinBC{T_alpha, T_beta, T_gamma} <: AbstractBoundaryCondition
    α::T_alpha  # Coefficient for field value
    β::T_beta   # Coefficient for normal gradient  
    γ::T_gamma  # Right-hand side value/function
    
    function RobinBC(α::T_alpha, β::T_beta, γ::T_gamma) where {T_alpha, T_beta, T_gamma}
        new{T_alpha, T_beta, T_gamma}(α, β, γ)
    end
end

"""
    MixedBC <: AbstractBoundaryCondition
    
Conditional boundary condition that switches between Dirichlet and Neumann.
"""
struct MixedBC <: AbstractBoundaryCondition
    condition::Function  # Returns true for Dirichlet, false for Neumann
    dirichlet_value::Any
    neumann_gradient::Any
    
    function MixedBC(condition_func, dirichlet_val, neumann_grad)
        new(condition_func, dirichlet_val, neumann_grad)
    end
end

# ============================================================================
# OPENFOAM-COMPATIBLE BC TYPES
# ============================================================================

"""
    FixedValueBC{T} <: AbstractBoundaryCondition
    
OpenFOAM fixedValue: φ = constant_value
"""
struct FixedValueBC{T} <: AbstractBoundaryCondition
    value::T
    
    FixedValueBC(value::T) where T = new{T}(value)
end

"""
    ZeroGradientBC <: AbstractBoundaryCondition
    
OpenFOAM zeroGradient: ∂φ/∂n = 0
"""
struct ZeroGradientBC <: AbstractBoundaryCondition
    # No parameters needed
end

"""
    CalculatedBC <: AbstractBoundaryCondition
    
OpenFOAM calculated: φ calculated from other fields
"""
struct CalculatedBC <: AbstractBoundaryCondition
    calculation_function::Function
end

# ============================================================================
# INLET/OUTLET BC TYPES
# ============================================================================

"""
    InletOutletBC <: AbstractBoundaryCondition
    
Switches between inlet (fixed value) and outlet (zero gradient) based on flow direction.
"""
struct InletOutletBC <: AbstractBoundaryCondition
    inlet_value::Any
    flow_direction_field::Symbol  # Field name to check flow direction (e.g., :U)
    
    function InletOutletBC(inlet_val, flow_field=:U)
        new(inlet_val, flow_field)
    end
end

"""
    PressureInletVelocityOutletBC <: AbstractBoundaryCondition
    
Pressure specified at inlet, zero gradient at outlet.
"""
struct PressureInletVelocityOutletBC <: AbstractBoundaryCondition
    pressure_value::Float64
    velocity_field::Symbol
    
    function PressureInletVelocityOutletBC(p_val, u_field=:U)
        new(p_val, u_field)
    end
end

"""
    TotalPressureBC <: AbstractBoundaryCondition
    
Total pressure boundary condition: p₀ = p + ½ρU²
"""
struct TotalPressureBC <: AbstractBoundaryCondition
    total_pressure::Float64
    density::Float64
    velocity_field::Symbol
    
    function TotalPressureBC(p0, ρ=1.0, u_field=:U)
        new(p0, ρ, u_field)
    end
end

"""
    FixedFluxPressureBC <: AbstractBoundaryCondition
    
Pressure BC that enforces specified mass flux.
"""
struct FixedFluxPressureBC <: AbstractBoundaryCondition
    mass_flux::Float64
    density::Float64
    
    function FixedFluxPressureBC(flux, ρ=1.0)
        new(flux, ρ)
    end
end

# ============================================================================
# WALL BC TYPES
# ============================================================================

"""
    NoSlipWallBC <: AbstractBoundaryCondition
    
No-slip wall: U = 0 (or U = wall_velocity for moving walls)
"""
struct NoSlipWallBC <: AbstractBoundaryCondition
    wall_velocity::Vector{Float64}
    
    NoSlipWallBC() = new([0.0, 0.0, 0.0])
    NoSlipWallBC(u_wall) = new(u_wall)
end

"""
    SlipWallBC <: AbstractBoundaryCondition
    
Slip wall: U·n = 0, ∂(U - (U·n)n)/∂n = 0
"""
struct SlipWallBC <: AbstractBoundaryCondition
    # Slip condition - no parameters needed
end

"""
    PartialSlipWallBC <: AbstractBoundaryCondition
    
Partial slip wall with slip coefficient: U_parallel = β·∂U_parallel/∂n
"""
struct PartialSlipWallBC <: AbstractBoundaryCondition
    slip_coefficient::Float64  # β parameter
    
    function PartialSlipWallBC(β=0.1)
        new(β)
    end
end

"""
    MovingWallBC <: AbstractBoundaryCondition
    
Moving wall with specified velocity or angular velocity.
"""
struct MovingWallBC <: AbstractBoundaryCondition
    velocity::Union{Vector{Float64}, Function}
    angular_velocity::Union{Vector{Float64}, Nothing}
    rotation_center::Union{Vector{Float64}, Nothing}
    
    function MovingWallBC(vel; ω=nothing, center=nothing)
        new(vel, ω, center)
    end
end

"""
    WallFunctionBC <: AbstractBoundaryCondition
    
Wall function for near-wall treatment in turbulent flows.
"""
struct WallFunctionBC <: AbstractBoundaryCondition
    wall_function_type::Symbol  # :standard, :enhanced, :scalable
    roughness_height::Float64
    roughness_constant::Float64
    
    function WallFunctionBC(type=:standard; ks=0.0, Cs=0.5)
        new(type, ks, Cs)
    end
end

# ============================================================================
# SYMMETRY AND PERIODICITY BC TYPES
# ============================================================================

"""
    SymmetryBC <: AbstractBoundaryCondition
    
Symmetry condition: ∂φ/∂n = 0, U·n = 0
"""
struct SymmetryBC <: AbstractBoundaryCondition
    # No parameters needed for symmetry
end

"""
    SymmetryPlaneBC <: AbstractBoundaryCondition
    
Symmetry plane with specified normal direction.
"""
struct SymmetryPlaneBC <: AbstractBoundaryCondition
    normal_direction::Vector{Float64}
    
    function SymmetryPlaneBC(normal=[0.0, 0.0, 1.0])
        new(normalize(normal))
    end
end

"""
    CyclicBC <: AbstractBoundaryCondition
    
Cyclic/periodic boundary condition for periodic domains.
"""
struct CyclicBC <: AbstractBoundaryCondition
    neighbor_patch::String
    rotation_angle::Float64
    rotation_axis::Vector{Float64}
    translation_vector::Vector{Float64}
    
    function CyclicBC(neighbor; rotation=0.0, axis=[0,0,1], translation=[0,0,0])
        new(neighbor, rotation, axis, translation)
    end
end

"""
    CyclicAMIBC <: AbstractBoundaryCondition
    
Cyclic Arbitrary Mesh Interface for non-matching periodic boundaries.
"""
struct CyclicAMIBC <: AbstractBoundaryCondition
    neighbor_patch::String
    low_weight_correction::Float64
    
    function CyclicAMIBC(neighbor; correction=0.2)
        new(neighbor, correction)
    end
end

"""
    PeriodicBC <: AbstractBoundaryCondition
    
Simple periodic BC: φ(x₁) = φ(x₂) where x₁ and x₂ are corresponding points.
"""
struct PeriodicBC <: AbstractBoundaryCondition
    paired_patch::String
    offset_vector::Vector{Float64}
    
    function PeriodicBC(pair_patch, offset=[0.0, 0.0, 0.0])
        new(pair_patch, offset)
    end
end

# ============================================================================
# ADVANCED BC TYPES
# ============================================================================

"""
    LinearRampBC <: AbstractBoundaryCondition
    
Linear ramp in time: φ(t) = φ₀ + (φ₁ - φ₀) × min(t/T, 1)
"""
struct LinearRampBC <: AbstractBoundaryCondition
    initial_value::Any
    final_value::Any
    ramp_duration::Float64
    start_time::Float64
    
    function LinearRampBC(φ₀, φ₁, duration; start_t=0.0)
        new(φ₀, φ₁, duration, start_t)
    end
end

"""
    TableBC <: AbstractBoundaryCondition
    
Tabulated boundary condition with interpolation.
"""
struct TableBC <: AbstractBoundaryCondition
    time_table::Vector{Float64}
    value_table::Vector{Any}
    interpolation_method::Symbol  # :linear, :cubic, :spline
    extrapolation::Symbol         # :constant, :linear, :cycle
    
    function TableBC(times, values; interp=:linear, extrap=:constant)
        new(times, values, interp, extrap)
    end
end

"""
    CodedBC <: AbstractBoundaryCondition
    
User-defined boundary condition via Julia function.
"""
struct CodedBC <: AbstractBoundaryCondition
    user_function::Function  # f(x, y, z, t, field_values) -> boundary_value
    dependencies::Vector{Symbol}  # Other fields this BC depends on
    
    function CodedBC(func; deps=Symbol[])
        new(func, deps)
    end
end

"""
    InterfaceBC <: AbstractBoundaryCondition
    
Interface boundary condition for coupled domains/physics.
"""
struct InterfaceBC <: AbstractBoundaryCondition
    coupled_field::Symbol
    coupled_domain::String
    transfer_function::Function
    coupling_strength::Float64
    
    function InterfaceBC(field, domain, transfer_func; strength=1.0)
        new(field, domain, transfer_func, strength)
    end
end

"""
    CoupledBC <: AbstractBoundaryCondition
    
Strongly coupled boundary condition for multi-physics problems.
"""
struct CoupledBC <: AbstractBoundaryCondition
    coupled_fields::Vector{Symbol}
    coupling_matrix::Matrix{Float64}
    residual_function::Function
    
    function CoupledBC(fields, coupling, residual)
        new(fields, coupling, residual)
    end
end

# ============================================================================
# TURBULENCE BC TYPES
# ============================================================================

"""
    KqRWallFunctionBC <: AbstractBoundaryCondition
    
Wall function for turbulent kinetic energy k.
"""
struct KqRWallFunctionBC <: AbstractBoundaryCondition
    wall_function_type::Symbol  # :standard, :enhanced
    
    function KqRWallFunctionBC(type=:standard)
        new(type)
    end
end

"""
    EpsilonWallFunctionBC <: AbstractBoundaryCondition
    
Wall function for turbulent dissipation rate ε.
"""
struct EpsilonWallFunctionBC <: AbstractBoundaryCondition
    wall_function_type::Symbol
    roughness_height::Float64
    
    function EpsilonWallFunctionBC(type=:standard; ks=0.0)
        new(type, ks)
    end
end

"""
    OmegaWallFunctionBC <: AbstractBoundaryCondition
    
Wall function for specific dissipation rate ω.
"""
struct OmegaWallFunctionBC <: AbstractBoundaryCondition
    wall_function_type::Symbol
    roughness_height::Float64
    
    function OmegaWallFunctionBC(type=:standard; ks=0.0)
        new(type, ks)
    end
end

"""
    TurbulentInletBC <: AbstractBoundaryCondition
    
Turbulent inlet with specified turbulence intensity and length scale.
"""
struct TurbulentInletBC <: AbstractBoundaryCondition
    velocity_magnitude::Float64
    turbulence_intensity::Float64  # I = u'/U
    length_scale::Float64           # Turbulence length scale
    
    function TurbulentInletBC(U_mag, I, L)
        new(U_mag, I, L)
    end
end

"""
    TurbulentIntensityInletBC <: AbstractBoundaryCondition
    
Inlet BC that calculates k and ε from turbulence intensity.
"""
struct TurbulentIntensityInletBC <: AbstractBoundaryCondition
    turbulence_intensity::Float64
    viscosity_ratio::Float64  # νₜ/ν
    
    function TurbulentIntensityInletBC(I, νₜ_ratio=10.0)
        new(I, νₜ_ratio)
    end
end

# ============================================================================
# HEAT TRANSFER BC TYPES
# ============================================================================

"""
    FixedTemperatureBC <: AbstractBoundaryCondition
    
Fixed temperature boundary condition.
"""
struct FixedTemperatureBC <: AbstractBoundaryCondition
    temperature::Float64
    
    function FixedTemperatureBC(T)
        new(T)
    end
end

"""
    ConvectiveHeatFluxBC <: AbstractBoundaryCondition
    
Convective heat transfer: q = h(T - T_∞)
"""
struct ConvectiveHeatFluxBC <: AbstractBoundaryCondition
    heat_transfer_coefficient::Float64  # h
    ambient_temperature::Float64        # T_∞
    
    function ConvectiveHeatFluxBC(h, T_amb)
        new(h, T_amb)
    end
end

"""
    RadiationBC <: AbstractBoundaryCondition
    
Radiation heat transfer: q = εσ(T⁴ - T_env⁴)
"""
struct RadiationBC <: AbstractBoundaryCondition
    emissivity::Float64
    environment_temperature::Float64
    stefan_boltzmann::Float64
    
    function RadiationBC(ε, T_env; σ=5.67e-8)
        new(ε, T_env, σ)
    end
end

"""
    ConjugateHeatTransferBC <: AbstractBoundaryCondition
    
Conjugate heat transfer at solid-fluid interface.
"""
struct ConjugateHeatTransferBC <: AbstractBoundaryCondition
    solid_conductivity::Float64
    solid_thickness::Float64
    solid_temperature::Union{Float64, Function}
    
    function ConjugateHeatTransferBC(k_solid, thickness, T_solid)
        new(k_solid, thickness, T_solid)
    end
end

# ============================================================================
# MULTIPHASE BC TYPES  
# ============================================================================

"""
    AlphaInletBC <: AbstractBoundaryCondition
    
Volume fraction inlet for multiphase flows.
"""
struct AlphaInletBC <: AbstractBoundaryCondition
    phase_fraction::Float64
    phase_velocity::Vector{Float64}
    
    function AlphaInletBC(α, U_phase=[0.0, 0.0, 0.0])
        new(α, U_phase)
    end
end

"""
    SurfaceTensionBC <: AbstractBoundaryCondition
    
Surface tension at interface.
"""
struct SurfaceTensionBC <: AbstractBoundaryCondition
    surface_tension_coefficient::Float64
    curvature_calculation::Symbol  # :geometric, :algebraic
    
    function SurfaceTensionBC(σ; curvature=:geometric)
        new(σ, curvature)
    end
end

"""
    ContactAngleBC <: AbstractBoundaryCondition
    
Contact angle at three-phase contact line.
"""
struct ContactAngleBC <: AbstractBoundaryCondition
    contact_angle::Float64  # In radians
    dynamic_contact_angle::Bool
    
    function ContactAngleBC(θ; dynamic=false)
        new(θ, dynamic)
    end
end

# ============================================================================
# BC UTILITIES AND APPLICATION FUNCTIONS
# ============================================================================

"""
    apply_boundary_condition!(matrix, rhs, bc, face_data, field_data, time)
    
Apply boundary condition to matrix equation Ax = b.
"""
function apply_boundary_condition!(A, b, bc::AbstractBoundaryCondition, 
                                  face_data, field_data, current_time=0.0)
    # Dispatch to specific implementation based on BC type
    if bc isa DirichletBC
        apply_dirichlet!(A, b, bc, face_data, field_data, current_time)
    elseif bc isa NeumannBC  
        apply_neumann!(A, b, bc, face_data, field_data, current_time)
    elseif bc isa RobinBC
        apply_robin!(A, b, bc, face_data, field_data, current_time)
    elseif bc isa MixedBC
        apply_mixed!(A, b, bc, face_data, field_data, current_time)
    else
        apply_general_bc!(A, b, bc, face_data, field_data, current_time)
    end
end

function apply_dirichlet!(A, b, bc::DirichletBC, face_data, field_data, t)
    # Implementation for Dirichlet BC
    owner_cell = face_data.owner_cell
    area = face_data.area
    distance = face_data.distance
    
    # Get boundary value
    bc_value = evaluate_bc_value(bc, face_data.center, t)
    
    # Modify matrix coefficients
    coeff = area / distance
    A[owner_cell, owner_cell] += coeff
    b[owner_cell] += coeff * bc_value
end

function apply_neumann!(A, b, bc::NeumannBC, face_data, field_data, t)
    # Implementation for Neumann BC
    owner_cell = face_data.owner_cell
    area = face_data.area
    
    # Get boundary gradient
    gradient = evaluate_bc_gradient(bc, face_data.center, t)
    
    # Add flux to RHS
    b[owner_cell] += gradient * area
end

function apply_robin!(A, b, bc::RobinBC, face_data, field_data, t)
    # Implementation for Robin BC: α·φ + β·(∂φ/∂n) = γ
    owner_cell = face_data.owner_cell
    area = face_data.area
    distance = face_data.distance
    
    # Robin BC coefficients
    α, β, γ = bc.α, bc.β, bc.γ
    
    # Modify matrix: (α + β/Δn)φ = γ
    coeff = area * β / distance
    A[owner_cell, owner_cell] += coeff + area * α
    b[owner_cell] += area * γ
end

function apply_mixed!(A, b, bc::MixedBC, face_data, field_data, t)
    # Evaluate condition to determine BC type
    if bc.condition(face_data.center, field_data, t)
        # Use Dirichlet
        dirichlet_bc = DirichletBC(bc.dirichlet_value)
        apply_dirichlet!(A, b, dirichlet_bc, face_data, field_data, t)
    else
        # Use Neumann
        neumann_bc = NeumannBC(bc.neumann_gradient)
        apply_neumann!(A, b, neumann_bc, face_data, field_data, t)
    end
end

function apply_general_bc!(A, b, bc::AbstractBoundaryCondition, face_data, field_data, t)
    # Default implementation for custom BC types
    # Subclasses should override this method
    @warn "Generic BC application for $(typeof(bc)) - implement specific method for better performance"
    
    # Try to convert to basic BC type
    if hasfield(typeof(bc), :value)
        # Treat as Dirichlet
        dirichlet_bc = DirichletBC(bc.value)
        apply_dirichlet!(A, b, dirichlet_bc, face_data, field_data, t)
    elseif hasfield(typeof(bc), :gradient)
        # Treat as Neumann
        neumann_bc = NeumannBC(bc.gradient)
        apply_neumann!(A, b, neumann_bc, face_data, field_data, t)
    else
        error("Unknown BC type: $(typeof(bc))")
    end
end

"""
    evaluate_bc_value(bc, location, time)
    
Evaluate boundary condition value at given location and time.
"""
function evaluate_bc_value(bc::DirichletBC, location, time=0.0)
    value = bc.value
    
    # Apply time dependence if specified
    if bc.time_dependence !== nothing
        value = bc.time_dependence(value, time)
    end
    
    # Apply spatial dependence if specified
    if bc.space_dependence !== nothing
        value = bc.space_dependence(value, location...)
    end
    
    return value
end

function evaluate_bc_gradient(bc::NeumannBC, location, time=0.0)
    gradient = bc.gradient
    
    # Apply time dependence if specified
    if bc.time_dependence !== nothing
        gradient = bc.time_dependence(gradient, time)
    end
    
    # Apply spatial dependence if specified
    if bc.space_dependence !== nothing
        gradient = bc.space_dependence(gradient, location...)
    end
    
    return gradient
end

"""
    update_boundary_values!(field, time)
    
Update time-dependent boundary values.
"""
function update_boundary_values!(field, current_time)
    for (patch_name, bc) in field.boundary_conditions
        if has_time_dependence(bc)
            # Update BC for current time
            update_bc_time!(bc, current_time)
        end
    end
end

function has_time_dependence(bc::AbstractBoundaryCondition)
    # Check if BC has time-dependent parameters
    return (hasfield(typeof(bc), :time_dependence) && 
            getfield(bc, :time_dependence) !== nothing) ||
           (hasfield(typeof(bc), :user_function)) ||
           (hasfield(typeof(bc), :final_value))  # For ramp BCs
end

function update_bc_time!(bc::AbstractBoundaryCondition, time)
    # Default implementation - specific BC types can override
    # For now, just store the current time if BC supports it
    if hasfield(typeof(bc), :current_time)
        setfield!(bc, :current_time, time)
    end
end

"""
    validate_boundary_condition(bc, field_type, physics_type)
    
Validate boundary condition compatibility with field and physics.
"""
function validate_boundary_condition(bc::AbstractBoundaryCondition, 
                                   field_type::Symbol, physics_type::Symbol)
    # Get BC info from registry
    bc_symbol = Symbol(typeof(bc).name.name)
    
    if haskey(BC_REGISTRY.info, bc_symbol)
        info = BC_REGISTRY.info[bc_symbol]
        
        # Check field type compatibility
        if !isempty(info.field_types) && !(field_type in info.field_types)
            @warn "BC $(bc_symbol) may not be suitable for $(field_type) fields"
            return false
        end
        
        # Check physics compatibility
        if !isempty(info.physics_types) && !(physics_type in info.physics_types)
            @warn "BC $(bc_symbol) may not be suitable for $(physics_type) physics"
            return false
        end
        
        # Run custom validation rules
        for rule in info.validation_rules
            if !rule(bc, field_type, physics_type)
                return false
            end
        end
    end
    
    return true
end

"""
    interpolate_boundary_value(bc, location, time, method=:linear)
    
Interpolate boundary value for table-based BCs.
"""
function interpolate_boundary_value(bc::TableBC, location, time, method=:linear)
    # Find time indices for interpolation
    t_idx = searchsorted(bc.time_table, time)
    
    if isempty(t_idx)
        # Before first time
        if bc.extrapolation == :constant
            return bc.value_table[1]
        elseif bc.extrapolation == :linear
            # Linear extrapolation
            dt = bc.time_table[2] - bc.time_table[1]
            dv = bc.value_table[2] - bc.value_table[1]
            return bc.value_table[1] + (time - bc.time_table[1]) * dv / dt
        end
    elseif t_idx.start > length(bc.time_table)
        # After last time
        if bc.extrapolation == :constant
            return bc.value_table[end]
        elseif bc.extrapolation == :cycle
            # Cyclic extrapolation
            period = bc.time_table[end] - bc.time_table[1]
            cyclic_time = mod(time - bc.time_table[1], period) + bc.time_table[1]
            return interpolate_boundary_value(bc, location, cyclic_time, method)
        end
    else
        # Interpolation between points
        if bc.interpolation_method == :linear
            i1 = t_idx.start - 1
            i2 = min(i1 + 1, length(bc.time_table))
            
            if i1 == i2
                return bc.value_table[i1]
            end
            
            # Linear interpolation
            t1, t2 = bc.time_table[i1], bc.time_table[i2]
            v1, v2 = bc.value_table[i1], bc.value_table[i2]
            
            weight = (time - t1) / (t2 - t1)
            return v1 + weight * (v2 - v1)
        end
    end
    
    return bc.value_table[1]  # Fallback
end

"""
    get_boundary_gradient(bc, field_data, face_data)
    
Calculate boundary gradient for various BC types.
"""
function get_boundary_gradient(bc::AbstractBoundaryCondition, field_data, face_data)
    if bc isa NeumannBC
        return bc.gradient
    elseif bc isa DirichletBC
        # Calculate gradient from Dirichlet value
        bc_value = evaluate_bc_value(bc, face_data.center, face_data.time)
        cell_value = field_data[face_data.owner_cell]
        return (bc_value - cell_value) / face_data.distance
    elseif bc isa ZeroGradientBC
        return 0.0
    else
        return 0.0  # Default zero gradient
    end
end

# ============================================================================
# REGISTRATION AND VALIDATION MACROS
# ============================================================================

"""
    @bc_register(BCType, info)
    
Register a boundary condition type with metadata.
"""
macro bc_register(bc_type, info_expr)
    return quote
        local bc_symbol = Symbol($(string(bc_type)))
        BC_REGISTRY.conditions[bc_symbol] = $(bc_type)
        BC_REGISTRY.info[bc_symbol] = $(esc(info_expr))
    end
end

"""
    @bc_validate(bc, conditions...)
    
Validate boundary condition with custom rules.
"""
macro bc_validate(bc, conditions...)
    validation_code = quote
        bc_val = $(esc(bc))
        all_valid = true
    end
    
    for condition in conditions
        push!(validation_code.args, quote
            if !($(esc(condition)))
                @error "BC validation failed: $($(string(condition)))"
                all_valid = false
            end
        end)
    end
    
    push!(validation_code.args, :(all_valid))
    return validation_code
end

# ============================================================================
# INITIALIZATION AND REGISTRY SETUP
# ============================================================================

function __init__()
    # Register core BC types
    register_core_boundary_conditions!()
end

function register_core_boundary_conditions!()
    # Register basic mathematical BCs
    @bc_register DirichletBC BoundaryConditionInfo(
        "DirichletBC", 
        "Fixed value boundary condition",
        [:scalar, :vector, :tensor],
        [:incompressible, :compressible, :thermal, :turbulent],
        "fixedValue",
        [validate_dirichlet_bc],
        ["DirichletBC(1.0)", "DirichletBC([1.0, 0.0, 0.0])"]
    )
    
    @bc_register NeumannBC BoundaryConditionInfo(
        "NeumannBC",
        "Fixed gradient boundary condition", 
        [:scalar, :vector, :tensor],
        [:incompressible, :compressible, :thermal, :turbulent],
        "fixedGradient / zeroGradient",
        [validate_neumann_bc],
        ["NeumannBC(0.0)", "NeumannBC([0.0, 0.0, 0.0])"]
    )
    
    @bc_register RobinBC BoundaryConditionInfo(
        "RobinBC",
        "Mixed boundary condition: α·φ + β·(∂φ/∂n) = γ",
        [:scalar, :vector],
        [:incompressible, :compressible, :thermal],
        "mixed",
        [validate_robin_bc],
        ["RobinBC(1.0, 1.0, 0.0)"]
    )
    
    # Register OpenFOAM-compatible BCs
    @bc_register FixedValueBC BoundaryConditionInfo(
        "FixedValueBC",
        "OpenFOAM fixedValue equivalent",
        [:scalar, :vector, :tensor],
        [:incompressible, :compressible, :thermal, :turbulent],
        "fixedValue",
        [validate_fixed_value_bc],
        ["FixedValueBC(1.0)"]
    )
    
    @bc_register ZeroGradientBC BoundaryConditionInfo(
        "ZeroGradientBC", 
        "OpenFOAM zeroGradient equivalent",
        [:scalar, :vector, :tensor],
        [:incompressible, :compressible, :thermal, :turbulent],
        "zeroGradient",
        [validate_zero_gradient_bc],
        ["ZeroGradientBC()"]
    )
    
    # Add aliases for common names
    BC_REGISTRY.aliases[:fixedValue] = :FixedValueBC
    BC_REGISTRY.aliases[:zeroGradient] = :ZeroGradientBC
    BC_REGISTRY.aliases[:noSlip] = :NoSlipWallBC
    BC_REGISTRY.aliases[:symmetry] = :SymmetryBC
end

# Validation functions for core BC types
function validate_dirichlet_bc(bc::DirichletBC, field_type, physics_type)
    # Ensure value is compatible with field type
    if field_type == :scalar && !(bc.value isa Number || bc.value isa Function)
        @error "Scalar field requires numeric or function value for DirichletBC"
        return false
    end
    return true
end

function validate_neumann_bc(bc::NeumannBC, field_type, physics_type)
    # Check gradient compatibility
    return true  # Generally permissive
end

function validate_robin_bc(bc::RobinBC, field_type, physics_type)
    # Ensure coefficients are reasonable
    if bc.β ≈ 0.0 && bc.α ≈ 0.0
        @error "RobinBC cannot have both α and β ≈ 0"
        return false
    end
    return true
end

function validate_fixed_value_bc(bc::FixedValueBC, field_type, physics_type)
    return true  # Simple validation
end

function validate_zero_gradient_bc(bc::ZeroGradientBC, field_type, physics_type)
    return true  # Always valid
end

end # module BoundaryConditions